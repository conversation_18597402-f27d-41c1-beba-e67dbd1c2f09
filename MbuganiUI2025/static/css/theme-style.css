/*!

Theme Name: micato-safaris
Description: Dedicated Wordpress theme for Default Theme

*/
@media only screen and (min-width: 1441px) {
  .home .hero-info-wrapper {
    max-width: 1440px;
    right: 0;
    margin: auto;
  }
}
@media only screen and (min-width: 769px) {
  .home .hero-info {
    right: auto;
    top: 47%;
    left: 0;
    transform: none;
  }
  .home .hero-info p {
    display: none;
  }
  .home .hero-info p:first-child {
    display: block;
  }
  .home .hero-info img {
    max-width: 375px;
  }
}
@media only screen and (max-width: 768px) {
  .home .page-hero__thumbnail {
    background: url(images/home-mobile.jpg) center -350px no-repeat !important;
    background-size: 779px 1350px !important;
  }
  .home .hero-info {
    top: 205px;
  }
  .home .hero-info p {
    display: none;
  }
  .home .hero-info p:not(:first-child) {
    display: block;
  }
  .home .hero-info img {
    max-width: 250px;
  }
  .home .hero-info .c-btn-wrapper {
    width: auto;
  }
  .home .hero-info .c-btn-wrapper a {
    transform: scale(0.75);
  }
  .maplinks__image.desktop-image {
    display:none;
  }
  .maplinks__image.mobile-image {
    display:block!important;
  }
  .maplinks__country .maplinks__country-img {
    bottom: 46px;
    left: -3px;
    width: 300px;
  }
  .maplinks__country-spot.maplinks__country-spot--botswana {
    bottom: 19.2%;
    right: 38.2%;
    width: 7vw;
    height: 8vw;
  }
  .maplinks__country-spot.maplinks__country-spot--kenya {
    bottom: 45.2%;
    right: 19.2%;
    height: 9vw;
  }
  .maplinks__country-spot.maplinks__country-spot--namibia {
    bottom: 16.2%;
    left: 46.2%;
    width: 8vw;
    height: 13vw;
  }
  .maplinks__country-spot.maplinks__country-spot--southafrica {
    width: 17vw;
    height: 9vw;
    bottom: 10.5%;
    right: 31.2%;
  }
  .maplinks__country-spot.maplinks__country-spot--rwanda {
    height: 4vw;
    width: 10vw;
  }
  .maplinks__country-spot.maplinks__country-spot--tanzania {
    height: 11vw;
    width: 10vw;
  }
  .maplinks__country-spot.maplinks__country-spot--zambia {
    bottom: 29.2%;
    width: 14vw;
    height: 8vw;
  }
  .maplinks__country-spot.maplinks__country-spot--zimbabwe {
    right: 31.1%;
    width: 7vw;
    height: 6vw;
  }
}
@media only screen and (max-width: 540px) {
  .home .page-hero__thumbnail {
    background: url(images/home-mobile.jpg) center bottom no-repeat !important;
    background-size: cover !important;
  }
}

.block-navigation-links {
  border-top: 90px solid #e5a732;
  border-bottom: 90px solid #e5a732;
  border-left: 78px solid #e5a732;
  border-right: 78px solid #e5a732;
  margin-bottom: 128px;
}
@media only screen and (max-width: 767px) {
  .block-navigation-links {
    border-top: 45px solid #e5a732;
    border-bottom: 45px solid #e5a732;
    border-left: 39px solid #e5a732;
    border-right: 39px solid #e5a732;
    margin-bottom: 96px;
  }
}
.block-navigation-links h2 {
  text-align: center;
}

.block-navigation-links .block-nav-list {
  display: flex;
  justify-content: center;
  width: fit-content;
  margin: 0 auto;
}
/* .block-navigation-links .block-nav-list__item {
  width: min-content!important;
} */

.block-navigation-links .block-nav-list__item a span {
  width: max-content;
  margin-right: 5px;
}


@media only screen and (min-width: 768px) {
  .block-navigation-links .block-nav-list__item.button-link {
    padding-right: 0;
  }
} 
.block-navigation-links .block-nav-list__item .scroll-down {
  display: block;
  position: relative;
  color: #9e6a16;
  padding: 3px 15px;
  letter-spacing: 0.02em;
}

.block-navigation-links .block-nav-list__item a {
  display: flex!important;
  width: fit-content;
  margin: 0 auto;
  position: relative;
  color: #9e6a16;
  padding: 3px 15px;
  letter-spacing: 0.02em;

}

.block-navigation-links .block-nav-list__item a.button, 
.block-navigation-links .block-nav-list__item .scroll-down.button {
  background: #9e6a16;
  color: #FFF;
  margin: 0;
  font-weight: 900;
}
@media only screen and (max-width: 767px) {
  .block-navigation-links .block-nav-list__item a.button, .block-navigation-links .block-nav-list__item .scroll-down.button {
    margin-bottom: 15px;
  }
}
.block-navigation-links .block-nav-list__item a.button:after, .block-navigation-links .block-nav-list__item .scroll-down.button:after {
  display: none;
}
.block-navigation-links .block-nav-list__item a:after, .block-navigation-links .block-nav-list__item .scroll-down:after {
  width: 7px;
  height: 11px;
  background-size: 7px 11px;
  top: 15px;
  right: 0;
}
@media only screen and (max-width: 767px) {
  .block-navigation-links .block-nav-list__item {
    margin-top: 0 !important;
  }
  .block-navigation-links .block-nav-list__item .colored-text {
    display: inline !important;
  }
  .block-navigation-links .block-nav-list__item .scroll-down {
    width: 100% !important;
  }
}
.block-navigation-links li, .block-navigation-links a, .block-navigation-links .colored-text, .block-navigation-links .scroll-down {
  font-family: "Verlag", sans-serif !important;
  font-weight: bold;
  font-size: 14px;
  text-transform: uppercase;
}

.stories-post__wrapper:hover:before {
  opacity: 1;
}
.stories-post__wrapper:before {
  background: -moz-linear-gradient(top, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0.65) 100%);
  background: -webkit-linear-gradient(top, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0.65) 100%);
  background: linear-gradient(to bottom, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0.65) 100%);
  filter: progid:DXImageTransform.Microsoft.gradient( startColorstr="#00000000", endColorstr="#a6000000",GradientType=0 );
  opacity: 0.65;
}

.single-i-experience .block-cta {
  background: #97d0cd;
}

.content-inner > a {
  color: #000;
}

.block-slider-img-text .info-wrapper .c-btn-wrapper-secondary {
  margin: 0;
}
.block-slider-img-text .info-wrapper .content-inner__subtitle {
  font-size: 36px;
  font-family: "GoudyOldstyleW01-Italic_706308", "sans-serif";
  line-height: normal;
  margin-bottom: 30px;
  font-style: normal;
}
.block-slider-img-text .info-wrapper .content-inner__subtitle em {
  font-family: "GoudyOldstyleW01-Italic_706308", "sans-serif";
}

/* blog posts */
@media only screen and (min-width: 1200px) {
  .blog-img {
    height: 188px;
  }
  .blog-img img {
    min-height: 188px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 1199px) {
  .blog-img {
    height: 154px;
  }
  .blog-img img {
    min-height: 154px;
  }
}
/* hubspot forms */
.hbspt-form *, .hbspt-form *:after, .hbspt-form *:before {
  box-sizing: border-box !important;
}
.hbspt-form label {
  font-weight: bold !important;
  display: inline-block;
  margin-bottom: 8px !important;
  font-family: "ITC Berkeley Oldstyle W01 Book", sans-serif !important;
  font-size: 16px;
}
.hbspt-form legend {
  font-size: 16px;
  line-height: normal;
}
.hbspt-form .input {
  margin-bottom: 20px;
}
.hbspt-form input[type=text], .hbspt-form input[type=email], .hbspt-form input[type=tel], .hbspt-form input[type=number], .hbspt-form textarea {
  width: 100% !important;
  min-height: 39px;
  height: auto;
  max-width: none !important;
  border-radius: 0;
  border: 1px solid rgba(187, 131, 42, 0.5);
}
.hbspt-form select {
  height: 39px !important;
  width: 100% !important;
  max-width: none !important;
  appearance: none;
  border: 1px solid rgba(187, 131, 42, 0.5);
  background: #FFF;
  border-radius: 0;
  padding: 0 10px;
  position: relative;
}
.hbspt-form .select-wrap {
  position: relative;
}
.hbspt-form .select-wrap:after {
  content: "";
  display: block;
  position: absolute;
  top: 15px;
  right: 20px;
  width: 0;
  height: 0;
  border-style: solid;
  border-width: 10px 5px 0 5px;
  border-color: #bb832a transparent transparent transparent;
}
.hbspt-form input[type=checkbox] {
  appearance: checkbox;
  margin-right: 10px;
}
.hbspt-form input[type=radio] {
  appearance: radio;
  margin-right: 10px;
}
.hbspt-form ul, .hbspt-form li { 
  list-style: none;
  /* margin: 0 !important;
  padding: 0 !important; */
}
.hbspt-form .hs-error-msgs {
  padding: 0;
}
.hbspt-form .hs-error-msgs label {
  font-weight: bold !important;
  color: red;
}

.gform_wrapper input[type=text], .gform_wrapper input[type=email], .gform_wrapper input[type=tel], .gform_wrapper input[type=number], .gform_wrapper textarea {
  width: 100% !important;
  min-height: 39px;
  height: auto;
  max-width: none !important;
  border-radius: 0;
  border: 1px solid rgba(187, 131, 42, 0.5);
}
.gform_wrapper .ginput_complex br {
  display: none;
}
.gform_wrapper .ginput_complex p {
  margin: 0;
}
.gform_wrapper .ginput_complex span {
  display: block;
  width: 100% !important;
  margin: 0;
}
.gform_wrapper select {
  height: 39px !important;
  width: 100% !important;
  max-width: none !important;
  appearance: none;
  border: 1px solid rgba(187, 131, 42, 0.5);
  background: #FFF;
  border-radius: 0;
  padding: 0 10px;
  position: relative;
}
.gform_wrapper .ginput_container_select {
  position: relative;
}
.gform_wrapper .ginput_container_select:after {
  content: "";
  display: block;
  position: absolute;
  top: 15px;
  right: 20px;
  width: 0;
  height: 0;
  border-style: solid;
  border-width: 10px 5px 0 5px;
  border-color: #bb832a transparent transparent transparent;
}
.gform_wrapper input[type=submit] {
  font-size: 13px !important;
}
.gform_wrapper input[type=radio] {
  appearance: radio;
  margin-right: 10px;
}

/* misc */
@media only screen and (min-width: 768px) and (max-width: 992px) {
  .page-id-1033 .aside-right {
    padding-top: 0;
  }
  .page-id-1033 .aside-right .alignnone {
    margin-top: 0;
  }
}
.rd-posts .rd-post__title {
  padding-bottom: 23px;
}

@media only screen and (max-width: 1199px) {
  .rd-posts .rd-post__title {
    padding-bottom: 17px;
  }
}
@media only screen and (max-width: 767px) {
  .rd-posts .rd-post__title {
    padding-bottom: 12px;
  }
}
.leadparagraph {
  letter-spacing: 0;
}

table td {
  letter-spacing: 0 !important;
}

.stories-hero__content {
  max-width: 100%;
  bottom: 0;
  background: -moz-linear-gradient(top, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0.65) 100%);
  background: -webkit-linear-gradient(top, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0.65) 100%);
  background: linear-gradient(to bottom, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0.65) 100%);
  filter: progid:DXImageTransform.Microsoft.gradient( startColorstr="#00000000", endColorstr="#a6000000",GradientType=0 );
}

.stories-hero__image {
  padding: 0;
  min-height: 782px;
}

.overlay-text {
  max-width: 825px;
  margin: auto;
  padding-bottom: 48px;
}

@media only screen and (max-width: 768px) {
  .stories-hero__image {
    padding: 0;
    min-height: 904px;
  }

  .stories-hero__content h1 {
    font-size: 50px;
    line-height: 1.1;
  }

  .overlay-text {
    max-width: 90%;
  }
}
@media only screen and (max-width: 560px) {
  .stories-hero__image {
    padding: 0;
    min-height: 567px;
  }

  .stories-hero__content h1 {
    font-size: 30px;
    line-height: 1.1;
  }
}

.block-slider-img-text .content-inner__title {
  margin-bottom: 0 !important;
}
@media only screen and (min-width: 992px) {
  .block-slider-img-text .content-inner__title {
    line-height: 45px !important;
  }
}
.block-slider-img-text .content-inner__subtitle {
  font-family: "GoudyOldstyleW01-Italic_706308", "sans-serif" !important;
  margin-bottom: 25px !important;
  display: block;
}

a:focus {
  outline: none !important;
}

.page-template-page-no-hero {
  padding-top: 178px;
}
@media only screen and (max-width: 1199px) {
  .page-template-page-no-hero {
    padding-top: 145px;
  }
}
.page-template-page-no-hero .main-header .main-header__logo svg {
  max-width: 260px;
}
.page-template-page-no-hero .main-header svg,
.page-template-page-no-hero .main-header path,
.single-i-camp-and-lodge .main-header svg,
.single-i-camp-and-lodge .main-header path,
.single-post .main-header svg,
.single-post .main-header path,
body.blog .main-header svg,
body.blog .main-header path {
  fill: #30241c !important;
}
.page-template-page-no-hero .main-header .menu > li > a {
  color: #30241c !important;
}
.page-template-page-no-hero .c-btn.c-btn-primary {
  color: #bb832a;
  border-color: #bb832a;
}
.page-template-page-no-hero .c-btn.c-btn-primary:hover {
  background-color: #bb832a;
  color: #FFF;
}

.main-footer__aside-link {
  text-align: center;
}
.main-footer__aside-link .c-btn-primary {
  border-color: #fbf4ea;
  color: #fbf4ea;
  margin-bottom: 20px;
}
@media only screen and (min-width: 576px) and (max-width: 992px) {
  .main-footer__aside-link .c-btn-primary {
    margin-bottom: 0;
    margin-top: 20px;
  }
}
.main-footer__aside-link .c-btn-primary:hover {
  background-color: #fbf4ea;
  color: #2D2120;
}

.content-wrapper__inner img {
  height: auto !important;
  width: auto !important;
  max-height: 48vh;
  margin-bottom: 4vh;
}
.content-wrapper__inner figcaption {
  max-height: 48vh;
  overflow-y: auto;
}

.bc-gallery__image--md, .bc-gallery__image--sm {
  display: none;
}

.page-id-206 .bc-gallery__image--md, .page-id-206 .bc-gallery__image--sm {
  display: none !important;
}

.mag {
  margin-bottom: 35px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.mag--cover {
  position: relative;
}

.mag--info {
  opacity: 0;
  z-index: -1;
  position: absolute;
  background: rgba(255, 255, 255, 0.9);
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  text-align: center;
  padding: 10px;
  font-family: "Verlag", sans-serif;
  line-height: 1;
  transition: 0.3s all;
}
.mag--info .close-mag {
  padding: 8px;
  text-align: center;
  color: #fbf4ea;
  position: absolute;
  display: inline-block;
  background: #9e6a16;
  top: 0;
  right: 0;
}
.mag--info .close-mag:hover {
  cursor: pointer;
}
.mag--info h4 {
  color: #9e6a16;
  letter-spacing: -0.001em;
  font-weight: 700;
  font-family: "Verlag", sans-serif;
  font-size: 24px;
  margin: 0;
}
.mag--info h5 {
  text-transform: uppercase;
  font-family: "Verlag", sans-serif;
  font-size: 14px;
  margin: 0;
}

.mag-visible .mag--info {
  opacity: 1;
  z-index: 2;
}

#pagination {
  margin: 38px 0 94px;
}
#pagination ul {
  display: inline-flex;
  margin: auto;
  text-transform: uppercase;
  font-size: 13px;
  font-weight: 900;
  line-height: 20px;
  font-family: "Verlag", sans-serif;
  border-radius: 0;
}
#pagination ul > * {
  padding: 13px 10px;
  display: inline-block;
  border: 2px solid #bb832a;
}
#pagination ul > *:not(:last-child) {
  display: none;
}
#pagination ul > *:first-child {
  border-left: 2px solid #bb832a;
}
#pagination ul > *.current {
  background: #bb832a;
  color: #fdf8f5;
  display: none;
}

#safaris-on-region-tax {
  padding-bottom: 0;
}
#safaris-on-region-tax > div > :last-child {
  margin-top: 80px;
}

@media only screen and (min-width: 992px) {
  .main-header__nav .sub-menu-wrapper .sub-menu > li > a {
    margin-right: 60px !important;
  }

  .main-header__nav .sub-menu-wrapper .mega-menu-wrapper ul {
    padding: 0 !important;
  }

  #menu-item-235 .mega-menu-wrapper {
    right: 15px !important;
  }
}
@media only screen and (min-width: 601px) {
  .column-cards .content-column > h3 + p {
    min-height: 225px;
  }
}
@media only screen and (max-width: 768px) {
  .page-id-17365 p {
    margin-bottom: 18px;
  }
  .page-id-17365 .block-content-aside--image {
    margin-bottom: 30px;
  }
}
#nav-search-form {
  right: 0;
  left: 0;
  top: 0;
  background-color: #fdf8f5;
  border-left: 1px solid #9A6C23;
  height: 100%;
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.3s ease-in;
  padding: 0 20px;
}
#nav-search-form form {
  flex: 1 1 auto;
  max-width: 1440px;
}

#nav-search-form .search-input {
  color: #30241c;
  font-family: "Verlag", sans-serif;
  font-size: 26px;
  line-height: 26px;
  font-weight: 400;
  display: block;
  border: none !important;
  border-radius: 0;
  box-shadow: none;
  background: none;
  outline: none;
  flex: 1 1 auto;
  height: 100px;
  padding-left: 22px;
}
#nav-search-form.active {
  opacity: 1;
  pointer-events: auto;
}
#nav-search-form.active .nav-search-close-x {
  pointer-events: auto !important;
}

#megamenu > .menu-item > a {
  font-family: "Verlag", sans-serif;
  font-weight: bold;
  font-size: 16px;
  line-height: 23px;
  letter-spacing: 0.3px;
  position: relative;
  padding: 8px;
  padding-bottom: 16px;
}

 .main-header:not(.main-header--transparent) #megamenu > .menu-item > a:after,
 .main-header.main-header--sticky #megamenu > .menu-item > a:after,
 body.page-template-page-no-hero .main-header #megamenu>.menu-item>a:after,
 body.search .main-header #megamenu>.menu-item>a:after, 
 body.single:not(.single-villa):not(.single-option-and-extension) .main-header #megamenu>.menu-item>a:after,
 body.blog .main-header #megamenu>.menu-item>a:after {
  content: url('images/icons/chev-expand.svg');
  width: 8px;
  margin-left: 5px;
  display: inline-block;
  transition: all 0.3s ease;
}

.main-header #megamenu > .menu-item > a:after {
  content: url('images/icons/chev-expand-white.svg');
  width: 8px;
  margin-left: 5px;
  display: inline-block;
  transition: all 0.3s ease;
}

.main-header:not(.main-header--transparent) #megamenu > .menu-item > a.active:after,
.main-header.main-header--sticky #megamenu > .menu-item > a.active:after {
  content: url('images/icons/chev-collapse.svg');
  width: 8px;
  margin-left: 5px;
  display: inline-block;
  transition: all 0.3s ease; 
}

.main-header #megamenu > .menu-item > a.active:after {
  content: url('images/icons/chev-collapse-white.svg');
  width: 8px;
  margin-left: 5px;
  display: inline-block;
  transition: all 0.3s ease; 
}

/* #megamenu > .menu-item > a:focus, #megamenu > .menu-item > a:hover {
  color: #9A6C23;
} */

#megamenu > .menu-item > a.active {
  border-bottom: 2px solid #9A6C23;
}

#megamenu > .menu-item > a:focus:after, #megamenu > .menu-item > a:hover:after {
  transform: scaleX(1);
}

.main-header--transparent {
  background: linear-gradient(0deg, rgba(0, 0, 0, 0), rgba(0, 0, 0, 0.15)) !important;
}

.main-header--transparent.main-header--sticky, .main-header--transparent.search {
  background: #fdf8f5 !important;
  box-shadow: 0px 3px 6px rgba(0, 0, 0, 0.10);
}

.main-header--transparent:not(.main-header--sticky):not(.search):not(.main-header--white) #megamenu > .menu-item > a {
  color: #fffefc;
}


.page-template-page-no-hero .main-header__logo,
.page-template-page-no-hero .link-inner,
.single-safari .main-header__logo,
.single-safari .link-inner,
.single-journey .main-header__logo,
.single-journey .link-inner {
  opacity: 1 !important;
}
.page-template-page-no-hero .main-header__logo--alt,
.single-i-camp-and-lodge .main-header__logo--alt
.page-template-page-no-hero .link-inner--alt,
.single-safari .main-header__logo--alt,
.single-safari .link-inner--alt,
.single-journey .main-header__logo--alt,
.single-journey .link-inner--alt {
  opacity: 0 !important;
}
.page-template-page-no-hero .main-header--transparent,
.single-safari .main-header--transparent,
.single-journey .main-header--transparent, 
.body.blog .main-header--transparent {
  background: #fdf8f5 !important;
}
.page-template-page-no-hero .main-header--transparent .c-btn:not(.form-button),
.single-i-camp-and-lodge .main-header--transparent .c-btn:not(.form-button),
.single-safari .main-header--transparent .c-btn:not(.form-button),
.single-journey .main-header--transparent .c-btn:not(.form-button),
.single-post .main-header--transparent .c-btn:not(.form-button),
body.search .main-header--transparent .c-btn:not(.form-button),
body.blog .main-header--transparent .c-btn:not(.form-button) {
  color: #bb832a;
  border: 2px solid #bb832a;
}
.page-template-page-no-hero .main-header--transparent:not(.main-header--sticky):not(.search):not(.main-header--white) #megamenu > .menu-item > a,
.single-safari .main-header--transparent:not(.main-header--sticky):not(.search):not(.main-header--white) #megamenu > .menu-item > a,
.single-journey .main-header--transparent:not(.main-header--sticky):not(.search):not(.main-header--white) #megamenu > .menu-item > a {
  color: #30241c;
}
.page-template-page-no-hero .main-header--transparent:not(.main-header--sticky):not(.search):not(.main-header--white) #megamenu > .menu-item > a:before, .page-template-page-no-hero .main-header--transparent:not(.main-header--sticky):not(.search):not(.main-header--white) #megamenu > .menu-item > a:after,
.single-safari .main-header--transparent:not(.main-header--sticky):not(.search):not(.main-header--white) #megamenu > .menu-item > a:before,
.single-safari .main-header--transparent:not(.main-header--sticky):not(.search):not(.main-header--white) #megamenu > .menu-item > a:after,
.single-journey .main-header--transparent:not(.main-header--sticky):not(.search):not(.main-header--white) #megamenu > .menu-item > a:before,
.single-journey .main-header--transparent:not(.main-header--sticky):not(.search):not(.main-header--white) #megamenu > .menu-item > a:after {
  opacity: 1;
  /* background-color: #CCAE7C; */
}
.page-template-page-no-hero .main-header .link-inner--alt,
.single-safari .main-header .link-inner--alt,
.single-journey .main-header .link-inner--alt {
  position: absolute;
  left: 0;
  top: 0;
  color: #30241c !important;
}

.page-template-page-no-hero .btn-hamburger span,
.single-post .btn-hamburger span,
.single-safari .btn-hamburger span,
.single-journey .btn-hamburger span {
  background-color: #30241c;
}

@media only screen and (max-width: 1100px) {
  .menu-new-main-nav-container,
.megamenu-consultation-link,
.megamenu-image-background,
.megamenu-close {
    display: none;
  }
}

.megamenu:not(.depth-2) {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
}


.menu-new-main-nav-new-main-navigation-container #megamenu li {
  padding-right: 1vw;
}

li.menu-item.depth-3 {
  width: 100%;
  padding: 0 1vw 0 0;
}

.menu-new-main-nav-new-main-navigation-container #megamenu > li:first-child {
  margin-left: -8px;
}


@media only screen and (max-width: 1100px) {
  .megamenu {
    flex-wrap: wrap;
  }
  .megamenu > .menu-item {
    max-width: 50%;
  }
  .megamenu > .menu-item .nav-link {
    padding-top: 2px !important;
    padding-bottom: 2px !important;
  }
}

.megamenu-wrapper {
  position: fixed;
  left: 0;
  top: 0;
  /* width: 57vw; */
  width: 100%;
  height: calc(100% - 103px);
  z-index: 999;
  padding: 0;
  pointer-events: none;
  flex-direction: column;
  display: flex;
  visibility: hidden;
  transition: visibility 0.1s 0.1s ease;
  background-color: transparent;
  border-top: 1px solid rgba(0, 0, 0, 0.05);
}

.megamenu-wrapper:after {
  width: 100%;
  height: 100%;
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  z-index: -1;
  transform: translateX(-100vw);
  transition: transform 0.6s ease-out;
}

.menu-item.text-gold-wrapper {
  line-height: 20px;
  margin-top: 35px;
  display: inline-block;
}
/* .menu-item .megamenu-link:focus, .menu-item .megamenu-link:hover {
  color: #946D2C;
} */
.menu-item .megamenu-link.text-gold {
  color: #946D2C;
  font-weight: bold;
}
.menu-item .megamenu-link.text-gold:focus, .menu-item .megamenu-link.text-gold:hover {
  color: #946D2C !important;
}
.menu-item:not(.has-description):not(.has-children) {
  display: block;
}
.menu-item:not(.has-description):not(.has-children):not(.depth-3) .megamenu-link {
  font-size: 16px;
  font-family: "Verlag", sans-serif;
  margin-bottom: 8px;
  border-bottom: 2px solid transparent !important;
  line-height: 20px;
  font-weight: 400;
}
.menu-item:not(.has-description):not(.has-children) .megamenu-link:focus, .menu-item:not(.has-description):not(.has-children):not(.bolder-text) .megamenu-link:hover {
  color: #30241c;
  border-color: rgba(185, 131, 41, 0.6) !important;
}
.menu-item:not(.has-description):not(.has-children):last-child .megamenu-link {
  margin-bottom: 0;
}

.megamenu-heading {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: flex-end;
  padding-right: 25px;
}


.megamenu-toggle + .megamenu-wrapper .megamenu-content{
  transform: translateX(-100vw);
  opacity: 0;
  transition: transform 0.6s ease-out, opacity 0.4s ease-in-out;
}

.megamenu-toggle.active + .megamenu-wrapper .megamenu-content {
  transform: translateX(0);
  opacity: 1; 
  transition: transform 0.6s ease-out, opacity 0.4s ease-in-out;
  position: relative;
  /* margin-top: 0; */
  width: 60%;
  flex: 1 1 auto;
  /* padding-right: 25px; */
  /* padding-bottom: 20px; */
  visibility: hidden;
  transition: visibility 0s 0.6s linear;
  overflow-y: hidden !important;
  -webkit-overflow-scrolling: touch;
  max-width: 1440px;
  padding: 0 0px 20px;
  margin: 0;    
  box-shadow: 4px 0 12px rgba(0, 0, 0, 0.5);
  background-color: #fdf8f5;
}

.megamenu-toggle + .megamenu-wrapper .megamenu-content > .megamenu-menu {
  width: calc(33% + 30px);
  padding: 30px 40px 0;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  height: calc(100vh - 133px);
  min-height: min-content;
  overflow-y: auto;
}
.megamenu-content > .megamenu-menu .menu-item .megamenu-link,
.megamenu-content > .megamenu-menu .megamenu-image-list .megamenu-link {
  transform: translateX(-30px);
  transition: opacity 0.2s ease-out, transform 0.2s ease-out, border 0.2s linear, color 0.2s linear;
  display: inline-block;
  opacity: 0;
}

.megamenu-content > .megamenu-menu > .menu-item {
  display: block;
  font-size: 24px;
  padding-left: 0;
  font-family: "GoudyOldstyleW01-Italic_706308", "sans-serif" !important;
  font-style: normal;
  margin-bottom: 35px;
  width: 100%;
}
.megamenu-content > .megamenu-menu > .menu-item > .megamenu-link {
  padding: 15px !important;
  font-size: 24px;
  line-height: 24px;
  border-bottom: 2px solid transparent !important;
  position: relative;
  transition: all 0.2s out;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
@media only screen and (min-width: 1200px) {
  .megamenu-content > .megamenu-menu > .menu-item-has-children > .megamenu-link {
    padding-left: 0;
  }
}
.megamenu-content > .megamenu-menu > .menu-item-has-children > .megamenu-link ~ .megamenu-menu {
  background-color: #fdf8f5;
  pointer-events: none;
  opacity: 0;
  transition: opacity 0.2s ease-out;
  /* overflow-y: auto; */
}
.megamenu-content > .megamenu-menu > .menu-item-has-children > .megamenu-link:after {
  content: "";
  position: relative;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center center;
  background-image: url("images/icons/link-arrow-colored2-big.svg");
  height: 13px;
  width: 13px;
  display: inline-block;
  /* left: calc(100% - 10px); */
  /* top: calc(50% - 10px); */
  opacity: 0;
  transition: opacity 0.2s linear;
  pointer-events: none;
  margin-left: 10px;
}

.megamenu-content .menu-item-type-post_type_archive.depth-2.has-children {
  display: flex;
  align-items: center;
  justify-content: space-between;
  cursor: pointer;
  transition: all 0.2s ease-out;
  margin-bottom: 5px !important;
}

.megamenu-content .menu-item-type-post_type_archive.depth-2.has-children.active {
  background-color: rgb(148 109 44 / 5%);
}

.megamenu-content .menu-item-type-post_type_archive.depth-2.has-children:after {
  content: "";
  position: relative;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: right;
  background-image: url("images/icons/link-arrow-colored2-big.svg");
  height: 13px;
  width: 25px;
  display: inline-block;
  opacity: 1;
  transition: opacity 0.2s 0.3s linear;
  pointer-events: none;
  right: 0;
  cursor: pointer;
  transition: .3s ease;
}


.megamenu-content > .megamenu-menu > .menu-item-has-children > .megamenu-link.active,
.megamenu-content > .megamenu-menu > .menu-item-has-children > .megamenu-link:hover {
  /* border-color: rgba(185, 131, 41, 0.6) !important; */
  background-color: rgb(148 109 44 / 5%);
}

.megamenu-content > .megamenu-menu > .menu-item-has-children > .megamenu-link.active:after,
.megamenu-content > .megamenu-menu > .menu-item-type-post_type_archive > .megamenu-link.active:after{
  opacity: 1;
}
.megamenu-content > .megamenu-menu > .menu-item-has-children > .megamenu-menu {
  position: absolute;
  right: 0;
  top: 0;
  /* width: 50%; */
  width: 33%;
  left: 33%;
  margin-top: 0;
  padding-right: 30px;
  padding-bottom: 30px;
  height: calc(100vh - 100px);
  min-height: 100%;
  overflow-y: auto;
  /* overflow-x: hidden; */
}

.megamenu-content > .megamenu-menu > .overflow-visible > .megamenu-menu {
  overflow-y: unset;
  overflow-x: unset;
}

.megamenu-content > .megamenu-menu > .menu-item-has-children > .megamenu-menu > li.menu-item-type-post_type_archive > .megamenu-menu   {
      position: absolute;
    right: 0;
    top: 0;
    /* width: 50%; */
    width: 100%;
    left: 100%;
    margin-top: 0;
    padding-right: 30px;
    padding-bottom: 30px;
    height: calc(100vh - 100px);
    min-height: 100%;
    overflow-y: auto;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.megamenu-content > .megamenu-menu > .menu-item-has-children > .megamenu-menu > li > .megamenu-menu::-webkit-scrollbar,
 ::-webkit-scrollbar {
  -webkit-appearance: none;
  width: 7px; /* Width of the scrollbar */
}


.megamenu-content > .megamenu-menu > .menu-item-has-children > .megamenu-menu > li > .megamenu-menu::-webkit-scrollbar-thumb,
::-webkit-scrollbar-thumb {
  background-color: rgba(0,0,0,.5); /* Color of the scrollbar thumb */
  border-radius: 4px;
  -webkit-box-shadow: 0 0 1px rgba(255,255,255,.2);
}


.megamenu-content > .megamenu-menu > .menu-item-has-children > .megamenu-menu > li.menu-item-type-post_type_archive > .megamenu-menu.active  {
  visibility: visible;
  opacity: 1;
  padding-bottom: 100px;
}

.megamenu-content .megamenu-link.depth-3 {
  font-size: 16px !important;
  font-family: "Verlag", sans-serif;
  margin-bottom: 8px;
  border-bottom: none !important;
  line-height: 20px;
  font-weight: 400;
  padding-bottom: 10px;
}

.megamenu-content > .megamenu-menu > .menu-item-has-children > .megamenu-menu > li.menu-item-type-post_type_archive > .megamenu-menu  

.megamenu-content > .megamenu-menu > .menu-item-has-children > .megamenu-menu > .menu-item {
  display: block;
}
.megamenu-content > .megamenu-menu > .menu-item-has-children > .megamenu-menu > .menu-item.has-description > .megamenu-menu, .megamenu-content > .megamenu-menu > .menu-item-has-children > .megamenu-menu > .menu-item.has-children > .megamenu-menu {
  margin-top: 8px;
  padding: 20px 0;
}

.megamenu-content > .megamenu-menu > .menu-item-has-children > .megamenu-menu > .menu-item.has-description > .megamenu-link, .megamenu-content > .megamenu-menu > .menu-item-has-children > .megamenu-menu > .menu-item.has-children > .megamenu-link {
  font-family: "Verlag", sans-serif;
  font-weight: 400;
  font-size: 18px;
  line-height: 24px;
  display: block;
  letter-spacing: 0.3px;
}
.megamenu-content > .megamenu-menu > .menu-item-has-children > .megamenu-menu > .menu-item.has-description {
  margin-bottom: 16px;
  padding: 10px;
  padding-right: 10px !important;
}
.megamenu-content > .megamenu-menu > .menu-item-has-children > .megamenu-menu > .menu-item.has-description > .megamenu-link {
  display: block;
}

.megamenu-content > .megamenu-menu > .menu-item-has-children > .megamenu-menu > .menu-item.has-children:first-of-type {
  margin-top: 0px;
}

.megamenu-content > .megamenu-menu > .menu-item-has-children > .megamenu-menu > .menu-item.has-children {
  margin: 17px 0;
}
/* .megamenu-content > .megamenu-menu > .menu-item-has-children > .megamenu-menu > .menu-item.has-children ~ .depth-2:not(.text-gold-wrapper) {
  margin-top: 33px;
} */

.megamenu-item-description {
  font-weight: normal;
  font-size: 12px;
  line-height: 16px;
  color: #946D2C;
  padding-top: 5px;
}

.mobilemenu-item-description {
  font-weight: normal;
  font-size: 15px;
  line-height: 18px;
  color: #946D2C;
  margin-top: 5px;
}

.megamenu-image {
  /* position: fixed; */
  position: absolute;
  top: 0 !important;
  /* left: 57vw; */
  left: 66%;
  top: 0;
  width: 33%;
  height: 100%;
  object-fit: cover;
  /* z-index: 998; */
  transform: translateX(200vw);
  pointer-events: none;
  /* transition: transform 0.6s ease-out, opacity 0.4s ease-in-out; */
  opacity: 0;
  padding-top: 32px;
  border-left: 1px solid rgba(0, 0, 0, 0.05);
  background-color: #fdf8f5;
  overflow: auto;
}

.megamenu-image__close-menu {
    border: 1px solid #30241c;
    background: transparent;
    cursor: pointer;
    transition: all .4s ease-out;
    position: relative;
    height: 35px;
    width: 35px;
    display: flex;
    position: absolute;
    right: 30px;
    align-items: center;
    justify-content: center;
}

.megamenu-image__close-menu:hover {
  background: #30241c;
}


a.megamenu-image__close-menu:after {
    /* position: relative; */
    content: '';
    content: "";
    position: relative;
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center center;
    background-image: url(images/icons/close-simple.svg);
    height: 12px;
    width: 12px;
    display: inline-block;
    opacity: 1;
    pointer-events: none;
    transition: all .4s ease;
}

a.megamenu-image__close-menu:hover:after {
    background-image: url(images/icons/close-simple-white.svg);

}

.megamenu-image-background {
  position: fixed;
  bottom: 0;
  height: 100%;
  left: 57vw;
  width: 43vw;
  z-index: 997;
  text-align: center;
  transform: translateX(100vw);
  pointer-events: none;
  transition: transform 0.6s ease-out;
  background-color: #fdf8f5;
}
.megamenu-image-background.active {
  transform: translateX(100%);
  pointer-events: auto;
}

.megamenu-consultation-link {
  position: fixed;
  bottom: 0;
  height: 80px;
  left: 57vw;
  width: 43vw;
  z-index: 999;
  text-align: center;
  transform: translateX(100vw);
  pointer-events: none;
  transition: transform 0.6s ease-out;
}
.megamenu-consultation-link a {
  width: 100%;
  display: block;
  background-color: #9A6C23;
  padding: 24px 15px;
  color: #fffefc;
  letter-spacing: normal !important;
  font-size: 20px;
  position: absolute;
  bottom: 0;
  right: 0;
  height: 80px;
  transform: translateY(80px);
  transition: background-color 0.2s linear, transform 0.2s 0.6s ease-out;
}
.megamenu-consultation-link a:focus, .megamenu-consultation-link a:hover {
  color: #fffefc;
  background-color: #BB832A;
}
.megamenu-consultation-link a:after {
  content: "";
  position: relative;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center center;
  background-image: url("images/icons/link-arrow-white-big.svg");
  height: 13px;
  width: 30px;
  display: inline-block;
}
.megamenu-consultation-link.active {
  transform: translateX(0);
  pointer-events: auto;
}
.megamenu-consultation-link.active a {
  transform: translateY(0);
}

.megamenu-section-title {
  margin-bottom: 0;
  padding-left: 30px;
  transform: translateX(-30px);
  opacity: 0;
  transition: opacity 0.2s 0s ease-out, transform 0.2s 0s ease-out;
}
@media only screen and (min-width: 1200px) {
  .megamenu-section-title {
    padding-left: 100px;
  }
}

.megamenu-close {
  float: right;
  color: #9A6C23;
  font-family: "Verlag", sans-serif;
  font-weight: 900;
  text-transform: uppercase;
  font-size: 13px;
  letter-spacing: 1px;
  padding-bottom: 2px;
  padding-top: 2px;
  opacity: 0;
  transition: opacity 0.15s ease-out;
}
.megamenu-close:focus, .megamenu-close:hover {
  color: #80591D;
  text-decoration: none;
}

.megamenu-close-x {
  position: fixed;
  top: 0;
  right: 0;
  height: 55px;
  width: 55px;
  z-index: 999;
  pointer-events: none;
  transition: opacity 0.15s ease-out, background-color 0.2s linear, background-image 0.2s linear;
  background-color: #fdf8f5;
  background-size: 18px 18px;
  background-repeat: no-repeat;
  background-position: center center;
  background-image: url("images/icons/close-simple.svg") !important;
  border-radius: 0;
  opacity: 0;
}
.megamenu-close-x:focus, .megamenu-close-x:hover {
  background-color: #9A6C23;
  background-image: url("images/icons/close-simple-white.svg") !important;
  outline: 0;
  box-shadow: none;
}
.megamenu-close-x.active {
  pointer-events: auto;
  opacity: 1;
  transition: opacity 0.15s 0.45s ease-out, background-color 0.2s linear, background-image 0.2s linear;
}

.nav-search-close-x {
  position: relative;
  transform: translateX(0);
  height: 20px;
  width: 20px;
  z-index: 999;
  pointer-events: none;
  transition: opacity 0.2s linear;
  background-color: #fdf8f5;
  background-size: 20px 20px;
  background-repeat: no-repeat;
  background-position: center center;
  background-image: url("images/icons/close-simple.svg") !important;
  border-radius: 0;
  opacity: 1;
  margin: 0 15px;
}
.nav-search-close-x:focus, .nav-search-close-x:hover {
  outline: 0;
  box-shadow: none;
  opacity: 0.8;
}

.megamenu-toggle.active + .megamenu-wrapper {
  pointer-events: auto;
  visibility: visible;
  /* transition: visibility 0s 0s linear; */
}
.megamenu-toggle.active + .megamenu-wrapper .megamenu-section-title {
  opacity: 1;
  transform: translateX(0);
  transition: opacity 0.2s 0.18s ease-out, transform 0.2s 0.18s ease-out;
}
.megamenu-toggle.active + .megamenu-wrapper .megamenu-close {
  opacity: 1;
  transition-delay: 0.45s;
}
.megamenu-toggle.active + .megamenu-wrapper:after {
  transform: translateX(0);
  background-color: rgba(187, 131, 42, 0.5);
}
.megamenu-toggle.active + .megamenu-wrapper .megamenu-link ~ .megamenu-image {
  transform: translateX(0);
}
.megamenu-toggle.active + .megamenu-wrapper .megamenu-link.active ~ .megamenu-image {
  pointer-events: auto;
  opacity: 1;
}
.megamenu-toggle.active + .megamenu-wrapper .megamenu-link.active ~ .megamenu-menu {
  pointer-events: auto;
  opacity: 1;
}
.megamenu-toggle.active + .megamenu-wrapper > .megamenu-content {
  visibility: visible;
  transition: visibility 0s 0s linear;
  animation-name: megamenuContent;
  animation-duration: 0.6s;
  animation-fill-mode: forwards;
}
.megamenu-toggle.active + .megamenu-wrapper > .megamenu-content > .megamenu-menu > .menu-item > .megamenu-link {
  transform: translateX(0);
  opacity: 1;
  pointer-events: auto;
  transition: all 0.3s ease;
}
.megamenu-toggle.active + .megamenu-wrapper > .megamenu-content > .megamenu-menu > .menu-item > .megamenu-link.active ~ .megamenu-menu .megamenu-link {
  transform: translateX(0);
  opacity: 1;
  pointer-events: auto;
}

.megamenu-toggle.active + .megamenu-wrapper > .megamenu-content > .megamenu-menu > .menu-item > .megamenu-link.active ~ .megamenu-image  .megamenu-link {
 transform: translateX(0);
  pointer-events: auto;
  opacity: 1;
}

.megamenu-toggle.active + .megamenu-wrapper > .megamenu-content > .megamenu-image{
  transform: translateX(0);
  pointer-events: auto;
  opacity: 1;
}
/* .megamenu-toggle.active + .megamenu-wrapper > .megamenu-content .menu-item:nth-child(1).depth-1 .megamenu-link {
  transition: opacity 0.2s 0.15s ease-out, transform 0.2s 0.15s ease-out, border 0.2s linear, color 0.2s linear !important;
} */
.megamenu-toggle.active + .megamenu-wrapper > .megamenu-content .menu-item:nth-child(1).depth-2 .megamenu-link, .megamenu-toggle.active + .megamenu-wrapper > .megamenu-content .menu-item:nth-child(1).depth-3 .megamenu-link {
  transition: opacity 0.2s 0.15s ease-out, transform 0.2s 0.15s ease-out, border 0.2s linear, color 0.2s linear !important;
}
.megamenu-toggle.active + .megamenu-wrapper > .megamenu-content .menu-item:nth-child(2).depth-1 .megamenu-link {
  transition: opacity 0.2s 0.2s ease-out, transform 0.2s 0.2s ease-out, border 0.2s linear, color 0.2s linear !important;
}
.megamenu-toggle.active + .megamenu-wrapper > .megamenu-content .menu-item:nth-child(2).depth-2 .megamenu-link, .megamenu-toggle.active + .megamenu-wrapper > .megamenu-content .menu-item:nth-child(2).depth-3 .megamenu-link {
  transition: opacity 0.2s 0.2s ease-out, transform 0.2s 0.2s ease-out, border 0.2s linear, color 0.2s linear !important;
}
.megamenu-toggle.active + .megamenu-wrapper > .megamenu-content .menu-item:nth-child(3).depth-1 .megamenu-link {
  transition: opacity 0.2s 0.25s ease-out, transform 0.2s 0.25s ease-out, border 0.2s linear, color 0.2s linear !important;
}
.megamenu-toggle.active + .megamenu-wrapper > .megamenu-content .menu-item:nth-child(3).depth-2 .megamenu-link, .megamenu-toggle.active + .megamenu-wrapper > .megamenu-content .menu-item:nth-child(3).depth-3 .megamenu-link {
  transition: opacity 0.2s 0.25s ease-out, transform 0.2s 0.25s ease-out, border 0.2s linear, color 0.2s linear !important;
}
.megamenu-toggle.active + .megamenu-wrapper > .megamenu-content .menu-item:nth-child(4).depth-1 .megamenu-link {
  transition: opacity 0.2s 0.3s ease-out, transform 0.2s 0.3s ease-out, border 0.2s linear, color 0.2s linear !important;
}
.megamenu-toggle.active + .megamenu-wrapper > .megamenu-content .menu-item:nth-child(4).depth-2 .megamenu-link, .megamenu-toggle.active + .megamenu-wrapper > .megamenu-content .menu-item:nth-child(4).depth-3 .megamenu-link {
  transition: opacity 0.2s 0.3s ease-out, transform 0.2s 0.3s ease-out, border 0.2s linear, color 0.2s linear !important;
}
.megamenu-toggle.active + .megamenu-wrapper > .megamenu-content .menu-item:nth-child(5).depth-1 .megamenu-link {
  transition: opacity 0.2s 0.35s ease-out, transform 0.2s 0.35s ease-out, border 0.2s linear, color 0.2s linear !important;
}
.megamenu-toggle.active + .megamenu-wrapper > .megamenu-content .menu-item:nth-child(5).depth-2 .megamenu-link, .megamenu-toggle.active + .megamenu-wrapper > .megamenu-content .menu-item:nth-child(5).depth-3 .megamenu-link {
  transition: opacity 0.2s 0.35s ease-out, transform 0.2s 0.35s ease-out, border 0.2s linear, color 0.2s linear !important;
}
.megamenu-toggle.active + .megamenu-wrapper > .megamenu-content .menu-item:nth-child(6).depth-1 .megamenu-link {
  transition: opacity 0.2s 0.4s ease-out, transform 0.2s 0.4s ease-out, border 0.2s linear, color 0.2s linear !important;
}
.megamenu-toggle.active + .megamenu-wrapper > .megamenu-content .menu-item:nth-child(6).depth-2 .megamenu-link, .megamenu-toggle.active + .megamenu-wrapper > .megamenu-content .menu-item:nth-child(6).depth-3 .megamenu-link {
  transition: opacity 0.2s 0.4s ease-out, transform 0.2s 0.4s ease-out, border 0.2s linear, color 0.2s linear !important;
}
.megamenu-toggle.active + .megamenu-wrapper > .megamenu-content .menu-item:nth-child(7).depth-1 .megamenu-link {
  transition: opacity 0.2s 0.45s ease-out, transform 0.2s 0.45s ease-out, border 0.2s linear, color 0.2s linear !important;
}
.megamenu-toggle.active + .megamenu-wrapper > .megamenu-content .menu-item:nth-child(7).depth-2 .megamenu-link, .megamenu-toggle.active + .megamenu-wrapper > .megamenu-content .menu-item:nth-child(7).depth-3 .megamenu-link {
  transition: opacity 0.2s 0.45s ease-out, transform 0.2s 0.45s ease-out, border 0.2s linear, color 0.2s linear !important;
}
.megamenu-toggle.active + .megamenu-wrapper > .megamenu-content .menu-item:nth-child(8).depth-1 .megamenu-link {
  transition: opacity 0.2s 0.5s ease-out, transform 0.2s 0.5s ease-out, border 0.2s linear, color 0.2s linear !important;
}
.megamenu-toggle.active + .megamenu-wrapper > .megamenu-content .menu-item:nth-child(8).depth-2 .megamenu-link, .megamenu-toggle.active + .megamenu-wrapper > .megamenu-content .menu-item:nth-child(8).depth-3 .megamenu-link {
  transition: opacity 0.2s 0.5s ease-out, transform 0.2s 0.5s ease-out, border 0.2s linear, color 0.2s linear !important;
}
.megamenu-toggle.active + .megamenu-wrapper > .megamenu-content .menu-item:nth-child(9).depth-1 .megamenu-link {
  transition: opacity 0.2s 0.55s ease-out, transform 0.2s 0.55s ease-out, border 0.2s linear, color 0.2s linear !important;
}
.megamenu-toggle.active + .megamenu-wrapper > .megamenu-content .menu-item:nth-child(9).depth-2 .megamenu-link, .megamenu-toggle.active + .megamenu-wrapper > .megamenu-content .menu-item:nth-child(9).depth-3 .megamenu-link {
  transition: opacity 0.2s 0.55s ease-out, transform 0.2s 0.55s ease-out, border 0.2s linear, color 0.2s linear !important;
}
.megamenu-toggle.active + .megamenu-wrapper > .megamenu-content .menu-item:nth-child(10).depth-1 .megamenu-link {
  transition: opacity 0.2s 0.6s ease-out, transform 0.2s 0.6s ease-out, border 0.2s linear, color 0.2s linear !important;
}
.megamenu-toggle.active + .megamenu-wrapper > .megamenu-content .menu-item:nth-child(10).depth-2 .megamenu-link, .megamenu-toggle.active + .megamenu-wrapper > .megamenu-content .menu-item:nth-child(10).depth-3 .megamenu-link {
  transition: opacity 0.2s 0.6s ease-out, transform 0.2s 0.6s ease-out, border 0.2s linear, color 0.2s linear !important;
}
.megamenu-toggle.active + .megamenu-wrapper > .megamenu-content .menu-item:nth-child(11).depth-1 .megamenu-link {
  transition: opacity 0.2s 0.65s ease-out, transform 0.2s 0.65s ease-out, border 0.2s linear, color 0.2s linear !important;
}
.megamenu-toggle.active + .megamenu-wrapper > .megamenu-content .menu-item:nth-child(11).depth-2 .megamenu-link, .megamenu-toggle.active + .megamenu-wrapper > .megamenu-content .menu-item:nth-child(11).depth-3 .megamenu-link {
  transition: opacity 0.2s 0.65s ease-out, transform 0.2s 0.65s ease-out, border 0.2s linear, color 0.2s linear !important;
}
.megamenu-toggle.active + .megamenu-wrapper > .megamenu-content .menu-item:nth-child(12).depth-1 .megamenu-link {
  transition: opacity 0.2s 0.7s ease-out, transform 0.2s 0.7s ease-out, border 0.2s linear, color 0.2s linear !important;
}
.megamenu-toggle.active + .megamenu-wrapper > .megamenu-content .menu-item:nth-child(12).depth-2 .megamenu-link, .megamenu-toggle.active + .megamenu-wrapper > .megamenu-content .menu-item:nth-child(12).depth-3 .megamenu-link {
  transition: opacity 0.2s 0.7s ease-out, transform 0.2s 0.7s ease-out, border 0.2s linear, color 0.2s linear !important;
}
.megamenu-toggle.active + .megamenu-wrapper > .megamenu-content .menu-item:nth-child(13).depth-1 .megamenu-link {
  transition: opacity 0.2s 0.75s ease-out, transform 0.2s 0.75s ease-out, border 0.2s linear, color 0.2s linear !important;
}
.megamenu-toggle.active + .megamenu-wrapper > .megamenu-content .menu-item:nth-child(13).depth-2 .megamenu-link, .megamenu-toggle.active + .megamenu-wrapper > .megamenu-content .menu-item:nth-child(13).depth-3 .megamenu-link {
  transition: opacity 0.2s 0.75s ease-out, transform 0.2s 0.75s ease-out, border 0.2s linear, color 0.2s linear !important;
}
.megamenu-toggle.active + .megamenu-wrapper > .megamenu-content .menu-item:nth-child(14).depth-1 .megamenu-link {
  transition: opacity 0.2s 0.8s ease-out, transform 0.2s 0.8s ease-out, border 0.2s linear, color 0.2s linear !important;
}
.megamenu-toggle.active + .megamenu-wrapper > .megamenu-content .menu-item:nth-child(14).depth-2 .megamenu-link, .megamenu-toggle.active + .megamenu-wrapper > .megamenu-content .menu-item:nth-child(14).depth-3 .megamenu-link {
  transition: opacity 0.2s 0.8s ease-out, transform 0.2s 0.8s ease-out, border 0.2s linear, color 0.2s linear !important;
}
.megamenu-toggle.active + .megamenu-wrapper > .megamenu-content .menu-item:nth-child(15).depth-1 .megamenu-link {
  transition: opacity 0.2s 0.85s ease-out, transform 0.2s 0.85s ease-out, border 0.2s linear, color 0.2s linear !important;
}
.megamenu-toggle.active + .megamenu-wrapper > .megamenu-content .menu-item:nth-child(15).depth-2 .megamenu-link, .megamenu-toggle.active + .megamenu-wrapper > .megamenu-content .menu-item:nth-child(15).depth-3 .megamenu-link {
  transition: opacity 0.2s 0.85s ease-out, transform 0.2s 0.85s ease-out, border 0.2s linear, color 0.2s linear !important;
}
.megamenu-toggle.active + .megamenu-wrapper > .megamenu-content .menu-item:nth-child(16).depth-1 .megamenu-link {
  transition: opacity 0.2s 0.9s ease-out, transform 0.2s 0.9s ease-out, border 0.2s linear, color 0.2s linear !important;
}
.megamenu-toggle.active + .megamenu-wrapper > .megamenu-content .menu-item:nth-child(16).depth-2 .megamenu-link, .megamenu-toggle.active + .megamenu-wrapper > .megamenu-content .menu-item:nth-child(16).depth-3 .megamenu-link {
  transition: opacity 0.2s 0.9s ease-out, transform 0.2s 0.9s ease-out, border 0.2s linear, color 0.2s linear !important;
}
.megamenu-toggle.active + .megamenu-wrapper > .megamenu-content .menu-item:nth-child(17).depth-1 .megamenu-link {
  transition: opacity 0.2s 0.95s ease-out, transform 0.2s 0.95s ease-out, border 0.2s linear, color 0.2s linear !important;
}
.megamenu-toggle.active + .megamenu-wrapper > .megamenu-content .menu-item:nth-child(17).depth-2 .megamenu-link, .megamenu-toggle.active + .megamenu-wrapper > .megamenu-content .menu-item:nth-child(17).depth-3 .megamenu-link {
  transition: opacity 0.2s 0.95s ease-out, transform 0.2s 0.95s ease-out, border 0.2s linear, color 0.2s linear !important;
}
.megamenu-toggle.active + .megamenu-wrapper > .megamenu-content .menu-item:nth-child(18).depth-1 .megamenu-link {
  transition: opacity 0.2s 1s ease-out, transform 0.2s 1s ease-out, border 0.2s linear, color 0.2s linear !important;
}
.megamenu-toggle.active + .megamenu-wrapper > .megamenu-content .menu-item:nth-child(18).depth-2 .megamenu-link, .megamenu-toggle.active + .megamenu-wrapper > .megamenu-content .menu-item:nth-child(18).depth-3 .megamenu-link {
  transition: opacity 0.2s 1s ease-out, transform 0.2s 1s ease-out, border 0.2s linear, color 0.2s linear !important;
}
.megamenu-toggle.active + .megamenu-wrapper > .megamenu-content .menu-item:nth-child(19).depth-1 .megamenu-link {
  transition: opacity 0.2s 1.05s ease-out, transform 0.2s 1.05s ease-out, border 0.2s linear, color 0.2s linear !important;
}
.megamenu-toggle.active + .megamenu-wrapper > .megamenu-content .menu-item:nth-child(19).depth-2 .megamenu-link, .megamenu-toggle.active + .megamenu-wrapper > .megamenu-content .menu-item:nth-child(19).depth-3 .megamenu-link {
  transition: opacity 0.2s 1.05s ease-out, transform 0.2s 1.05s ease-out, border 0.2s linear, color 0.2s linear !important;
}
.megamenu-toggle.active + .megamenu-wrapper > .megamenu-content .menu-item:nth-child(20).depth-1 .megamenu-link {
  transition: opacity 0.2s 1.1s ease-out, transform 0.2s 1.1s ease-out, border 0.2s linear, color 0.2s linear !important;
}
.megamenu-toggle.active + .megamenu-wrapper > .megamenu-content .menu-item:nth-child(20).depth-2 .megamenu-link, .megamenu-toggle.active + .megamenu-wrapper > .megamenu-content .menu-item:nth-child(20).depth-3 .megamenu-link {
  transition: opacity 0.2s 1.1s ease-out, transform 0.2s 1.1s ease-out, border 0.2s linear, color 0.2s linear !important;
}
.megamenu-toggle.active + .megamenu-wrapper > .megamenu-content .menu-item:nth-child(21).depth-1 .megamenu-link {
  transition: opacity 0.2s 1.15s ease-out, transform 0.2s 1.15s ease-out, border 0.2s linear, color 0.2s linear !important;
}
.megamenu-toggle.active + .megamenu-wrapper > .megamenu-content .menu-item:nth-child(21).depth-2 .megamenu-link, .megamenu-toggle.active + .megamenu-wrapper > .megamenu-content .menu-item:nth-child(21).depth-3 .megamenu-link {
  transition: opacity 0.2s 1.15s ease-out, transform 0.2s 1.15s ease-out, border 0.2s linear, color 0.2s linear !important;
}
.megamenu-toggle.active + .megamenu-wrapper > .megamenu-content .menu-item:nth-child(22).depth-1 .megamenu-link {
  transition: opacity 0.2s 1.2s ease-out, transform 0.2s 1.2s ease-out, border 0.2s linear, color 0.2s linear !important;
}
.megamenu-toggle.active + .megamenu-wrapper > .megamenu-content .menu-item:nth-child(22).depth-2 .megamenu-link, .megamenu-toggle.active + .megamenu-wrapper > .megamenu-content .menu-item:nth-child(22).depth-3 .megamenu-link {
  transition: opacity 0.2s 1.2s ease-out, transform 0.2s 1.2s ease-out, border 0.2s linear, color 0.2s linear !important;
}
.megamenu-toggle.active + .megamenu-wrapper > .megamenu-content .menu-item:nth-child(23).depth-1 .megamenu-link {
  transition: opacity 0.2s 1.25s ease-out, transform 0.2s 1.25s ease-out, border 0.2s linear, color 0.2s linear !important;
}
.megamenu-toggle.active + .megamenu-wrapper > .megamenu-content .menu-item:nth-child(23).depth-2 .megamenu-link, .megamenu-toggle.active + .megamenu-wrapper > .megamenu-content .menu-item:nth-child(23).depth-3 .megamenu-link {
  transition: opacity 0.2s 1.25s ease-out, transform 0.2s 1.25s ease-out, border 0.2s linear, color 0.2s linear !important;
}
.megamenu-toggle.active + .megamenu-wrapper > .megamenu-content .menu-item:nth-child(24).depth-1 .megamenu-link {
  transition: opacity 0.2s 1.3s ease-out, transform 0.2s 1.3s ease-out, border 0.2s linear, color 0.2s linear !important;
}
.megamenu-toggle.active + .megamenu-wrapper > .megamenu-content .menu-item:nth-child(24).depth-2 .megamenu-link, .megamenu-toggle.active + .megamenu-wrapper > .megamenu-content .menu-item:nth-child(24).depth-3 .megamenu-link {
  transition: opacity 0.2s 1.3s ease-out, transform 0.2s 1.3s ease-out, border 0.2s linear, color 0.2s linear !important;
}
.megamenu-toggle.active + .megamenu-wrapper > .megamenu-content .menu-item:nth-child(25).depth-1 .megamenu-link {
  transition: opacity 0.2s 1.35s ease-out, transform 0.2s 1.35s ease-out, border 0.2s linear, color 0.2s linear !important;
}
.megamenu-toggle.active + .megamenu-wrapper > .megamenu-content .menu-item:nth-child(25).depth-2 .megamenu-link, .megamenu-toggle.active + .megamenu-wrapper > .megamenu-content .menu-item:nth-child(25).depth-3 .megamenu-link {
  transition: opacity 0.2s 1.35s ease-out, transform 0.2s 1.35s ease-out, border 0.2s linear, color 0.2s linear !important;
}

@keyframes megamenuContent {
  0% {
    overflow-y: hidden;
  }
  99% {
    overflow-y: hidden;
  }
  100% {
    overflow-y: auto;
  }
}
.main-header .icon-link {
  position: relative;
  display: block;
}
.main-header .icon-link .link-inner {
  color: #30241c;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
}
.main-header .icon-link .link-inner span {
  white-space: nowrap;
  padding-left: 8px;
  display: none;
}
@media only screen and (min-width: 1300px) {
  .main-header .icon-link .link-inner span {
    display: block;
  }
}
.main-header .icon-link .link-inner svg path {
  transition: fill 0.2s linear;
}
.main-header .icon-link .link-inner--alt {
  position: absolute;
  left: 0;
  top: 0;
  color: #fffefc;
}
.main-header .icon-link:hover .link-inner {
  color: #9A6C23 !important;
}
.main-header .icon-link:hover .link-inner svg path {
  fill: #9A6C23 !important;
}
.main-header .c-btn {
  padding: 8px 10px;
  min-width: 160px;
  margin-left: 22px;
  letter-spacing: 0.8px;
  box-shadow: 0px 3px 6px rgba(0,0,0,0.15);
}
.main-header .nav-search-overlay {
  opacity: 0;
  transition: opacity 0.3s ease-in;
  pointer-events: none;
}
.main-header.search .nav-search-overlay {
  position: fixed;
  left: 0;
  top: 100px;
  width: 100%;
  bottom: 0;
  background-color: #000;
  opacity: 0.7;
  z-index: -1;
  pointer-events: auto;
}

.main-header__left {
  flex: 1 1 auto;
}

.main-header__row {
  flex-wrap: nowrap;
}

.main-header__nav-new {
  flex: 1 1 auto;
}

.main-header__right {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  margin-left: auto;
  margin-right: 0;
  padding-right: 0;
  padding-bottom: 15px;
}
.main-header__right .icon-links {
  font-size: 17px;
  line-height: 14px;
}
.main-header__right .icon-links:first-child {
  padding-right: 3px;
}
.main-header__right .icon-links:first-child .icon-link:last-child {
  margin-right: 0;
}
.main-header__right .icon-links:last-child {
  padding-right: 0;
}
.main-header__right .icon-links:last-child .icon-link:last-child {
  margin-right: 0;
  padding: 0;
}


.main-header__right #cta-trigger, 
body.page-template-page-no-hero .main-header .main-header__right #cta-trigger:hover,
body.single:not(.single-villa):not(.single-option-and-extension) .main-header .main-header__right #cta-trigger:hover,
body.blog .main-header .main-header__right #cta-trigger:hover,
body.search .main-header .main-header__right #cta-trigger:hover {
  background-color: #fff;
  color: #30241c;
  border: 1px solid #fff;
}

.main-header__right #cta-trigger span,
#cta-trigger-mobile span {
  display: flex;
  align-items: center;
  justify-content: center;
}

.main-header__right #cta-trigger span:before {
  content: '';
  position: relative;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center center;
  background-image: url("images/icons/brochure.svg");
  height: 20px;
  width: 20px;
  display: block;
  margin-right: 8px;
  filter: none;
  transition: filter 0.3s ease;
}

#cta-trigger-mobile span:before {
  content: '';
  position: relative;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center center;
  background-image: url("images/icons/brochure.svg");
  height: 20px;
  width: 20px;
  display: block;
  margin-right: 8px;
  filter: brightness(0) invert(1) grayscale(1);
  transition: filter 0.3s ease;
}

.main-header.main-header--sticky .main-header__right #cta-trigger span:before,
.main-header:not(.main-header--transparent) .main-header__right #cta-trigger span:before, 
body.page-template-page-no-hero .main-header__right #cta-trigger span:before,
body.search .main-header__right #cta-trigger span:before,
body.single:not(.single-villa):not(.single-option-and-extension) .main-header__right #cta-trigger span:before,
body.blog .main-header__right #cta-trigger span:before {
  filter: brightness(0) invert(1) grayscale(1);
}

.main-header.main-header--sticky .main-header__right #cta-trigger,
.main-header:not(.main-header--transparent) .main-header__right #cta-trigger, 
#cta-trigger-mobile,
body.page-template-page-no-hero .main-header .main-header__right #cta-trigger,
body.single:not(.single-villa):not(.single-option-and-extension) .main-header .main-header__right #cta-trigger,
body.blog .main-header .main-header__right #cta-trigger,
body.search .main-header .main-header__right #cta-trigger {
  background-color: #30241c;
  color: #fff;
  border: 1px solid #30241c;
  transition: all 0.3s ease;
}

.main-header.main-header--sticky .main-header__right #cta-trigger:hover,
.main-header:not(.main-header--transparent) .main-header__right #cta-trigger:hover {
  background-color: #30241c;
  color: #fff;
  border: 1px solid #30241c;
}

.main-header__nav-mobile--wrapper .link-inner--alt {
  opacity: 1 !important;
}
.main-header__nav-mobile--wrapper .icon-link {
  display: inline-block;
  height: 20px;
}
.main-header__nav-mobile--wrapper .icon-link svg {
  width: 20px;
  height: 20px;
}

.footer-logo {
  display: block;
  max-width: 100%;
}
@media only screen and (max-width: 768px) {
  .footer-logo {
    margin-left: auto;
    margin-right: 0;
  }
}
@media only screen and (max-width: 480px) {
  .footer-logo {
    margin-right: auto;
  }
  .maplinks .maplinks__country-img {
    display: none;
  }
  .maplinks__image.mobile-image {
    filter: none!important;
  }
}
.footer-logo svg {
  width: 65px;
  height: 119px;
}
@media only screen and (min-width: 992px) {
  .footer-logo svg {
    position: absolute;
    right: 0;
    top: 0;
  }
}

.main-footer__aside-link {
  padding-top: 3px;
}
.main-footer__aside-link .c-btn-primary {
  border-color: #ccae7c !important;
  color: #ccae7c !important;
}
@media only screen and (min-width: 992px) {
  .main-footer__aside-link .c-btn-primary {
    width: 100%;
  }
}
.main-footer__aside-link .c-btn-primary:hover {
  background-color: #ccae7c !important;
  color: #fff !important;
}

.footer-menu-left ul,
.footer-menu-right ul {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
}

.main-footer__bottom-left {
  flex-wrap: wrap;
}

@media only screen and (max-width: 1275px) {
  .main-header .c-btn {
    margin-left: 7px;
    margin-right: 7px;
    font-size: 11px;
  }

  .main-header__right {
    padding-left: 0;
  }

  .main-header__wrapper {
    padding: 0 15px;
  }
}
@media only screen and (max-width: 1100px) {
  .main-header {
    padding-top: 29px;
    height: 100px;
  }

  .main-header .row {
    margin: 0;
  }

  .main-header__row {
    height: auto;
  }

  .main-header__logo {
    max-width: 230px;
  }

  .main-header__logo svg {
    width: 230px;
  }

  .main-header__wrapper {
    padding: 0 40px;
    max-width: initial;
  }

  .main-header__wrapper .container-fluid {
    padding: 0;
    max-width: initial;
  }

  .main-header__nav {
    display: none;
  }
  
  .main-header__left {
    padding: 0;
  }

  .main-header__right {
    display: none !important;
  }


  .main-header--sticky,
.search {
    padding: 8px 0 12px 0;
    height: 60px;
  }

  .main-header__bottom {
    padding: 8px 0 12px 0;
    height: 60px;
  }

  .main-header--sticky .btn-hamburger,
.search .btn-hamburger {
    top: 31px;
  }

  .btn-hamburger {
    display: block;
    top: 52px;
    right: 25px;
  }

}


@media only screen and (max-width: 1100px) {
  .main-header .main-header__nav-new {
    display: none;
  }
}
@media only screen and (max-width: 599px) {
  .main-header {
    height: 60px;
    padding: 8px 0 12px 0;
  }

  .main-header__wrapper {
    padding: 0 20px;
  }

  .btn-hamburger {
    right: 20px;
    top: 31px;
  }
}
/*New Safari and Journey Pages*/
.sj-hero {
  margin-top: 60px;
  position: relative;
  background-color: #332524;
}
@media only screen and (min-width: 599px) {
  .sj-hero {
    margin-top: 100px;
  }
}

@media only screen and (min-width: 1101px ){
  .sj-hero {
    margin-top: 103px;
  }  
}

.sj-hero .col-left,
.sj-hero .col-right {
  height: 592px;
  min-height: 592px;
  position: relative;
}
@media (max-width: 1199.98px) {
  .sj-hero .col-left,
.sj-hero .col-right {
    height: 41vw;
    min-height: 293px;
  }
}
@media (max-width: 767.98px) {
  .sj-hero .col-left,
.sj-hero .col-right {
    height: 293px;
    min-height: 293px;
  }
}
.sj-hero .col-left {
  flex-grow: 1;
  max-width: 100%;
  padding: 10px 40px 10px calc(15px + (100vw - 1420px) / 2);
}
@media (min-width: 1435px) {
  .sj-hero .col-left {
    flex: 0 0 75%;
    max-width: 75%;
  }
}
@media (max-width: 1434.98px) {
  .sj-hero .col-left {
    padding-left: 25px;
  }
}
@media (max-width: 575.98px) {
  .sj-hero .col-left {
    padding: 10px 20px;
  }
}
.sj-hero .col-left:before {
  content: "";
  background: linear-gradient(0deg, rgba(0, 0, 0, 0) 0%, #000000 100%);
  width: 100%;
  position: absolute;
  z-index: 2;
  left: 0;
  top: 0;
  height: 22%;
  opacity: 0.5;
  pointer-events: none;
}
.sj-hero .col-left:after {
  content: "";
  background: linear-gradient(180deg, rgba(29, 19, 18, 0) 0%, #1d1312 100%);
  width: 100%;
  position: absolute;
  z-index: 2;
  left: 0;
  bottom: 0;
  height: 40%;
  opacity: 0.8;
  pointer-events: none;
}
.sj-hero .col-right {
  max-width: 350px;
}
@media (min-width: 1435px) {
  .sj-hero .col-right {
    flex: 0 0 25%;
    max-width: 25%;
  }
}
.sj-hero .col-right > .row {
  height: 100%;
}
.sj-hero .col-right > .row > .col-12 {
  height: 50%;
}
.sj-hero .full-width {
  flex: 0 0 100% !important;
  max-width: 100% !important;
}
.sj-hero .lightbox-gallery__single-thumb {
  position: absolute;
  bottom: 39px;
  right: 35px;
  z-index: 9;
  background-color: #fff;
  border: none;
  padding: 12px 15px 11px 45px;
  min-width: 175px;
  color: #993d0f;
}
@media (max-width: 991.98px) {
  .sj-hero .lightbox-gallery__single-thumb {
    bottom: auto;
    top: 14px;
    right: 14px;
    width: 42px;
    height: 42px;
    padding: 0;
    min-width: 0;
  }
  .sj-hero .lightbox-gallery__single-thumb span {
    display: none;
  }
}
.sj-hero .lightbox-gallery__single-thumb:before {
  content: "";
  background-image: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='1024' height='1024'%3E%3Cpath fill='%23993d0f' d='M448 416V288h32v128h128v32H480v128h-32V448H320v-32h128zm196.65 219.35C596.61 678.057 533.335 704 464 704c-150.22 0-272-121.78-272-272s121.78-272 272-272 272 121.78 272 272c0 69.335-25.943 132.612-68.65 180.65L672 608l208.25 208.25c8.802 8.802 8.588 22.915-.25 31.75-8.898 8.898-23.052 8.948-31.75.25L640 640l4.65-4.65zM464 672c132.548 0 240-107.452 240-240S596.548 192 464 192 224 299.452 224 432s107.452 240 240 240z'/%3E%3C/svg%3E");
  background-size: contain;
  width: 30px;
  height: 30px;
  display: inline-block;
  background-repeat: no-repeat;
  transition: all 0.2s linear;
  position: absolute;
  left: 15px;
  top: 50%;
  transform: translateY(-50%);
}
@media (max-width: 991.98px) {
  .sj-hero .lightbox-gallery__single-thumb:before {
    left: 50%;
    transform: translate(-50%, -50%);
  }
}
.sj-hero .lightbox-gallery__single-thumb:hover {
  color: #fff;
  background-color: #993d0f;
}
.sj-hero .lightbox-gallery__single-thumb:hover:before {
  background-image: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='1024' height='1024'%3E%3Cpath fill='%23fff' d='M448 416V288h32v128h128v32H480v128h-32V448H320v-32h128zm196.65 219.35C596.61 678.057 533.335 704 464 704c-150.22 0-272-121.78-272-272s121.78-272 272-272 272 121.78 272 272c0 69.335-25.943 132.612-68.65 180.65L672 608l208.25 208.25c8.802 8.802 8.588 22.915-.25 31.75-8.898 8.898-23.052 8.948-31.75.25L640 640l4.65-4.65zM464 672c132.548 0 240-107.452 240-240S596.548 192 464 192 224 299.452 224 432s107.452 240 240 240z'/%3E%3C/svg%3E");
}

.sj-brochure-cta {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  z-index: 1;
  padding: 14px 20px;
  border-top: 1px solid #d9d4d0;
  text-align: center;
}

.sj-brochure-cta a {
  min-width: 100% !important;
}

@media (min-width: 768px) {
  .sj-brochure-cta {
    display: none;
  }
}
@media (max-width: 575.98px) {
  .sj-brochure-cta .c-btn {
    width: 100%;
  }
}

.sj-breadcrumbs {
  position: absolute;
  top: 0;
  left: 0;
  font-family: "Verlag", sans-serif;
  font-size: 16px;
  line-height: 24px;
  font-weight: 300;
  color: #fff;
  z-index: 10;
  padding: 10px 40px 10px calc(15px + (100vw - 1420px) / 2);
}
@media (max-width: 1434.98px) {
  .sj-breadcrumbs {
    padding-left: 25px;
  }
}
@media (max-width: 767.98px) {
  .sj-breadcrumbs {
    display: none;
  }
}
.sj-breadcrumbs a {
  color: #fff;
  padding: 10px 2px;
  display: inline-block;
}

.sj-hero-image {
  object-fit: cover;
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1;
}

.sj-hero-title {
  color: #fff;
  position: relative;
  z-index: 3;
  text-shadow: 0 1px 20px black;
}
@media (max-width: 1434.98px) {
  .sj-hero-title {
    font-size: 62px;
    line-height: 70px;
  }
}
@media (max-width: 1050.98px) {
  .sj-hero-title {
    font-size: 49px;
    line-height: 56px;
  }
}
@media (max-width: 575.98px) {
  .sj-hero-title {
    font-size: 36px;
    line-height: 40px;
    margin-bottom: 7px;
  }
}

.single-safari .main-header .c-btn, .single-safari .search .c-btn,
.single-journey .main-header .c-btn,
.single-journey .search .c-btn {
  color: #993d0f;
  border-color: #993d0f;
}
.single-safari .main-header .c-btn:hover, .single-safari .search .c-btn:hover,
.single-journey .main-header .c-btn:hover,
.single-journey .search .c-btn:hover {
  color: #fff;
  background-color: #993d0f;
}
.single-safari .block-camps-lodges-slider,
.single-safari .block-post-links,
.single-journey .block-camps-lodges-slider,
.single-journey .block-post-links {
  background-color: transparent !important;
  padding-bottom: 0;
}
.single-safari .block-camps-lodges-slider .row,
.single-safari .block-post-links .row,
.single-journey .block-camps-lodges-slider .row,
.single-journey .block-post-links .row {
  margin-top: 60px;
  margin-bottom: 20px;
  padding-top: 0;
  padding-bottom: 0;
}
@media (max-width: 767.98px) {
  .single-safari .block-camps-lodges-slider .row,
.single-safari .block-post-links .row,
.single-journey .block-camps-lodges-slider .row,
.single-journey .block-post-links .row {
    padding: 0 15px;
    margin-top: 42px;
    margin-bottom: 16px;
  }
}
@media (max-width: 575.98px) {
  .single-safari .block-camps-lodges-slider .row,
.single-safari .block-post-links .row,
.single-journey .block-camps-lodges-slider .row,
.single-journey .block-post-links .row {
    padding: 0 5px;
  }
}
@media (max-width: 767.98px) {
  .single-safari .block-camps-lodges-slider .sj-headings-row,
.single-safari .block-post-links .sj-headings-row,
.single-journey .block-camps-lodges-slider .sj-headings-row,
.single-journey .block-post-links .sj-headings-row {
    margin-bottom: 2px;
  }
}
.single-safari .block-camps-lodges-slider .inner-wrapper:before,
.single-safari .block-post-links .inner-wrapper:before,
.single-journey .block-camps-lodges-slider .inner-wrapper:before,
.single-journey .block-post-links .inner-wrapper:before {
  display: none;
}
.single-safari .block-camps-lodges-slider .b-content-images__col.content-col .info-wrapper,
.single-safari .block-post-links .b-content-images__col.content-col .info-wrapper,
.single-journey .block-camps-lodges-slider .b-content-images__col.content-col .info-wrapper,
.single-journey .block-post-links .b-content-images__col.content-col .info-wrapper {
  background-color: #fff;
  max-width: 542px;
  margin-top: -127px;
  padding: 37px 43px;
  box-shadow: 0 15px 30px 0 rgba(0, 0, 0, 0.07);
}
@media (max-width: 991.98px) {
  .single-safari .block-camps-lodges-slider .b-content-images__col.content-col .info-wrapper,
.single-safari .block-post-links .b-content-images__col.content-col .info-wrapper,
.single-journey .block-camps-lodges-slider .b-content-images__col.content-col .info-wrapper,
.single-journey .block-post-links .b-content-images__col.content-col .info-wrapper {
    margin-top: 0;
    padding: 16px 25px 19px 25px;
    max-width: none;
    width: 100%;
  }
}
.single-safari .block-camps-lodges-slider .b-content-images__col .content-inner__title,
.single-safari .block-post-links .b-content-images__col .content-inner__title,
.single-journey .block-camps-lodges-slider .b-content-images__col .content-inner__title,
.single-journey .block-post-links .b-content-images__col .content-inner__title {
  font-size: 36px !important;
  line-height: 42px !important;
  margin-bottom: 2px !important;
}
@media (max-width: 575.98px) {
  .single-safari .block-camps-lodges-slider .b-content-images__col .content-inner__title,
.single-safari .block-post-links .b-content-images__col .content-inner__title,
.single-journey .block-camps-lodges-slider .b-content-images__col .content-inner__title,
.single-journey .block-post-links .b-content-images__col .content-inner__title {
    font-size: 19px !important;
    line-height: 23px !important;
    margin-bottom: 4px !important;
  }
}
.single-safari .block-camps-lodges-slider .b-content-images__col .content-inner__title a,
.single-safari .block-post-links .b-content-images__col .content-inner__title a,
.single-journey .block-camps-lodges-slider .b-content-images__col .content-inner__title a,
.single-journey .block-post-links .b-content-images__col .content-inner__title a {
  color: #30241c;
}
.single-safari .block-camps-lodges-slider .b-content-images__col .content-inner__subtitle,
.single-safari .block-post-links .b-content-images__col .content-inner__subtitle,
.single-journey .block-camps-lodges-slider .b-content-images__col .content-inner__subtitle,
.single-journey .block-post-links .b-content-images__col .content-inner__subtitle {
  font-style: italic !important;
  font-size: 23px !important;
  line-height: 28px !important;
  margin-bottom: 17px !important;
}
@media (max-width: 575.98px) {
  .single-safari .block-camps-lodges-slider .b-content-images__col .content-inner__subtitle,
.single-safari .block-post-links .b-content-images__col .content-inner__subtitle,
.single-journey .block-camps-lodges-slider .b-content-images__col .content-inner__subtitle,
.single-journey .block-post-links .b-content-images__col .content-inner__subtitle {
    font-size: 15px !important;
    line-height: 19px !important;
    margin-bottom: 8px !important;
  }
}
.single-safari .block-camps-lodges-slider .b-content-images__col .content-inner__text,
.single-safari .block-post-links .b-content-images__col .content-inner__text,
.single-journey .block-camps-lodges-slider .b-content-images__col .content-inner__text,
.single-journey .block-post-links .b-content-images__col .content-inner__text {
  font-size: 18px;
  line-height: 28px;
  font-family: "Goudy Oldstyle W01", serif;
}
@media (max-width: 575.98px) {
  .single-safari .block-camps-lodges-slider .b-content-images__col .content-inner__text,
.single-safari .block-post-links .b-content-images__col .content-inner__text,
.single-journey .block-camps-lodges-slider .b-content-images__col .content-inner__text,
.single-journey .block-post-links .b-content-images__col .content-inner__text {
    font-size: 14px;
    line-height: 20px;
  }
}
.single-safari .block-camps-lodges-slider .b-content-images__col .c-btn,
.single-safari .block-post-links .b-content-images__col .c-btn,
.single-journey .block-camps-lodges-slider .b-content-images__col .c-btn,
.single-journey .block-post-links .b-content-images__col .c-btn {
  padding-left: 0;
  padding-right: 0;
  min-width: 0;
  margin-top: 6px;
  border: 0;
}
@media (max-width: 575.98px) {
  .single-safari .block-camps-lodges-slider .b-content-images__col .c-btn,
.single-safari .block-post-links .b-content-images__col .c-btn,
.single-journey .block-camps-lodges-slider .b-content-images__col .c-btn,
.single-journey .block-post-links .b-content-images__col .c-btn {
    margin-top: 2px;
  }
}
.single-safari .block-camps-lodges-slider .b-content-images__col .c-btn:last-child,
.single-safari .block-post-links .b-content-images__col .c-btn:last-child,
.single-journey .block-camps-lodges-slider .b-content-images__col .c-btn:last-child,
.single-journey .block-post-links .b-content-images__col .c-btn:last-child {
  margin-bottom: -11px;
}
@media (max-width: 575.98px) {
  .single-safari .block-camps-lodges-slider .b-content-images__col .c-btn:last-child,
.single-safari .block-post-links .b-content-images__col .c-btn:last-child,
.single-journey .block-camps-lodges-slider .b-content-images__col .c-btn:last-child,
.single-journey .block-post-links .b-content-images__col .c-btn:last-child {
    margin-bottom: -6px;
  }
}
.single-safari .block-post-links .b-content-images__col .content-inner__title,
.single-journey .block-post-links .b-content-images__col .content-inner__title {
  font-size: 48px !important;
  line-height: 48px !important;
  margin-bottom: 11px !important;
}
@media (max-width: 575.98px) {
  .single-safari .block-post-links .b-content-images__col .content-inner__title,
.single-journey .block-post-links .b-content-images__col .content-inner__title {
    font-size: 21px !important;
    line-height: 24px !important;
    margin-bottom: 4px !important;
  }
}
.single-safari .block-post-links .b-content-images__col .content-inner__title a,
.single-journey .block-post-links .b-content-images__col .content-inner__title a {
  color: #30241c;
}
@media (max-width: 767.98px) {
  .single-safari .block-post-links > .container,
.single-journey .block-post-links > .container {
    padding-left: 0;
    padding-right: 0;
  }
}
.single-safari .single-accordion__content,
.single-journey .single-accordion__content {
  font-family: "Goudy Oldstyle W01", serif;
  font-size: 20px;
  line-height: 32px;
  padding: 22px 20px 40px;
}
.single-safari .single-accordion__content .c-btn-wrapper,
.single-journey .single-accordion__content .c-btn-wrapper {
  margin-bottom: 0;
}
@media (max-width: 1050.98px) {
  .single-safari .single-accordion__content,
.single-journey .single-accordion__content {
    font-size: 18px;
  }
}
@media (max-width: 575.98px) {
  .single-safari .single-accordion__content,
.single-journey .single-accordion__content {
    font-size: 15px;
  }
}
.single-safari .page-accordion .table-wrapper,
.single-journey .page-accordion .table-wrapper {
  margin-top: 23px;
}
.single-safari .page-accordion .table-wrapper .tablepress tbody,
.single-journey .page-accordion .table-wrapper .tablepress tbody {
  font-size: 20px;
  line-height: 32px;
}
.single-safari .page-accordion .table-wrapper .tablepress tbody td,
.single-journey .page-accordion .table-wrapper .tablepress tbody td {
  text-align: left;
  border-bottom: none;
  padding: 8px 20px 7px 20px;
}
.single-safari .single-accordion__title,
.single-journey .single-accordion__title {
  border-bottom: 1px solid #dfdbd8;
  padding: 23px 60px 24px 20px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: flex-start;
}
@media (max-width: 767.98px) {
  .single-safari .single-accordion__title,
.single-journey .single-accordion__title {
    padding: 20px 60px 21px 20px;
    align-items: center;
  }
}
@media (max-width: 575.98px) {
  .single-safari .single-accordion__title,
.single-journey .single-accordion__title {
    padding: 16px 21px 16px 0px;
    align-items: center;
  }
}
.single-safari .single-accordion__title h4,
.single-journey .single-accordion__title h4 {
  border-bottom: none;
  padding: 0;
  color: #993D0F;
  font-family: "Goudy Oldstyle W01", serif;
  font-size: 31px !important;
  letter-spacing: -0.5px;
  line-height: 31px !important;
  margin-bottom: 0 !important;
}
@media (max-width: 1050.98px) {
  .single-safari .single-accordion__title h4,
.single-journey .single-accordion__title h4 {
    font-size: 24px !important;
    line-height: 24px !important;
  }
}
@media (max-width: 575.98px) {
  .single-safari .single-accordion__title h4,
.single-journey .single-accordion__title h4 {
    font-size: 18px !important;
    line-height: 18px !important;
  }
}
.single-safari .single-accordion__title:after,
.single-journey .single-accordion__title:after {
  background-image: url("data:image/svg+xml;charset=utf8,%3Csvg version='1.1' id='Layer_1' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink' x='0px' y='0px' viewBox='-292.404 370 34.79 54' enable-background='new -292.404 370 34.79 54' xml:space='preserve' width='35' height='54'%3E%3Cpath fill='%23993d0f' stroke='%23993d0f' stroke-width='1' stroke-miterlimit='10' d='M-289.771,371l26.15,25.95L-289.97,423h4.891 l26.05-26.05L-285.08,371H-289.771z'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  top: 50%;
  right: 34px;
}
@media (max-width: 575.98px) {
  .single-safari .single-accordion__title:after,
.single-journey .single-accordion__title:after {
    right: 2px;
    width: 9px;
    height: 13px;
  }
}
.single-safari .single-accordion,
.single-journey .single-accordion {
  margin: 0;
}

.two-columns ul {
  column-count: 2;
}
@media (max-width: 991.98px) {
  .two-columns ul {
    column-count: 1;
  }
}
@media (max-width: 767.98px) {
  .two-columns .sj-headings-row + ul {
    margin-top: -33px;
  }
}

.sj-container {
  max-width: 1320px;
}

@media (max-width: 767.98px) {
  .sj-sidebar-navigation {
    z-index: 99999;
    order: -1;
    position: fixed;
    top: 0px;
    left: 0;
    padding-left: 20px;
    padding-right: 20px;
    pointer-events: none;
    transform: translateY(-60px);
    transition: transform 0.4s ease-in-out;
  }
  .sj-sidebar-navigation.active {
    transform: translateY(0);
  }
}

.sj-sidebar-navigation-link-wrapper {
  position: sticky;
  top: 100px;
  padding-top: 33px;
}

@media only screen and (min-width: 1441px) {
  .sj-sidebar-navigation-link-wrapper {
    transform: translateX(50px);
  }
}
@media (max-width: 767.98px) {
  .sj-sidebar-navigation-link-wrapper {
    position: relative;
    top: 0;
    padding-top: 0;
    display: flex;
    flex-direction: column;
  }
  .sj-sidebar-navigation-link-wrapper:after {
    content: "";
    position: absolute;
    top: -20px;
    left: -20px;
    right: -20px;
    bottom: 0;
    z-index: -1;
    background-color: #fff;
    pointer-events: none;
    opacity: 0;
    transform: translateY(-100vh);
    transition: transform 0.4s ease-in-out, opacity 0.4s ease-in-out;
  }
  .sj-sidebar-navigation-link-wrapper.active:after {
    opacity: 1;
    transform: translateY(0);
    pointer-events: auto;
  }
  .sj-sidebar-navigation-link-wrapper.active .sj-sidebar-navigation-links-button:after {
    transform: translateY(-50%) rotate(-90deg);
  }
}
.sj-sidebar-navigation-link-wrapper .c-btn {
  margin-bottom: 16px;
}

.sj-widget-area {
  margin-top: 30px;
  padding-top: 40px;
  border-top: 1px solid #d9d4d0;
  margin-bottom: 30px;
}
@media (max-width: 767.98px) {
  .sj-widget-area {
    border-top: none;
    padding-top: 10px;
    margin-top: 0;
  }
}
.sj-widget-area .widget_text {
  text-align: center;
  font-size: 19px;
  line-height: 26px;
  font-weight: 300;
  box-shadow: 0 10px 30px 0 rgba(66, 50, 50, 0.1);
  color: #807C79;
  padding: 20px;
}
.sj-widget-area .widget_text a {
  color: #993d0f !important;
}

@media (max-width: 767.98px) {
  .sj-sidebar-navigation-content {
    opacity: 0;
    transform: translateY(-100vh);
    transition: transform 0.4s ease-in-out, opacity 0.4s ease-in-out;
    pointer-events: none;
    z-index: 3;
    display: flex;
    flex-direction: column;
  }
  .sj-sidebar-navigation-content.active {
    opacity: 1;
    transform: translateY(0);
    pointer-events: auto;
  }
}

.sj-sidebar-navigation-links {
  list-style: none;
  margin: 10px 0 0 0;
  padding: 0;
}
@media (max-width: 767.98px) {
  .sj-sidebar-navigation-links {
    order: -1;
  }
}

@media (max-width: 767.98px) {
  .brochure-link {
    display: none;
  }
}

@media (max-width: 767.98px) {
  .itinerary-download-link {
    position: relative;
    z-index: 2;
    margin-top: 20px;
  }
}

.sj-sidebar-navigation-link {
  display: block;
  color: #30241c;
  font-family: "Verlag", sans-serif;
  font-size: 18px;
  line-height: 24px;
  font-weight: bold;
  padding: 8px 0;
}
.sj-sidebar-navigation-link:hover {
  color: rgba(48, 36, 28, 0.8);
}
.sj-sidebar-navigation-link:focus, .sj-sidebar-navigation-link:active {
  color: #30241c;
}
.sj-sidebar-navigation-link.active {
  color: #993d0f !important;
}

.sj-sidebar-navigation-links-button {
  order: -2;
  position: relative;
  padding-top: 18px;
  padding-bottom: 17px;
  text-align: left;
  text-decoration: none !important;
  color: #993d0f !important;
  border: none !important;
  z-index: 4;
  pointer-events: auto;
}
.sj-sidebar-navigation-links-button:after {
  content: "";
  background-image: url("data:image/svg+xml;charset=utf8,%3Csvg version='1.1' id='Layer_1' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink' x='0px' y='0px' viewBox='-292.404 370 34.79 54' enable-background='new -292.404 370 34.79 54' xml:space='preserve' width='35' height='54'%3E%3Cpath fill='%23993d0f' stroke='%23993d0f' stroke-width='1' stroke-miterlimit='10' d='M-289.771,371l26.15,25.95L-289.97,423h4.891 l26.05-26.05L-285.08,371H-289.771z'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-size: contain;
  width: 11px;
  height: 17px;
  transition: all 0.2s linear;
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%) rotate(90deg);
}
.sj-sidebar-navigation-links-button:before {
  content: "";
  border-bottom: 1px solid #d9d4d0;
  position: absolute;
  top: 0;
  bottom: -1px;
  left: -20px;
  right: -20px;
  background-color: #fff;
  z-index: -1;
}

@media (max-width: 575.98px) {
  .sj-block-buttons {
    max-width: calc(100% - 10px);
    margin-left: auto;
    margin-right: auto;
  }
}

.sj-section {
  line-height: 1.55em;
  font-family: "Goudy Oldstyle W01", serif;
  font-size: 21px;
}
@media (max-width: 1050.98px) {
  .sj-section {
    font-size: 19px;
  }
}
@media (max-width: 575.98px) {
  .sj-section {
    font-size: 16px;
  }
}
@media (max-width: 575.98px) {
  .sj-section > .container {
    padding-left: 5px;
    padding-right: 5px;
  }
}
.sj-section h2 {
  font-size: 58px !important;
  line-height: 68px !important;
  margin-bottom: 24px !important;
}
@media (max-width: 1050.98px) {
  .sj-section h2 {
    font-size: 48px !important;
    line-height: 56px !important;
  }
}
@media (max-width: 767.98px) {
  .sj-section h2 {
    font-size: 38px !important;
    line-height: 42px !important;
  }
}
@media (max-width: 575.98px) {
  .sj-section h2 {
    font-size: 31px !important;
    line-height: 33px !important;
  }
}
.sj-section h3 {
  font-size: 48px !important;
  line-height: 58px !important;
  margin-bottom: 24px !important;
}
@media (max-width: 1050.98px) {
  .sj-section h3 {
    font-size: 40px !important;
    line-height: 49px !important;
  }
}
@media (max-width: 767.98px) {
  .sj-section h3 {
    font-size: 32px !important;
    line-height: 37px !important;
  }
}
@media (max-width: 575.98px) {
  .sj-section h3 {
    font-size: 28px !important;
    line-height: 30px !important;
    margin-bottom: 28px !important;
  }
}
.sj-section h4 {
  font-size: 40px !important;
  line-height: 50px !important;
}
@media (max-width: 1050.98px) {
  .sj-section h4 {
    font-size: 32px !important;
    line-height: 40px !important;
  }
}
@media (max-width: 767.98px) {
  .sj-section h4 {
    font-size: 28px !important;
    line-height: 30px !important;
  }
}
@media (max-width: 575.98px) {
  .sj-section h4 {
    font-size: 22px !important;
    line-height: 24px !important;
  }
}
.sj-section.block-content {
  margin-bottom: 63px;
}
.sj-section.block-content .row > div > h3:first-child {
  font-size: 58px !important;
  line-height: 68px !important;
  margin-bottom: 24px !important;
}
@media (max-width: 991.98px) {
  .sj-section.block-content > .container + .page-accordion {
    margin-top: -42px;
  }
}
.sj-section.block-content > .container + .page-accordion ~ .sj-block-buttons {
  margin-top: 11px;
  width: 100%;
}
.sj-section.block-content > .container + .page-accordion ~ .sj-block-buttons a {
  display: inline-block !important;
}
@media (max-width: 575.98px) {
  .sj-section.block-content > .container + .page-accordion ~ .sj-block-buttons a {
    width: 100%;
  }
}
@media (max-width: 575.98px) {
  .sj-section.block-content > .container + .page-accordion > .container {
    padding-left: 5px;
    padding-right: 5px;
  }
}
.sj-section.block-content > .container + .page-accordion .single-accordion__title {
  padding: 22px;
}
@media (max-width: 767.98px) {
  .sj-section.block-content > .container + .page-accordion .single-accordion__title {
    padding: 14px 21px 14px 0px;
  }
}
.sj-section p {
  margin-bottom: 23px;
}
@media (max-width: 575.98px) {
  .sj-section p {
    margin-bottom: 18px;
  }
}
.sj-section ul:not([class]) li {
  padding-left: 25px;
  margin-left: 12px;
  margin-bottom: 1.55em;
}
@media (max-width: 991.98px) {
  .sj-section ul:not([class]) li {
    margin-bottom: 18px;
  }
}
@media (max-width: 575.98px) {
  .sj-section ul:not([class]) li {
    margin-left: 0;
  }
}
.sj-section ul:not([class]) li:before {
  width: 6px;
  height: 6px;
}
@media (max-width: 575.98px) {
  .sj-section ul:not([class]) li:before {
    width: 5px;
    height: 5px;
    top: 9px;
    left: 4px;
  }
}
.sj-section .table-wrapper .tablepress th {
  color: #30241c;
}
@media (min-width: 768px) {
  .sj-section .table-wrapper .tablepress th {
    font-size: 20px;
    line-height: 24px;
  }
}
@media (min-width: 768px) {
  .sj-section .table-wrapper .tablepress tbody td {
    padding: 11px 0;
    line-height: 1.6;
    border-bottom: 1px solid #d9d4d0;
  }
}

.sj-details {
  background-color: #332524;
  color: #fff;
  font-family: "Goudy Oldstyle W01", serif;
  padding-top: 30px;
  padding-bottom: 35px;
}
@media (max-width: 767.98px) {
  .sj-details {
    padding-top: 24px;
    padding-bottom: 14px;
    line-height: 1.45;
  }
}
.sj-details .container {
  padding-left: 15px;
  padding-right: 15px;
  max-width: 1420px;
}
.sj-details .container .row {
  margin-left: 0;
  margin-right: 0;
}
@media (max-width: 575.98px) {
  .sj-details .container .row {
    margin-left: -10px;
    margin-right: -10px;
  }
}
.sj-details .col-md-3 {
  max-width: 280px;
}
@media (max-width: 767.98px) {
  .sj-details .col-12 {
    margin-bottom: 19px;
  }
}
.sj-details h6 {
  text-transform: uppercase;
  font-family: "Verlag", sans-serif;
  font-size: 13px;
  font-weight: 900;
  letter-spacing: 0.8px;
  margin-bottom: 4px;
}
@media (max-width: 575.98px) {
  .sj-details h6 {
    margin-bottom: 0;
    font-size: 12px;
  }
}

@media (max-width: 575.98px) {
  .sj-post-intro {
    padding-bottom: 16px;
  }
}
.sj-post-intro .intro-top {
  padding-bottom: 0;
}
@media (max-width: 575.98px) {
  .sj-post-intro .intro-top {
    padding-top: 8px;
  }
}
.sj-post-intro .intro-map blockquote {
  margin: 0;
}
.sj-post-intro .intro-map blockquote cite {
  font-family: "GoudyOldstyleW01-Italic_706308", serif !important;
  font-style: italic;
  font-size: 25px;
  line-height: 36px;
  margin-bottom: 21px;
}
@media (max-width: 575.98px) {
  .sj-post-intro .intro-map blockquote cite {
    font-size: 20px;
    line-height: 28px;
    margin-bottom: 11px;
  }
}
@media (max-width: 575.98px) {
  .sj-post-intro .intro-map blockquote .author {
    margin-bottom: 0;
  }
}
.sj-post-intro .intro-map blockquote span {
  font-size: 16px;
  line-height: 1.5;
}
.sj-post-intro .intro-quote {
  padding-left: 50px;
  padding-right: 50px;
}
@media (max-width: 575.98px) {
  .sj-post-intro .intro-quote {
    padding-left: 0;
    padding-right: 0;
    margin-left: -10px;
    margin-right: -10px;
  }
}
.sj-post-intro .intro-highlights-text ul {
  column-count: 2;
}
@media (max-width: 991.98px) {
  .sj-post-intro .intro-highlights-text ul {
    column-count: 1;
  }
}
@media (max-width: 991.98px) {
  .sj-post-intro .intro-highlights {
    margin-top: 33px;
  }
}
.sj-post-intro .intro-image-wrapper {
  text-align: left;
}
.sj-post-intro .intro-image-wrapper img {
  width: 370px;
  height: auto;
}

.sj-headings-row {
  margin-bottom: 36px;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
}
@media (max-width: 991.98px) {
  .sj-headings-row {
    flex-direction: column;
    align-items: flex-start;
    justify-content: flex-start;
  }
}
.sj-headings-row h2 {
  margin-bottom: 0 !important;
}
@media (max-width: 991.98px) {
  .sj-headings-row h2 {
    margin-bottom: 18px !important;
  }
}
@media (max-width: 991.98px) {
  .sj-headings-row.sj-post-itineraries-headings-row {
    margin-bottom: 10px;
  }
}

.sj-post-itineraries .itinerary-day {
  color: #993d0f;
  width: 150px;
  display: inline-block;
  font-weight: bold;
  font-size: 19px;
  font-family: "ITC Berkeley Oldstyle W01 Book", sans-serif;
  margin-top: 7px;
  line-height: 24px;
  flex: 0 0 150px;
}
@media (max-width: 1050.98px) {
  .sj-post-itineraries .itinerary-day {
    font-size: 14px;
    line-height: 20px;
    width: 110px;
    margin-top: auto;
    margin-bottom: auto;
    flex: 0 0 110px;
  }
}
@media (max-width: 575.98px) {
  .sj-post-itineraries .itinerary-day {
    font-size: 12px;
    line-height: 16px;
    width: 85px;
    margin-top: auto;
    margin-bottom: auto;
    flex: 0 0 85px;
  }
}
.sj-post-itineraries .itinerary-title {
  font-family: "Goudy Oldstyle W01", serif;
  font-size: 30px;
  display: inline-block;
}
@media (max-width: 1050.98px) {
  .sj-post-itineraries .itinerary-title {
    font-size: 23px;
    line-height: 28px;
  }
}
@media (max-width: 575.98px) {
  .sj-post-itineraries .itinerary-title {
    font-size: 17px;
    line-height: 20px;
  }
}

.itinerary-expand-all {
  color: #993d0f;
  text-transform: uppercase;
  font-family: "Verlag", sans-serif;
  font-size: 13px;
  font-weight: 900;
  letter-spacing: 0.8px;
}
@media (max-width: 991.98px) {
  .itinerary-expand-all {
    padding-left: 0;
    min-width: 0;
    order: 9;
    text-align: left;
  }
}
@media (max-width: 575.98px) {
  .itinerary-expand-all {
    width: 100%;
    padding-right: 0;
  }
}

@media (max-width: 991.98px) {
  .itinerary-download-button {
    margin-top: 10px;
  }
}
@media (max-width: 575.98px) {
  .itinerary-download-button {
    width: 100%;
  }
}

.c-btn-primary-orange {
  color: #ffffff;
  border: 2px solid #993d0f;
  background-color: #993d0f;
}

.c-btn-primary-orange:hover {
  background-color: #742e0b;
  border: 2px solid #742e0b;
}

.c-btn-primary-orange-outline {
  color: #993d0f;
  border: 2px solid #993d0f;
  background-color: transparent;
}

.c-btn-primary-orange-outline:hover {
  color: #fff;
  background-color: #993d0f;
  border: 2px solid #993d0f;
}

.c-btn-primary-transparent {
  color: #993d0f !important;
  border: 2px solid transparent;
  background-color: transparent;
}

.c-btn-primary-transparent:hover {
  background-color: transparent;
  border: 2px solid transparent;
  color: #742e0b !important;
}

.minw-0 {
  min-width: 0;
}

.slider-image-wrap-new .slick-track {
  display: flex;
  flex-direction: row;
  position: relative;
  max-height: 643px;
  height: 42vw;
}
@media (max-width: 767.98px) {
  .slider-image-wrap-new .slick-track {
    height: 59vw;
  }
}
.slider-image-wrap-new .slick-slide {
  border-bottom: none;
  float: none;
  height: 100%;
}
.slider-image-wrap-new .slick-slide img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.slider-image-wrap-new .slick-arrow {
  background-color: #fff;
  z-index: 9;
  width: 64px;
  height: 64px;
  top: auto;
  transform: none;
  left: auto;
  margin: 0;
  transition: background-color 0.2s linear;
}
@media (max-width: 991.98px) {
  .slider-image-wrap-new .slick-arrow {
    width: 48px;
    height: 48px;
  }
}
.slider-image-wrap-new .slick-arrow:after {
  background-image: url("data:image/svg+xml;charset=utf8,%3Csvg version='1.1' id='Layer_1' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink' x='0px' y='0px' viewBox='-292.404 370 34.79 54' enable-background='new -292.404 370 34.79 54' xml:space='preserve' width='35' height='54'%3E%3Cpath fill='%23993d0f' stroke='%23993d0f' stroke-width='1' stroke-miterlimit='10' d='M-289.771,371l26.15,25.95L-289.97,423h4.891 l26.05-26.05L-285.08,371H-289.771z'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  width: 11px;
  height: 17px;
  transition: all 0.2s linear;
  left: 50% !important;
}
@media (max-width: 991.98px) {
  .slider-image-wrap-new .slick-arrow:after {
    width: 9px;
    height: 13px;
  }
}
.slider-image-wrap-new .slick-arrow:hover {
  background-color: #993d0f;
}
.slider-image-wrap-new .slick-arrow:hover:after {
  background-image: url("data:image/svg+xml;charset=utf8,%3Csvg version='1.1' id='Layer_1' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink' x='0px' y='0px' viewBox='-292.404 370 34.79 54' enable-background='new -292.404 370 34.79 54' xml:space='preserve' width='35' height='54'%3E%3Cpath fill='%23ffffff' stroke='%23ffffff' stroke-width='1' stroke-miterlimit='10' d='M-289.771,371l26.15,25.95L-289.97,423h4.891 l26.05-26.05L-285.08,371H-289.771z'/%3E%3C/svg%3E");
  width: 11px;
  height: 17px;
}
@media (max-width: 991.98px) {
  .slider-image-wrap-new .slick-arrow:hover:after {
    width: 9px;
    height: 13px;
  }
}
.slider-image-wrap-new .slick-arrow.slick-prev {
  right: 0;
  bottom: 64px;
  border-bottom: 1px solid #d9d4d0;
}
.slider-image-wrap-new .slick-arrow.slick-prev:after {
  transform: translate(-50%, -50%) rotate(180deg) !important;
}
@media (max-width: 991.98px) {
  .slider-image-wrap-new .slick-arrow.slick-prev {
    border-bottom: none;
    border-right: 1px solid #d9d4d0;
    bottom: 0;
    right: 48px;
  }
}
.slider-image-wrap-new .slick-arrow.slick-next {
  right: 0;
  bottom: 0;
}
.slider-image-wrap-new .slick-arrow.slick-next:after {
  transform: translate(-50%, -50%) !important;
}

.sj-navigation-links-card {
  background-color: #fff;
  box-shadow: 0 15px 30px 0 rgba(0, 0, 0, 0.07);
  padding: 53px 30px 31px 30px;
}
@media (max-width: 991.98px) {
  .sj-navigation-links-card {
    padding: 36px 20px 17px 20px;
    margin-bottom: 24px;
  }
}
.sj-navigation-links-card a {
  color: #30241C;
}
.sj-navigation-links-card h5 {
  font-family: "Verlag", sans-serif;
  font-size: 24px;
  font-weight: bold;
  letter-spacing: 0.36px;
  line-height: 24px;
  margin-bottom: 8px;
}
@media (max-width: 575.98px) {
  .sj-navigation-links-card h5 {
    font-size: 20px;
    line-height: 20px;
    margin-bottom: 7px;
  }
}
.sj-navigation-links-card p {
  font-family: "Verlag", sans-serif;
  font-size: 18px;
  font-weight: 300;
  letter-spacing: 0.34px;
  line-height: 24px;
  color: #807C79;
  margin-bottom: 13px;
}
@media (max-width: 575.98px) {
  .sj-navigation-links-card p {
    font-size: 16px;
    line-height: 20px;
    margin-bottom: 0;
  }
}

.sj-navigation-links-brochure {
  margin-top: 60px;
}
@media (max-width: 575.98px) {
  .sj-navigation-links-brochure {
    margin-top: 34px;
  }
}
.sj-navigation-links-brochure h3 {
  line-height: 40px !important;
  font-size: 40px !important;
  margin-bottom: 14px !important;
}
@media (max-width: 575.98px) {
  .sj-navigation-links-brochure h3 {
    line-height: 29px !important;
    font-size: 29px !important;
    margin-bottom: 7px !important;
  }
}
.sj-navigation-links-brochure p {
  font-family: "Goudy Oldstyle W01", serif;
  font-size: 18px;
  line-height: 28px;
  margin-bottom: 30px;
}
@media (max-width: 575.98px) {
  .sj-navigation-links-brochure p {
    font-size: 16px;
    line-height: 24px;
    margin-bottom: 22px;
  }
}

.sj-navigation-links-brochure-text {
  padding: 54px 61px;
}
@media (max-width: 575.98px) {
  .sj-navigation-links-brochure-text {
    padding: 15px 15px 5px 15px;
  }
}
.sj-navigation-links-brochure-text .c-btn {
  padding-left: 24px;
  padding-right: 24px;
}
@media (max-width: 575.98px) {
  .sj-navigation-links-brochure-text .c-btn {
    width: 100%;
  }
}

.sj-block-post-links-full {
  margin-bottom: 77px;
  padding-top: 0;
}
@media (max-width: 767.98px) {
  .sj-block-post-links-full {
    margin-bottom: 41px;
  }
}
.sj-block-post-links-full > .container {
  max-width: 1320px;
  overflow: hidden;
}
.sj-block-post-links-full h2.section-heading {
  font-size: 56px !important;
  letter-spacing: -0.44px !important;
  line-height: 67px !important;
  margin-bottom: 31px !important;
}
@media (max-width: 1050.98px) {
  .sj-block-post-links-full h2.section-heading {
    font-size: 40px !important;
    letter-spacing: -0.24px !important;
    line-height: 48px !important;
    margin-bottom: 11px !important;
    margin-top: -15px;
  }
}
@media (max-width: 767.98px) {
  .sj-block-post-links-full h2.section-heading {
    font-size: 33px !important;
    line-height: 40px !important;
  }
}
@media (max-width: 575.98px) {
  .sj-block-post-links-full h2.section-heading {
    font-size: 28px !important;
    line-height: 35px !important;
  }
}
.sj-block-post-links-full .sj-details {
  background-color: #fff;
  color: #30241C;
  padding-top: 0;
  padding-bottom: 35px;
}
@media (max-width: 767.98px) {
  .sj-block-post-links-full .sj-details {
    padding-bottom: 8px;
  }
}
.sj-block-post-links-full .sj-details .row {
  padding-top: 32px;
  padding-bottom: 0;
  margin-top: 0;
  margin-bottom: 0;
}
@media (max-width: 575.98px) {
  .sj-block-post-links-full .sj-details .row {
    padding-top: 25px;
    padding-left: 0;
    padding-right: 0;
  }
}
.sj-block-post-links-full .sj-details .col-md-3 {
  max-width: none;
}
.sj-block-post-links-full .c-btn {
  min-width: 200px;
}
@media (max-width: 575.98px) {
  .sj-block-post-links-full .c-btn {
    width: 100%;
    max-width: calc(100% - 30px);
  }
}

.sj-block-post-links-full-card {
  box-shadow: 0 15px 30px 0 rgba(0, 0, 0, 0.07);
  margin-bottom: 40px;
}
@media (max-width: 767.98px) {
  .sj-block-post-links-full-card {
    margin-left: 15px;
    margin-right: 15px;
    margin-bottom: 24px;
  }
}
.sj-block-post-links-full-card .image-wrapper {
  background-color: #332524;
  min-height: 192px;
  display: block;
  border: none !important;
}
.sj-block-post-links-full-card .image-wrapper:after {
  content: "";
  background: linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, #000000 100%);
  width: 100%;
  position: absolute;
  z-index: 1;
  left: 0;
  bottom: 0;
  height: 44%;
  opacity: 0.06;
}
.sj-block-post-links-full-card h2 {
  position: absolute;
  bottom: 13px;
  left: 48px;
  z-index: 2;
  color: #fff;
  font-size: 64px !important;
  letter-spacing: -0.5px !important;
  line-height: 64px !important;
  text-shadow: 0 12px 20px rgba(0, 0, 0, 0.07);
}
@media (max-width: 1050.98px) {
  .sj-block-post-links-full-card h2 {
    font-size: 50px !important;
    line-height: 50px !important;
  }
}
@media (max-width: 767.98px) {
  .sj-block-post-links-full-card h2 {
    left: 20px;
    font-size: 40px !important;
    line-height: 40px !important;
    text-align: left;
    margin-bottom: 15px !important;
    bottom: 0;
  }
}
@media (max-width: 575.98px) {
  .sj-block-post-links-full-card h2 {
    font-size: 28px !important;
    line-height: 28px !important;
    letter-spacing: -0.25px !important;
  }
}

@media only screen and (max-width: 991px) {
  button.itinerary-expand-all {
    padding: 12px 15px 10px;
    width: 100%;
    text-align: center;
  }
}
header .icon-links {
  display: flex;
}

.link-inner {
  display: inline-block;
}
.link-inner img {
  max-width: 20px;
  max-height: 20px;
}

.block-cta h3 {
  color: #30241c !important;
  margin-bottom: 16px !important;
}
.block-cta .c-btn-wrapper {
  margin: auto;
  text-align: center;
}
.block-cta .c-btn-wrapper > a {
  background: #993d0e;
  color: #fff9ff;
  border-color: transparent;
}
.block-cta svg path {
  fill: #30241c !important;
}

@font-face {
  font-family: "BernhardMod BT";
  src: url("fonts/BernhardModernBT-Italic.woff2") format("woff2"), url("fonts/BernhardModernBT-Italic.woff") format("woff");
  font-weight: normal;
  font-style: italic;
  font-display: swap;
}
@font-face {
  font-family: "BernhardMod BT";
  src: url("fonts/BernhardModernBT-Roman.woff2") format("woff2"), url("fonts/BernhardModernBT-Roman.woff") format("woff");
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}
.villa-hero-bg,
.villa-hero-content {
  min-height: 844px;
}
@media only screen and (max-width: 992px) {
  .villa-hero-bg,
.villa-hero-content {
    min-height: 422px;
  }
}

.villa-hero-content {
  display: flex;
  justify-content: center;
  align-items: center;
  padding-top: 128px;
  padding-bottom: 128px;
}
.villa-hero-content > .content {
  max-width: 422px;
  margin: auto;
}
.villa-hero-content > .content img {
  max-width: 344px;
  display: block;
}
@media only screen and (max-width: 600px) {
  .villa-hero-content > .content img {
    max-width: 292px;
  }
}
.villa-hero-content > .content h2 {
  font-family: "BernhardMod BT";
  font-style: italic;
  font-weight: normal !important;
}
.villa-hero-content > .content h2.villa-location {
  font-size: 30px;
}

.bg-green {
  background: #27372d;
  color: #FFF;
}

.bg-tan {
  background: #bf9d55;
}
.bg-tan h2 {
  color: #FFF;
}
.bg-tan .c-btn {
  color: #FFF;
  border-color: #FFF;
}

.villa-post-bg,
.villa-hero-bg {
  background-position: center center;
  background-repeat: no-repeat;
  background-size: cover;
}

.villa-post-content,
.villa-post-bg {
  min-height: 715px;
}

.villa-post-content {
  text-align: center;
  display: flex;
  justify-content: center;
  align-items: center;
}
.villa-post-content > .content {
  margin: auto;
  max-width: 475px;
}
.villa-post-content h2 {
  font-family: "BernhardMod BT";
  font-weight: normal !important;
  letter-spacing: 1.33px;
}
.villa-post-content h6 {
  font-family: "Verlag", sans-serif;
  font-size: 15px;
  font-weight: bold;
  text-transform: uppercase;
}
.villa-post-content .c-btn-wrapper {
  margin-bottom: 0;
}

.single-villa main > span + .block-content-images {
  padding-top: 137px;
}
@media only screen and (max-width: 560px) {
  .single-villa main > span + .block-content-images {
    padding-top: 84px;
  }
}
.single-villa main > span + .block-content-images::after {
  content: "";
  display: block;
  width: 100%;
  height: 73px;
  position: absolute;
  top: 50px;
  left: 0;
  right: 0;
  background: url(images/villa-logo-horz.svg) no-repeat top center;
  background-size: 1920px 73px;
}
@media only screen and (max-width: 560px) {
  .single-villa main > span + .block-content-images::after {
    background-size: 900px 30px;
    top: 25px;
  }
}
.single-villa .villa-hero-content > .content {
  max-width: 420px;
}
.single-villa .villa-hero-content h1 {
  color: #bf9d55;
  font-size: 50px;
  font-family: "BernhardMod BT";
  font-weight: normal !important;
  line-height: normal;
}
@media only screen and (min-width: 768px) {
  .single-villa .slick-arrow::before, .single-villa .slick-arrow::after {
    background-image: url(images/icons/new-arrow.svg);
  }
}
@media only screen and (max-width: 767px) {
  .single-villa .slick-arrow {
    transform: scale(2);
    margin-top: -40px;
  }
  .single-villa .slick-arrow.slick-prev {
    margin-left: 20px;
  }
  .single-villa .slick-arrow.slick-next {
    margin-right: 20px;
  }
}
.single-villa .block-gallery-slider,
.single-villa .block-camps-lodges {
  background: #27372d;
  color: #FFF;
}
.single-villa .block-gallery-slider .text-colored,
.single-villa .block-camps-lodges .text-colored {
  color: #c3a159;
}
.single-villa .block-gallery-slider .content-link__title,
.single-villa .block-camps-lodges .content-link__title {
  color: #FFF;
}
.single-villa .block-gallery-slider .c-btn,
.single-villa .block-camps-lodges .c-btn {
  color: #c3a159;
  border-color: #c3a159;
}
.single-villa .block-gallery-slider h2,
.single-villa .block-camps-lodges h2 {
  font-family: "BernhardMod BT";
  font-weight: normal !important;
}
.single-villa .block-camps-lodges h2 {
  font-style: italic;
}
.block-gallery-slider .bc-gallery__caption {
  text-align: center;
}
@media only screen and (max-width: 767px) {
  .single-villa .block-camps-lodges {
    padding-top: 28px;
  }
}
.single-villa .block-content-images::before {
  background-color: transparent;
}
.single-villa .block-content-images .block-title,
.single-villa .block-content-images .block-title-alt {
  color: #FFF;
  font-size: 38px;
  font-family: "BernhardMod BT";
  line-height: normal;
  font-style: italic;
}
@media only screen and (max-width: 767px) {
  .single-villa .block-content-images .block-title,
.single-villa .block-content-images .block-title-alt {
    font-size: 28px;
  }
}
.single-villa .block-content-images {
  padding-bottom: 32px;
}
.single-villa .block-content,
.single-villa .block-content-images {
  background: #bf9d55;
}
@media only screen and (min-width: 992px) {
  .single-villa .block-content,
.single-villa .block-content-images {
    padding-top: 137px;
    padding-bottom: 137px;
  }
}
@media only screen and (min-width: 992px) {
  .single-villa .block-content-images .content-left,
.single-villa .block-content-images .content-right,
.single-villa .block-content-images .image-left,
.single-villa .block-content-images .image-right {
    flex: 0 0 50% !important;
    max-width: 50% !important;
    margin: 0;
  }
  .single-villa .block-content-images .content-left {
    padding-left: 4%;
  }
  .single-villa .block-content-images .content-right {
    padding-right: 4%;
  }
  .single-villa .block-content-images .b-content-images__content-wrapper {
    max-width: 100%;
  }
  .single-villa .block-content-images .b-content-images__content-wrapper .block-title {
    text-align: center;
  }
  .single-villa .block-content-images.right-aligned .b-content-images__content-wrapper {
    padding-left: 0;
    padding-right: 42px;
  }
  .single-villa .block-content-images.left-aligned .b-content-images__content-wrapper {
    padding-right: 0;
    padding-left: 42px;
  }
}
.single-villa .bc-gallery__image--md,
.single-villa .bc-gallery__image--sm {
  display: none !important;
}
.single-villa .bc-gallery__caption {
  font-style: italic;
  color: #FFF;
  font-size: 16px;
}

.block-logos {
  background: #27372d;
  color: #FFF;
  padding: 65px 0;
}
.block-logos h2 {
  margin-bottom: 50px;
  font-family: "BernhardMod BT";
  font-weight: normal !important;
  font-style: italic;
}

.block-gallery-slider + .block-logos {
  padding-top: 0;
}

.block-logos-content {
  display: grid;
  gap: 50px;
  grid-template-columns: repeat(3, 1fr);
}
@media only screen and (max-width: 991px) {
  .block-logos-content {
    grid-template-columns: repeat(2, minmax(32px, 1fr));
  }
}
@media only screen and (max-width: 768px) {
  .block-logos-content {
    grid-template-columns: repeat(1, minmax(32px, 1fr));
  }
}

.block-logo {
  font-family: "Verlag", sans-serif;
  font-size: 15px;
  font-weight: bold;
  text-transform: uppercase;
  display: flex;
}
.block-logo svg {
  width: 100%;
  height: auto;
  max-height: 32px;
  max-width: 32px;
  margin-right: 24px;
  display: block;
}

body:not(.single-villa) .bc-gallery__image img {
  max-width: 920px;
  margin: auto;
}

@media only screen and (max-width: 992px) {
  .villa-post-content,
.villa-hero-content {
    min-height: 0;
    padding-top: 32px;
    padding-bottom: 32px;
  }

  .villa-post-bg {
    min-height: 422px;
  }

  .villa-post .bg-tan {
    order: 2;
  }
}
@media only screen and (min-width: 992px) {
  .content > .villa-post:nth-of-type(even) .villa-post-content {
    order: 2;
  }
}
.block-content-images {
  overflow: visible;
}
.block-content-images figure .small-text {
  display: none;
}
.block-content-images .small-text {
  bottom: -23px;
}
.block-content-images.right-aligned .small-text {
  right: 0;
  text-align: right;
}
.block-content-images.left-aligned .small-text {
  left: 0;
}
.block-content-images.block-content-images--alt figure .small-text {
  display: block;
}
.block-content-images.block-content-images--alt figure + .small-text {
  display: none;
}
.block-content-images .b-content-images__col,
.block-content-images .row {
  position: relative;
}

.small-text {
  font-size: 14px;
  line-height: 18px;
  letter-spacing: 0.02em;
  padding: 18px;
  padding-top: 3px;
  margin-bottom: 16px;
  display: block;
}

.alert-banner {
  font-size: 24px;
  padding-top: 24px !important;
  padding-bottom: 24px;
  background: #BB832A;
  color: #FFF;
  font-family: "Goudy Oldstyle W01", serif;
}
@media only screen and (max-width: 768px) {
  .alert-banner {
    font-size: 1rem;
    padding-top: 16px !important;
    padding-bottom: 16px;
  }
}
.alert-banner a {
  color: #FFF;
  display: inline-block;
  padding-left: 16px;
  padding-right: 16px;
  border: none !important;
  text-decoration: none !important;
}
.alert-banner a:after {
  content: "";
  background: url(images/icons/link-arrow-white.svg) no-repeat;
  background-size: 16px 16px;
  width: 16px;
  height: 16px;
  display: inline-block;
  margin-left: 8px;
}

.round-logo {
  max-width: 225px;
  display: block;
  margin: auto;
}

.single-safari .content-column .round-logo {
  margin-left: auto;
  margin-right: auto;
}

.submenu {
  opacity: 0;
  top: 0;
  position: absolute;
}

.submenu.visible {
  position: absolute;
  top: calc(100% + 15px);
  left: 37px;
  background-color: white;
  padding: 16px;
  list-style: none;
  opacity: 1;
  transition: all 0.3s ease-in-out;
}

.main-header__cta-wrapper--mobile {
  flex-direction: column;
  align-items: flex-start;
}

.main-header__cta-wrapper--mobile a{
  margin-bottom: 15px;
}

@media (min-width: 768px) {
  .megamenu-wrapper button {
    opacity: 0;
  }

 }



 @media (max-width: 767px) {

  .main-header__cta-wrapper--mobile {
    flex-direction: column;
    align-items: flex-start;
  }

  /* ul.sub-menu {
    overflow-y: auto;
  } */
 }

 .main-footer__top .container {
  max-width: 1140px;
 }

 .footer-logo img {
  width: 100%;
 }

 .hbspt-form .select-wrap::after {
  z-index: 1;
}

.hbspt-form select {
  background: transparent;
  z-index: 3;
}

.select-wrap {
	background-color: #FFFFFF;
}

.megamenu-menu ul {
  height: 100%;
  /* background-color: #fdf8f5; */
  padding: 32px;
  height: min-content;
}

/* .megamenu-content {
  background-color: #fdf8f5;
} */

@media (max-width: 767px) {
  .top-banner-menu, .icon-links {
    display: none;
  }

  .main-header__nav-mobile {
    width: 100%!important;
  }

  .main-header--transparent:not(.main-header--sticky) .c-btn:not(.form-button) {
    color: #bb832a;
    border: 2px solid #bb832a;
  }
}


@media (min-width: 768px) {
  .megamenu-wrapper,
  .megamenu-toggle.active + .megamenu-wrapper .megamenu-link.active ~ .megamenu-image {
    top: 108px;
    overflow-y: auto;
  }
  
  body.admin-bar .megamenu-wrapper,
  body.admin-bar .megamenu-toggle.active + .megamenu-wrapper .megamenu-link.active ~ .megamenu-image {
    top: 140px;
  }

  .view-all-btn {
    position: absolute;
    top: 27px;
    right: 32px;
    font-weight: bold!important;
  }

  .view-all-btn a{
    font-weight: bold!important;
    font-size: 14px!important;
  }
}

.megamenu-wrapper::after {
  background-color: #fdf8f5;
}



.view-all-btn > a{
  display: flex!important;
  align-items: center!important;
}

.view-all-btn a::after{
  content: url('images/icons/arrow-2.svg');
  width: 16px;
  height: 16px;
  margin-left: 5px;
}

.megamenu-image-list li {
  display: flex;
  flex-direction: column;
  padding: 0 !important;
}

.megamenu-image-list li {
  margin-bottom: 28px;
}

.megamenu-image-list li h4 {
  font-family: "Verlag", sans-serif;
  font-weight: bold;
  font-size: 18px;
  line-height: 18px;
  display: block;
  letter-spacing: 0.3px;
  margin-top: 8px;
  margin-bottom: 0;
}

.megamenu-image-list li h4 + p{
  font-family: "Verlag", sans-serif;
  font-weight: normal;
  font-style: normal;
  font-size: 12px;
  line-height: 16px;
  color: #946D2C;
  padding-top: 5px;
  max-width: 300px;
}

.megamenu-image .megamenu-image-list {
  padding: 32px 30px;
  /* height: calc(100vh - 133px); */
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  top: 40px;
  position: relative;
}

li.menu-item-18927 .megamenu-image .megamenu-image-list {
  align-items: flex-start;
}


@media (max-width: 1100px) {
  .top-nav-row {
    display: none;
  }
  

  .main-header__logo {
    z-index: 199;
    position: absolute;
  }

  .main-header .c-btn {
    min-width: 100%;
  }

  ul.icon-links {
    display: flex;
    justify-content: center;
    width: 100%;
  }
}

@media (max-width: 767px) {

  ul.sub-menu {
    height: 100%;
  }

  .view-all-btn {
    position: absolute;
    top: 63px;
    right: 10px;
    font-size: 12px;
    display: flex;
    align-items: center;
  }

  /* .main-header__nav-mobile a {
    border-bottom: none;
  } */
}

@media (min-width: 1101px) {
  .icon-links.icon-links-left {
    display: none;
  }

  .desktop-hidden {
  display: none!important;
  }
}

.menu-new-main-nav-new-main-navigation-container ul#megamenu li.menu-item-18927 ul.megamenu-menu {
  align-items: flex-start;
  width: 40%;
}

.menu-new-main-nav-new-main-navigation-container ul#megamenu li:last-child ul.megamenu-menu .menu-item{
  padding-left: 0px;
}

/* .menu-new-main-nav-new-main-navigation-container ul#megamenu li.menu-item-18927 .megamenu-image {
  left: 40%;
  width: 60%;
} */

.menu-new-main-nav-new-main-navigation-container ul#megamenu li:last-child .megamenu-image .megamenu-image-list {
  width: 100%;
}


/* .megamenu-image {
  height: calc(100% - 133px);
} */


/* .megamenu-image-list img {
  max-height: 150px;
} */


li.menu-item-18910 {
  margin-top: 17px;
}

.megamenu-menua li:not(:first-child) .megamenu-link.depth-2 {
  font-size: 16px!important;
}


li.desktop-hidden {
  background-color: #ffffff;
}

li.desktop-hidden.first-hidden {
  margin-top: auto;
}

li.desktop-hidden a {
  border: none;
  padding: 20px 0;
}

.bolder-text a:not(.depth-3) {
  font-weight: bold!important;  
}

li.menu-item.view-all a {
  display: flex !important;
  align-items: center;
  justify-content: flex-end;
  /* border-bottom: 1px solid #9e6a16 !important; */
}

li.view-all a:after {
  content: "";
  display: inline-block;
  background: url(images/icons/arrow-2.svg) no-repeat right center;
  padding-right: 20px; /* or the width of your image + some space */
  vertical-align: middle;
  font-size: 14px;
  position: relative;
  width: 20px;
  height: 20px;
}

li .menu-item.bolder-text a.depth-2{
  font-family: "Verlag", sans-serif !important;
  font-size: 20px !important;
  line-height: 24px !important;
  display: block;
  letter-spacing: 0.3px !important;
}

.top-banner-menu {
  background-color: #fdf8f5;
}

.top-banner-menu .container {
  max-width: 1440px;
  width: 100%;
  margin: 0 auto;
  padding: 0 30px;
}

.top-nav-row {
  justify-content: flex-end;
  width: 100%;
}

.top-menu-items {
  display: none !important;
  padding-left: 0;
  margin-bottom: 0;
  list-style: none;
}

@media screen and (min-width: 1101px) {
  .top-menu-items {
    display: flex !important;
    max-width: 470px;
    margin: 0 0 0 auto;
    justify-content: space-between !important;
    padding: 0;
  }
  
}

.main-header:not(.main-header--transparent) .top-menu-items li a, 
.main-header.main-header--sticky .top-menu-items li a, 
body.page-template-page-no-hero .main-header .top-menu-items li a, 
body.single:not(.single-villa):not(.single-option-and-extension) .main-header .top-menu-items li a, 
body.blog .main-header .top-menu-items li a, 
body.search .main-header .top-menu-items li a{
  color: #30241c;
  font-size: 16px;
  display: flex;
}

.main-header .top-menu-items li a{
  color: #fff;
  font-size: 16px;
  display: flex;
}

.top-menu-items li a:hover{
  color: #bb832a;
}    


 .top-menu-items li a img {
  margin-right: 5px;
  transition: all .4s ease;  
 }
 
.main-header:not(.main-header--transparent) .top-menu-items li a img,
.main-header.main-header--sticky .top-menu-items li a img, 
body.page-template-page-no-hero .main-header .top-menu-items li a img, 
body.single:not(.single-villa):not(.single-option-and-extension) .main-header .top-menu-items li a img, 
body.blog .main-header .top-menu-items li a img, 
body.search .main-header .top-menu-items li a img{
  filter: invert(0);
}

.main-header .top-menu-items li a img{
  filter: invert(1);
}

.top-menu-items li:not(:last-child) a{
  margin-right: 15px;
}

.main-header--mobile-open {
  background: #fdf8f5 !important;
}


.filter-list-wrapper .list.open .dropdown-list {
  max-height: 300px;
  overflow-x: auto;
}

.tax-india_region .block-gallery-slider h2 {
  text-align: center;
}


/* Grid  */
.m-grid {
  padding: 75px 0;
  background-color: #fff1e8;
}
@media screen and (max-width: 768px) {
  .m-grid {
    padding: 72px 0;
  }
}

.m-grid__container {
  max-width: 1312px;
  margin: 0 auto;
  padding: 0 15px;
}

.m-grid__title{
  margin-bottom: 60px;
  font-size: 50px;
  line-height: 54px;
  font-weight: 400;
}

@media screen and (max-width: 768px) {
  .m-grid__title {
    font-size: 30px;
    line-height: 34px;
    margin-bottom: 30px;
  }
}

.m-grid__wrapper {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 32px;
}
@media screen and (max-width: 1000px) {
  .m-grid__wrapper {
    grid-template-columns: repeat(2, 1fr);
  }
}
@media screen and (max-width: 660px) {
  .m-grid__wrapper {
    grid-template-columns: repeat(1, 1fr);
  }
}

.m-grid__card {
  width: 100%;
}

.m-grid__card-image {
  height: 232px;
  background-size: cover;
  background-repeat: no-repeat;
  overflow: hidden;
}
.m-grid__card-image img {
  width: 100%;
  /* height: 100%;
  object-fit: cover; */
}

.m-grid__card-title {
  font-family: 'Verlag';
  font-weight: 400;
  margin: 20px 0 10px;
  font-size: 18px;
  line-height: 24px;
  color: #0A0A0A;
}
.m-grid__card-text {
  font-size: 16px;
  line-height: 24px;
  color: #0A0A0A;
}
/* Grid  */
/* Text rows, columns  */
.m-text-rows, .m-text-columns {
  padding: 75px 0;
}
.m-text-columns {
  padding-bottom: 150px;
}
.m-text-rows__container, 
.m-text-columns__container {
  max-width: 1312px;
  margin: 0 auto;
  padding: 0 15px;
}
.m-text-rows__wrapper,
.m-text-columns__wrapper {
  margin-top: 40px;
}
.m-text-columns__wrapper {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16px 55px;
}
.m-text-rows__text-row {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  align-items: center;
  gap: 0 20px;
  border-bottom: 1px solid #F1CFBB;
  padding: 36px 0 48px;
  box-sizing: border-box;
}
.m-text-rows__text-row-title {
  font-size: 24px;
  line-height: 32px;
  font-weight: 400;
  color: #1C1D22;
}
.m-text-rows__text-row-subtitle {
  font-size: 18px;
  line-height: 28px;
  font-weight: 400;
  color: #1C1D22;
  margin-left: -100px;
}

/* columns  */
.m-text-columns__text-column {
  max-width: 416px;
  padding-left: 28px;
  background-position: 0px 5px;
  background-repeat: no-repeat;
  background-image: url("data:image/svg+xml,%3Csvg width='18' height='18' viewBox='0 0 18 18' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M8.99984 17.3334C13.6022 17.3334 17.3332 13.6025 17.3332 9.00008C17.3332 4.39771 13.6022 0.666748 8.99984 0.666748C4.39746 0.666748 0.666504 4.39771 0.666504 9.00008C0.666504 13.6025 4.39746 17.3334 8.99984 17.3334ZM7.41699 13.1249L3.45866 9.16657L4.57491 8.05032L7.41699 10.8845L13.4257 4.87573L14.542 5.9999L7.41699 13.1249Z' fill='%23946D2C'/%3E%3C/svg%3E%0A");
}
.m-text-columns__text-column-text {
  font-weight: 400;
  font-size: 18px; 
  line-height: 28px;
}

@media screen and (max-width: 945px) {
  .m-text-rows__text-row-subtitle {
    margin-left: 0;
  }
}
@media screen and (max-width: 768px) {
  .m-text-rows {
    padding: 72px 0;
  }
  .m-text-columns {
    padding-bottom: 72px;
  }
  .m-text-rows__text-row {
    padding: 20px 0;
  }
  .m-text-rows__text-row-title {
    font-size: 18px;
    line-height: 28px;
  }

  .m-text-rows__text-row-subtitle {
    font-size: 14px;
    line-height: 24px;
  }

  .m-text-columns__wrapper {
    grid-template-columns: repeat(2, 1fr);
    gap: 16px;
  }
  .m-text-columns__text-column-text {
    font-size: 16px;
    line-height: 24px;
  }
}
@media screen and (max-width: 550px) {
  .m-text-columns__wrapper {
    grid-template-columns: repeat(1, 1fr);
  }
}

/* Text rows, columns  */


/* Maplinks */
.maplinks {
  max-width: 905px;
  margin: 0 auto;
  position: relative;
}

.block-caption {
  max-width: 905px;
  margin: 0 auto;
  text-align:center;
  font-style: italic;
  font-weight: 600;
  font-size: 18px;
}

.maplinks__image {
  transition: all .2s ease;
}

.maplinks__image.mobile-image {
  display:none;
}

.maplinks__country-img {
  position: absolute;
  bottom: 0px;
  left: -100px;
  width: 490px;
  transition: all .2s ease;
  z-index:-1;
  opacity: 0;
}

.maplinks__country-spot {
  position: absolute;
  /* background-color: black; */
  width: 65px;
  height: 65px;
  border-radius: 50%;
  cursor: pointer;

}
.maplinks__country-spot--botswana {
  bottom: 20.2%;
  right: 38.2%;
}
.maplinks__country-spot--kenya {
  bottom: 46.2%;
  right: 22.2%;
}
.maplinks__country-spot--namibia {
  width: 48px;
  bottom: 17.2%;
  left: 48.2%;
  height: 100px;
  border-radius: 0;
}
.maplinks__country-spot--rwanda {
  bottom: 45.2%;
  right: 33.6%;
  width: 81px;
  height: 30px;
}
.maplinks__country-spot--southafrica {
  width: 133px;
  height: 80px;
  bottom: 10.5%;
  right: 34.2%;
  transform: rotate(-30deg);
}
.maplinks__country-spot--tanzania {
  bottom: 35.1%;
  right: 24.2%;
  height: 118px;
  width: 85px;
  transform: rotate(-45deg);
}
.maplinks__country-spot--zambia {
  bottom: 30.2%;
  right: 30.2%;
  width: 130px;
  height: 60px;
  transform: rotate(-25deg);
}
.maplinks__country-spot--zimbabwe {
  bottom: 23.4%;
  right: 32.1%;
  width: 60px;
  height: 58px;
}

/* Maplinks */

/* Remove arrows from input type number  */
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}
input[type=number] {
  -moz-appearance: textfield;
}
/* Remove arrows from input type number  */