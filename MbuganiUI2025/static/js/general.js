jQuery(document).ready(function () {
  //Press covers
  // if (jQuery("body").hasClass("post-type-archive-press")) {
  //   jQuery(".mag").each(function () {
  //     jQuery(this)
  //       .find("a.mag--toggle")
  //       .on("click", function (e) {
  //         e.preventDefault();
  //         jQuery(".mag").removeClass("mag-visible");
  //         jQuery(this).parent().parent().toggleClass("mag-visible");
  //       });

  //     jQuery(this)
  //       .find(".close-mag")
  //       .on("click", function () {
  //         jQuery(this).parent().parent().parent().toggleClass("mag-visible");
  //       });
  //   });
  // }

  //CDN Images
  const siteHost = window.location.hostname;
  const siteProtocol = window.location.protocol;
  const sitePath = siteProtocol + "//" + siteHost;
  const cdnPath = "https://micato.com";

  let tempCSS, newCSS;
  let re = new RegExp(sitePath, "g");
  const styles = document.getElementsByTagName("style");

  for (let i = 0; i < styles.length; i++) {
    if (styles[i].innerHTML.includes(sitePath)) {
      tempCSS = styles[i].innerHTML;
      newCSS = tempCSS.replace(re, cdnPath);
      styles[i].innerHTML = "";
      styles[i].innerHTML = newCSS;
    }
  }

  //Megamenu
  jQuery(
    ".megamenu-content > .megamenu-menu > .menu-item-has-children:first-child > .megamenu-link"
  ).addClass("active");

  jQuery("#megamenu").on("click", ".megamenu-toggle", function (event) {
    event.preventDefault();
    let $this = jQuery(this);

    if (
      $this.hasClass("active") ||
      jQuery(".megamenu-toggle.active").length === 0
    ) {
    } else {
      jQuery(".megamenu-toggle").removeClass("active");
      $this.addClass("active");
      jQuery(".megamenu-consultation-link").addClass("active");
      jQuery(".megamenu-menu li").addClass("menu-item-has-children");
      jQuery(".megamenu-image-background").addClass("active");
      // jQuery('.megamenu-close-x').addClass('active');
      jQuery("body").addClass("lock-scroll");
    }

    if (jQuery(".megamenu-toggle.active").length === 0) {
      jQuery("body").removeClass("lock-scroll");
      setTimeout(function () {
        jQuery(".main-header").addClass("main-header--transparent");
      }, 300);
    }

    var consultationLink = jQuery(this).attr("data-consultation");

    if (!consultationLink) {
      var consultationLink = jQuery(this)
        .next(".megamenu-wrapper")
        .find(".megamenu-link.active[data-consultation]")
        .first()
        .attr("data-consultation");
    }

    if (consultationLink) {
      jQuery(".megamenu-consultation-link a").attr("href", consultationLink);
    }
  });

  let menuItems = document.querySelectorAll('#megamenu>li>a[href="#"]');

  menuItems.forEach((item) => {
    item.addEventListener("click", (e) => {
      if (e.target.classList.contains("active")) {
        e.target.classList.remove("active");
        return;
      }
      menuItems.forEach((e) => e.classList.remove("active"));
      e.target.classList.add("active");
      jQuery("body").addClass("lock-scroll");
      //e.target.children.classList.add('menu-item-has-children');
      jQuery(".megamenu-image-background").addClass("active");
    });
  });

  jQuery("#megamenu").on(
    "mouseenter",
    ".menu-item-has-children .megamenu-link.depth-1",
    function (event) {
      // event.preventDefault();
      jQuery(this)
        .parent()
        .siblings()
        .find(".megamenu-link.depth-1")
        .removeClass("active");
      jQuery(this).addClass("active");

      var consultationLink = jQuery(this).attr("data-consultation");

      if (!consultationLink) {
        var consultationLink = jQuery(this)
          .parents(".menu-item.depth-0")
          .find(".megamenu-toggle[data-consultation]")
          .first()
          .attr("data-consultation");
      }

      if (consultationLink) {
        jQuery(".megamenu-consultation-link a").attr("href", consultationLink);
      }
    }
  );

  // Toggle third level menu items based on the li tag and not the anchor tag
  jQuery("#megamenu").on(
    "mouseenter",
    ".menu-item.depth-2",
    function (event) {
      jQuery(this)
        .siblings()
        .find(".megamenu-menu")
        .removeClass("active");
      jQuery(this)
        .siblings()
        .removeClass("active");
      jQuery(this).addClass("active");
      jQuery(this).find(".megamenu-menu").addClass("active");
    }
  );

  // jQuery('.megamenu-close').on('click', function(event) {
  //   event.preventDefault();
  //   jQuery('.megamenu-toggle').removeClass('active');
  //   jQuery('.megamenu-consultation-link').removeClass('active');
  //   jQuery('.megamenu-image-background').removeClass('active');
  //   jQuery('.megamenu-close-x').removeClass('active');
  //   jQuery('body').removeClass('lock-scroll');
  // });

  jQuery(".megamenu-image__close-menu").on("click", function (event) {
    event.preventDefault();
    jQuery(".megamenu-toggle").removeClass("active");
    jQuery(".megamenu-consultation-link").removeClass("active");
    jQuery(".megamenu-image-background").removeClass("active");
    jQuery(".megamenu-close-x").removeClass("active");
    jQuery("body").removeClass("lock-scroll");
    setTimeout(function () {
      jQuery(".main-header").addClass("main-header--transparent");
    }, 600);
  });

  function isMegamenuActive() {
    return jQuery(".megamenu-toggle.nav-link").hasClass("active-menu");
  }

  jQuery(".main-header").on("mouseenter", function (event) {
    if (!isMegamenuActive()) {
      jQuery(this).removeClass("main-header--transparent");
    }
  });

  jQuery(".main-header").on("mouseleave", function (event) {
    if (!isMegamenuActive()) {
      jQuery(this).addClass("main-header--transparent");
    }
  });

  //Mobile Menu

  jQuery(document).on("click", ".btn-hamburger", function (event) {
    event.preventDefault();
    let $this = jQuery(this);

    if ($this.hasClass("open")) {
      jQuery(".main-header").addClass("main-header--mobile-open");
    } else {
      jQuery(".main-header").removeClass("main-header--mobile-open");
    }
  });

  // Iterate over each .sub-menu within .main-header__nav-mobile
  jQuery(".main-header__nav-mobile .sub-menu").each(function () {
    // Find the first li.desktop-hidden within this .sub-menu and add a class
    jQuery(this).children("li.desktop-hidden").first().addClass("first-hidden");
  });

  jQuery(window).on("resize", function () {
    if (window.innerWidth >= 1101) {
      // If the window is 1101px or wider, remove the class for mobile open
      jQuery(".main-header").removeClass("main-header--mobile-open");
    }
  });

  //Navbar Search
  jQuery(".link-inner[data-search]").on("click", function (event) {
    event.preventDefault();
    jQuery("#nav-search-form").addClass("active");
    jQuery(".main-header").addClass("search");
    jQuery("#nav-search-form .search-input").focus();
  });

  jQuery(".nav-search-close, .nav-search-overlay").on(
    "click",
    function (event) {
      event.preventDefault();
      jQuery("#nav-search-form").removeClass("active");
      jQuery(".main-header").removeClass("search");
    }
  );
});

//hubspot form styling
jQuery(window).load(function () {
  jQuery(".hs-button, input.gform_button").addClass("c-btn c-btn-primary");
  jQuery(".hs-button").removeClass("hs-button");
  jQuery(".hbspt-form fieldset, .hbspt-form form, .hs-input").removeClass();
  jQuery(".hbspt-form select").wrap('<div class="select-wrap"></div>');
});
/*
// Optinmonster pop up
if(jQuery('body').hasClass('safari-template-default')) {
  jQuery('a.itinerary-download-link').click(function() {
    if(this) {
      setTimeout(function() {
        jQuery('div.optin a')[0].click();
      }, 1000);
    }
  });
} 
*/
(function ($) {
  //Safari and Journey Sidebar Navigation
  const sjNav = $(".sj-sidebar-navigation");
  const sjSections = $(".sj-section");
  const pbody = $("body");

  $(".sj-sidebar-navigation-links").on(
    "click",
    ".sj-sidebar-navigation-link",
    function () {
      $(
        ".sj-sidebar-navigation-link-wrapper, .sj-sidebar-navigation-content"
      ).removeClass("active");
      $("body").removeClass("overlayed lock-scroll");
      $(
        ".sj-sidebar-navigation-links .sj-sidebar-navigation-link.active"
      ).removeClass("active");
      $(this).addClass("active");
      pbody.addClass("scrolling");
      setTimeout(function () {
        pbody.removeClass("scrolling");
      }, 600);
    }
  );

  if (sjNav.length) {
    $(window).scroll(function () {
      if (!pbody.hasClass("scrolling")) {
        var scroll = $(window).scrollTop();
        var height = $(window).height();

        if (scroll > 200) {
          $(".sj-sidebar-navigation").addClass("active");
        } else {
          $(".sj-sidebar-navigation").removeClass("active");
        }

        var top = $.grep(sjSections, function (item) {
          return $(item).position().top + 300 <= scroll;
        });

        var sectionID = $(top).last().attr("id");

        if (sectionID) {
          var navItem = $(
            '.sj-sidebar-navigation-links .sj-sidebar-navigation-link[href="#' +
              sectionID +
              '"]:not(.active):not(.inactive)'
          );

          if (navItem.length) {
            $(
              ".sj-sidebar-navigation-links .sj-sidebar-navigation-link.active"
            ).removeClass("active");
            navItem.addClass("active");
            $(".sj-sidebar-navigation-links-button").html(navItem.html());
          }
        }
      }
    });
  }

  //Safari and Journey Expand All Button
  $(".itinerary-expand-all").on("click", function () {
    $(this)
      .parent()
      .next(".page-accordion")
      .find(".single-accordion__title")
      .toggleClass("active")
      .next()
      .stop()
      .slideToggle(250);
    $(this)
      .find(".expand-text")
      .toggleClass("d-none")
      .next(".hide-text")
      .toggleClass("d-none");
  });

  //Mobile Sidebar Navigation Links
  $(".sj-sidebar-navigation-links-button").on("click", function (e) {
    e.preventDefault();
    $(
      ".sj-sidebar-navigation-link-wrapper, .sj-sidebar-navigation-content"
    ).toggleClass("active");
    $("body").toggleClass("overlayed lock-scroll");
  });

  //Camps & Lodges Slider
  /*
    $('.slider-image-wrap-new').each(function() {
        var sliderText = '#' + $(this).parent().next().find(".info-wrapper__content").attr('id');
        
        console.log(sliderText);

        $(this).slick({
            slidesToShow: 1,
            dots: false,
            arrows: true,
            asNavFor: sliderText,
            speed: 300,
        });
    });
    
    $('.slider-image-wrap-new').slick({
        slidesToShow: 1,
        dots: false,
        arrows: true,
        asNavFor: $(this).attr('data-target'),
        speed: 300,
    });

    $('.info-wrapper__content-new').slick({
        slidesToShow: 1,
        dots: false,
        arrows: false,
        draggable: false,
        asNavFor: $(this).attr('data-target'),
        speed: 300,
    });
*/

  $(".slider-image-wrap-new").each(function () {
    $(this).on('init', function (event, slick) {
      $('.slick-track').attr('aria-label', 'Image slider');
  });
    var sliderNavTarget = $(this).attr("data-target");
    $(this).slick({
      slidesToShow: 1,
      dots: false,
      arrows: true,
      asNavFor: sliderNavTarget,
      speed: 300,
    });
  });

  $(".info-wrapper__content-new").each(function () {
    $(this).on('init', function (event, slick) {
      $('.slick-track').attr('aria-label', 'Info slider');
  });
    var sliderNavTarget = $(this).attr("data-target");
    $(this).slick({
      slidesToShow: 1,
      dots: false,
      arrows: false,
      draggable: false,
      asNavFor: sliderNavTarget,
      speed: 300,
      adaptiveHeight: true,
    });
  });


  // Maplinks
  $('.maplinks__country-spot').hover(
    function() {
      let dataHover = $(this).data('hover');
      $(`.maplinks__country-img[data-hover="${dataHover}"]`)
      .css('opacity', 1)
      .css('z-index', 1); 
      $('.maplinks__image').css('filter', 'grayscale(100%)');
    }, function() {
      let dataHover = $(this).data('hover');
      $(`.maplinks__country-img[data-hover="${dataHover}"]`)
      .css('opacity', 0)
      .css('z-index', -1); 
      $('.maplinks__image').css('filter', 'grayscale(0)');
    }
  );
  // Maplinks
  
})(jQuery);

//"Camp & Lodge" Single Page Headers
function blackHeader() {
  const headers = document.querySelectorAll(
    ".single-camp-and-lodge .main-header"
  );

  if (headers) {
    headers.forEach((header) => {
      header.classList.add("main-header--sticky", "header-black");
    });
  }
}

blackHeader();
