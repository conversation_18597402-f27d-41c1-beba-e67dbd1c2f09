// This script is used to load a video in the background of a hero section.
// It checks if the user is on a mobile device or not and loads the appropriate video type (YouTube or Wistia).

document.addEventListener('DOMContentLoaded', function () {
    const container = document.getElementById('video-background');
    if (!container) return;

    const isMobile = window.innerWidth <= 1100;
    const id = isMobile ? container.dataset.mobileId : container.dataset.desktopId;
    const type = (isMobile ? container.dataset.mobileType : container.dataset.desktopType).toLowerCase();

    let iframe = document.createElement('iframe');
    iframe.setAttribute('allow', 'autoplay; fullscreen');
    iframe.setAttribute('frameborder', '0');

    let iframeWrapper = document.createElement('div');
    iframeWrapper.classList.add('iframeWrapper');
    let iframeContainer = document.createElement('div');
    iframeContainer.classList.add('iframe-wrapper');

    iframeContainer.appendChild(iframe);
    iframeWrapper.appendChild(iframeContainer);

    container.appendChild(iframeWrapper);

    function showVideo() {
    setTimeout(() => {
        container.classList.add('visible');
    }, 800);
}

    if (type === 'youtube') {
        iframe.id = 'yt-background-video';
        iframe.classList.add('iframe_video');

        const checkYTReady = setInterval(() => {
            if (typeof YT !== 'undefined' && YT && YT.Player) {
                clearInterval(checkYTReady);

                iframe.src = `https://www.youtube.com/embed/${id}?enablejsapi=1&autoplay=1&mute=1&loop=1&playlist=${id}&controls=0&modestbranding=1&showinfo=0&rel=0&iv_load_policy=3`;

                new YT.Player('yt-background-video', {
                    events: {
                        'onReady': showVideo
                    }
                });
            }
        }, 100);
    } else if (type === 'wistia') {
        const wistiaScript = document.createElement('script');
        wistiaScript.src = 'https://fast.wistia.com/assets/external/E-v1.js';
        document.head.appendChild(wistiaScript);

        window._wq = window._wq || [];
        _wq.push({
            id: id,
            onReady: function () {
                showVideo();
            }
        });

        iframe.src = `https://fast.wistia.net/embed/iframe/${id}?autoplay=1&muted=true&endVideoBehavior=loop`;
        iframe.classList.add('wistia_embed');
    }
});
