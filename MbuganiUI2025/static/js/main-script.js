! function(e) {
    var t = {};

    function n(i) {
        if (t[i]) return t[i].exports;
        var a = t[i] = {
            i: i,
            l: !1,
            exports: {}
        };
        return e[i].call(a.exports, a, a.exports, n), a.l = !0, a.exports
    }
    n.m = e, n.c = t, n.d = function(e, t, i) {
        n.o(e, t) || Object.defineProperty(e, t, {
            enumerable: !0,
            get: i
        })
    }, n.r = function(e) {
        "undefined" != typeof Symbol && Symbol.toStringTag && Object.defineProperty(e, Symbol.toStringTag, {
            value: "Module"
        }), Object.defineProperty(e, "__esModule", {
            value: !0
        })
    }, n.t = function(e, t) {
        if (1 & t && (e = n(e)), 8 & t) return e;
        if (4 & t && "object" == typeof e && e && e.__esModule) return e;
        var i = Object.create(null);
        if (n.r(i), Object.defineProperty(i, "default", {
                enumerable: !0,
                value: e
            }), 2 & t && "string" != typeof e)
            for (var a in e) n.d(i, a, function(t) {
                return e[t]
            }.bind(null, a));
        return i
    }, n.n = function(e) {
        var t = e && e.__esModule ? function() {
            return e.default
        } : function() {
            return e
        };
        return n.d(t, "a", t), t
    }, n.o = function(e, t) {
        return Object.prototype.hasOwnProperty.call(e, t)
    }, n.p = "", n(n.s = 29)
}([function(e, t, n) {
    "use strict";
    Object.defineProperty(t, "__esModule", {
        value: !0
    });
    var i = jQuery.noConflict(),
        a = !1,
        r = 0;
    t.default = function() {
        i(window).bind("scroll", function() {
            a && i(window).scrollTop(r)
        }), i(window).bind("touchmove", function() {
            i(window).trigger("scroll")
        })
    }, t.enableScroll = function() {
        a = !1, i("body").removeClass("lock-scroll")
    }, t.stopScroll = function() {
        a = !0, r = i(window).scrollTop(), i("body").addClass("lock-scroll")
    }
}, function(e, t, n) {
    "use strict";
    Object.defineProperty(t, "__esModule", {
        value: !0
    });
    var i = function() {
            function e(e, t) {
                for (var n = 0; n < t.length; n++) {
                    var i = t[n];
                    i.enumerable = i.enumerable || !1, i.configurable = !0, "value" in i && (i.writable = !0), Object.defineProperty(e, i.key, i)
                }
            }
            return function(t, n, i) {
                return n && e(t.prototype, n), i && e(t, i), t
            }
        }(),
        a = n(0);
    var r = jQuery.noConflict(),
        o = function() {
            function e() {
                var t = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : ".btn-hamburger",
                    n = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : "body",
                    i = arguments.length > 2 && void 0 !== arguments[2] ? arguments[2] : ".menu-item-has-children > a",
                    a = arguments.length > 3 && void 0 !== arguments[3] ? arguments[3] : ".link-back";
                ! function(e, t) {
                    if (!(e instanceof t)) throw new TypeError("Cannot call a class as a function")
                }(this, e), this.trigger = t, this.pbody = n, this.listParent = i, this.linkBack = a
            }
            return i(e, [{
                key: "init",
                value: function() {
                    r(this.trigger).on("click", this.setNavState), r(this.listParent).on("click", this.openMobileNavItems), r(this.pbody).on("click", this.linkBack, this.returnToPreviousMenu)
                }
            }, {
                key: "setNavState",
                value: function(t) {
                    t.preventDefault();
                    var n = r("body");
                    r(this).hasClass("open") ? e.hideWrapper(this, n) : e.showWrapper(this, n)
                }
            }, {
                key: "openMobileNavItems",
                value: function(e) {
                    if ( ! jQuery(e.currentTarget).hasClass('depth-2') ) {
                        e.preventDefault();
                    }
                    var t = r(this).parent().find("> .sub-menu"),
                        n = '<li class="list-item-back"><button class="link-back"><span>' + r(this)[0].childNodes[0].nodeValue  + "</span></button></li>";
                    r(this).addClass("active-menu"), r(".menu-item-has-children > a").removeClass("non-active-menu"), t.prepend(n)
                }
            }, {
                key: "returnToPreviousMenu",
                value: function() {
                    var e = r(this).closest(".menu-item-has-children").find("> a");
                    e.removeClass("active-menu"), e.addClass("non-active-menu"), r(this).parent().remove()
                }
            }, {
                key: "resized",
                value: function() {
                    !r(this.trigger).is(":visible") && r(this.trigger).hasClass("open") && e.hideWrapper(this.trigger, r(this.pbody))
                }
            }, {
                key: "hideOutsideClick",
                value: function(t) {
                    if (r(this.trigger).length > 0 && r(this.trigger).hasClass("open")) {
                        var n = r(".main-header");
                        n.is(t.target) || 0 !== n.has(t.target).length || e.hideWrapper(this.trigger, r(this.pbody))
                    }
                }
            }, {
                key: "setProperMenuHeight",
                value: function() {
                    //var e = window.innerHeight + "px";
                    //r(".main-header__nav-mobile--wrapper").css("height", e)
                }
            }], [{
                key: "hideWrapper",
                value: function(e, t) {
                    var n = r(".main-header__nav-mobile").find(".active-menu"),
                        i = r(".list-item-back"),
                        o = r(".main-header:not('.header-black')");
                    r(e).removeClass("open"), t.removeClass("overlayed"), r(e).next().removeClass("active"), n.removeClass("active-menu"), i.remove(), r(window).scrollTop() > 0 && r(o).addClass("main-header--sticky"), (0, a.enableScroll)()
                }
            }, {
                key: "showWrapper",
                value: function(e, t) {
                    r(e).addClass("open"), t.addClass("overlayed"), r(e).next().addClass("active");
                    var n = r(".main-header:not('.header-black')");
                    n.hasClass("main-header--sticky") && n.removeClass("main-header--sticky"), (0, a.stopScroll)()
                }
            }]), e
        }();
    t.default = o
}, function(e, t, n) {
    "use strict";
    Object.defineProperty(t, "__esModule", {
        value: !0
    });
    var i = function() {
        function e(e, t) {
            for (var n = 0; n < t.length; n++) {
                var i = t[n];
                i.enumerable = i.enumerable || !1, i.configurable = !0, "value" in i && (i.writable = !0), Object.defineProperty(e, i.key, i)
            }
        }
        return function(t, n, i) {
            return n && e(t.prototype, n), i && e(t, i), t
        }
    }();
    var a = jQuery.noConflict(),
        r = function() {
            function e(t, n) {
                ! function(e, t) {
                    if (!(e instanceof t)) throw new TypeError("Cannot call a class as a function")
                }(this, e), this.wrapper = t, this.items = n
            }
            return i(e, [{
                key: "init",
                value: function() {
                    var e = a(this.wrapper).find(this.items),
                        t = e.length,
                        n = Math.floor(Math.random() * t);
                    e.eq(n).addClass("is-visible")
                }
            }]), e
        }();
    t.default = new r(".aside-link", ".aside-link-subtitle")
}, function(e, t, n) {
    "use strict";
    Object.defineProperty(t, "__esModule", {
        value: !0
    });
    var i = function() {
        function e(e, t) {
            for (var n = 0; n < t.length; n++) {
                var i = t[n];
                i.enumerable = i.enumerable || !1, i.configurable = !0, "value" in i && (i.writable = !0), Object.defineProperty(e, i.key, i)
            }
        }
        return function(t, n, i) {
            return n && e(t.prototype, n), i && e(t, i), t
        }
    }();
    var a = jQuery.noConflict(),
        r = function() {
            function e(t, n) {
                ! function(e, t) {
                    if (!(e instanceof t)) throw new TypeError("Cannot call a class as a function")
                }(this, e), this.IntroWrap = t, this.sideWrapper = n
            }
            return i(e, [{
                key: "init",
                value: function() {
                    var e = a(this.sideWrapper).outerHeight();
                    a(this.IntroWrap).css("min-height", e)
                }
            }]), e
        }(),
        o = new r(".sj-post-intro__wrap", ".side-wrapper"),
        s = new r(".regions-destinations-intro .intro-column", ".intro-image-wrapper img");
    t.default = o, t.setRegionIntroHeight = s
}, function(e, t, n) {
    "use strict";
    Object.defineProperty(t, "__esModule", {
        value: !0
    });
    var i = function() {
        function e(e, t) {
            for (var n = 0; n < t.length; n++) {
                var i = t[n];
                i.enumerable = i.enumerable || !1, i.configurable = !0, "value" in i && (i.writable = !0), Object.defineProperty(e, i.key, i)
            }
        }
        return function(t, n, i) {
            return n && e(t.prototype, n), i && e(t, i), t
        }
    }();
    var a = jQuery.noConflict(),
        r = new(function() {
            function e(t, n) {
                ! function(e, t) {
                    if (!(e instanceof t)) throw new TypeError("Cannot call a class as a function")
                }(this, e), this.selector = t, this.arrow = n
            }
            return i(e, [{
                key: "init",
                value: function() {
                    var e = window.matchMedia("screen and (max-width: 767px)"),
                        t = this;
                    e.matches && (t.slickify(), a(t.arrow).on("click", function() {
                        a(t.selector).slick("slickNext")
                    })), e.addListener(function(e) {
                        e.matches ? (t.slickify(), a(t.arrow).on("click", function() {
                            a(t.selector).slick("slickNext")
                        })) : a(t.selector).slick("unslick")
                    })
                }
            }, {
                key: "slickify",
                value: function() {
                    a(this.selector).slick({
                        dots: !1,
                        arrows: !1,
                        infinite: !0,
                        slidesToShow: 2,
                        slidesToScroll: 2,
                        speed: 600
                    }), this.setNavItemsHeight()
                }
            }, {
                key: "setNavItemsHeight",
                value: function() {}
            }]), e
        }())(".scroll-nav-list", ".nav-arrow");
    t.default = r
}, function(e, t, n) {
    "use strict";
    Object.defineProperty(t, "__esModule", {
        value: !0
    });
    var i = function() {
        function e(e, t) {
            for (var n = 0; n < t.length; n++) {
                var i = t[n];
                i.enumerable = i.enumerable || !1, i.configurable = !0, "value" in i && (i.writable = !0), Object.defineProperty(e, i.key, i)
            }
        }
        return function(t, n, i) {
            return n && e(t.prototype, n), i && e(t, i), t
        }
    }();
    var a = jQuery.noConflict(),
        r = function() {
            function e(t, n, i, a) {
                ! function(e, t) {
                    if (!(e instanceof t)) throw new TypeError("Cannot call a class as a function")
                }(this, e), this.caption = t, this.contentWrapper = n, this.lightbox = i, this.slideContent = a
            }
            return i(e, [{
                key: "init",
                value: function() {
                    if (a(this.caption).length > 0) {
                        var e = a(this.contentWrapper).offset().top - a(this.contentWrapper).parent().offset().top + a(this.contentWrapper).find(this.slideContent).outerHeight();
                        a(this.caption).css({
                            top: e
                        })
                    }
                }
            }]), e
        }(),
        o = new r(".lightbox--video .slider-caption", ".content-wrapper__inner", ".lightbox--video", "iframe"),
        s = new r(".lightbox--gallery .slider-caption", ".content-wrapper__inner", ".lightbox--gallery", "img");
    t.default = o, t.setCaptPosGallery = s
}, function(e, t, n) {
    "use strict";
    Object.defineProperty(t, "__esModule", {
        value: !0
    });
    var i = function() {
        function e(e, t) {
            for (var n = 0; n < t.length; n++) {
                var i = t[n];
                i.enumerable = i.enumerable || !1, i.configurable = !0, "value" in i && (i.writable = !0), Object.defineProperty(e, i.key, i)
            }
        }
        return function(t, n, i) {
            return n && e(t.prototype, n), i && e(t, i), t
        }
    }();
    var a = jQuery.noConflict(),
        r = function() {
            function e(t, n, i) {
                ! function(e, t) {
                    if (!(e instanceof t)) throw new TypeError("Cannot call a class as a function")
                }(this, e), this.blog = t, this.mainHeader = n, this.mainHeaderTransp = i
            }
            return i(e, [{
                key: "init",
                value: function() {
                    var e = this,
                        t = window.matchMedia("screen and (max-width: 991px)"),
                        n = this;
                    a("body").hasClass(this.blog) && (t.matches && a(n.mainHeader).addClass(this.mainHeaderTransp), t.addListener(function(t) {
                        t.matches ? a(n.mainHeader).addClass(e.mainHeaderTransp) : a(n.mainHeader).hasClass(e.mainHeaderTransp) && a(n.mainHeader).removeClass(e.mainHeaderTransp)
                    }))
                }
            }]), e
        }();
    t.default = new r("blog", ".main-header", "main-header--transparent")
}, function(e, t, n) {
    "use strict";
    Object.defineProperty(t, "__esModule", {
        value: !0
    });
    var i = function() {
        function e(e, t) {
            for (var n = 0; n < t.length; n++) {
                var i = t[n];
                i.enumerable = i.enumerable || !1, i.configurable = !0, "value" in i && (i.writable = !0), Object.defineProperty(e, i.key, i)
            }
        }
        return function(t, n, i) {
            return n && e(t.prototype, n), i && e(t, i), t
        }
    }();
    var a = jQuery.noConflict(),
        r = new(function() {
            function e(t, n, i) {
                ! function(e, t) {
                    if (!(e instanceof t)) throw new TypeError("Cannot call a class as a function")
                }(this, e), this.btn = t, this.hiddenText = n, this.btnHide = i
            }
            return i(e, [{
                key: "init",
                value: function() {
                    var e = this;
                    a(this.btn).on("click", function(t) {
                        a(e.btn).hasClass("text-visible") ? (a(e.hiddenText).slideUp(), a(e.btn).removeClass("text-visible")) : (a(e.hiddenText).slideDown(), a(e.btn).addClass("text-visible")), a(t.currentTarget).find("span").each(function(e, t) {
                            a(t).hasClass("btn-hide") ? a(t).removeClass("btn-hide") : a(t).addClass("btn-hide")
                        })
                    })
                }
            }]), e
        }())(".toggle-text", ".hidden-wrapper", ".btn-hide");
    t.default = r
}, function(e, t, n) {
    "use strict";
    Object.defineProperty(t, "__esModule", {
        value: !0
    });
    var i = function() {
        function e(e, t) {
            for (var n = 0; n < t.length; n++) {
                var i = t[n];
                i.enumerable = i.enumerable || !1, i.configurable = !0, "value" in i && (i.writable = !0), Object.defineProperty(e, i.key, i)
            }
        }
        return function(t, n, i) {
            return n && e(t.prototype, n), i && e(t, i), t
        }
    }();
    var a = jQuery.noConflict(),
        r = new(function() {
            function e(t) {
                var n = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : ".page-hero";
                ! function(e, t) {
                    if (!(e instanceof t)) throw new TypeError("Cannot call a class as a function")
                }(this, e), this.heroScrollBtn = t, this.hero = n
            }
            return i(e, [{
                key: "init",
                value: function() {
                    this.setScrollBtnPos(), a(this.heroScrollBtn).on("click", this.scrollDownHeader)
                }
            }, {
                key: "scrollDownHeader",
                value: function(e) {
                    var t = a(e.currentTarget).closest(".page-hero").outerHeight(),
                        n = a(".main-header").outerHeight();
                    a("html, body").animate({
                        scrollTop: t - n
                    }, 400)
                }
            }, {
                key: "setScrollBtnPos",
                value: function() {
                    var e = a(window).scrollTop() + a(window).height();
                    a(this.hero).outerHeight() > e ? a(this.heroScrollBtn).css({
                        position: "fixed"
                    }) : a(this.heroScrollBtn).css({
                        position: "absolute"
                    })
                }
            }]), e
        }())(".hero-scroll-down", ".page-hero");
    t.default = r
}, function(e, t, n) {
    "use strict";
    Object.defineProperty(t, "__esModule", {
        value: !0
    });
    var i = function() {
        function e(e, t) {
            for (var n = 0; n < t.length; n++) {
                var i = t[n];
                i.enumerable = i.enumerable || !1, i.configurable = !0, "value" in i && (i.writable = !0), Object.defineProperty(e, i.key, i)
            }
        }
        return function(t, n, i) {
            return n && e(t.prototype, n), i && e(t, i), t
        }
    }();
    var a = jQuery.noConflict(),
        r = new(function() {
            function e(t) {
                var n = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : "main-header";
                ! function(e, t) {
                    if (!(e instanceof t)) throw new TypeError("Cannot call a class as a function")
                }(this, e), this.scrollBtn = t, this.header = n
            }
            return i(e, [{
                key: "init",
                value: function() {
                    var e = this;
                    a(this.scrollBtn).on("click", function(t) {
                        var n = a(t.currentTarget).closest("section"),
                            i = a("section").index(n) + 1,
                            r = a("section").eq(i),
                            o = r.offset().top,
                            s = a(e.header).outerHeight();
                        r.length > 0 && a("html, body").animate({
                            scrollTop: o - s
                        }, 400)
                    })
                }
            }]), e
        }())(".scroll-down");
    t.default = r
}, function(e, t, n) {
    "use strict";
    Object.defineProperty(t, "__esModule", {
        value: !0
    });
    var i = function() {
        function e(e, t) {
            for (var n = 0; n < t.length; n++) {
                var i = t[n];
                i.enumerable = i.enumerable || !1, i.configurable = !0, "value" in i && (i.writable = !0), Object.defineProperty(e, i.key, i)
            }
        }
        return function(t, n, i) {
            return n && e(t.prototype, n), i && e(t, i), t
        }
    }();
    var a = jQuery.noConflict(),
        r = new(function() {
            function e(t) {
                ! function(e, t) {
                    if (!(e instanceof t)) throw new TypeError("Cannot call a class as a function")
                }(this, e), this.map = t
            }
            return i(e, [{
                key: "init",
                value: function() {
                    var e = this;
                    a(this.map).each(function(t, n) {
                        e.newMap(a(n))
                    })
                }
            }, {
                key: "newMap",
                value: function(e) {
                    var t = this,
                        n = e.find(".marker"),
                        i = {
                            zoom: 16,
                            center: new google.maps.LatLng(0, 0),
                            mapTypeId: google.maps.MapTypeId.ROADMAP,
                            disableDefaultUI: !0
                        },
                        r = new google.maps.Map(e[0], i);
                    return r.markers = [], n.each(function(e, n) {
                        t.add_marker(a(n), r)
                    }), this.center_map(r), r
                }
            }, {
                key: "add_marker",
                value: function(e, t) {
                    var n = new google.maps.LatLng(e.attr("data-lat"), e.attr("data-lng")),
                        i = new google.maps.Marker({
                            position: n,
                            map: t
                        });
                    if (t.markers.push(i), e.html()) {
                        var a = new google.maps.InfoWindow({
                            content: e.html()
                        });
                        google.maps.event.addListener(i, "click", function() {
                            a.open(t, i)
                        })
                    }
                }
            }, {
                key: "center_map",
                value: function(e) {
                    var t = new google.maps.LatLngBounds;
                    a.each(e.markers, function(e, n) {
                        var i = new google.maps.LatLng(n.position.lat(), n.position.lng());
                        t.extend(i)
                    }), 1 == e.markers.length ? (e.setCenter(t.getCenter()), e.setZoom(7)) : e.fitBounds(t)
                }
            }]), e
        }())(".acf-map");
    t.default = r
}, function(e, t, n) {
    "use strict";
    Object.defineProperty(t, "__esModule", {
        value: !0
    });
    var i = function() {
        function e(e, t) {
            for (var n = 0; n < t.length; n++) {
                var i = t[n];
                i.enumerable = i.enumerable || !1, i.configurable = !0, "value" in i && (i.writable = !0), Object.defineProperty(e, i.key, i)
            }
        }
        return function(t, n, i) {
            return n && e(t.prototype, n), i && e(t, i), t
        }
    }();
    var a = jQuery.noConflict(),
        r = function() {
            function e(t) {
                ! function(e, t) {
                    if (!(e instanceof t)) throw new TypeError("Cannot call a class as a function")
                }(this, e), this.hiddenItem = t
            }
            return i(e, [{
                key: "init",
                value: function() {
                    a(window).scrollTop() > 0 ? a(this.hiddenItem).addClass("visible") : a(this.hiddenItem).removeClass("visible")
                }
            }]), e
        }();
    t.default = new r(".addtoany-content")
}, function(e, t, n) {
    "use strict";
    Object.defineProperty(t, "__esModule", {
        value: !0
    });
    var i = function() {
        function e(e, t) {
            for (var n = 0; n < t.length; n++) {
                var i = t[n];
                i.enumerable = i.enumerable || !1, i.configurable = !0, "value" in i && (i.writable = !0), Object.defineProperty(e, i.key, i)
            }
        }
        return function(t, n, i) {
            return n && e(t.prototype, n), i && e(t, i), t
        }
    }();
    var a = jQuery.noConflict(),
        r = function() {
            function e(t) {
                var n = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : ".main-header";
                ! function(e, t) {
                    if (!(e instanceof t)) throw new TypeError("Cannot call a class as a function")
                }(this, e), this.section = t, this.header = n
            }
            return i(e, [{
                key: "init",
                value: function() {
                    this.smoothscroll()
                }
            }, {
                key: "smoothscroll",
                value: function() {
                    if (a(this.section).length > 0 && a(this.section).data("filter")) {
                        var e = a(this.section).offset().top - a(this.header).outerHeight();
                        a("html, body").animate({
                            scrollTop: e
                        }, 600)
                    }
                }
            }]), e
        }(),
        o = new r("body.category .blog-posts"),
        s = new r(".cl-posts"),
        l = new r(".archive-posts--sj");
    t.default = o, t.scrollToCLArchive = s, t.scrollToSJArchive = l
}, function(e, t, n) {
    "use strict";
    Object.defineProperty(t, "__esModule", {
        value: !0
    });
    var i = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function(e) {
            return typeof e
        } : function(e) {
            return e && "function" == typeof Symbol && e.constructor === Symbol && e !== Symbol.prototype ? "symbol" : typeof e
        },
        a = function() {
            function e(e, t) {
                for (var n = 0; n < t.length; n++) {
                    var i = t[n];
                    i.enumerable = i.enumerable || !1, i.configurable = !0, "value" in i && (i.writable = !0), Object.defineProperty(e, i.key, i)
                }
            }
            return function(t, n, i) {
                return n && e(t.prototype, n), i && e(t, i), t
            }
        }();
    var r = jQuery.noConflict(),
        o = function() {
            function e(t, n, i) {
                ! function(e, t) {
                    if (!(e instanceof t)) throw new TypeError("Cannot call a class as a function")
                }(this, e), this.loadMore = t, this.section = n, this.funcName = i, this.offset = 1 * r(this.section).data("offset"), this.currentOffset = this.offset
            }
            return a(e, [{
                key: "init",
                value: function() {
                    this.bindEvents(), this.hideLoadMore(), this.runAjaxOnLoad()
                }
            }, {
                key: "runAjaxOnLoad",
                value: function(e) {
                    var t = r(this.section),
                        n = r(t).data("filter"),
                        i = r(t).data("class"),
                        a = r(t).data("taxonomy"),
                        o = r(t).data("post-type"),
                        s = window.location.href.split("?")[1];
                    if (s) {
                        var l = Number(s.split("posts=")[1]);
                        this.loadMoreAjax(this.currentOffset, l, n, i, this.funcName, a, o, "load"), this.currentOffset = this.currentOffset + l
                    }
                }
            }, {
                key: "bindEvents",
                value: function() {
                    var e = this;
                    r(this.loadMore).on("click", function(t) {
                        return e.loadMorePosts(t)
                    })
                }
            }, {
                key: "hideLoadMore",
                value: function() {
                    r(this.section).data("post-num") <= this.offset && r(this.loadMore).hide()
                }
            }, {
                key: "loadMorePosts",
                value: function(e) {
                    var t = r(e.currentTarget).closest("section"),
                        n = r(t).data("filter"),
                        i = r(t).data("class"),
                        a = r(t).data("taxonomy"),
                        o = r(t).data("post-type");
                        if (!this.currentOffset) this.currentOffset = 7;
                    this.loadMoreAjax(this.currentOffset, this.offset, n, i, this.funcName, a, o, e.type), this.currentOffset = this.currentOffset + this.offset
                }
            }, {
                key: "loadMoreAjax",
                value: function(e, t, n, a, o, s, l, u) {
                    var c = r(this.section),
                        f = c.find(".row"),
                        d = WP.ajaxUrl,
                        h = !0,
                        p = r(this.loadMore),
                        v = '<img src="' + WP.templateUrl + '/images/gifs/loading.gif" alt="loading" class="loader"/>';
                    if (h) {
                        h = !1, r(v).insertAfter(f);
                        var m = new FormData;
                        m.append("action", o), m.append("num", t), m.append("curnum", e), m.append("classes", a), m.append("taxonomy", s), m.append("postType", l), "" !== n && m.append("taxonomy_name", n);
                        r.ajax({
                            type: "POST",
                            url: d,
                            contentType: !1,
                            processData: !1,
                            data: m,
                            dataType: "json",
                            success: function(e) {
                                if (h = !0, r(".loader").detach(), e && "object" === (void 0 === e ? "undefined" : i(e))) {
                                    if (e.html.length > 0) {
                                        var t = r(e.html);
                                        f.append(t), c.find("article").length < c.data("post-num") ? p.show() : p.hide()
                                    }
                                    "click" === u && e.postsNo && window.history.pushState("posts", "", window.location.href.split("?")[0] + "?posts=" + e.postsNo)
                                }
                            }
                        })
                    }
                }
            }]), e
        }(),
        s = new o(".blog-posts .load-more", ".blog-posts", "load_more"),
        l = new o(".cl-posts .load-more", ".cl-posts", "load_more_cl");
    t.default = s, t.loadPostsCl = l
}, function(e, t, n) {
    "use strict";
    Object.defineProperty(t, "__esModule", {
        value: !0
    });
    var i = function() {
        function e(e, t) {
            for (var n = 0; n < t.length; n++) {
                var i = t[n];
                i.enumerable = i.enumerable || !1, i.configurable = !0, "value" in i && (i.writable = !0), Object.defineProperty(e, i.key, i)
            }
        }
        return function(t, n, i) {
            return n && e(t.prototype, n), i && e(t, i), t
        }
    }();
    var a = jQuery.noConflict(),
        r = function() {
            function e(t, n, i) {
                ! function(e, t) {
                    if (!(e instanceof t)) throw new TypeError("Cannot call a class as a function")
                }(this, e), this.listWrapper = t, this.listTitle = n, this.list = i
            }
            return i(e, [{
                key: "init",
                value: function() {
                    "ontouchstart" in window || navigator.msMaxTouchPoints ? a(this.list).addClass("non-hoverable") : a(this.list).addClass("hoverable"), this.bindEvents()
                }
            }, {
                key: "bindEvents",
                value: function() {
                    a(this.listTitle).on("click", this.toggleList)
                }
            }, {
                key: "toggleList",
                value: function() {
                    var e = a(this).parent();
                    e.hasClass("open") ? e.removeClass("open") : e.addClass("open")
                }
            }]), e
        }();
    t.default = new r(".blog-intro__list-wrapper", ".list__title", ".list")
}, function(e, t, n) {
    "use strict";
    Object.defineProperty(t, "__esModule", {
        value: !0
    });
    var i = function() {
        function e(e, t) {
            for (var n = 0; n < t.length; n++) {
                var i = t[n];
                i.enumerable = i.enumerable || !1, i.configurable = !0, "value" in i && (i.writable = !0), Object.defineProperty(e, i.key, i)
            }
        }
        return function(t, n, i) {
            return n && e(t.prototype, n), i && e(t, i), t
        }
    }();
    var a = jQuery.noConflict(),
        r = function() {
            function e(t, n, i, a) {
                ! function(e, t) {
                    if (!(e instanceof t)) throw new TypeError("Cannot call a class as a function")
                }(this, e), this.postsWrapper = t, this.loadMoreButton = n, this.postType = i, this.taxonomy = a
            }
            return i(e, [{
                key: "init",
                value: function() {
                    this.initMasonry(), this.bindEvents()
                }
            }, {
                key: "initMasonry",
                value: function() {
                    a(this.postsWrapper).isotope({
                        itemSelector: ".stories-post",
                        percentPosition: !0,
                        masonry: {
                            columnWidth: ".stories-post--default"
                        }
                    })
                }
            }, {
                key: "bindEvents",
                value: function() {
                    var e = this;
                    a(this.loadMoreButton).on("click", function(t) {
                        return e.loadMorePosts(t)
                    })
                }
            }, {
                key: "loadMorePosts",
                value: function() {
                    var e = this,
                        t = !0,
                        n = '<img src="' + WP.templateUrl + '/images/gifs/loading.gif" alt="loading" class="stories-loader"/>',
                        i = {
                            action: "load_more_stories",
                            postType: this.postType,
                            taxonomy: this.taxonomy,
                            offset: parseInt(a(this.postsWrapper).attr("data-offset"), 10)
                        };
                    "string" == typeof a(this.postsWrapper).attr("data-taxonomy-value") && (i.taxonomyValue = a(this.postsWrapper).attr("data-taxonomy-value")), t && a.ajax({
                        type: "POST",
                        url: WP.ajaxUrl,
                        data: i,
                        beforeSend: function() {
                            t = !1, a(n).insertBefore(a(e.loadMoreButton))
                        },
                        success: function(n) {
                            t = !0, a(".stories-loader").detach();
                            var i = a.parseHTML(n);
                            console.log(i);
                            var r = i.length / 2;
                            a(e.postsWrapper).attr("data-offset", parseInt(a(e.postsWrapper).attr("data-offset"), 10) + r), a(e.postsWrapper).append(i).isotope("appended", i), parseInt(a(e.postsWrapper).attr("data-offset"), 10) >= parseInt(a(e.postsWrapper).attr("data-total-posts"), 10) && a(e.loadMoreButton).detach()
                        }
                    })
                }
            }]), e
        }();
    t.default = new r(".stories-posts", ".stories-load-more", "stories", "stories-category")
}, function(e, t, n) {
    "use strict";
    Object.defineProperty(t, "__esModule", {
        value: !0
    });
    var i = function() {
        function e(e, t) {
            for (var n = 0; n < t.length; n++) {
                var i = t[n];
                i.enumerable = i.enumerable || !1, i.configurable = !0, "value" in i && (i.writable = !0), Object.defineProperty(e, i.key, i)
            }
        }
        return function(t, n, i) {
            return n && e(t.prototype, n), i && e(t, i), t
        }
    }();
    var a = jQuery.noConflict(),
        r = function() {
            function e(t) {
                ! function(e, t) {
                    if (!(e instanceof t)) throw new TypeError("Cannot call a class as a function")
                }(this, e), this.quoteSlider = t
            }
            return i(e, [{
                key: "init",
                value: function() {
                    this.initSlider()
                }
            }, {
                key: "initSlider",
                value: function() {
                    a(this.quoteSlider).slick({
                        slidesToShow: this.slideNo,
                        dots: !1,
                        infinite: !0,
                        speed: 0,
                        cssEase: "linear",
                        swipe: !1
                    }), a(this.quoteSlider).on("beforeChange", function(e, t, n, i) {
                        a(".slick-slide").eq(n).addClass("opacity-off")
                    }), a(this.quoteSlider).on("afterChange", function(e, t, n, i) {
                        a(".slick-slide").removeClass("opacity-off")
                    })
                }
            }]), e
        }();
    t.default = new r(".quote-slider")
}, function(e, t, n) {
    "use strict";
    Object.defineProperty(t, "__esModule", {
        value: !0
    });
    var i = function() {
        function e(e, t) {
            for (var n = 0; n < t.length; n++) {
                var i = t[n];
                i.enumerable = i.enumerable || !1, i.configurable = !0, "value" in i && (i.writable = !0), Object.defineProperty(e, i.key, i)
            }
        }
        return function(t, n, i) {
            return n && e(t.prototype, n), i && e(t, i), t
        }
    }();
    var a = jQuery.noConflict(),
        r = function() {
            function e(t, n, i) {
                ! function(e, t) {
                    if (!(e instanceof t)) throw new TypeError("Cannot call a class as a function")
                }(this, e), this.sliderImageWrap = t, this.infoWrapperContent = n, this.nextSlide = i
            }
            return i(e, [{
                key: "init",
                value: function() {
                    var e = this;
                    this.initSlider(), a(this.nextSlide).on("click", function() {
                        a(e.sliderImageWrap).slick("slickNext"), a(e.infoWrapperContent).slick("slickNext")
                    })
                }
            }, {
                key: "initSlider",
                value: function() {
                    a(this.sliderImageWrap).slick({
                        dots: !1,
                        arrows: !1,
                        speed: 300,
                        slidesToShow: 1,
                        draggable: !1,
                        asNavFor: ".info-wrapper__content"
                    }), a(this.infoWrapperContent).slick({
                        dots: !1,
                        arrows: !1,
                        speed: 300,
                        slidesToShow: 1,
                        draggable: !1,
                        asNavFor: ".slider-image-wrap"
                    })
                }
            }]), e
        }();
    t.default = new r(".slider-image-wrap", ".info-wrapper__content", ".next-slide")
}, function(e, t, n) {
    "use strict";
    Object.defineProperty(t, "__esModule", {
        value: !0
    });
    var i = function() {
        function e(e, t) {
            for (var n = 0; n < t.length; n++) {
                var i = t[n];
                i.enumerable = i.enumerable || !1, i.configurable = !0, "value" in i && (i.writable = !0), Object.defineProperty(e, i.key, i)
            }
        }
        return function(t, n, i) {
            return n && e(t.prototype, n), i && e(t, i), t
        }
    }();
    var a = jQuery.noConflict(),
        r = function() {
            function e() {
                ! function(e, t) {
                    if (!(e instanceof t)) throw new TypeError("Cannot call a class as a function")
                }(this, e), this.tables = a("table.tablepress")
            }
            return i(e, [{
                key: "init",
                value: function() {
                    this.wrapTables(), this.responsiveTableWidth(), this.toggleTablesShadow()
                }
            }, {
                key: "wrapTables",
                value: function() {
                    this.tables.wrap('<div class="table-wrapper"></div>')
                }
            }, {
                key: "toggleTablesShadow",
                value: function() {
                    this.tables.each(function() {
                        var e = a(this),
                            t = e.find("tbody");
                        t[0].offsetWidth < t[0].scrollWidth ? e.addClass("has-scroll") : e.removeClass("has-scroll")
                    })
                }
            }, {
                key: "responsiveTableWidth",
                value: function() {
                    this.tables.each(function() {
                        var e = a(window).width() - a(this).offset().left;
                        a(".tablet-checker").is(":visible") ? a(this).width(e) : a(this).width("")
                    })
                }
            }]), e
        }();
    t.default = new r
}, function(e, t, n) {
    "use strict";
    Object.defineProperty(t, "__esModule", {
        value: !0
    });
    var i = function(e) {
        return e && e.__esModule ? e : {
            default: e
        }
    }(n(1));
    var a = jQuery.noConflict();

    function r(e) {
        e.preventDefault();
        var t = a(".main-header"),
            n = a(a(this).attr("href")),
            r = t.outerHeight();
        "#next" === a(this).attr("href") && a(this).parents("section").next().length > 0 ? (a("html, body").animate({
            scrollTop: a(this).parents("section").next().offset().top - r
        }, 600), i.default.hideWrapper(".btn-hamburger", a("body"))) : n.length && (a("html, body").animate({
            scrollTop: n.offset().top - r
        }, 600), i.default.hideWrapper(".btn-hamburger", a("body")))
    }
    t.default = function() {
        a('a[href^="#"]:not([href="#"])').on("click", r)
    }
}, function(e, t, n) {
    "use strict";
    Object.defineProperty(t, "__esModule", {
        value: !0
    });
    var i = function() {
        function e(e, t) {
            for (var n = 0; n < t.length; n++) {
                var i = t[n];
                i.enumerable = i.enumerable || !1, i.configurable = !0, "value" in i && (i.writable = !0), Object.defineProperty(e, i.key, i)
            }
        }
        return function(t, n, i) {
            return n && e(t.prototype, n), i && e(t, i), t
        }
    }();
    var a = jQuery.noConflict(),
        r = function() {
            function e(t) {
                var n = arguments.length > 1 && void 0 !== arguments[1] && arguments[1],
                    i = arguments.length > 2 && void 0 !== arguments[2] ? arguments[2] : 1,
                    a = arguments.length > 3 && void 0 !== arguments[3] ? arguments[3] : 1,
                    r = arguments.length > 4 && void 0 !== arguments[4] ? arguments[4] : 600,
                    o = arguments.length > 5 && void 0 !== arguments[5] && arguments[5];
                ! function(e, t) {
                    if (!(e instanceof t)) throw new TypeError("Cannot call a class as a function")
                }(this, e), this.selector = t, this.num = i, this.tabNum = a, this.variableWidth = o, this.slideSpeed = r, this.fade = n
            }
            return i(e, [{
                key: "init",
                value: function() {
                    var e = this.num,
                        t = this.fade,
                        n = this.tabNum,
                        i = this.slideSpeed,
                        r = this.variableWidth;
                    a(this.selector).each(function() {
                        a(this).slick({
                            dots: !1,
                            arrows: !0,
                            infinite: !0,
                            slidesToShow: e,
                            slidesToScroll: 1,
                            variableWidth: r,
                            fade: t,
                            pauseOnHover: !1,
                            speed: i,
                            responsive: [{
                                breakpoint: 991,
                                settings: {
                                    slidesToShow: n,
                                    slidesToScroll: 1
                                }
                            }, {
                                breakpoint: 767,
                                settings: {
                                    slidesToShow: 1,
                                    slidesToScroll: 1,
                                    swipe: !0
                                }
                            }]
                        })
                    })
                }
            }]), e
        }(),
        o = new r(".bc-gallery__slider"),
        s = new r(".lightbox__slider");
    t.SimpleSlider = o, t.LightboxSlider = s
}, function(e, t, n) {
    "use strict";
    Object.defineProperty(t, "__esModule", {
        value: !0
    });
    t.default = function() {
        navigator.platform.indexOf("Mac") > -1 && document.querySelector("body").classList.add("mac-os-system")
    }
}, function(e, t, n) {
    "use strict";
    Object.defineProperty(t, "__esModule", {
        value: !0
    });
    t.default = function() {
        for (var e = Array.prototype.slice.call(document.querySelectorAll(".page-content iframe")), t = 0; t < e.length; t += 1)
            if (!0 !== e[t].parentNode.classList.contains("iframe-wrapper")) {
                var n = document.createElement("div");
                n.classList.add("iframe-wrapper"), n.innerHTML = e[t].outerHTML, e[t].parentNode.insertBefore(n, e[t]), e[t].parentNode.removeChild(e[t])
            }
    }
}, function(e, t, n) {
    "use strict";
    Object.defineProperty(t, "__esModule", {
        value: !0
    }), t.lightboxVideo = void 0;
    var i = function() {
            function e(e, t) {
                for (var n = 0; n < t.length; n++) {
                    var i = t[n];
                    i.enumerable = i.enumerable || !1, i.configurable = !0, "value" in i && (i.writable = !0), Object.defineProperty(e, i.key, i)
                }
            }
            return function(t, n, i) {
                return n && e(t.prototype, n), i && e(t, i), t
            }
        }(),
        a = n(0);
    var r = jQuery.noConflict(),
        o = function() {
            function e(t, n, i, a) {
                ! function(e, t) {
                    if (!(e instanceof t)) throw new TypeError("Cannot call a class as a function")
                }(this, e), this.lightboxes = t, this.lightboxOpen = n, this.lightboxClose = i, this.lightboxWrapper = a
            }
            return i(e, [{
                key: "init",
                value: function() {
                    this.bindEvents()
                }
            }, {
                key: "bindEvents",
                value: function() {
                    r(this.lightboxOpen).on("click", r.proxy(this.openLightbox, this)), r(this.lightboxClose).on("click", r.proxy(this.closeLightbox, this))
                }
            }, {
                key: "openLightbox",
                value: function(e) {
                    e.preventDefault();
                    var t = parseInt(r(e.currentTarget).attr("href").slice(1), 10),
                        n = r(e.currentTarget).closest("section");
                    n.find(this.lightboxWrapper).addClass("active"), n.find(this.lightboxes).slick("slickGoTo", t, !0), (0, a.stopScroll)()
                }
            }, {
                key: "refreshSlider",
                value: function() {
                    r(this.lightboxes).slick("refresh")
                }
            }, {
                key: "closeLightbox",
                value: function(e) {
                    r(e.currentTarget).closest(this.lightboxWrapper).removeClass("active"), (0, a.enableScroll)()
                }
            }]), e
        }(),
        s = new o(".lightbox__slider", ".lightbox-gallery__single-thumb", ".lightbox__close", ".lightbox"),
        l = new o(".lightbox__slider", ".content-link--video", ".lightbox__close", ".lightbox");
    t.default = s, t.lightboxVideo = l
}, function(e, t, n) {
    "use strict";
    Object.defineProperty(t, "__esModule", {
        value: !0
    });
    var i = function() {
        function e(e, t) {
            for (var n = 0; n < t.length; n++) {
                var i = t[n];
                i.enumerable = i.enumerable || !1, i.configurable = !0, "value" in i && (i.writable = !0), Object.defineProperty(e, i.key, i)
            }
        }
        return function(t, n, i) {
            return n && e(t.prototype, n), i && e(t, i), t
        }
    }();
    var a = jQuery.noConflict(),
        r = function() {
            function e(t, n, i) {
                ! function(e, t) {
                    if (!(e instanceof t)) throw new TypeError("Cannot call a class as a function")
                }(this, e), this.tabsWrapper = a(t), this.tabsLink = a(n), this.tabsControls = this.tabsWrapper.find(i)
            }
            return i(e, [{
                key: "init",
                value: function() {
                    this.tabsLink.on("click", this.toggleTab)
                }
            }, {
                key: "toggleTab",
                value: function(e) {
                    e.preventDefault();
                    var t = a(this),
                        n = this.hash,
                        i = t.closest(".block-tabs"),
                        r = i.find('.tabs__tab-content[data-tab-id="' + n + '"]');
                    i.find(".tabs__tab-content").removeClass("active"), i.find(".tabs__link").removeClass("active"), t.parent().addClass("active"), r.addClass("active")
                }
            }]), e
        }();
    t.default = new r(".tabs__link-list-wrapper", ".tabs__link a", ".tab-nav-button")
}, function(e, t, n) {
    "use strict";
    Object.defineProperty(t, "__esModule", {
        value: !0
    });
    var i = function() {
        function e(e, t) {
            for (var n = 0; n < t.length; n++) {
                var i = t[n];
                i.enumerable = i.enumerable || !1, i.configurable = !0, "value" in i && (i.writable = !0), Object.defineProperty(e, i.key, i)
            }
        }
        return function(t, n, i) {
            return n && e(t.prototype, n), i && e(t, i), t
        }
    }();
    var a = jQuery.noConflict(),
        r = function() {
            function e(t) {
                ! function(e, t) {
                    if (!(e instanceof t)) throw new TypeError("Cannot call a class as a function")
                }(this, e), this.trigger = a(t)
            }
            return i(e, [{
                key: "init",
                value: function() {
                    this.bindEvents()
                }
            }, {
                key: "bindEvents",
                value: function() {
                    this.trigger.on("click", this.toggleAccordion)
                }
            }, {
                key: "toggleAccordion",
                value: function() {
                    a(this).parent().toggleClass("active"), a(this).next().stop().slideToggle(250)
                }
            }]), e
        }();
    t.default = new r(".single-accordion__title")
}, function(e, t, n) {
    "use strict";
    Object.defineProperty(t, "__esModule", {
        value: !0
    });
    var i = function() {
        function e(e, t) {
            for (var n = 0; n < t.length; n++) {
                var i = t[n];
                i.enumerable = i.enumerable || !1, i.configurable = !0, "value" in i && (i.writable = !0), Object.defineProperty(e, i.key, i)
            }
        }
        return function(t, n, i) {
            return n && e(t.prototype, n), i && e(t, i), t
        }
    }();
    var a = jQuery.noConflict(),
        r = function() {
            function e() {
                var t = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : ".main-header:not(.header-black)";
                ! function(e, t) {
                    if (!(e instanceof t)) throw new TypeError("Cannot call a class as a function")
                }(this, e), this.mainHeader = t
            }
            return i(e, [{
                key: "init",
                value: function() {
                    a(window).scrollTop() > 0 ? a(this.mainHeader).addClass("main-header--sticky") : a(this.mainHeader).hasClass("main-header--sticky") && a(this.mainHeader).removeClass("main-header--sticky")
                }
            }]), e
        }();
    t.default = r
}, function(e, t, n) {
    "use strict";
    Object.defineProperty(t, "__esModule", {
        value: !0
    });
    var i = function() {
        function e(e, t) {
            for (var n = 0; n < t.length; n++) {
                var i = t[n];
                i.enumerable = i.enumerable || !1, i.configurable = !0, "value" in i && (i.writable = !0), Object.defineProperty(e, i.key, i)
            }
        }
        return function(t, n, i) {
            return n && e(t.prototype, n), i && e(t, i), t
        }
    }();
    var a = jQuery.noConflict();

    function r() {
        return "ontouchstart" in window || navigator.msMaxTouchPoints
    }
    var o = function() {
        function e() {
            ! function(e, t) {
                if (!(e instanceof t)) throw new TypeError("Cannot call a class as a function")
            }(this, e), this.header = a(".main-header"), this.menu = this.header.find(".main-header__nav"), this.headerLinks = this.menu.find("> .menu > li > a"), this.menuItems = this.menu.find("> .menu > .menu-item"), this.subMenuItem = a(".sub-menu-wrapper").find(".sub-menu > li"), this.subMenu = a(".sub-menu-wrapper").find(".sub-menu")
        }
        return i(e, [{
            key: "toggleMegaMenu",
            value: function(t) {
                var n = this;
                if (a(t.target).parent().hasClass("menu-item-has-children")) {
                    t.preventDefault();
                    var i = a(".main-header__nav").find("> .menu > li > a"),
                        r = a(this).next(".sub-menu-wrapper"),
                        o = r.find(".sub-menu > li").find("> a"),
                        s = a(".main-header"),
                        l = r.find(".mega-menu-wrapper").eq(0);
                    if (r.find(".mega-menu-wrapper-background").css("width", l.outerWidth()), r.hasClass("active")) return;
                    e.hideInnerMenu(), e.hideMegaMenu(t), i.removeClass("open"), a(this).addClass("open"), a(s).hasClass("main-header--transparent") && a(s).removeClass("main-header--transparent"), setTimeout(function() {
                        o.each(function(t, n) {
                            e.animateItemsIn(a(n))
                        }), 0 !== a(n).next(".sub-menu-wrapper").length && (r.addClass("active"), e.animateMenuBackground(r.outerHeight(), t))
                    }, 200)
                }
            }
        }, {
            key: "bindEvents",
            value: function() {
                r() ? (this.headerLinks.on("click", this.toggleMegaMenu), a(document).on("click touchstart", e.hideMegaMenuOnTouch), this.subMenuItem.on("click", e.toggleInnerMenu)) : (this.menuItems.on("mouseenter", e.showMegaMenu), this.menuItems.on("mouseleave", e.hideMegaMenu), this.subMenuItem.on("mouseenter", e.showInnerMenu), this.subMenuItem.on("mouseleave", e.hideInnerMenu), this.header.on("mouseenter", e.removeTransparency), this.header.on("mouseleave", e.addTransparency))
            }
        }, {
            key: "showArrows",
            value: function() {
                r() && a(".sub-menu-wrapper").addClass("touch-device"), this.subMenuItem.each(function(e, t) {
                    a(t).find(".menu").length > 0 && a(t).addClass("arrow-visible")
                })
            }
        }, {
            key: "init",
            value: function() {
                this.showArrows(), this.bindEvents()
            }
        }], [{
            key: "showMegaMenu",
            value: function(t) {
                var n = a(this).find(".sub-menu-wrapper"),
                    i = n.find(".sub-menu > li").find("> a"),
                    r = a(this).find(".mega-menu-wrapper").eq(0),
                    o = n.find(".mega-menu-wrapper-background"),
                    s = 0 !== a(".main-header .mega-menu-background").height() ? 0 : 250;
                a(this).find("> a").addClass("open"), o.css("width", r.outerWidth()), a(".main-header").hasClass("main-header--transparent") && e.removeTransparency(), e.subMenuTimeout = setTimeout(function() {
                    e.animateItemsIn(i), n.addClass("active"), e.animateMenuBackground(n.outerHeight(), t)
                }, s)
            }
        }, {
            key: "hideMegaMenu",
            value: function(t) {
                clearTimeout(e.subMenuTimeout);
                var n = a(".sub-menu-wrapper.active"),
                    i = n.find("*");
                a(".main-header__nav").find("> .menu > li > a").removeClass("open"), n.removeClass("active"), i.removeClass("active"), "click" === t.type ? e.animateMenuBackground(0, t) : a(t.relatedTarget).hasClass("main-header") || a(t.relatedTarget).closest(".main-header").length > 0 ? e.animateMenuBackground(0, t) : e.animateMenuBackgroundHeader(0, t)
            }
        }, {
            key: "animateItemsIn",
            value: function(e) {
                var t = 0;
                e.each(function(e, n) {
                    t += .05, a(n).css("transition-delay", t + "s"), a(n).addClass("active")
                })
            }
        }, {
            key: "animateMenuBackground",
            value: function(e, t) {
                var n = a(".main-header").find(".mega-menu-background");
                null === e && (e = 0), n.stop().animate({
                    height: e + "px"
                }, 300)
            }
        }, {
            key: "animateMenuBackgroundHeader",
            value: function(e, t) {
                var n = a(".main-header"),
                    i = n.find(".mega-menu-background"),
                    r = a(".main-header__nav").find("> .menu > li > a");
                null === e && (e = 0), i.stop().animate({
                    height: e + "px"
                }, 300, function() {
                    r.hasClass("open") || n.hasClass("main-header--white") || n.addClass("main-header--transparent")
                })
            }
        }, {
            key: "showInnerMenu",
            value: function() {
                var t = a(this).find(".mega-menu-wrapper"),
                    n = a(this).find("> a"),
                    i = t.find("a"),
                    r = a(this).closest(".sub-menu"),
                    o = a(this).closest(".sub-menu-wrapper"),
                    s = 0 !== a(".main-header .mega-menu-background").height() ? 0 : 250;
                n.addClass("active-link"), e.innerMenuTimeout = setTimeout(function() {
                    e.animateItemsIn(i), t.addClass("active"), r.css("height", t.outerHeight()), o.outerHeight() > t.outerHeight() ? e.animateMenuBackground(o.outerHeight()) : e.animateMenuBackground(t.outerHeight())
                }, s)
            }
        }, {
            key: "hideInnerMenu",
            value: function() {
                clearTimeout(e.innerMenuTimeout);
                var t = a(".sub-menu-wrapper").find("a.active-link"),
                    n = a(".mega-menu-wrapper.active"),
                    i = n.find("a");
                t.removeClass("active-link"), n.removeClass("active"), i.removeClass("active")
            }
        }, {
            key: "addTransparency",
            value: function(t) {
                e.animateMenuBackgroundHeader(0, t)
            }
        }, {
            key: "removeTransparency",
            value: function(e) {
                var t = a(".main-header");
                t.hasClass("main-header--transparent") && t.removeClass("main-header--transparent")
            }
        }, {
            key: "hideMegaMenuOnTouch",
            value: function(t) {
                var n = a(".main-header__nav");
                n.is(t.target) || 0 !== n.has(t.target).length || (e.hideMegaMenu(t), e.addTransparency())
            }
        }, {
            key: "toggleInnerMenu",
            value: function(t) {
                //t.preventDefault();
                var n = a(this).find("> a"),
                    i = a(this).closest(".sub-menu"),
                    r = a(this).find(".mega-menu-wrapper"),
                    o = r.find("a"),
                    s = 0 !== a(".main-header .mega-menu-background").height() ? 0 : 250;
                n.hasClass("active-link") || (clearTimeout(e.innerMenuTimeout), e.hideInnerMenu(), n.addClass("active-link"), e.innerMenuTimeout = setTimeout(function() {
                    e.animateItemsIn(o), r.addClass("active"), i.css("height", r.outerHeight()), e.animateMenuBackground(r.outerHeight())
                }, s))
            }
        }]), e
    }();
    t.default = new o
}, function(e, t, n) {
    "use strict";
    Object.defineProperty(t, "__esModule", {
        value: !0
    });
    var i = B(n(27)),
        a = B(n(1)),
        r = B(n(26)),
        o = B(n(25)),
        s = B(n(24)),
        l = n(23),
        u = B(l),
        c = B(n(0)),
        f = B(n(22)),
        d = B(n(21)),
        h = n(20),
        p = B(n(19)),
        v = B(n(18)),
        m = B(n(17)),
        b = B(n(16)),
        g = B(n(15)),
        y = B(n(14)),
        w = n(13),
        k = B(w),
        C = n(12),
        _ = B(C),
        M = B(n(11)),
        T = B(n(10)),
        j = B(n(9)),
        x = B(n(8)),
        P = B(n(7)),
        O = B(n(6)),
        S = n(5),
        W = B(S),
        H = B(n(4)),
        I = n(3),
        E = B(I),
        L = B(n(2));

    function B(e) {
        return e && e.__esModule ? e : {
            default: e
        }
    }
    var Q = new a.default,
        N = new r.default,
        A = {
            init: function() {
                document.querySelector("html").classList.remove("no-js"), i.default.init(), Q.init(), o.default.init(), s.default.init(), (0, c.default)(), (0, f.default)(), (0, d.default)(), h.SimpleSlider.init(), h.LightboxSlider.init(), u.default.init(), l.lightboxVideo.init(), (0, p.default)(), v.default.init(), m.default.init(), b.default.init(), g.default.init(), y.default.init(), k.default.init(), w.loadPostsCl.init(), T.default.init(), j.default.init(), x.default.init(), P.default.init(), O.default.init(), H.default.init(), L.default.init()
            },
            loaded: function() {
                document.querySelector("body").classList.add("page-has-loaded"), Q.setProperMenuHeight(), _.default.init(), C.scrollToCLArchive.init(), C.scrollToSJArchive.init(), W.default.init(), S.setCaptPosGallery.init(), E.default.init(), I.setRegionIntroHeight.init()
            },
            resized: function() {
                Q.resized(), v.default.toggleTablesShadow(), v.default.responsiveTableWidth(), u.default.refreshSlider(), Q.setProperMenuHeight(), W.default.init(), S.setCaptPosGallery.init(), x.default.setScrollBtnPos(), H.default.setNavItemsHeight(), E.default.init(), I.setRegionIntroHeight.init()
            },
            scrolled: function() {
                N.init(), M.default.init(), x.default.setScrollBtnPos()
            },
            mouseUp: function(e) {
                Q.hideOutsideClick(e)
            }
        };
    t.default = A
}, function(e, t, n) {
    "use strict";
    var i = function(e) {
        return e && e.__esModule ? e : {
            default: e
        }
    }(n(28));
    jQuery.noConflict();
    i.default.init(), window.onload = function() {
        i.default.loaded()
    }, window.onresize = function() {
        i.default.resized()
    }, window.onscroll = function() {
        i.default.scrolled()
    }, jQuery(document).mouseup(function(e) {
        i.default.mouseUp(e)
    })
}]);
//# sourceMappingURL=script.js.map