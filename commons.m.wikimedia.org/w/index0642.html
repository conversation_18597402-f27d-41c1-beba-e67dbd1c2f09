<!DOCTYPE html>
<html class="client-nojs skin-theme-clientpref-day mf-expand-sections-clientpref-0 mf-font-size-clientpref-small mw-mf-amc-clientpref-0" lang="en" dir="ltr">

<!-- Mirrored from commons.m.wikimedia.org/w/index.php?title=Special:MobileOptions&returnto=File%3AMadafu-chopping.jpg by HTTrack Website Copier/3.x [XR&CO'2014], Sun, 03 Aug 2025 15:20:55 GMT -->
<!-- Added by HTTrack --><meta http-equiv="content-type" content="text/html;charset=UTF-8" /><!-- /Added by HTTrack -->
<head>
<meta charset="UTF-8">
<title>Settings - Wikimedia Commons</title>
<script>(function(){var className="client-js skin-theme-clientpref-day mf-expand-sections-clientpref-0 mf-font-size-clientpref-small mw-mf-amc-clientpref-0";var cookie=document.cookie.match(/(?:^|; )commonswikimwclientpreferences=([^;]+)/);if(cookie){cookie[1].split('%2C').forEach(function(pref){className=className.replace(new RegExp('(^| )'+pref.replace(/-clientpref-\w+$|[^\w-]+/g,'')+'-clientpref-\\w+( |$)'),'$1'+pref+'$2');});}document.documentElement.className=className;}());RLCONF={"wgBreakFrames":true,"wgSeparatorTransformTable":["",""],"wgDigitTransformTable":["",""],"wgDefaultDateFormat":"dmy","wgMonthNames":["","January","February","March","April","May","June","July","August","September","October","November","December"],"wgRequestId":"4cf40e42-a712-4477-8fe8-3b5111a97bc6","wgCanonicalNamespace":"Special","wgCanonicalSpecialPageName":"MobileOptions","wgNamespaceNumber":-1,"wgPageName":"Special:MobileOptions","wgTitle":"MobileOptions","wgCurRevisionId":0,"wgRevisionId":0,"wgArticleId":0,"wgIsArticle":false,"wgIsRedirect":false,"wgAction":"view","wgUserName":null,"wgUserGroups":["*"],"wgPageViewLanguage":"en","wgPageContentLanguage":"en","wgPageContentModel":"wikitext","wgRelevantPageName":"Special:MobileOptions","wgRelevantArticleId":0,"wgIsProbablyEditable":false,"wgRelevantPageIsProbablyEditable":false,"wgNoticeProject":"commons","wgCiteReferencePreviewsActive":false,"wgMediaViewerOnClick":true,"wgMediaViewerEnabledByDefault":false,"wgVisualEditor":{"pageLanguageCode":"en","pageLanguageDir":"ltr","pageVariantFallbacks":"en"},"wgMFMode":"stable","wgMFAmc":false,"wgMFAmcOutreachActive":false,"wgMFAmcOutreachUserEligible":false,"wgMFLazyLoadImages":true,"wgMFEditNoticesFeatureConflict":false,"wgMFDisplayWikibaseDescriptions":{"search":true,"watchlist":true,"tagline":true,"nearby":true},"wgMFIsSupportedEditRequest":false,"wgMFScriptPath":"","wgWMESchemaEditAttemptStepOversample":false,"wgWMEPageLength":0,"wgMetricsPlatformUserExperiments":{"active_experiments":[],"overrides":[],"enrolled":[],"assigned":[],"subject_ids":[],"sampling_units":[]},"wgMFEnableFontChanger":true,"upwizPropertyTitles":{"P180":"Main subjects visible in this work"},"upwizPropertyPlaceholders":{"P180":"e.g., Angolan giraffe"},"upwizPropertyCopyLabels":{"P180":"Main subjects"},"wgEditSubmitButtonLabelPublish":true,"wbmiDefaultProperties":["P180"],"wbmiPropertyTitles":{"P180":"Items portrayed in this file"},"wbmiPropertyTypes":{"P180":"wikibase-item"},"wbmiRepoApiUrl":"/w/api.php","wbmiHelpUrls":{"P180":"https://commons.wikimedia.org/wiki/Special:MyLanguage/Commons:Depicts"},"wbmiExternalEntitySearchBaseUri":"https://www.wikidata.org/w/api.php","wbmiSupportedDataTypes":["wikibase-item","string","quantity","time","monolingualtext","external-id","globe-coordinate","url"],"wgCheckUserClientHintsHeadersJsApi":["brands","architecture","bitness","fullVersionList","mobile","model","platform","platformVersion"],"wgMinervaPermissions":{"watchable":false,"watch":false},"wgMinervaFeatures":{"beta":false,"donateBanner":true,"donate":false,"mobileOptionsLink":true,"categories":false,"pageIssues":true,"talkAtTop":false,"historyInPageActions":false,"overflowSubmenu":false,"tabsOnSpecials":true,"personalMenu":false,"mainMenuExpanded":false,"nightMode":true},"wgMinervaDownloadNamespaces":[0]};
RLSTATE={"ext.gadget.Long-Image-Names-in-Categories":"ready","ext.globalCssJs.user.styles":"ready","site.styles":"ready","user.styles":"ready","ext.globalCssJs.user":"ready","user":"ready","user.options":"loading","mobile.special.styles":"ready","mobile.special.codex.styles":"ready","mobile.special.mobileoptions.styles":"ready","oojs-ui-core.styles":"ready","oojs-ui.styles.indicators":"ready","mediawiki.widgets.styles":"ready","oojs-ui-core.icons":"ready","skins.minerva.styles":"ready","skins.minerva.content.styles.images":"ready","mediawiki.hlist":"ready","skins.minerva.codex.styles":"ready","skins.minerva.icons":"ready","skins.minerva.amc.styles":"ready","ext.wikimediamessages.styles":"ready","mobile.init.styles":"ready"};RLPAGEMODULES=["ext.xLab","mobile.special.mobileoptions.scripts","site","mediawiki.page.ready","skins.minerva.scripts","ext.centralNotice.geoIP","ext.gadget.Slideshow","ext.gadget.ZoomViewer","ext.gadget.CollapsibleTemplates","ext.gadget.Stockphoto","ext.gadget.WatchlistNotice","ext.gadget.AjaxQuickDelete","ext.gadget.WikiMiniAtlas","ext.gadget.LanguageSelect","ext.gadget.PictureOfTheYearEnhancements","ext.urlShortener.toolbar","ext.centralauth.centralautologin","mobile.init","ext.echo.centralauth","ext.eventLogging","ext.wikimediaEvents","ext.wikimediaEvents.wikibase","ext.navigationTiming","ext.checkUser.clientHints"];</script>
<script>(RLQ=window.RLQ||[]).push(function(){mw.loader.impl(function(){return["user.options@12s5i",function($,jQuery,require,module){mw.user.tokens.set({"patrolToken":"+\\","watchToken":"+\\","csrfToken":"+\\"});
}];});});</script>
<link rel="stylesheet" href="https://commons.m.wikimedia.org/w/load.php?lang=en&amp;modules=ext.wikimediamessages.styles%7Cmediawiki.hlist%7Cmediawiki.widgets.styles%7Cmobile.init.styles%7Cmobile.special.codex.styles%7Cmobile.special.mobileoptions.styles%7Cmobile.special.styles%7Coojs-ui-core.icons%2Cstyles%7Coojs-ui.styles.indicators%7Cskins.minerva.amc.styles%7Cskins.minerva.codex.styles%7Cskins.minerva.content.styles.images%7Cskins.minerva.icons%2Cstyles&amp;only=styles&amp;skin=minerva">
<script async="" src="https://commons.m.wikimedia.org/w/load.php?lang=en&amp;modules=startup&amp;only=scripts&amp;raw=1&amp;skin=minerva"></script>
<meta name="ResourceLoaderDynamicStyles" content="">
<link rel="stylesheet" href="https://commons.m.wikimedia.org/w/load.php?lang=en&amp;modules=ext.gadget.Long-Image-Names-in-Categories&amp;only=styles&amp;skin=minerva">
<link rel="stylesheet" href="https://commons.m.wikimedia.org/w/load.php?lang=en&amp;modules=site.styles&amp;only=styles&amp;skin=minerva">
<meta name="generator" content="MediaWiki 1.45.0-wmf.12">
<meta name="referrer" content="origin">
<meta name="referrer" content="origin-when-cross-origin">
<meta name="robots" content="noindex,nofollow,max-image-preview:standard">
<meta name="format-detection" content="telephone=no">
<meta name="theme-color" content="#eaecf0">
<meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=yes, minimum-scale=0.25, maximum-scale=5.0">
<meta property="og:title" content="Settings - Wikimedia Commons">
<meta property="og:type" content="website">
<link rel="manifest" href="https://commons.m.wikimedia.org/w/api.php?action=webapp-manifest">
<link rel="apple-touch-icon" href="https://commons.m.wikimedia.org/static/apple-touch/commons.png">
<link rel="icon" href="https://commons.m.wikimedia.org/static/favicon/commons.ico">
<link rel="search" type="application/opensearchdescription+xml" href="https://commons.m.wikimedia.org/w/rest.php/v1/search" title="Wikimedia Commons">
<link rel="EditURI" type="application/rsd+xml" href="http://commons.wikimedia.org/w/api.php?action=rsd">
<link rel="canonical" href="https://commons.wikimedia.org/w/index.php?returnto=File%3AMadafu-chopping.jpg&amp;title=Special:MobileOptions">
<link rel="license" href="https://creativecommons.org/licenses/by-sa/4.0/">
<link rel="dns-prefetch" href="https://commons.m.wikimedia.org/w/auth.wikimedia.org">
</head>
<body class="mw-mf-special-page mediawiki ltr sitedir-ltr mw-hide-empty-elt ns--1 ns-special mw-special-MobileOptions page-Special_MobileOptions rootpage-Special_MobileOptions stable skin-minerva action-view skin--responsive mw-mf-amc-disabled mw-mf"><div id="mw-mf-viewport">
	<div id="mw-mf-page-center">
		<a class="mw-mf-page-center__mask" href="#"></a>
		<header class="header-container header-chrome">
			<div class="minerva-header">
				<nav class="navigation-drawer toggle-list view-border-box">
					<input type="checkbox" id="main-menu-input"
						data-event-name="ui.mainmenu"
						class="toggle-list__checkbox" role="button" aria-haspopup="true" aria-expanded="false" aria-labelledby="mw-mf-main-menu-button">
					<label for="main-menu-input" id="mw-mf-main-menu-button" aria-hidden="true" class="cdx-button cdx-button--size-large cdx-button--fake-button cdx-button--fake-button--enabled cdx-button--icon-only cdx-button--weight-quiet toggle-list__toggle">
    <span class="minerva-icon minerva-icon--menu"></span>
<span></span>
</label>

					<div id="mw-mf-page-left" class="menu view-border-box">
	<ul id="p-navigation" class="toggle-list__list">
			<li class="toggle-list-item ">
				<a class="toggle-list-item__anchor menu__item--home" href="https://commons.m.wikimedia.org/wiki/Main_Page" data-mw="interface">
					<span class="minerva-icon minerva-icon--home"></span>

					<span class="toggle-list-item__label">Home</span>
				</a>
			</li>
			<li class="toggle-list-item ">
				<a class="toggle-list-item__anchor menu__item--random" href="https://commons.m.wikimedia.org/wiki/Special:Random" data-mw="interface">
					<span class="minerva-icon minerva-icon--die"></span>

					<span class="toggle-list-item__label">Random</span>
				</a>
			</li>
			<li class="toggle-list-item skin-minerva-list-item-jsonly">
				<a class="toggle-list-item__anchor menu__item--nearby" href="https://commons.m.wikimedia.org/wiki/Special:Nearby" data-event-name="menu.nearby" data-mw="interface">
					<span class="minerva-icon minerva-icon--mapPin"></span>

					<span class="toggle-list-item__label">Nearby</span>
				</a>
			</li>
	</ul>
	<ul id="p-personal" class="toggle-list__list">
			<li class="toggle-list-item ">
				<a class="toggle-list-item__anchor mw-list-item menu__item--login" href="https://commons.m.wikimedia.org/w/index.php?title=Special:UserLogin&amp;returnto=Special%3AMobileOptions&amp;returntoquery=returnto%3DFile%253AMadafu-chopping.jpg" data-event-name="menu.login" data-mw="interface">
					<span class="minerva-icon minerva-icon--logIn"></span>

					<span class="toggle-list-item__label">Log in</span>
				</a>
			</li>
	</ul>
	<ul id="pt-preferences" class="toggle-list__list">
			<li class="toggle-list-item skin-minerva-list-item-jsonly">
				<a class="toggle-list-item__anchor menu__item--settings" href="https://commons.m.wikimedia.org/w/index.php?title=Special:MobileOptions&amp;returnto=Special%3AMobileOptions" data-event-name="menu.settings" data-mw="interface">
					<span class="minerva-icon minerva-icon--settings"></span>

					<span class="toggle-list-item__label">Settings</span>
				</a>
			</li>
	</ul>
			<div class="donate-banner">
				<a href="https://donate.wikimedia.org/?wmf_source=donate&amp;wmf_medium=sidebar&amp;wmf_campaign=commons.wikimedia.org&amp;uselang=en&amp;wmf_key=minerva" class="donate-banner__link"
					data-event-name="menu.donateBanner">
					<div class="donate-banner__text-container">
						<span class="donate-banner__text">Donate Now</span>
						<span class="donate-banner__subtitle">If this site has been useful to you, please give today.</span>
					</div>
					<picture>
						<source
			    			srcset="https://en.wikipedia.org/static/images/donate/donate.png"
			   				media="(prefers-reduced-motion)" />
						<img src="https://en.wikipedia.org/static/images/donate/donate.gif" alt="" class="donate-banner__gif" loading="lazy">
					</picture>
				</a>
			</div>
	<ul class="hlist">
			<li class="toggle-list-item ">
				<a class="toggle-list-item__anchor menu__item--about" href="https://commons.m.wikimedia.org/wiki/Commons:Welcome" data-mw="interface">
					
					<span class="toggle-list-item__label">About Wikimedia Commons</span>
				</a>
			</li>
			<li class="toggle-list-item ">
				<a class="toggle-list-item__anchor menu__item--disclaimers" href="https://commons.m.wikimedia.org/wiki/Commons:General_disclaimer" data-mw="interface">
					
					<span class="toggle-list-item__label">Disclaimers</span>
				</a>
			</li>
	</ul>
</div>

					<label class="main-menu-mask" for="main-menu-input"></label>
				</nav>
				<div class="branding-box">
					<a href="https://commons.m.wikimedia.org/wiki/Main_Page">
						<span><img src="https://commons.m.wikimedia.org/static/images/mobile/copyright/commonswiki-wordmark.svg" alt="Wikimedia Commons" width="115" height="32"
	style="width: 7.1875em; height: 2em;"/>

</span>
						
					</a>
				</div>
					<form role="search" action="https://commons.m.wikimedia.org/w/index.php" method="get" class="minerva-search-form">
				<div class="search-box search-box--typeahead">
					<div class="cdx-typeahead-search cdx-typeahead-search--show-thumbnail cdx-typeahead-search--auto-expand-width">
						<div class="cdx-search-input">
							<div class="cdx-search-input__input-wrapper">
								<div class="cdx-text-input cdx-text-input--has-start-icon">
									<input type="hidden" name="title" value="Special:MediaSearch"/>
									<input class="search skin-minerva-search-trigger cdx-text-input__input" id="searchInput"
									 type="search" name="search" placeholder="Search Wikimedia Commons" aria-label="Search Wikimedia Commons" autocapitalize="sentences" spellcheck="false" title="Search Wikimedia Commons [f]" accesskey="f" autocomplete="off">
									<span class="cdx-text-input__icon cdx-text-input__start-icon"></span>
								</div>
							</div>
						</div>
					</div>
				</div>
				<button id="searchIcon" class="cdx-button cdx-button--size-large cdx-button--icon-only cdx-button--weight-quiet skin-minerva-search-trigger">
	    <span class="minerva-icon minerva-icon--search"></span>
<span>Search</span>
	</button>
</form>
		<nav class="minerva-user-navigation" aria-label="User navigation">
					
				</nav>
			</div>
		</header>
		<main id="content" class="mw-body">
			<div class="banner-container">
			<div id="siteNotice"></div>
			</div>
			
			<div class="pre-content heading-holder">
				<div class="page-heading">
					<h1 id="firstHeading" class="firstHeading mw-first-heading">Settings</h1>
					<div class="tagline"></div>
				</div>
				<div id="mw-content-subtitle"></div>
			</div>
			<div id="bodyContent" class="content">
				<div id="mw-content-text" class="mw-body-content"><script>function mfTempOpenSection(id){var block=document.getElementById("mf-section-"+id);block.className+=" open-block";block.previousSibling.className+=" open-block";}</script><form id='mobile-options' method='POST' action='https://commons.m.wikimedia.org/wiki/Special:MobileOptions' class='oo-ui-layout oo-ui-formLayout mw-mf-settings'><span id='mw-mf-settings-save' class='oo-ui-widget oo-ui-widget-enabled oo-ui-inputWidget oo-ui-buttonElement oo-ui-buttonElement-framed oo-ui-labelElement oo-ui-flaggedElement-primary oo-ui-flaggedElement-progressive oo-ui-buttonInputWidget' data-ooui='{"_":"OO.ui.ButtonInputWidget","type":"submit","value":"Save","label":"Save","flags":["primary","progressive"]}'><button type='submit' tabindex='0' value='Save' class='oo-ui-inputWidget-input oo-ui-buttonElement-button'><span class='oo-ui-iconElement-icon oo-ui-iconElement-noIcon oo-ui-image-invert'></span><span class='oo-ui-labelElement-label'>Save</span><span class='oo-ui-indicatorElement-indicator oo-ui-indicatorElement-noIndicator oo-ui-image-invert'></span></button></span></form><!--esi <esi:include src="/esitest-fa8a495983347898/content" /> --><noscript><img src="https://commons.m.wikimedia.org/wiki/Special:CentralAutoLogin/start?type=1x1&amp;usesul3=1" alt="" width="1" height="1" style="border: none; position: absolute;"></noscript>
<div class="printfooter" data-nosnippet="">Retrieved from "<a dir="ltr" href="https://commons.wikimedia.org/wiki/Special:MobileOptions">https://commons.wikimedia.org/wiki/Special:MobileOptions</a>"</div></div>
				
			</div>
			<div class="post-content" id="page-secondary-actions">
			</div>
		</main>
		<footer class="mw-footer minerva-footer" role="contentinfo">
			<div class="post-content footer-content">
			
			<div id="p-lang">
	    <h4>Languages</h4>
	    <section>
	        <ul id="p-variants" class="minerva-languages"></ul>
	        <p>This page is not available in other languages.</p>
	    </section>
	</div>
	<div class="minerva-footer-logo">
				<img src="https://commons.m.wikimedia.org/static/images/mobile/copyright/commonswiki-wordmark.svg" alt="Wikimedia Commons" width="115" height="32"
	style="width: 7.1875em; height: 2em;"/>


				<ul id="footer-icons" class="footer-icons">
	<li id="footer-copyrightico"><a href="https://www.wikimedia.org/" class="cdx-button cdx-button--fake-button cdx-button--size-large cdx-button--fake-button--enabled"><picture><source media="(min-width: 500px)" srcset="/static/images/footer/wikimedia-button.svg" width="84" height="29"><img src="https://commons.m.wikimedia.org/static/images/footer/wikimedia.svg" width="25" height="25" alt="Wikimedia Foundation" lang="en" loading="lazy"></picture></a></li>
	<li id="footer-poweredbyico"><a href="https://www.mediawiki.org/" class="cdx-button cdx-button--fake-button cdx-button--size-large cdx-button--fake-button--enabled"><picture><source media="(min-width: 500px)" srcset="/w/resources/assets/poweredby_mediawiki.svg" width="88" height="31"><img src="https://commons.m.wikimedia.org/w/resources/assets/mediawiki_compact.svg" alt="Powered by MediaWiki" lang="en" width="25" height="25" loading="lazy"></picture></a></li>
</ul>
			</div>
			<ul id="footer-info" class="footer-info hlist hlist-separated">
</ul>

			<ul id="footer-places" class="footer-places hlist hlist-separated">
	<li id="footer-places-privacy"><a href="https://foundation.wikimedia.org/wiki/Special:MyLanguage/Policy:Privacy_policy">Privacy policy</a></li>
	<li id="footer-places-about"><a href="https://commons.m.wikimedia.org/wiki/Commons:Welcome">About Wikimedia Commons</a></li>
	<li id="footer-places-disclaimers"><a href="https://commons.m.wikimedia.org/wiki/Commons:General_disclaimer">Disclaimers</a></li>
	<li id="footer-places-wm-codeofconduct"><a href="https://foundation.wikimedia.org/wiki/Special:MyLanguage/Policy:Universal_Code_of_Conduct">Code of Conduct</a></li>
	<li id="footer-places-developers"><a href="https://developer.wikimedia.org/">Developers</a></li>
	<li id="footer-places-statslink"><a href="https://stats.wikimedia.org/#/commons.wikimedia.org">Statistics</a></li>
	<li id="footer-places-cookiestatement"><a href="https://foundation.wikimedia.org/wiki/Special:MyLanguage/Policy:Cookie_statement">Cookie statement</a></li>
	<li id="footer-places-terms-use"><a href="https://foundation.m.wikimedia.org/wiki/Special:MyLanguage/Policy:Terms_of_Use">Terms of Use</a></li>
	<li id="footer-places-desktop-toggle"><a id="mw-mf-display-toggle" href="http://commons.wikimedia.org/w/index.php?title=Special:MobileOptions&amp;returnto=File%3AMadafu-chopping.jpg&amp;mobileaction=toggle_view_desktop" data-event-name="switch_to_desktop">Desktop</a></li>
</ul>

			</div>
		</footer>
			</div>
</div>
<div class="mw-portlet mw-portlet-dock-bottom emptyPortlet" id="p-dock-bottom">
        <ul>
                
        </ul>
</div>
<div class="mw-notification-area" data-mw="interface"></div>
<script>(RLQ=window.RLQ||[]).push(function(){mw.config.set({"wgHostname":"mw-web.eqiad.main-7b48b5fb74-ftklg","wgBackendResponseTime":77});});</script>
<script>(window.NORLQ=window.NORLQ||[]).push(function(){var ns,i,p,img;ns=document.getElementsByTagName('noscript');for(i=0;i<ns.length;i++){p=ns[i].nextSibling;if(p&&p.className&&p.className.indexOf('lazy-image-placeholder')>-1){img=document.createElement('img');img.setAttribute('src',p.getAttribute('data-mw-src'));img.setAttribute('width',p.getAttribute('data-width'));img.setAttribute('height',p.getAttribute('data-height'));img.setAttribute('alt',p.getAttribute('data-alt'));p.parentNode.replaceChild(img,p);}}});</script>
</body>

<!-- Mirrored from commons.m.wikimedia.org/w/index.php?title=Special:MobileOptions&returnto=File%3AMadafu-chopping.jpg by HTTrack Website Copier/3.x [XR&CO'2014], Sun, 03 Aug 2025 15:20:55 GMT -->
</html>