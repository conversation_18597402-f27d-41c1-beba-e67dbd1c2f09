<!DOCTYPE html>
<html class="client-nojs skin-theme-clientpref-day mf-expand-sections-clientpref-0 mf-font-size-clientpref-small mw-mf-amc-clientpref-0" lang="en" dir="ltr">

<!-- Mirrored from commons.m.wikimedia.org/wiki/File:Gustavsvik,_Visby_02.jpg by HTTrack Website Copier/3.x [XR&CO'2014], Sun, 03 Aug 2025 15:20:54 GMT -->
<!-- Added by HTTrack --><meta http-equiv="content-type" content="text/html;charset=UTF-8" /><!-- /Added by HTTrack -->
<head>
<meta charset="UTF-8">
<title>File:Gustavsvik, Visby 02.jpg - Wikimedia Commons</title>
<script>(function(){var className="client-js skin-theme-clientpref-day mf-expand-sections-clientpref-0 mf-font-size-clientpref-small mw-mf-amc-clientpref-0";var cookie=document.cookie.match(/(?:^|; )commonswikimwclientpreferences=([^;]+)/);if(cookie){cookie[1].split('%2C').forEach(function(pref){className=className.replace(new RegExp('(^| )'+pref.replace(/-clientpref-\w+$|[^\w-]+/g,'')+'-clientpref-\\w+( |$)'),'$1'+pref+'$2');});}document.documentElement.className=className;}());RLCONF={"wgBreakFrames":true,"wgSeparatorTransformTable":["",""],"wgDigitTransformTable":["",""],"wgDefaultDateFormat":"dmy","wgMonthNames":["","January","February","March","April","May","June","July","August","September","October","November","December"],"wgRequestId":"ed47bdcd-eb21-4b27-83af-c5928d12a9fd","wgCanonicalNamespace":"File","wgCanonicalSpecialPageName":false,"wgNamespaceNumber":6,"wgPageName":"File:Gustavsvik,_Visby_02.jpg","wgTitle":"Gustavsvik, Visby 02.jpg","wgCurRevisionId":**********,"wgRevisionId":**********,"wgArticleId":*********,"wgIsArticle":true,"wgIsRedirect":false,"wgAction":"view","wgUserName":null,"wgUserGroups":["*"],"wgPageViewLanguage":"en","wgPageContentLanguage":"en","wgPageContentModel":"wikitext","wgRelevantPageName":"File:Gustavsvik,_Visby_02.jpg","wgRelevantArticleId":*********,"wgIsProbablyEditable":true,"wgRelevantPageIsProbablyEditable":true,"wgRestrictionEdit":[],"wgRestrictionMove":[],"wgRestrictionUpload":[],"wgNoticeProject":"commons","wgCiteReferencePreviewsActive":false,"wgMediaViewerOnClick":true,"wgMediaViewerEnabledByDefault":false,"wgVisualEditor":{"pageLanguageCode":"en","pageLanguageDir":"ltr","pageVariantFallbacks":"en"},"wgMFMode":"stable","wgMFAmc":false,"wgMFAmcOutreachActive":false,"wgMFAmcOutreachUserEligible":false,"wgMFLazyLoadImages":true,"wgMFEditNoticesFeatureConflict":false,"wgMFDisplayWikibaseDescriptions":{"search":true,"watchlist":true,"tagline":true,"nearby":true},"wgMFIsSupportedEditRequest":true,"wgMFScriptPath":"","wgWMESchemaEditAttemptStepOversample":false,"wgWMEPageLength":500,"wbUserPreferredContentLanguages":["en"],"wbUserSpecifiedLanguages":["en"],"wbCopyright":{"version":"wikibase-1","messageHtml":"By clicking \"publish\", you agree to the \u003Ca href=\"/wiki/Commons:Copyrights\" class=\"mw-redirect\" title=\"Commons:Copyrights\"\u003Eterms of use\u003C/a\u003E, and you irrevocably agree to release your contribution under the \u003Ca rel=\"nofollow\" class=\"external text\" href=\"https://creativecommons.org/publicdomain/zero/1.0/\"\u003ECreative Commons CC0 License\u003C/a\u003E."},"wbBadgeItems":[],"wbMultiLingualStringLimit":250,"wbTaintedReferencesEnabled":false,"wgMetricsPlatformUserExperiments":{"active_experiments":[],"overrides":[],"enrolled":[],"assigned":[],"subject_ids":[],"sampling_units":[]},"wbEntityId":"M*********","wgEditSubmitButtonLabelPublish":true,"wgCoordinates":{"lat":57.662703,"lon":18.318657},"upwizPropertyTitles":{"P180":"Main subjects visible in this work"},"upwizPropertyPlaceholders":{"P180":"e.g., Angolan giraffe"},"upwizPropertyCopyLabels":{"P180":"Main subjects"},"wbmiDefaultProperties":["P180"],"wbmiPropertyTitles":{"P180":"Items portrayed in this file"},"wbmiPropertyTypes":{"P180":"wikibase-item","P571":"time","P7482":"wikibase-item","P170":"wikibase-item","P2093":"string","P4174":"external-id","P2699":"url","P275":"wikibase-item","P6216":"wikibase-item","P1259":"globe-coordinate","P4082":"wikibase-item"},"wbmiRepoApiUrl":"/w/api.php","wbmiHelpUrls":{"P180":"https://commons.wikimedia.org/wiki/Special:MyLanguage/Commons:Depicts"},"wbmiExternalEntitySearchBaseUri":"https://www.wikidata.org/w/api.php","wbmiSupportedDataTypes":["wikibase-item","string","quantity","time","monolingualtext","external-id","globe-coordinate","url"],"wbCurrentRevision":**********,"wbEntity":{"type":"mediainfo","id":"M*********","labels":{"gl":{"language":"gl","value":"Praia cuberta de neve en Gustavsvik, Visby."}},"descriptions":[],"statements":{"P571":[{"mainsnak":{"snaktype":"value","property":"P571","hash":"057351ede3101f4a1ea84c2461d4d53cf22b4e0f","datavalue":{"value":{"time":"+2025-03-12T00:00:00Z","timezone":0,"before":0,"after":0,"precision":11,"calendarmodel":"http://www.wikidata.org/entity/Q1985727"},"type":"time"}},"type":"statement","id":"M*********$F49BA185-7E19-4BA6-A9FF-1B0101B90776","rank":"normal"}],"P7482":[{"mainsnak":{"snaktype":"value","property":"P7482","hash":"83568a288a8b8b4714a68e7239d8406833762864","datavalue":{"value":{"entity-type":"item","numeric-id":66458942,"id":"Q66458942"},"type":"wikibase-entityid"}},"type":"statement","id":"M*********$8E68680E-B116-479E-9E8E-19A3334589BC","rank":"normal"}],"P170":[{"mainsnak":{"snaktype":"somevalue","property":"P170","hash":"d3550e860f988c6675fff913440993f58f5c40c5"},"type":"statement","qualifiers":{"P2093":[{"snaktype":"value","property":"P2093","hash":"5bf5396799f3dfcbb6d42ffc93e9100fc0b59912","datavalue":{"value":"Bene Riobó","type":"string"}}],"P4174":[{"snaktype":"value","property":"P4174","hash":"328cd1a8d4d752cc05c0597e3f3e6ad5648b810f","datavalue":{"value":"Beninho","type":"string"}}],"P2699":[{"snaktype":"value","property":"P2699","hash":"0a6faea8809f7fac23b4c68ba5766a7801da41dd","datavalue":{"value":"https://commons.wikimedia.org/wiki/User:Beninho","type":"string"}}]},"qualifiers-order":["P2093","P4174","P2699"],"id":"M*********$BAD42779-231A-4D08-BAF8-A2BAAD13DCCB","rank":"normal"}],"P275":[{"mainsnak":{"snaktype":"value","property":"P275","hash":"ec6e754c5042e13b53376139e505ebd6708745a4","datavalue":{"value":{"entity-type":"item","numeric-id":18199165,"id":"*********"},"type":"wikibase-entityid"}},"type":"statement","id":"M*********$1C64663C-42DE-46F5-A44E-70FD5EF42698","rank":"normal"}],"P6216":[{"mainsnak":{"snaktype":"value","property":"P6216","hash":"5570347fdc76d2a80732f51ea10ee4b144a084e0","datavalue":{"value":{"entity-type":"item","numeric-id":50423863,"id":"Q50423863"},"type":"wikibase-entityid"}},"type":"statement","id":"M*********$82867E78-923E-4215-9CF7-4E077DEF21B0","rank":"normal"}],"P1259":[{"mainsnak":{"snaktype":"value","property":"P1259","hash":"f9335cf7c3271d04f729b04ec322058df3ff2ddf","datavalue":{"value":{"latitude":57.662703,"longitude":18.318657,"altitude":null,"precision":1.0e-6,"globe":"http://www.wikidata.org/entity/Q2"},"type":"globecoordinate"}},"type":"statement","id":"M*********$649E523F-321A-48C5-A927-3F8DA4CC77C8","rank":"normal"}],"P4082":[{"mainsnak":{"snaktype":"value","property":"P4082","hash":"3538538ca6ffbabc87f697a2e886951459b9db71","datavalue":{"value":{"entity-type":"item","numeric-id":22815060,"id":"Q22815060"},"type":"wikibase-entityid"}},"type":"statement","id":"M*********$E2F08FEE-FBD9-4075-AAE0-99FDADFC8A65","rank":"normal"}]}},"wbmiMinCaptionLength":5,"wbmiMaxCaptionLength":250,"wbmiParsedMessageAnonEditWarning":"\u003Cp\u003EYou are not logged in and your \u003Ca href=\"https://en.wikipedia.org/wiki/IP_address\" class=\"extiw\" title=\"w:IP address\"\u003EIP address\u003C/a\u003E will be publicly visible if you make any edits. \u003Ca href=\"/wiki/Special:UserLogin\" title=\"Special:UserLogin\"\u003ELogging in\u003C/a\u003E or \u003Ca href=\"/wiki/Special:CreateAccount\" title=\"Special:CreateAccount\"\u003Ecreating an account\u003C/a\u003E will conceal your IP address and provide you with many other \u003Ca href=\"https://en.wikipedia.org/wiki/Wikipedia:Why_create_an_account%3F\" class=\"extiw\" title=\"w:Wikipedia:Why create an account?\"\u003Ebenefits\u003C/a\u003E. Please do not save test edits. If you want to experiment, please use the \u003Ca href=\"/wiki/Commons:Sandbox\" title=\"Commons:Sandbox\"\u003ESandbox\u003C/a\u003E.\n\u003C/p\u003E","wbmiProtectionMsg":null,"wbmiShowIPEditingWarning":true,"wgCheckUserClientHintsHeadersJsApi":["brands","architecture","bitness","fullVersionList","mobile","model","platform","platformVersion"],"wgMinervaPermissions":{"watchable":true,"watch":false},"wgMinervaFeatures":{"beta":false,"donateBanner":true,"donate":false,"mobileOptionsLink":true,"categories":false,"pageIssues":true,"talkAtTop":false,"historyInPageActions":false,"overflowSubmenu":false,"tabsOnSpecials":true,"personalMenu":false,"mainMenuExpanded":false,"nightMode":true},"wgMinervaDownloadNamespaces":[0]};
RLSTATE={"ext.gadget.Long-Image-Names-in-Categories":"ready","ext.globalCssJs.user.styles":"ready","site.styles":"ready","user.styles":"ready","ext.globalCssJs.user":"ready","user":"ready","user.options":"loading","ext.kartographer.style":"ready","wikibase.alltargets":"ready","mediawiki.interface.helpers.styles":"ready","mediawiki.interface.helpers.linker.styles":"ready","mediawiki.action.view.filepage":"ready","skins.minerva.styles":"ready","skins.minerva.content.styles.images":"ready","mediawiki.hlist":"ready","skins.minerva.codex.styles":"ready","skins.minerva.icons":"ready","filepage":"ready","ext.wikimediamessages.styles":"ready","mobile.init.styles":"ready","wikibase.mediainfo.filepage.styles":"ready","wikibase.mediainfo.statements.styles":"ready"};RLPAGEMODULES=["ext.xLab","ext.kartographer.link","wikibase.entityPage.entityLoaded","mediawiki.action.view.metadata","site","mediawiki.page.ready","skins.minerva.scripts","ext.centralNotice.geoIP","ext.centralNotice.startUp","ext.gadget.Slideshow","ext.gadget.ZoomViewer","ext.gadget.CollapsibleTemplates","ext.gadget.Stockphoto","ext.gadget.WatchlistNotice","ext.gadget.AjaxQuickDelete","ext.gadget.WikiMiniAtlas","ext.gadget.LanguageSelect","ext.gadget.PictureOfTheYearEnhancements","ext.urlShortener.toolbar","ext.centralauth.centralautologin","mobile.init","ext.echo.centralauth","ext.eventLogging","ext.wikimediaEvents","ext.wikimediaEvents.wikibase","ext.navigationTiming","wikibase.mobile","wikibase.mediainfo.filePageDisplay","ext.checkUser.clientHints"];</script>
<script>(RLQ=window.RLQ||[]).push(function(){mw.loader.impl(function(){return["user.options@12s5i",function($,jQuery,require,module){mw.user.tokens.set({"patrolToken":"+\\","watchToken":"+\\","csrfToken":"+\\"});
}];});});</script>
<link rel="stylesheet" href="https://commons.m.wikimedia.org/w/load.php?lang=en&amp;modules=ext.kartographer.style%7Cext.wikimediamessages.styles%7Cfilepage%7Cmediawiki.action.view.filepage%7Cmediawiki.hlist%7Cmediawiki.interface.helpers.linker.styles%7Cmediawiki.interface.helpers.styles%7Cmobile.init.styles%7Cskins.minerva.codex.styles%7Cskins.minerva.content.styles.images%7Cskins.minerva.icons%2Cstyles%7Cwikibase.alltargets%7Cwikibase.mediainfo.filepage.styles%7Cwikibase.mediainfo.statements.styles&amp;only=styles&amp;skin=minerva">
<script async="" src="https://commons.m.wikimedia.org/w/load.php?lang=en&amp;modules=startup&amp;only=scripts&amp;raw=1&amp;skin=minerva"></script>
<meta name="ResourceLoaderDynamicStyles" content="">
<link rel="stylesheet" href="https://commons.m.wikimedia.org/w/load.php?lang=en&amp;modules=ext.gadget.Long-Image-Names-in-Categories&amp;only=styles&amp;skin=minerva">
<link rel="stylesheet" href="https://commons.m.wikimedia.org/w/load.php?lang=en&amp;modules=site.styles&amp;only=styles&amp;skin=minerva">
<meta name="generator" content="MediaWiki 1.45.0-wmf.12">
<meta name="referrer" content="origin">
<meta name="referrer" content="origin-when-cross-origin">
<meta name="robots" content="max-image-preview:standard">
<meta name="format-detection" content="telephone=no">
<meta name="theme-color" content="#eaecf0">
<meta property="og:image" content="https://upload.wikimedia.org/wikipedia/commons/thumb/6/6b/Gustavsvik%2C_Visby_02.jpg/1200px-Gustavsvik%2C_Visby_02.jpg">
<meta property="og:image:width" content="1200">
<meta property="og:image:height" content="800">
<meta property="og:image" content="https://upload.wikimedia.org/wikipedia/commons/thumb/6/6b/Gustavsvik%2C_Visby_02.jpg/960px-Gustavsvik%2C_Visby_02.jpg">
<meta property="og:image:width" content="800">
<meta property="og:image:height" content="533">
<meta property="og:image:width" content="640">
<meta property="og:image:height" content="427">
<meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=yes, minimum-scale=0.25, maximum-scale=5.0">
<meta property="og:title" content="File:Gustavsvik, Visby 02.jpg - Wikimedia Commons">
<meta property="og:type" content="website">
<link rel="preconnect" href="http://upload.wikimedia.org/">
<link rel="alternate" href="https://commons.wikimedia.org/wiki/Special:EntityData/M*********.json" type="application/json">
<link rel="alternate" href="https://commons.wikimedia.org/wiki/Special:EntityData/M*********.php" type="application/vnd.php.serialized">
<link rel="alternate" href="https://commons.wikimedia.org/wiki/Special:EntityData/M*********.n3" type="text/n3">
<link rel="alternate" href="https://commons.wikimedia.org/wiki/Special:EntityData/M*********.ttl" type="text/turtle">
<link rel="alternate" href="https://commons.wikimedia.org/wiki/Special:EntityData/M*********.nt" type="application/n-triples">
<link rel="alternate" href="https://commons.wikimedia.org/wiki/Special:EntityData/M*********.rdf" type="application/rdf+xml">
<link rel="alternate" href="https://commons.wikimedia.org/wiki/Special:EntityData/M*********.jsonld" type="application/ld+json">
<link rel="manifest" href="https://commons.m.wikimedia.org/w/api.php?action=webapp-manifest">
<link rel="alternate" type="application/x-wiki" title="Edit" href="https://commons.m.wikimedia.org/w/index.php?title=File:Gustavsvik,_Visby_02.jpg&amp;action=edit">
<link rel="apple-touch-icon" href="https://commons.m.wikimedia.org/static/apple-touch/commons.png">
<link rel="icon" href="https://commons.m.wikimedia.org/static/favicon/commons.ico">
<link rel="search" type="application/opensearchdescription+xml" href="https://commons.m.wikimedia.org/w/rest.php/v1/search" title="Wikimedia Commons">
<link rel="EditURI" type="application/rsd+xml" href="http://commons.wikimedia.org/w/api.php?action=rsd">
<link rel="canonical" href="https://commons.wikimedia.org/wiki/File:Gustavsvik,_Visby_02.jpg">
<link rel="license" href="https://creativecommons.org/licenses/by-sa/4.0/">
<link rel="dns-prefetch" href="http://meta.wikimedia.org/" />
<link rel="dns-prefetch" href="https://commons.m.wikimedia.org/wiki/auth.wikimedia.org">
</head>
<body class="mediawiki ltr sitedir-ltr mw-hide-empty-elt ns-6 ns-subject mw-editable page-File_Gustavsvik_Visby_02_jpg rootpage-File_Gustavsvik_Visby_02_jpg stable skin-minerva action-view skin--responsive mw-mf-amc-disabled mw-mf wb-entitypage wb-mediainfopage wb-mediainfopage-M*********"><div id="mw-mf-viewport">
	<div id="mw-mf-page-center">
		<a class="mw-mf-page-center__mask" href="#"></a>
		<header class="header-container header-chrome">
			<div class="minerva-header">
				<nav class="navigation-drawer toggle-list view-border-box">
					<input type="checkbox" id="main-menu-input"
						data-event-name="ui.mainmenu"
						class="toggle-list__checkbox" role="button" aria-haspopup="true" aria-expanded="false" aria-labelledby="mw-mf-main-menu-button">
					<label for="main-menu-input" id="mw-mf-main-menu-button" aria-hidden="true" class="cdx-button cdx-button--size-large cdx-button--fake-button cdx-button--fake-button--enabled cdx-button--icon-only cdx-button--weight-quiet toggle-list__toggle">
    <span class="minerva-icon minerva-icon--menu"></span>
<span></span>
</label>

					<div id="mw-mf-page-left" class="menu view-border-box">
	<ul id="p-navigation" class="toggle-list__list">
			<li class="toggle-list-item ">
				<a class="toggle-list-item__anchor menu__item--home" href="https://commons.m.wikimedia.org/wiki/Main_Page" data-mw="interface">
					<span class="minerva-icon minerva-icon--home"></span>

					<span class="toggle-list-item__label">Home</span>
				</a>
			</li>
			<li class="toggle-list-item ">
				<a class="toggle-list-item__anchor menu__item--random" href="https://commons.m.wikimedia.org/wiki/Special:Random" data-mw="interface">
					<span class="minerva-icon minerva-icon--die"></span>

					<span class="toggle-list-item__label">Random</span>
				</a>
			</li>
			<li class="toggle-list-item skin-minerva-list-item-jsonly">
				<a class="toggle-list-item__anchor menu__item--nearby" href="https://commons.m.wikimedia.org/wiki/Special:Nearby" data-event-name="menu.nearby" data-mw="interface">
					<span class="minerva-icon minerva-icon--mapPin"></span>

					<span class="toggle-list-item__label">Nearby</span>
				</a>
			</li>
	</ul>
	<ul id="p-personal" class="toggle-list__list">
			<li class="toggle-list-item ">
				<a class="toggle-list-item__anchor mw-list-item menu__item--login" href="https://commons.m.wikimedia.org/w/index.php?title=Special:UserLogin&amp;returnto=File%3AGustavsvik%2C+Visby+02.jpg" data-event-name="menu.login" data-mw="interface">
					<span class="minerva-icon minerva-icon--logIn"></span>

					<span class="toggle-list-item__label">Log in</span>
				</a>
			</li>
	</ul>
	<ul id="pt-preferences" class="toggle-list__list">
			<li class="toggle-list-item skin-minerva-list-item-jsonly">
				<a class="toggle-list-item__anchor menu__item--settings" href="https://commons.m.wikimedia.org/w/index.php?title=Special:MobileOptions&amp;returnto=File%3AGustavsvik%2C+Visby+02.jpg" data-event-name="menu.settings" data-mw="interface">
					<span class="minerva-icon minerva-icon--settings"></span>

					<span class="toggle-list-item__label">Settings</span>
				</a>
			</li>
	</ul>
			<div class="donate-banner">
				<a href="https://donate.wikimedia.org/?wmf_source=donate&amp;wmf_medium=sidebar&amp;wmf_campaign=commons.wikimedia.org&amp;uselang=en&amp;wmf_key=minerva" class="donate-banner__link"
					data-event-name="menu.donateBanner">
					<div class="donate-banner__text-container">
						<span class="donate-banner__text">Donate Now</span>
						<span class="donate-banner__subtitle">If this site has been useful to you, please give today.</span>
					</div>
					<picture>
						<source
			    			srcset="https://en.wikipedia.org/static/images/donate/donate.png"
			   				media="(prefers-reduced-motion)" />
						<img src="https://en.wikipedia.org/static/images/donate/donate.gif" alt="" class="donate-banner__gif" loading="lazy">
					</picture>
				</a>
			</div>
	<ul class="hlist">
			<li class="toggle-list-item ">
				<a class="toggle-list-item__anchor menu__item--about" href="https://commons.m.wikimedia.org/wiki/Commons:Welcome" data-mw="interface">
					
					<span class="toggle-list-item__label">About Wikimedia Commons</span>
				</a>
			</li>
			<li class="toggle-list-item ">
				<a class="toggle-list-item__anchor menu__item--disclaimers" href="https://commons.m.wikimedia.org/wiki/Commons:General_disclaimer" data-mw="interface">
					
					<span class="toggle-list-item__label">Disclaimers</span>
				</a>
			</li>
	</ul>
</div>

					<label class="main-menu-mask" for="main-menu-input"></label>
				</nav>
				<div class="branding-box">
					<a href="https://commons.m.wikimedia.org/wiki/Main_Page">
						<span><img src="https://commons.m.wikimedia.org/static/images/mobile/copyright/commonswiki-wordmark.svg" alt="Wikimedia Commons" width="115" height="32"
	style="width: 7.1875em; height: 2em;"/>

</span>
						
					</a>
				</div>
					<form role="search" action="https://commons.m.wikimedia.org/w/index.php" method="get" class="minerva-search-form">
				<div class="search-box search-box--typeahead">
					<div class="cdx-typeahead-search cdx-typeahead-search--show-thumbnail cdx-typeahead-search--auto-expand-width">
						<div class="cdx-search-input">
							<div class="cdx-search-input__input-wrapper">
								<div class="cdx-text-input cdx-text-input--has-start-icon">
									<input type="hidden" name="title" value="Special:MediaSearch"/>
									<input class="search skin-minerva-search-trigger cdx-text-input__input" id="searchInput"
									 type="search" name="search" placeholder="Search Wikimedia Commons" aria-label="Search Wikimedia Commons" autocapitalize="sentences" spellcheck="false" title="Search Wikimedia Commons [f]" accesskey="f" autocomplete="off">
									<span class="cdx-text-input__icon cdx-text-input__start-icon"></span>
								</div>
							</div>
						</div>
					</div>
				</div>
				<button id="searchIcon" class="cdx-button cdx-button--size-large cdx-button--icon-only cdx-button--weight-quiet skin-minerva-search-trigger">
	    <span class="minerva-icon minerva-icon--search"></span>
<span>Search</span>
	</button>
</form>
		<nav class="minerva-user-navigation" aria-label="User navigation">
					
				</nav>
			</div>
		</header>
		<main id="content" class="mw-body">
			<div class="banner-container">
			<div id="siteNotice"></div>
			</div>
			
			<div class="pre-content heading-holder">
				<div class="page-heading">
					<h1 id="firstHeading" class="firstHeading mw-first-heading"><span class="mw-page-title-namespace">File</span><span class="mw-page-title-separator">:</span><span class="mw-page-title-main">Gustavsvik, Visby 02.jpg</span></h1>
					<div class="tagline"></div>
				</div>
				<nav class="page-actions-menu">
	<ul id="p-views" class="page-actions-menu__list minerva-icon-only-menu">
		<li id="language-selector" class="page-actions-menu__list-item">
				<a role="button" href="#" data-mw="interface" data-event-name="menu.languages" title="Language" class="cdx-button cdx-button--size-large cdx-button--fake-button cdx-button--fake-button--enabled cdx-button--icon-only cdx-button--weight-quiet language-selector disabled">
				    <span class="minerva-icon minerva-icon--language"></span>
<span>Language</span>
				</a>
		</li>
		<li id="page-actions-watch" class="page-actions-menu__list-item">
				<a role="button" id="ca-watch" href="https://commons.m.wikimedia.org/w/index.php?title=Special:UserLogin&amp;returnto=File%3AGustavsvik%2C+Visby+02.jpg" data-event-name="menu.watch" class="cdx-button cdx-button--size-large cdx-button--fake-button cdx-button--fake-button--enabled cdx-button--icon-only cdx-button--weight-quiet menu__item--page-actions-watch">
				    <span class="minerva-icon minerva-icon--star"></span>
<span>Watch</span>
				</a>
		</li>
		<li id="page-actions-edit" class="page-actions-menu__list-item">
				<a role="button" id="ca-edit" href="https://commons.m.wikimedia.org/w/index.php?title=File:Gustavsvik,_Visby_02.jpg&amp;action=edit" data-event-name="menu.edit" data-mw="interface" class="cdx-button cdx-button--size-large cdx-button--fake-button cdx-button--fake-button--enabled cdx-button--icon-only cdx-button--weight-quiet edit-page menu__item--page-actions-edit">
				    <span class="minerva-icon minerva-icon--edit"></span>
<span>Edit</span>
				</a>
		</li>
	</ul>
</nav>
				<div id="mw-content-subtitle"></div>
			</div>
			<div id="bodyContent" class="content">
				<div id="mw-content-text" class="mw-body-content"><script>function mfTempOpenSection(id){var block=document.getElementById("mf-section-"+id);block.className+=" open-block";block.previousSibling.className+=" open-block";}</script><ul id="filetoc" role="navigation"><li><a href="#file">File</a></li>
<li><a href="#filehistory">File history</a></li>
<li><a href="#filelinks">File usage on Commons</a></li>
<li><a href="#metadata">Metadata</a></li></ul><div class="fullImageLink" id="file"><a href="https://upload.wikimedia.org/wikipedia/commons/6/6b/Gustavsvik%2C_Visby_02.jpg"><img alt="File:Gustavsvik, Visby 02.jpg" src="https://upload.wikimedia.org/wikipedia/commons/thumb/6/6b/Gustavsvik%2C_Visby_02.jpg/960px-Gustavsvik%2C_Visby_02.jpg?20250609061324" decoding="async" width="800" height="533" srcset="https://upload.wikimedia.org/wikipedia/commons/thumb/6/6b/Gustavsvik%2C_Visby_02.jpg/1200px-Gustavsvik%2C_Visby_02.jpg?20250609061324 1.5x, https://upload.wikimedia.org/wikipedia/commons/thumb/6/6b/Gustavsvik%2C_Visby_02.jpg/1599px-Gustavsvik%2C_Visby_02.jpg?20250609061324 2x" data-file-width="6000" data-file-height="4000"></a><div class="mw-filepage-resolutioninfo">Size of this preview: <a href="https://upload.wikimedia.org/wikipedia/commons/thumb/6/6b/Gustavsvik%2C_Visby_02.jpg/960px-Gustavsvik%2C_Visby_02.jpg" class="mw-thumbnail-link">800 × 533 pixels</a>. <span class="mw-filepage-other-resolutions">Other resolutions: <a href="https://upload.wikimedia.org/wikipedia/commons/thumb/6/6b/Gustavsvik%2C_Visby_02.jpg/330px-Gustavsvik%2C_Visby_02.jpg" class="mw-thumbnail-link">320 × 213 pixels</a> | <a href="https://upload.wikimedia.org/wikipedia/commons/thumb/6/6b/Gustavsvik%2C_Visby_02.jpg/960px-Gustavsvik%2C_Visby_02.jpg" class="mw-thumbnail-link">640 × 427 pixels</a> | <a href="https://upload.wikimedia.org/wikipedia/commons/thumb/6/6b/Gustavsvik%2C_Visby_02.jpg/1024px-Gustavsvik%2C_Visby_02.jpg" class="mw-thumbnail-link">1,024 × 683 pixels</a> | <a href="https://upload.wikimedia.org/wikipedia/commons/thumb/6/6b/Gustavsvik%2C_Visby_02.jpg/1280px-Gustavsvik%2C_Visby_02.jpg" class="mw-thumbnail-link">1,280 × 853 pixels</a> | <a href="https://upload.wikimedia.org/wikipedia/commons/thumb/6/6b/Gustavsvik%2C_Visby_02.jpg/2560px-Gustavsvik%2C_Visby_02.jpg" class="mw-thumbnail-link">2,560 × 1,707 pixels</a> | <a href="https://upload.wikimedia.org/wikipedia/commons/6/6b/Gustavsvik%2C_Visby_02.jpg" class="mw-thumbnail-link">6,000 × 4,000 pixels</a>.</span></div></div>
<div class="fullMedia"><bdi dir="ltr"><a href="https://upload.wikimedia.org/wikipedia/commons/6/6b/Gustavsvik%2C_Visby_02.jpg" class="internal" title="Gustavsvik, Visby 02.jpg">Original file</a></bdi> <span class="fileInfo">(6,000 × 4,000 pixels, file size: 10.45 MB, MIME type: <span class="mime-type">image/jpeg</span>)</span></div><div class='wbmi-tabs-container oo-ui-layout oo-ui-panelLayout'><div id='ooui-php-1' class='wbmi-tabs oo-ui-layout oo-ui-menuLayout oo-ui-menuLayout-static oo-ui-menuLayout-top oo-ui-menuLayout-showMenu oo-ui-indexLayout' data-ooui='{"_":"OO.ui.IndexLayout","classes":["wbmi-tabs"],"expanded":false,"menuPanel":{"tag":"ooui-php-2"},"contentPanel":{"tag":"ooui-php-3"},"autoFocus":false,"tabPanels":{"wikiTextPlusCaptions":{"tag":"ooui-php-4"},"statements":{"tag":"ooui-php-5"}},"tabSelectWidget":{"tag":"ooui-php-6"}}'><div aria-hidden='false' class='oo-ui-menuLayout-menu'><div id='ooui-php-2' class='oo-ui-layout oo-ui-panelLayout oo-ui-indexLayout-tabPanel' data-ooui='{"_":"OO.ui.PanelLayout","preserveContent":false,"expanded":false}'><div role='tablist' aria-multiselectable='false' tabindex='0' id='ooui-php-6' class='oo-ui-selectWidget oo-ui-selectWidget-unpressed oo-ui-widget oo-ui-widget-enabled oo-ui-tabSelectWidget oo-ui-tabSelectWidget-frameless' data-ooui='{"_":"OO.ui.TabSelectWidget","framed":false,"items":[{"tag":"ooui-php-7"},{"tag":"ooui-php-8"}]}'><div aria-selected='true' role='tab' id='ooui-php-7' class='oo-ui-widget oo-ui-widget-enabled oo-ui-labelElement oo-ui-optionWidget oo-ui-tabOptionWidget oo-ui-optionWidget-selected' data-ooui='{"_":"OO.ui.TabOptionWidget","selected":true,"label":"File information","data":"wikiTextPlusCaptions"}'><span class='oo-ui-labelElement-label'>File information</span></div><div aria-selected='false' role='tab' id='ooui-php-8' class='oo-ui-widget oo-ui-widget-enabled oo-ui-labelElement oo-ui-optionWidget oo-ui-tabOptionWidget' data-ooui='{"_":"OO.ui.TabOptionWidget","label":"Structured data","data":"statements"}'><span class='oo-ui-labelElement-label'>Structured data</span></div></div></div></div><div class='oo-ui-menuLayout-content'><div id='ooui-php-3' class='oo-ui-layout oo-ui-panelLayout oo-ui-stackLayout oo-ui-indexLayout-stackLayout' data-ooui='{"_":"OO.ui.StackLayout","preserveContent":false,"expanded":false,"items":[{"tag":"ooui-php-4"},{"tag":"ooui-php-5"}]}'><div role='tabpanel' id='ooui-php-4' class='wbmi-tab oo-ui-layout oo-ui-panelLayout oo-ui-panelLayout-scrollable oo-ui-tabPanelLayout oo-ui-tabPanelLayout-active' data-ooui='{"_":"OO.ui.TabPanelLayout","name":"wikiTextPlusCaptions","label":"File information","scrollable":true,"expanded":false,"classes":["wbmi-tab"]}'><h2 class="wbmi-captions-header">Captions</h2><div class="wbmi-entityview-captionsPanel oo-ui-layout oo-ui-panelLayout oo-ui-panelLayout-framed"><h3 class="wbmi-entityview-captions-header">Captions</h3><div class="wbmi-entityview-caption oo-ui-layout oo-ui-horizontalLayout"><label class="wbmi-language-label oo-ui-widget oo-ui-widget-enabled oo-ui-labelElement-label oo-ui-labelElement oo-ui-labelWidget">English</label><div lang="en" dir="ltr" class="wbmi-caption-value wbmi-entityview-emptyCaption">Add a one-line explanation of what this file represents</div></div><div style="display:none;" class="wbmi-entityview-caption wbmi-entityview-hideable oo-ui-layout oo-ui-horizontalLayout"><label class="wbmi-language-label oo-ui-widget oo-ui-widget-enabled oo-ui-labelElement-label oo-ui-labelElement oo-ui-labelWidget">Galician</label><div lang="gl" dir="ltr" class="wbmi-caption-value">Praia cuberta de neve en Gustavsvik, Visby.</div></div></div><div id="mw-imagepage-content"><div class="mw-content-ltr mw-parser-output" lang="en" dir="ltr"><div class="mw-heading mw-heading2"><h2 id="Summary">Summary</h2><span class="mw-editsection">
<a role="button" href="https://commons.m.wikimedia.org/w/index.php?title=File:Gustavsvik,_Visby_02.jpg&amp;action=edit&amp;section=1" title="Edit section: Summary" class="cdx-button cdx-button--size-large cdx-button--fake-button cdx-button--fake-button--enabled cdx-button--icon-only cdx-button--weight-quiet ">
    <span class="minerva-icon minerva-icon--edit"></span>
<span>edit</span>
</a>

</span>

</div>
<div class="hproduct commons-file-information-table">
<table class="fileinfotpl-type-information vevent" dir="ltr">

<tbody><tr>
<td id="fileinfotpl_desc" class="fileinfo-paramfield" lang="en">Description<span class="summary fn" style="display:none">Gustavsvik, Visby 02.jpg</span></td>
<td class="description">
<div class="description gl" dir="ltr" lang="gl"><span class="language gl" title="Galician"><b>Galego: </b></span> Praia cuberta de neve en Gustavsvik, Visby.</div></td>
</tr>

<tr>
<td id="fileinfotpl_date" class="fileinfo-paramfield" lang="en">Date</td>
<td lang="en">
<time class="dtstart" datetime="2025-03-12 13:52:40" lang="en" dir="ltr" style="white-space:nowrap">12 March 2025, 13:52:40</time></td>
</tr>

<tr>
<td id="fileinfotpl_src" class="fileinfo-paramfield" lang="en">Source</td>
<td>
<span class="int-own-work" lang="en">Own work</span></td>
</tr>

<tr>
<td id="fileinfotpl_aut" class="fileinfo-paramfield" lang="en">Author</td>
<td>
<a href="https://commons.m.wikimedia.org/wiki/User:Beninho" title="User:Beninho">Bene Riobó</a></td>
</tr>


</tbody></table>
</div>
<table class="toccolours layouttemplate commons-file-information-table" style="width: 100%;" dir="ltr" lang="en">
<tbody><tr><th class="type fileinfo-paramfield">Camera location</th><td style="border:none;"><span class="plainlinks"><span class="plainlinksneverexpand"><a class="external text" href="https://geohack.toolforge.org/geohack.php?pagename=File:Gustavsvik,_Visby_02.jpg&amp;params=057.662703_N_0018.318657_E_globe:Earth_type:camera__&amp;language=en">57°&nbsp;39′&nbsp;45.73″&nbsp;N, 18°&nbsp;19′&nbsp;07.17″&nbsp;E</a></span></span>&nbsp;<a class="mw-kartographer-maplink no-icon" data-mw-kartographer="maplink" data-style="osm-intl" href="https://commons.m.wikimedia.org/wiki/Special:Map/13/57.662703/18.318657/en" data-zoom="13" data-lat="57.662703" data-lon="18.318657" data-overlays="[&quot;_d3afd67676e54025da49a8cd3043e2d84b30a252&quot;]"><span typeof="mw:File"><span title="Kartographer map based on OpenStreetMap."><img alt="Kartographer map based on OpenStreetMap." src="https://upload.wikimedia.org/wikipedia/commons/thumb/b/b0/Openstreetmap_logo.svg/20px-Openstreetmap_logo.svg.png" decoding="async" width="20" height="20" class="mw-file-element" srcset="https://upload.wikimedia.org/wikipedia/commons/thumb/b/b0/Openstreetmap_logo.svg/40px-Openstreetmap_logo.svg.png 1.5x" data-file-width="256" data-file-height="256"></span></span></a></td><td style="border:none;">View this and other nearby images on: <a class="external text" href="https://wikimap.toolforge.org/?wp=false&amp;cluster=false&amp;zoom=16&amp;lat=057.662703&amp;lon=0018.318657">OpenStreetMap</a></td><td style="border:none;"><span class="skin-invert" typeof="mw:File"><a href="https://commons.m.wikimedia.org/wiki/Commons:Geocoding" title="Commons:Geocoding"><img alt="info" src="https://upload.wikimedia.org/wikipedia/commons/thumb/6/69/OOjs_UI_icon_help.svg/20px-OOjs_UI_icon_help.svg.png" decoding="async" width="18" height="18" class="mw-file-element" srcset="https://upload.wikimedia.org/wikipedia/commons/thumb/6/69/OOjs_UI_icon_help.svg/27px-OOjs_UI_icon_help.svg.png 1.5x, https://upload.wikimedia.org/wikipedia/commons/thumb/6/69/OOjs_UI_icon_help.svg/36px-OOjs_UI_icon_help.svg.png 2x" data-file-width="24" data-file-height="24"></a></span><span class="geo" style="display:none"> 57.662703;   18.318657</span></td></tr>
</tbody></table>
<div class="mw-heading mw-heading2"><h2 id="Licensing">Licensing</h2><span class="mw-editsection">
<a role="button" href="https://commons.m.wikimedia.org/w/index.php?title=File:Gustavsvik,_Visby_02.jpg&amp;action=edit&amp;section=2" title="Edit section: Licensing" class="cdx-button cdx-button--size-large cdx-button--fake-button cdx-button--fake-button--enabled cdx-button--icon-only cdx-button--weight-quiet ">
    <span class="minerva-icon minerva-icon--edit"></span>
<span>edit</span>
</a>

</span>

</div>
<div style="clear:both; margin:0.5em auto; background-color:var(--background-color-interactive,#eaecf0); color:inherit; border:2px solid var(--border-color-subtle,#c8ccd1); padding:8px; direction:ltr;" class="licensetpl_wrapper"><div class="center" style="font-weight:bold;"><div lang="en" dir="ltr" class="description en" style="display:inline;">I, the copyright holder of this work, hereby publish it under the following license:</div></div>
<div class="responsive-license-cc layouttemplate licensetpl" dir="ltr" lang="en"><div class="rlicense-icons"><span class="skin-invert" typeof="mw:File"><span title="w:en:Creative Commons"><img alt="w:en:Creative Commons" src="https://upload.wikimedia.org/wikipedia/commons/thumb/7/79/CC_some_rights_reserved.svg/120px-CC_some_rights_reserved.svg.png" decoding="async" width="90" height="36" class="mw-file-element" srcset="https://upload.wikimedia.org/wikipedia/commons/thumb/7/79/CC_some_rights_reserved.svg/250px-CC_some_rights_reserved.svg.png 1.5x" data-file-width="744" data-file-height="300"></span></span><br>
<span class="skin-invert" typeof="mw:File"><span title="attribution"><img alt="attribution" src="https://upload.wikimedia.org/wikipedia/commons/thumb/1/11/Cc-by_new_white.svg/40px-Cc-by_new_white.svg.png" decoding="async" width="24" height="24" class="mw-file-element" srcset="https://upload.wikimedia.org/wikipedia/commons/thumb/1/11/Cc-by_new_white.svg/60px-Cc-by_new_white.svg.png 2x" data-file-width="64" data-file-height="64"></span></span> <span class="skin-invert" typeof="mw:File"><span title="share alike"><img alt="share alike" src="https://upload.wikimedia.org/wikipedia/commons/thumb/d/df/Cc-sa_white.svg/40px-Cc-sa_white.svg.png" decoding="async" width="24" height="24" class="mw-file-element" srcset="https://upload.wikimedia.org/wikipedia/commons/thumb/d/df/Cc-sa_white.svg/60px-Cc-sa_white.svg.png 2x" data-file-width="64" data-file-height="64"></span></span></div><div class="rlicense-text"><div class="rlicense-declaration">This file is licensed under the <a href="https://en.wikipedia.org/wiki/en:Creative_Commons" class="extiw" title="w:en:Creative Commons">Creative Commons</a> <a href="http://creativecommons.org/licenses/by-sa/4.0/deed.en" class="extiw" title="creativecommons:by-sa/4.0/deed.en">Attribution-Share Alike 4.0 International</a> license.</div><div class="rlicense-desc" style="text-align:start;" lang="en">
<dl><dd>You are free:
<ul><li><b>to share</b> – to copy, distribute and transmit the work</li>
<li><b>to remix</b> – to adapt the work</li></ul></dd>
<dd>Under the following conditions:
<ul><li><b>attribution</b> – You must give appropriate credit, provide a link to the license, and indicate if changes were made. You may do so in any reasonable manner, but not in any way that suggests the licensor endorses you or your use.</li>
<li><b>share alike</b> – If you remix, transform, or build upon the material, you must distribute your contributions under the <a href="https://creativecommons.org/share-your-work/licensing-considerations/compatible-licenses" class="extiw" title="ccorg:share-your-work/licensing-considerations/compatible-licenses">same or compatible license</a> as the original.</li></ul></dd></dl></div><span class="licensetpl_link" style="display:none;">https://creativecommons.org/licenses/by-sa/4.0</span><span class="licensetpl_short" style="display:none;">CC BY-SA 4.0 </span><span class="licensetpl_long" style="display:none;">Creative Commons Attribution-Share Alike 4.0 </span><span class="licensetpl_link_req" style="display:none;">true</span><span class="licensetpl_attr_req" style="display:none;">true</span></div></div></div><h1 class="mw-slot-header"><mediainfoslotheader></mediainfoslotheader></h1>
<!-- 
NewPP limit report
Parsed by mw‐web.eqiad.main‐7b48b5fb74‐6j4lr
Cached time: 20250803081726
Cache expiry: 2592000
Reduced expiry: false
Complications: []
CPU time usage: 0.089 seconds
Real time usage: 0.127 seconds
Preprocessor visited node count: 246/1000000
Revision size: 469/2097152 bytes
Post‐expand include size: 11810/2097152 bytes
Template argument size: 58/2097152 bytes
Highest expansion depth: 7/100
Expensive parser function count: 1/500
Unstrip recursion depth: 0/20
Unstrip post‐expand size: 787/5000000 bytes
Lua time usage: 0.044/10.000 seconds
Lua memory usage: 1429648/52428800 bytes
Number of Wikibase entities loaded: 1/400
-->
<!--
Transclusion expansion time report (%,ms,calls,template)
100.00%  101.325      1 -total
 59.49%   60.283      1 Template:Information
 22.63%   22.931      1 Template:Location
 17.51%   17.744      1 Template:Self
  9.18%    9.303      1 Template:Cc-by-sa-layout
  7.10%    7.193      1 Template:Gl
  1.79%    1.812      1 Template:Own
  1.49%    1.513      1 Template:License_template_tag
-->

<!-- Saved in parser cache with key commonswiki:pcache:*********:|#|:idhash:wb=3!wbMobile=1 and timestamp 20250803081726 and revision id **********. Rendering was triggered because: page-view
 -->
</div>
<!-- MobileFormatter took 0.001 seconds --></div><h2 id="filehistory">File history</h2>
<div id="mw-imagepage-section-filehistory">
<p>Click on a date/time to view the file as it appeared at that time.
</p>
<table class="wikitable filehistory">
<tr><th></th><th>Date/Time</th><th>Thumbnail</th><th>Dimensions</th><th>User</th><th>Comment</th></tr>
<tr><td>current</td><td class="filehistory-selected" style="white-space: nowrap;"><a href="https://upload.wikimedia.org/wikipedia/commons/6/6b/Gustavsvik%2C_Visby_02.jpg">06:13, 9 June 2025</a></td><td><a href="https://upload.wikimedia.org/wikipedia/commons/6/6b/Gustavsvik%2C_Visby_02.jpg"><img alt="Thumbnail for version as of 06:13, 9 June 2025" src="https://upload.wikimedia.org/wikipedia/commons/thumb/6/6b/Gustavsvik%2C_Visby_02.jpg/120px-Gustavsvik%2C_Visby_02.jpg?20250609061324" decoding="async" loading="lazy" width="120" height="80" data-file-width="6000" data-file-height="4000"></a></td><td>6,000 × 4,000 <span style="white-space: nowrap;">(10.45 MB)</span></td><td><a href="https://commons.m.wikimedia.org/wiki/User:Beninho" class="mw-userlink" title="User:Beninho"><bdi>Beninho</bdi></a><span style="white-space: nowrap;"> <span class="mw-usertoollinks">(<a href="https://commons.m.wikimedia.org/wiki/User_talk:Beninho" class="mw-usertoollinks-talk" title="User talk:Beninho">talk</a> | <a href="https://commons.m.wikimedia.org/wiki/Special:Contributions/Beninho" class="mw-usertoollinks-contribs" title="Special:Contributions/Beninho">contribs</a>)</span></span></td><td dir="ltr">Uploaded own work with UploadWizard</td></tr>
</table>

</div>
<div class="mw-imagepage-upload-links"><p id="mw-imagepage-upload-disallowed">You cannot overwrite this file.</p></div><h2 id="filelinks">File usage on Commons</h2>
<div id="mw-imagepage-nolinkstoimage">
<p>There are no pages that use this file.
</p>
</div>
<!-- MobileFormatter took 0.000 seconds --><h2 id="metadata">Metadata</h2>
<div class="mw-imagepage-section-metadata"><p>This file contains additional information such as Exif metadata which may have been added by the digital camera, scanner, or software program used to create or digitize it. If the file has been modified from its original state, some details such as the timestamp may not fully reflect those of the original file. The timestamp is only as accurate as the clock in the camera, and it may be completely wrong.</p><table id="mw_metadata" class="mw_metadata collapsed">
<tbody><tr class="exif-make"><th>Camera manufacturer</th><td><a href="https://en.wikipedia.org/wiki/Canon_(company)" class="extiw" title="w:Canon (company)">Canon</a></td></tr><tr class="exif-model"><th>Camera model</th><td><a href="https://en.wikipedia.org/wiki/en:Special:Search/Canon_EOS_80D" class="extiw" title="w:en:Special:Search/Canon EOS 80D">Canon EOS 80D</a></td></tr><tr class="exif-artist"><th>Author</th><td>Bene Riobo</td></tr><tr class="exif-copyright"><th>Copyright holder</th><td><ul class="metadata-langlist"><li class="mw-metadata-lang-default"><span class="mw-metadata-lang-value">Ver detalles da licenza</span></li>
</ul></td></tr><tr class="exif-exposuretime"><th>Exposure time</th><td>1/100 sec (0.01)</td></tr><tr class="exif-fnumber"><th><a href="https://en.wikipedia.org/wiki/F-number" class="extiw" title="wikipedia:F-number">F-number</a></th><td>f/10</td></tr><tr class="exif-isospeedratings"><th>ISO speed rating</th><td>100</td></tr><tr class="exif-datetimeoriginal"><th>Date and time of data generation</th><td>13:52, 12 March 2025</td></tr><tr class="exif-focallength"><th>Lens focal length</th><td>18 mm</td></tr><tr class="exif-gpslatitude"><th>Latitude</th><td>57° 39′ 45.73″ N</td></tr><tr class="exif-gpslongitude"><th>Longitude</th><td>18° 19′ 7.17″ E</td></tr><tr class="exif-label"><th>Label</th><td></td></tr><tr class="exif-orientation mw-metadata-collapsible"><th>Orientation</th><td>Normal</td></tr><tr class="exif-xresolution mw-metadata-collapsible"><th>Horizontal resolution</th><td>240 dpi</td></tr><tr class="exif-yresolution mw-metadata-collapsible"><th>Vertical resolution</th><td>240 dpi</td></tr><tr class="exif-software mw-metadata-collapsible"><th>Software used</th><td><a href="https://en.wikipedia.org/wiki/Adobe_Photoshop_26.6_(Windows)" class="extiw" title="w:Adobe Photoshop 26.6 (Windows)">Adobe Photoshop 26.6 (Windows)</a></td></tr><tr class="exif-datetime mw-metadata-collapsible"><th>File change date and time</th><td>15:28, 25 May 2025</td></tr><tr class="exif-exposureprogram mw-metadata-collapsible"><th>Exposure Program</th><td>Manual</td></tr><tr class="exif-exifversion mw-metadata-collapsible"><th>Exif version</th><td>2.3</td></tr><tr class="exif-datetimedigitized mw-metadata-collapsible"><th>Date and time of digitizing</th><td>13:52, 12 March 2025</td></tr><tr class="exif-shutterspeedvalue mw-metadata-collapsible"><th>APEX shutter speed</th><td>6.643856</td></tr><tr class="exif-aperturevalue mw-metadata-collapsible"><th>APEX aperture</th><td>6.643856</td></tr><tr class="exif-exposurebiasvalue mw-metadata-collapsible"><th>APEX exposure bias</th><td>0</td></tr><tr class="exif-maxaperturevalue mw-metadata-collapsible"><th>Maximum land aperture</th><td>3.625 APEX (f/3.51)</td></tr><tr class="exif-meteringmode mw-metadata-collapsible"><th>Metering mode</th><td>Pattern</td></tr><tr class="exif-flash mw-metadata-collapsible"><th>Flash</th><td>Flash did not fire, compulsory flash suppression</td></tr><tr class="exif-subsectime mw-metadata-collapsible"><th>DateTime subseconds</th><td>20</td></tr><tr class="exif-subsectimeoriginal mw-metadata-collapsible"><th>DateTimeOriginal subseconds</th><td>20</td></tr><tr class="exif-subsectimedigitized mw-metadata-collapsible"><th>DateTimeDigitized subseconds</th><td>20</td></tr><tr class="exif-colorspace mw-metadata-collapsible"><th>Color space</th><td>Uncalibrated</td></tr><tr class="exif-focalplanexresolution mw-metadata-collapsible"><th>Focal plane X resolution</th><td>2,688.7265930176</td></tr><tr class="exif-focalplaneyresolution mw-metadata-collapsible"><th>Focal plane Y resolution</th><td>2,688.7265930176</td></tr><tr class="exif-focalplaneresolutionunit mw-metadata-collapsible"><th>Focal plane resolution unit</th><td>3</td></tr><tr class="exif-customrendered mw-metadata-collapsible"><th>Custom image processing</th><td>Normal process</td></tr><tr class="exif-exposuremode mw-metadata-collapsible"><th>Exposure mode</th><td>Manual exposure</td></tr><tr class="exif-whitebalance mw-metadata-collapsible"><th>White balance</th><td>Manual white balance</td></tr><tr class="exif-scenecapturetype mw-metadata-collapsible"><th>Scene capture type</th><td>Standard</td></tr><tr class="exif-gpsversionid mw-metadata-collapsible"><th>GPS tag version</th><td>0.0.2.2</td></tr><tr class="exif-serialnumber mw-metadata-collapsible"><th>Serial number of camera</th><td>303024001144</td></tr><tr class="exif-lens mw-metadata-collapsible"><th>Lens used</th><td>EF-S18-55mm f/3.5-5.6 IS STM</td></tr><tr class="exif-rating mw-metadata-collapsible"><th>Rating (out of 5)</th><td>1</td></tr><tr class="exif-datetimemetadata mw-metadata-collapsible"><th>Date metadata was last modified</th><td>17:28, 25 May 2025</td></tr><tr class="exif-originaldocumentid mw-metadata-collapsible"><th>Unique ID of original document</th><td>F44252EC72E21947821AD6B2BC546275</td></tr></tbody></table>

<!-- MobileFormatter took 0.001 seconds --></div><!--esi <esi:include src="/esitest-fa8a495983347898/content" /> --><noscript><img src="https://commons.m.wikimedia.org/wiki/Special:CentralAutoLogin/start?type=1x1&amp;usesul3=1" alt="" width="1" height="1" style="border: none; position: absolute;"></noscript></div><div role='tabpanel' aria-hidden='true' id='ooui-php-5' class='wbmi-tab oo-ui-layout oo-ui-panelLayout oo-ui-panelLayout-scrollable oo-ui-tabPanelLayout oo-ui-element-hidden' data-ooui='{"_":"OO.ui.TabPanelLayout","name":"statements","label":"Structured data","scrollable":true,"expanded":false,"classes":["wbmi-tab"]}'><h2 class="wbmi-structured-data-header">Structured data</h2><div id="P180" data-mw-property="P180" data-mw-statements="[]" data-mw-formatvalue="[]" class="wbmi-entityview-statementsGroup wbmi-entityview-statementsGroup-P180 oo-ui-layout oo-ui-panelLayout oo-ui-panelLayout-framed"><div class="wbmi-statements-widget"><div class="wbmi-statement-header"><div class="wbmi-entity-data"><div class="wbmi-entity-title"><h3 class="wbmi-statements-title">Items portrayed in this file</h3><h4 class="wbmi-entity-label"><bdi><a target="_blank" title="d:Special:EntityPage/P180" href="https://www.wikidata.org/wiki/Special:EntityPage/P180">depicts</a></bdi></h4></div></div></div><div class="wbmi-content-items-group"></div></div></div><div id="P170" data-mw-property="P170" data-mw-statements="[{&quot;mainsnak&quot;:{&quot;snaktype&quot;:&quot;somevalue&quot;,&quot;property&quot;:&quot;P170&quot;,&quot;hash&quot;:&quot;d3550e860f988c6675fff913440993f58f5c40c5&quot;},&quot;type&quot;:&quot;statement&quot;,&quot;qualifiers&quot;:{&quot;P2093&quot;:[{&quot;snaktype&quot;:&quot;value&quot;,&quot;property&quot;:&quot;P2093&quot;,&quot;hash&quot;:&quot;5bf5396799f3dfcbb6d42ffc93e9100fc0b59912&quot;,&quot;datavalue&quot;:{&quot;value&quot;:&quot;Bene Riob\u00f3&quot;,&quot;type&quot;:&quot;string&quot;}}],&quot;P4174&quot;:[{&quot;snaktype&quot;:&quot;value&quot;,&quot;property&quot;:&quot;P4174&quot;,&quot;hash&quot;:&quot;328cd1a8d4d752cc05c0597e3f3e6ad5648b810f&quot;,&quot;datavalue&quot;:{&quot;value&quot;:&quot;Beninho&quot;,&quot;type&quot;:&quot;string&quot;}}],&quot;P2699&quot;:[{&quot;snaktype&quot;:&quot;value&quot;,&quot;property&quot;:&quot;P2699&quot;,&quot;hash&quot;:&quot;0a6faea8809f7fac23b4c68ba5766a7801da41dd&quot;,&quot;datavalue&quot;:{&quot;value&quot;:&quot;https:\/\/commons.wikimedia.org\/wiki\/User:Beninho&quot;,&quot;type&quot;:&quot;string&quot;}}]},&quot;qualifiers-order&quot;:[&quot;P2093&quot;,&quot;P4174&quot;,&quot;P2699&quot;],&quot;id&quot;:&quot;M*********$BAD42779-231A-4D08-BAF8-A2BAAD13DCCB&quot;,&quot;rank&quot;:&quot;normal&quot;}]" data-mw-formatvalue="{&quot;{\&quot;value\&quot;:{\&quot;entity-type\&quot;:\&quot;property\&quot;,\&quot;numeric-id\&quot;:170,\&quot;id\&quot;:\&quot;P170\&quot;},\&quot;type\&quot;:\&quot;wikibase-entityid\&quot;}&quot;:{&quot;text\/html&quot;:{&quot;en&quot;:{&quot;&quot;:&quot;<a target=\&quot;_blank\&quot; title=\&quot;d:Special:EntityPage\/P170\&quot; href=https://commons.m.wikimedia.org/"https:////www.wikidata.org//wiki//Special:EntityPage//P170/">creator<\/a>&quot;}},&quot;text\/plain&quot;:{&quot;en&quot;:{&quot;&quot;:&quot;creator&quot;}}},&quot;{\&quot;value\&quot;:{\&quot;entity-type\&quot;:\&quot;property\&quot;,\&quot;numeric-id\&quot;:2093,\&quot;id\&quot;:\&quot;P2093\&quot;},\&quot;type\&quot;:\&quot;wikibase-entityid\&quot;}&quot;:{&quot;text\/html&quot;:{&quot;en&quot;:{&quot;&quot;:&quot;<a target=\&quot;_blank\&quot; title=\&quot;d:Special:EntityPage\/P2093\&quot; href=https://commons.m.wikimedia.org/"https:////www.wikidata.org//wiki//Special:EntityPage//P2093/">author name string<\/a>&quot;}},&quot;text\/plain&quot;:{&quot;en&quot;:{&quot;&quot;:&quot;author name string&quot;}}},&quot;{\&quot;value\&quot;:\&quot;Bene Riob\\u00f3\&quot;,\&quot;type\&quot;:\&quot;string\&quot;}&quot;:{&quot;text\/html&quot;:{&quot;en&quot;:{&quot;P2093&quot;:&quot;Bene Riob\u00f3&quot;}},&quot;text\/plain&quot;:{&quot;en&quot;:{&quot;P2093&quot;:&quot;Bene Riob\u00f3&quot;}}},&quot;{\&quot;value\&quot;:{\&quot;entity-type\&quot;:\&quot;property\&quot;,\&quot;numeric-id\&quot;:4174,\&quot;id\&quot;:\&quot;P4174\&quot;},\&quot;type\&quot;:\&quot;wikibase-entityid\&quot;}&quot;:{&quot;text\/html&quot;:{&quot;en&quot;:{&quot;&quot;:&quot;<a target=\&quot;_blank\&quot; title=\&quot;d:Special:EntityPage\/P4174\&quot; href=https://commons.m.wikimedia.org/"https:////www.wikidata.org//wiki//Special:EntityPage//P4174/">Wikimedia username<\/a>&quot;}},&quot;text\/plain&quot;:{&quot;en&quot;:{&quot;&quot;:&quot;Wikimedia username&quot;}}},&quot;{\&quot;value\&quot;:\&quot;Beninho\&quot;,\&quot;type\&quot;:\&quot;string\&quot;}&quot;:{&quot;text\/html&quot;:{&quot;en&quot;:{&quot;P4174&quot;:&quot;<a target=\&quot;_blank\&quot; class=\&quot;wb-external-id external\&quot; href=https://commons.m.wikimedia.org/"https:////www.wikidata.org//wiki//Special:CentralAuth?target=Beninho\%22 rel=\&quot;nofollow\&quot;>Beninho<\/a>&quot;}},&quot;text\/plain&quot;:{&quot;en&quot;:{&quot;P4174&quot;:&quot;Beninho&quot;}}},&quot;{\&quot;value\&quot;:{\&quot;entity-type\&quot;:\&quot;property\&quot;,\&quot;numeric-id\&quot;:2699,\&quot;id\&quot;:\&quot;P2699\&quot;},\&quot;type\&quot;:\&quot;wikibase-entityid\&quot;}&quot;:{&quot;text\/html&quot;:{&quot;en&quot;:{&quot;&quot;:&quot;<a target=\&quot;_blank\&quot; title=\&quot;d:Special:EntityPage\/P2699\&quot; href=https://commons.m.wikimedia.org/"https:////www.wikidata.org//wiki//Special:EntityPage//P2699/">URL<\/a>&quot;}},&quot;text\/plain&quot;:{&quot;en&quot;:{&quot;&quot;:&quot;URL&quot;}}},&quot;{\&quot;value\&quot;:\&quot;https:\\\/\\\/commons.wikimedia.org\\\/wiki\\\/User:Beninho\&quot;,\&quot;type\&quot;:\&quot;string\&quot;}&quot;:{&quot;text\/html&quot;:{&quot;en&quot;:{&quot;P2699&quot;:&quot;<a target=\&quot;_blank\&quot; rel=\&quot;nofollow\&quot; class=\&quot;external free\&quot; href=https://commons.m.wikimedia.org/"https:////commons.wikimedia.org//wiki//User:Beninho/">https:\/\/commons.wikimedia.org\/wiki\/User:Beninho<\/a>&quot;}},&quot;text\/plain&quot;:{&quot;en&quot;:{&quot;P2699&quot;:&quot;https:\/\/commons.wikimedia.org\/wiki\/User:Beninho&quot;}}}}" class="wbmi-entityview-statementsGroup wbmi-entityview-statementsGroup-P170 oo-ui-layout oo-ui-panelLayout oo-ui-panelLayout-framed"><div class="wbmi-statements-widget"><div class="wbmi-statement-header"><div class="wbmi-entity-data"><div class="wbmi-entity-title"><h4 class="wbmi-entity-label"><bdi><a target="_blank" title="d:Special:EntityPage/P170" href="https://www.wikidata.org/wiki/Special:EntityPage/P170">creator</a></bdi></h4></div></div></div><div class="wbmi-content-items-group"><div class="wbmi-item wbmi-item-read"><div class="wbmi-item-container"><div class="wbmi-entity-header"><div class="wbmi-entity-data"><div class="wbmi-entity-title"><h4 class="wbmi-entity-label"><bdi>some value</bdi></h4></div></div></div><div class="wbmi-item-qualifiers"><div class="wbmi-snaklist-container"><div class="wbmi-snaklist-content"><div class="wbmi-snak"><div class="wbmi-snak-value"><a target="_blank" title="d:Special:EntityPage/P2093" href="https://www.wikidata.org/wiki/Special:EntityPage/P2093">author name string</a><span class="wbmi-snak-value-separator">: </span><span class="wbmi-snak-value--value">Bene Riobó</span></div></div><div class="wbmi-snak"><div class="wbmi-snak-value"><a target="_blank" title="d:Special:EntityPage/P4174" href="https://www.wikidata.org/wiki/Special:EntityPage/P4174">Wikimedia username</a><span class="wbmi-snak-value-separator">: </span><span class="wbmi-snak-value--value"><a target="_blank" class="wb-external-id external" href="https://www.wikidata.org/wiki/Special:CentralAuth?target=Beninho" rel="nofollow">Beninho</a></span></div></div><div class="wbmi-snak"><div class="wbmi-snak-value"><a target="_blank" title="d:Special:EntityPage/P2699" href="https://www.wikidata.org/wiki/Special:EntityPage/P2699">URL</a><span class="wbmi-snak-value-separator">: </span><span class="wbmi-snak-value--value"><a target="_blank" rel="nofollow" class="external free" href="https://commons.wikimedia.org/wiki/User:Beninho">https://commons.wikimedia.org/wiki/User:Beninho</a></span></div></div></div></div></div></div></div></div></div></div><div id="P6216" data-mw-property="P6216" data-mw-statements="[{&quot;mainsnak&quot;:{&quot;snaktype&quot;:&quot;value&quot;,&quot;property&quot;:&quot;P6216&quot;,&quot;hash&quot;:&quot;5570347fdc76d2a80732f51ea10ee4b144a084e0&quot;,&quot;datavalue&quot;:{&quot;value&quot;:{&quot;entity-type&quot;:&quot;item&quot;,&quot;numeric-id&quot;:50423863,&quot;id&quot;:&quot;Q50423863&quot;},&quot;type&quot;:&quot;wikibase-entityid&quot;}},&quot;type&quot;:&quot;statement&quot;,&quot;id&quot;:&quot;M*********$82867E78-923E-4215-9CF7-4E077DEF21B0&quot;,&quot;rank&quot;:&quot;normal&quot;}]" data-mw-formatvalue="{&quot;{\&quot;value\&quot;:{\&quot;entity-type\&quot;:\&quot;property\&quot;,\&quot;numeric-id\&quot;:6216,\&quot;id\&quot;:\&quot;P6216\&quot;},\&quot;type\&quot;:\&quot;wikibase-entityid\&quot;}&quot;:{&quot;text\/html&quot;:{&quot;en&quot;:{&quot;&quot;:&quot;<a target=\&quot;_blank\&quot; title=\&quot;d:Special:EntityPage\/P6216\&quot; href=https://commons.m.wikimedia.org/"https:////www.wikidata.org//wiki//Special:EntityPage//P6216/">copyright status<\/a>&quot;}},&quot;text\/plain&quot;:{&quot;en&quot;:{&quot;&quot;:&quot;copyright status&quot;}}},&quot;{\&quot;value\&quot;:{\&quot;entity-type\&quot;:\&quot;item\&quot;,\&quot;numeric-id\&quot;:50423863,\&quot;id\&quot;:\&quot;Q50423863\&quot;},\&quot;type\&quot;:\&quot;wikibase-entityid\&quot;}&quot;:{&quot;text\/html&quot;:{&quot;en&quot;:{&quot;P6216&quot;:&quot;<a target=\&quot;_blank\&quot; title=\&quot;d:Special:EntityPage\/Q50423863\&quot; href=https://commons.m.wikimedia.org/"https:////www.wikidata.org//wiki//Special:EntityPage//Q50423863/">copyrighted<\/a>&quot;}},&quot;text\/plain&quot;:{&quot;en&quot;:{&quot;P6216&quot;:&quot;copyrighted&quot;}}}}" class="wbmi-entityview-statementsGroup wbmi-entityview-statementsGroup-P6216 oo-ui-layout oo-ui-panelLayout oo-ui-panelLayout-framed"><div class="wbmi-statements-widget"><div class="wbmi-statement-header"><div class="wbmi-entity-data"><div class="wbmi-entity-title"><h4 class="wbmi-entity-label"><bdi><a target="_blank" title="d:Special:EntityPage/P6216" href="https://www.wikidata.org/wiki/Special:EntityPage/P6216">copyright status</a></bdi></h4></div></div></div><div class="wbmi-content-items-group"><div class="wbmi-item wbmi-item-read"><div class="wbmi-item-container"><div class="wbmi-entity-header"><div class="wbmi-entity-data"><div class="wbmi-entity-title"><h4 class="wbmi-entity-label"><bdi><a target="_blank" title="d:Special:EntityPage/Q50423863" href="https://www.wikidata.org/wiki/Special:EntityPage/Q50423863">copyrighted</a></bdi></h4></div></div></div></div></div></div></div></div><div id="P275" data-mw-property="P275" data-mw-statements="[{&quot;mainsnak&quot;:{&quot;snaktype&quot;:&quot;value&quot;,&quot;property&quot;:&quot;P275&quot;,&quot;hash&quot;:&quot;ec6e754c5042e13b53376139e505ebd6708745a4&quot;,&quot;datavalue&quot;:{&quot;value&quot;:{&quot;entity-type&quot;:&quot;item&quot;,&quot;numeric-id&quot;:18199165,&quot;id&quot;:&quot;*********&quot;},&quot;type&quot;:&quot;wikibase-entityid&quot;}},&quot;type&quot;:&quot;statement&quot;,&quot;id&quot;:&quot;M*********$1C64663C-42DE-46F5-A44E-70FD5EF42698&quot;,&quot;rank&quot;:&quot;normal&quot;}]" data-mw-formatvalue="{&quot;{\&quot;value\&quot;:{\&quot;entity-type\&quot;:\&quot;property\&quot;,\&quot;numeric-id\&quot;:275,\&quot;id\&quot;:\&quot;P275\&quot;},\&quot;type\&quot;:\&quot;wikibase-entityid\&quot;}&quot;:{&quot;text\/html&quot;:{&quot;en&quot;:{&quot;&quot;:&quot;<a target=\&quot;_blank\&quot; title=\&quot;d:Special:EntityPage\/P275\&quot; href=https://commons.m.wikimedia.org/"https:////www.wikidata.org//wiki//Special:EntityPage//P275/">copyright license<\/a>&quot;}},&quot;text\/plain&quot;:{&quot;en&quot;:{&quot;&quot;:&quot;copyright license&quot;}}},&quot;{\&quot;value\&quot;:{\&quot;entity-type\&quot;:\&quot;item\&quot;,\&quot;numeric-id\&quot;:18199165,\&quot;id\&quot;:\&quot;*********\&quot;},\&quot;type\&quot;:\&quot;wikibase-entityid\&quot;}&quot;:{&quot;text\/html&quot;:{&quot;en&quot;:{&quot;P275&quot;:&quot;<a target=\&quot;_blank\&quot; title=\&quot;d:Special:EntityPage\/*********\&quot; href=https://commons.m.wikimedia.org/"https:////www.wikidata.org//wiki//Special:EntityPage//*********/">Creative Commons Attribution-ShareAlike 4.0 International<\/a>&quot;}},&quot;text\/plain&quot;:{&quot;en&quot;:{&quot;P275&quot;:&quot;Creative Commons Attribution-ShareAlike 4.0 International&quot;}}}}" class="wbmi-entityview-statementsGroup wbmi-entityview-statementsGroup-P275 oo-ui-layout oo-ui-panelLayout oo-ui-panelLayout-framed"><div class="wbmi-statements-widget"><div class="wbmi-statement-header"><div class="wbmi-entity-data"><div class="wbmi-entity-title"><h4 class="wbmi-entity-label"><bdi><a target="_blank" title="d:Special:EntityPage/P275" href="https://www.wikidata.org/wiki/Special:EntityPage/P275">copyright license</a></bdi></h4></div></div></div><div class="wbmi-content-items-group"><div class="wbmi-item wbmi-item-read"><div class="wbmi-item-container"><div class="wbmi-entity-header"><div class="wbmi-entity-data"><div class="wbmi-entity-title"><h4 class="wbmi-entity-label"><bdi><a target="_blank" title="d:Special:EntityPage/*********" href="https://www.wikidata.org/wiki/Special:EntityPage/*********">Creative Commons Attribution-ShareAlike 4.0 International</a></bdi></h4></div></div></div></div></div></div></div></div><div id="P571" data-mw-property="P571" data-mw-statements="[{&quot;mainsnak&quot;:{&quot;snaktype&quot;:&quot;value&quot;,&quot;property&quot;:&quot;P571&quot;,&quot;hash&quot;:&quot;057351ede3101f4a1ea84c2461d4d53cf22b4e0f&quot;,&quot;datavalue&quot;:{&quot;value&quot;:{&quot;time&quot;:&quot;+2025-03-12T00:00:00Z&quot;,&quot;timezone&quot;:0,&quot;before&quot;:0,&quot;after&quot;:0,&quot;precision&quot;:11,&quot;calendarmodel&quot;:&quot;http:\/\/www.wikidata.org\/entity\/Q1985727&quot;},&quot;type&quot;:&quot;time&quot;}},&quot;type&quot;:&quot;statement&quot;,&quot;id&quot;:&quot;M*********$F49BA185-7E19-4BA6-A9FF-1B0101B90776&quot;,&quot;rank&quot;:&quot;normal&quot;}]" data-mw-formatvalue="{&quot;{\&quot;value\&quot;:{\&quot;entity-type\&quot;:\&quot;property\&quot;,\&quot;numeric-id\&quot;:571,\&quot;id\&quot;:\&quot;P571\&quot;},\&quot;type\&quot;:\&quot;wikibase-entityid\&quot;}&quot;:{&quot;text\/html&quot;:{&quot;en&quot;:{&quot;&quot;:&quot;<a target=\&quot;_blank\&quot; title=\&quot;d:Special:EntityPage\/P571\&quot; href=https://commons.m.wikimedia.org/"https:////www.wikidata.org//wiki//Special:EntityPage//P571/">inception<\/a>&quot;}},&quot;text\/plain&quot;:{&quot;en&quot;:{&quot;&quot;:&quot;inception&quot;}}},&quot;{\&quot;value\&quot;:{\&quot;time\&quot;:\&quot;+2025-03-12T00:00:00Z\&quot;,\&quot;timezone\&quot;:0,\&quot;before\&quot;:0,\&quot;after\&quot;:0,\&quot;precision\&quot;:11,\&quot;calendarmodel\&quot;:\&quot;http:\\\/\\\/www.wikidata.org\\\/entity\\\/Q1985727\&quot;},\&quot;type\&quot;:\&quot;time\&quot;}&quot;:{&quot;text\/html&quot;:{&quot;en&quot;:{&quot;P571&quot;:&quot;12 March 2025&quot;}},&quot;text\/plain&quot;:{&quot;en&quot;:{&quot;P571&quot;:&quot;12 March 2025&quot;}}}}" class="wbmi-entityview-statementsGroup wbmi-entityview-statementsGroup-P571 oo-ui-layout oo-ui-panelLayout oo-ui-panelLayout-framed"><div class="wbmi-statements-widget"><div class="wbmi-statement-header"><div class="wbmi-entity-data"><div class="wbmi-entity-title"><h4 class="wbmi-entity-label"><bdi><a target="_blank" title="d:Special:EntityPage/P571" href="https://www.wikidata.org/wiki/Special:EntityPage/P571">inception</a></bdi></h4></div></div></div><div class="wbmi-content-items-group"><div class="wbmi-item wbmi-item-read"><div class="wbmi-item-container"><div class="wbmi-entity-header"><div class="wbmi-entity-data"><div class="wbmi-entity-title"><h4 class="wbmi-entity-label"><bdi>12 March 2025</bdi></h4></div></div></div></div></div></div></div></div><div id="P7482" data-mw-property="P7482" data-mw-statements="[{&quot;mainsnak&quot;:{&quot;snaktype&quot;:&quot;value&quot;,&quot;property&quot;:&quot;P7482&quot;,&quot;hash&quot;:&quot;83568a288a8b8b4714a68e7239d8406833762864&quot;,&quot;datavalue&quot;:{&quot;value&quot;:{&quot;entity-type&quot;:&quot;item&quot;,&quot;numeric-id&quot;:66458942,&quot;id&quot;:&quot;Q66458942&quot;},&quot;type&quot;:&quot;wikibase-entityid&quot;}},&quot;type&quot;:&quot;statement&quot;,&quot;id&quot;:&quot;M*********$8E68680E-B116-479E-9E8E-19A3334589BC&quot;,&quot;rank&quot;:&quot;normal&quot;}]" data-mw-formatvalue="{&quot;{\&quot;value\&quot;:{\&quot;entity-type\&quot;:\&quot;property\&quot;,\&quot;numeric-id\&quot;:7482,\&quot;id\&quot;:\&quot;P7482\&quot;},\&quot;type\&quot;:\&quot;wikibase-entityid\&quot;}&quot;:{&quot;text\/html&quot;:{&quot;en&quot;:{&quot;&quot;:&quot;<a target=\&quot;_blank\&quot; title=\&quot;d:Special:EntityPage\/P7482\&quot; href=https://commons.m.wikimedia.org/"https:////www.wikidata.org//wiki//Special:EntityPage//P7482/">source of file<\/a>&quot;}},&quot;text\/plain&quot;:{&quot;en&quot;:{&quot;&quot;:&quot;source of file&quot;}}},&quot;{\&quot;value\&quot;:{\&quot;entity-type\&quot;:\&quot;item\&quot;,\&quot;numeric-id\&quot;:66458942,\&quot;id\&quot;:\&quot;Q66458942\&quot;},\&quot;type\&quot;:\&quot;wikibase-entityid\&quot;}&quot;:{&quot;text\/html&quot;:{&quot;en&quot;:{&quot;P7482&quot;:&quot;<a target=\&quot;_blank\&quot; title=\&quot;d:Special:EntityPage\/Q66458942\&quot; href=https://commons.m.wikimedia.org/"https:////www.wikidata.org//wiki//Special:EntityPage//Q66458942/">original creation by uploader<\/a>&quot;}},&quot;text\/plain&quot;:{&quot;en&quot;:{&quot;P7482&quot;:&quot;original creation by uploader&quot;}}}}" class="wbmi-entityview-statementsGroup wbmi-entityview-statementsGroup-P7482 oo-ui-layout oo-ui-panelLayout oo-ui-panelLayout-framed"><div class="wbmi-statements-widget"><div class="wbmi-statement-header"><div class="wbmi-entity-data"><div class="wbmi-entity-title"><h4 class="wbmi-entity-label"><bdi><a target="_blank" title="d:Special:EntityPage/P7482" href="https://www.wikidata.org/wiki/Special:EntityPage/P7482">source of file</a></bdi></h4></div></div></div><div class="wbmi-content-items-group"><div class="wbmi-item wbmi-item-read"><div class="wbmi-item-container"><div class="wbmi-entity-header"><div class="wbmi-entity-data"><div class="wbmi-entity-title"><h4 class="wbmi-entity-label"><bdi><a target="_blank" title="d:Special:EntityPage/Q66458942" href="https://www.wikidata.org/wiki/Special:EntityPage/Q66458942">original creation by uploader</a></bdi></h4></div></div></div></div></div></div></div></div><div id="P1259" data-mw-property="P1259" data-mw-statements="[{&quot;mainsnak&quot;:{&quot;snaktype&quot;:&quot;value&quot;,&quot;property&quot;:&quot;P1259&quot;,&quot;hash&quot;:&quot;f9335cf7c3271d04f729b04ec322058df3ff2ddf&quot;,&quot;datavalue&quot;:{&quot;value&quot;:{&quot;latitude&quot;:57.662703,&quot;longitude&quot;:18.318657,&quot;altitude&quot;:null,&quot;precision&quot;:1.0e-6,&quot;globe&quot;:&quot;http:\/\/www.wikidata.org\/entity\/Q2&quot;},&quot;type&quot;:&quot;globecoordinate&quot;}},&quot;type&quot;:&quot;statement&quot;,&quot;id&quot;:&quot;M*********$649E523F-321A-48C5-A927-3F8DA4CC77C8&quot;,&quot;rank&quot;:&quot;normal&quot;}]" data-mw-formatvalue="{&quot;{\&quot;value\&quot;:{\&quot;entity-type\&quot;:\&quot;property\&quot;,\&quot;numeric-id\&quot;:1259,\&quot;id\&quot;:\&quot;P1259\&quot;},\&quot;type\&quot;:\&quot;wikibase-entityid\&quot;}&quot;:{&quot;text\/html&quot;:{&quot;en&quot;:{&quot;&quot;:&quot;<a target=\&quot;_blank\&quot; title=\&quot;d:Special:EntityPage\/P1259\&quot; href=https://commons.m.wikimedia.org/"https:////www.wikidata.org//wiki//Special:EntityPage//P1259/">coordinates of the point of view<\/a>&quot;}},&quot;text\/plain&quot;:{&quot;en&quot;:{&quot;&quot;:&quot;coordinates of the point of view&quot;}}},&quot;{\&quot;value\&quot;:{\&quot;latitude\&quot;:57.662703,\&quot;longitude\&quot;:18.318657,\&quot;altitude\&quot;:null,\&quot;precision\&quot;:1.0e-6,\&quot;globe\&quot;:\&quot;http:\\\/\\\/www.wikidata.org\\\/entity\\\/Q2\&quot;},\&quot;type\&quot;:\&quot;globecoordinate\&quot;}&quot;:{&quot;text\/html&quot;:{&quot;en&quot;:{&quot;P1259&quot;:&quot;57\u00b039&amp;apos;45.731&amp;quot;N, 18\u00b019&amp;apos;7.165&amp;quot;E&quot;}},&quot;text\/plain&quot;:{&quot;en&quot;:{&quot;P1259&quot;:&quot;57\u00b039'45.731\&quot;N, 18\u00b019'7.165\&quot;E&quot;}}}}" class="wbmi-entityview-statementsGroup wbmi-entityview-statementsGroup-P1259 oo-ui-layout oo-ui-panelLayout oo-ui-panelLayout-framed"><div class="wbmi-statements-widget"><div class="wbmi-statement-header"><div class="wbmi-entity-data"><div class="wbmi-entity-title"><h4 class="wbmi-entity-label"><bdi><a target="_blank" title="d:Special:EntityPage/P1259" href="https://www.wikidata.org/wiki/Special:EntityPage/P1259">coordinates of the point of view</a></bdi></h4></div></div></div><div class="wbmi-content-items-group"><div class="wbmi-item wbmi-item-read"><div class="wbmi-item-container"><div class="wbmi-entity-header"><div class="wbmi-entity-data"><div class="wbmi-entity-title"><h4 class="wbmi-entity-label"><bdi>57°39'45.731"N, 18°19'7.165"E</bdi></h4></div></div></div></div></div></div></div></div><div id="P4082" data-mw-property="P4082" data-mw-statements="[{&quot;mainsnak&quot;:{&quot;snaktype&quot;:&quot;value&quot;,&quot;property&quot;:&quot;P4082&quot;,&quot;hash&quot;:&quot;3538538ca6ffbabc87f697a2e886951459b9db71&quot;,&quot;datavalue&quot;:{&quot;value&quot;:{&quot;entity-type&quot;:&quot;item&quot;,&quot;numeric-id&quot;:22815060,&quot;id&quot;:&quot;Q22815060&quot;},&quot;type&quot;:&quot;wikibase-entityid&quot;}},&quot;type&quot;:&quot;statement&quot;,&quot;id&quot;:&quot;M*********$E2F08FEE-FBD9-4075-AAE0-99FDADFC8A65&quot;,&quot;rank&quot;:&quot;normal&quot;}]" data-mw-formatvalue="{&quot;{\&quot;value\&quot;:{\&quot;entity-type\&quot;:\&quot;property\&quot;,\&quot;numeric-id\&quot;:4082,\&quot;id\&quot;:\&quot;P4082\&quot;},\&quot;type\&quot;:\&quot;wikibase-entityid\&quot;}&quot;:{&quot;text\/html&quot;:{&quot;en&quot;:{&quot;&quot;:&quot;<a target=\&quot;_blank\&quot; title=\&quot;d:Special:EntityPage\/P4082\&quot; href=https://commons.m.wikimedia.org/"https:////www.wikidata.org//wiki//Special:EntityPage//P4082/">captured with<\/a>&quot;}},&quot;text\/plain&quot;:{&quot;en&quot;:{&quot;&quot;:&quot;captured with&quot;}}},&quot;{\&quot;value\&quot;:{\&quot;entity-type\&quot;:\&quot;item\&quot;,\&quot;numeric-id\&quot;:22815060,\&quot;id\&quot;:\&quot;Q22815060\&quot;},\&quot;type\&quot;:\&quot;wikibase-entityid\&quot;}&quot;:{&quot;text\/html&quot;:{&quot;en&quot;:{&quot;P4082&quot;:&quot;<a target=\&quot;_blank\&quot; title=\&quot;d:Special:EntityPage\/Q22815060\&quot; href=https://commons.m.wikimedia.org/"https:////www.wikidata.org//wiki//Special:EntityPage//Q22815060/">Canon EOS 80D<\/a>&quot;}},&quot;text\/plain&quot;:{&quot;en&quot;:{&quot;P4082&quot;:&quot;Canon EOS 80D&quot;}}}}" class="wbmi-entityview-statementsGroup wbmi-entityview-statementsGroup-P4082 oo-ui-layout oo-ui-panelLayout oo-ui-panelLayout-framed"><div class="wbmi-statements-widget"><div class="wbmi-statement-header"><div class="wbmi-entity-data"><div class="wbmi-entity-title"><h4 class="wbmi-entity-label"><bdi><a target="_blank" title="d:Special:EntityPage/P4082" href="https://www.wikidata.org/wiki/Special:EntityPage/P4082">captured with</a></bdi></h4></div></div></div><div class="wbmi-content-items-group"><div class="wbmi-item wbmi-item-read"><div class="wbmi-item-container"><div class="wbmi-entity-header"><div class="wbmi-entity-data"><div class="wbmi-entity-title"><h4 class="wbmi-entity-label"><bdi><a target="_blank" title="d:Special:EntityPage/Q22815060" href="https://www.wikidata.org/wiki/Special:EntityPage/Q22815060">Canon EOS 80D</a></bdi></h4></div></div></div></div></div></div></div></div></div></div></div></div></div>
<div class="printfooter" data-nosnippet="">Retrieved from "<a dir="ltr" href="https://commons.wikimedia.org/w/index.php?title=File:Gustavsvik,_Visby_02.jpg&amp;oldid=**********">https://commons.wikimedia.org/w/index.php?title=File:Gustavsvik,_Visby_02.jpg&amp;oldid=**********</a>"</div></div>
				
			</div>
			<div class="post-content" id="page-secondary-actions">
			</div>
		</main>
		<footer class="mw-footer minerva-footer" role="contentinfo">
		  <a class="last-modified-bar" href="https://commons.m.wikimedia.org/w/index.php?title=File:Gustavsvik,_Visby_02.jpg&amp;action=history">
  	<div class="post-content last-modified-bar__content">
  		 <span class="minerva-icon minerva-icon-size-medium minerva-icon--modified-history"></span>
 
  		<span class="last-modified-bar__text modified-enhancement"
  				data-user-name="BotMultichillT"
  				data-user-gender="unknown"
  				data-timestamp="1749450491">
  				<span>Last edited on 9 June 2025, at 06:28</span>
  		</span>
  		 <span class="minerva-icon minerva-icon-size-small minerva-icon--expand"></span>
 
  	</div>
  </a>
	<div class="post-content footer-content">
			
			<div id="p-lang">
	    <h4>Languages</h4>
	    <section>
	        <ul id="p-variants" class="minerva-languages"></ul>
	        <p>This page is not available in other languages.</p>
	    </section>
	</div>
	<div class="minerva-footer-logo">
				<img src="https://commons.m.wikimedia.org/static/images/mobile/copyright/commonswiki-wordmark.svg" alt="Wikimedia Commons" width="115" height="32"
	style="width: 7.1875em; height: 2em;"/>


				<ul id="footer-icons" class="footer-icons">
	<li id="footer-copyrightico"><a href="https://www.wikimedia.org/" class="cdx-button cdx-button--fake-button cdx-button--size-large cdx-button--fake-button--enabled"><picture><source media="(min-width: 500px)" srcset="/static/images/footer/wikimedia-button.svg" width="84" height="29"><img src="https://commons.m.wikimedia.org/static/images/footer/wikimedia.svg" width="25" height="25" alt="Wikimedia Foundation" lang="en" loading="lazy"></picture></a></li>
	<li id="footer-poweredbyico"><a href="https://www.mediawiki.org/" class="cdx-button cdx-button--fake-button cdx-button--size-large cdx-button--fake-button--enabled"><picture><source media="(min-width: 500px)" srcset="/w/resources/assets/poweredby_mediawiki.svg" width="88" height="31"><img src="https://commons.m.wikimedia.org/w/resources/assets/mediawiki_compact.svg" alt="Powered by MediaWiki" lang="en" width="25" height="25" loading="lazy"></picture></a></li>
</ul>
			</div>
			<ul id="footer-info" class="footer-info hlist hlist-separated">
	<li id="footer-info-lastmod"> This page was last edited on 9 June 2025, at 06:28.</li>
	<li id="footer-info-copyright">Files are available under licenses specified on their description page. All structured data from the file namespace is available under the <a rel="nofollow" class="external text" href="https://creativecommons.org/publicdomain/zero/1.0/">Creative Commons CC0 License</a>; all unstructured text is available under the <a rel="nofollow" class="external text" href="https://creativecommons.org/licenses/by-sa/4.0/">Creative Commons Attribution-ShareAlike License</a>;
additional terms may apply.
By using this site, you agree to the <a class="external text" href="https://foundation.wikimedia.org/wiki/Special:MyLanguage/Policy:Terms_of_Use">Terms of Use</a> and the <a class="external text" href="https://foundation.wikimedia.org/wiki/Special:MyLanguage/Policy:Privacy_policy">Privacy Policy</a>.</li>
</ul>

			<ul id="footer-places" class="footer-places hlist hlist-separated">
	<li id="footer-places-privacy"><a href="https://foundation.wikimedia.org/wiki/Special:MyLanguage/Policy:Privacy_policy">Privacy policy</a></li>
	<li id="footer-places-about"><a href="https://commons.m.wikimedia.org/wiki/Commons:Welcome">About Wikimedia Commons</a></li>
	<li id="footer-places-disclaimers"><a href="https://commons.m.wikimedia.org/wiki/Commons:General_disclaimer">Disclaimers</a></li>
	<li id="footer-places-wm-codeofconduct"><a href="https://foundation.wikimedia.org/wiki/Special:MyLanguage/Policy:Universal_Code_of_Conduct">Code of Conduct</a></li>
	<li id="footer-places-developers"><a href="https://developer.wikimedia.org/">Developers</a></li>
	<li id="footer-places-statslink"><a href="https://stats.wikimedia.org/#/commons.wikimedia.org">Statistics</a></li>
	<li id="footer-places-cookiestatement"><a href="https://foundation.wikimedia.org/wiki/Special:MyLanguage/Policy:Cookie_statement">Cookie statement</a></li>
	<li id="footer-places-terms-use"><a href="https://foundation.m.wikimedia.org/wiki/Special:MyLanguage/Policy:Terms_of_Use">Terms of Use</a></li>
	<li id="footer-places-desktop-toggle"><a id="mw-mf-display-toggle" href="http://commons.wikimedia.org/w/index.php?title=File:Gustavsvik,_Visby_02.jpg&amp;mobileaction=toggle_view_desktop" data-event-name="switch_to_desktop">Desktop</a></li>
</ul>

			</div>
		</footer>
			</div>
</div>
<div class="mw-portlet mw-portlet-dock-bottom emptyPortlet" id="p-dock-bottom">
        <ul>
                
        </ul>
</div>
<div class="mw-notification-area" data-mw="interface"></div>
<script>(RLQ=window.RLQ||[]).push(function(){mw.config.set({"wgHostname":"mw-web.eqiad.main-7b48b5fb74-6j4lr","wgBackendResponseTime":438,"wgPageParseReport":{"limitreport":{"cputime":"0.089","walltime":"0.127","ppvisitednodes":{"value":246,"limit":1000000},"revisionsize":{"value":469,"limit":2097152},"postexpandincludesize":{"value":11810,"limit":2097152},"templateargumentsize":{"value":58,"limit":2097152},"expansiondepth":{"value":7,"limit":100},"expensivefunctioncount":{"value":1,"limit":500},"unstrip-depth":{"value":0,"limit":20},"unstrip-size":{"value":787,"limit":5000000},"entityaccesscount":{"value":1,"limit":400},"timingprofile":["100.00%  101.325      1 -total"," 59.49%   60.283      1 Template:Information"," 22.63%   22.931      1 Template:Location"," 17.51%   17.744      1 Template:Self","  9.18%    9.303      1 Template:Cc-by-sa-layout","  7.10%    7.193      1 Template:Gl","  1.79%    1.812      1 Template:Own","  1.49%    1.513      1 Template:License_template_tag"]},"scribunto":{"limitreport-timeusage":{"value":"0.044","limit":"10.000"},"limitreport-memusage":{"value":1429648,"limit":52428800}},"cachereport":{"origin":"mw-web.eqiad.main-7b48b5fb74-6j4lr","timestamp":"20250803081726","ttl":2592000,"transientcontent":false}}});});</script>
<script type="application/ld+json">{"@context":"https:\/\/schema.org","@type":"ImageObject","contentUrl":"https:\/\/upload.wikimedia.org\/wikipedia\/commons\/6\/6b\/Gustavsvik%2C_Visby_02.jpg","license":"https:\/\/creativecommons.org\/licenses\/by-sa\/4.0","acquireLicensePage":"\/\/commons.wikimedia.org\/wiki\/File:Gustavsvik,_Visby_02.jpg","uploadDate":"2025-06-09 06:13:24"}</script><script>(window.NORLQ=window.NORLQ||[]).push(function(){var ns,i,p,img;ns=document.getElementsByTagName('noscript');for(i=0;i<ns.length;i++){p=ns[i].nextSibling;if(p&&p.className&&p.className.indexOf('lazy-image-placeholder')>-1){img=document.createElement('img');img.setAttribute('src',p.getAttribute('data-mw-src'));img.setAttribute('width',p.getAttribute('data-width'));img.setAttribute('height',p.getAttribute('data-height'));img.setAttribute('alt',p.getAttribute('data-alt'));p.parentNode.replaceChild(img,p);}}});</script>
</body>

<!-- Mirrored from commons.m.wikimedia.org/wiki/File:Gustavsvik,_Visby_02.jpg by HTTrack Website Copier/3.x [XR&CO'2014], Sun, 03 Aug 2025 15:20:55 GMT -->
</html>