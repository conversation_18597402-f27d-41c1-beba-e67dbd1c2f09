<!DOCTYPE html>
<html class="client-nojs vector-feature-language-in-header-disabled vector-feature-language-in-main-page-header-disabled vector-feature-page-tools-pinned-disabled vector-feature-toc-pinned-clientpref-1 vector-feature-main-menu-pinned-disabled vector-feature-limited-width-clientpref-1 vector-feature-limited-width-content-disabled vector-feature-custom-font-size-clientpref-1 vector-feature-appearance-pinned-clientpref-1 vector-feature-night-mode-enabled skin-theme-clientpref-day vector-sticky-header-enabled vector-toc-not-available" lang="en" dir="ltr">

<!-- Mirrored from commons.wikimedia.org/w/index.php?title=File:Madafu-chopping.jpg&action=edit by HTTrack Website Copier/3.x [XR&CO'2014], Sun, 03 Aug 2025 08:17:51 GMT -->
<!-- Added by HTTrack --><meta http-equiv="content-type" content="text/html;charset=UTF-8" /><!-- /Added by HTTrack -->
<head>
<meta charset="UTF-8">
<title>Editing File:Madafu-chopping.jpg - Wikimedia Commons</title>
<script>(function(){var className="client-js vector-feature-language-in-header-disabled vector-feature-language-in-main-page-header-disabled vector-feature-page-tools-pinned-disabled vector-feature-toc-pinned-clientpref-1 vector-feature-main-menu-pinned-disabled vector-feature-limited-width-clientpref-1 vector-feature-limited-width-content-disabled vector-feature-custom-font-size-clientpref-1 vector-feature-appearance-pinned-clientpref-1 vector-feature-night-mode-enabled skin-theme-clientpref-day vector-sticky-header-enabled vector-toc-not-available";var cookie=document.cookie.match(/(?:^|; )commonswikimwclientpreferences=([^;]+)/);if(cookie){cookie[1].split('%2C').forEach(function(pref){className=className.replace(new RegExp('(^| )'+pref.replace(/-clientpref-\w+$|[^\w-]+/g,'')+'-clientpref-\\w+( |$)'),'$1'+pref+'$2');});}document.documentElement.className=className;}());RLCONF={"wgBreakFrames":true,"wgSeparatorTransformTable":["",""],"wgDigitTransformTable":["",""],"wgDefaultDateFormat":"dmy","wgMonthNames":["","January","February","March","April","May","June","July","August","September","October","November","December"],"wgRequestId":"118bb94a-0077-48f3-b4ef-483b264ba2c6","wgCanonicalNamespace":"File","wgCanonicalSpecialPageName":false,"wgNamespaceNumber":6,"wgPageName":"File:Madafu-chopping.jpg","wgTitle":"Madafu-chopping.jpg","wgCurRevisionId":1033630111,"wgRevisionId":0,"wgArticleId":36601495,"wgIsArticle":false,"wgIsRedirect":false,"wgAction":"edit","wgUserName":null,"wgUserGroups":["*"],"wgCategories":[],"wgPageViewLanguage":"en","wgPageContentLanguage":"en","wgPageContentModel":"wikitext","wgRelevantPageName":"File:Madafu-chopping.jpg","wgRelevantArticleId":36601495,"wgIsProbablyEditable":true,"wgRelevantPageIsProbablyEditable":true,"wgRestrictionEdit":[],"wgRestrictionMove":[],"wgRestrictionUpload":[],"wgNoticeProject":"commons","wgCiteReferencePreviewsActive":true,"wgMediaViewerOnClick":true,"wgMediaViewerEnabledByDefault":false,"wgVisualEditor":{"pageLanguageCode":"en","pageLanguageDir":"ltr","pageVariantFallbacks":"en"},"wgMFDisplayWikibaseDescriptions":{"search":true,"watchlist":true,"tagline":true,"nearby":true},"wgWMESchemaEditAttemptStepOversample":false,"wgWMEPageLength":400,"wgMetricsPlatformUserExperiments":{"active_experiments":[],"overrides":[],"enrolled":[],"assigned":[],"subject_ids":[],"sampling_units":[]},"wgEditMessage":"editing","wgEditSubmitButtonLabelPublish":true,"upwizPropertyTitles":{"P180":"Main subjects visible in this work"},"upwizPropertyPlaceholders":{"P180":"e.g., Angolan giraffe"},"upwizPropertyCopyLabels":{"P180":"Main subjects"},"wgULSPosition":"personal","wgULSisCompactLinksEnabled":true,"wgVector2022LanguageInHeader":false,"wgULSisLanguageSelectorEmpty":false,"wbmiDefaultProperties":["P180"],"wbmiPropertyTitles":{"P180":"Items portrayed in this file"},"wbmiPropertyTypes":{"P180":"wikibase-item"},"wbmiRepoApiUrl":"/w/api.php","wbmiHelpUrls":{"P180":"https://commons.wikimedia.org/wiki/Special:MyLanguage/Commons:Depicts"},"wbmiExternalEntitySearchBaseUri":"https://www.wikidata.org/w/api.php","wbmiSupportedDataTypes":["wikibase-item","string","quantity","time","monolingualtext","external-id","globe-coordinate","url"],"wgCheckUserClientHintsHeadersJsApi":["brands","architecture","bitness","fullVersionList","mobile","model","platform","platformVersion"]};
RLSTATE={"ext.gadget.Long-Image-Names-in-Categories":"ready","ext.globalCssJs.user.styles":"ready","site.styles":"ready","user.styles":"ready","ext.globalCssJs.user":"ready","user":"ready","user.options":"loading","ext.wikiEditor.styles":"ready","mediawiki.action.edit.styles":"ready","mediawiki.codex.messagebox.styles":"ready","mediawiki.editfont.styles":"ready","mediawiki.interface.helpers.styles":"ready","oojs-ui-core.styles":"ready","oojs-ui.styles.indicators":"ready","mediawiki.widgets.styles":"ready","oojs-ui-core.icons":"ready","skins.vector.search.codex.styles":"ready","skins.vector.styles":"ready","skins.vector.icons":"ready","filepage":"ready","ext.charinsert.styles":"ready","ext.wikimediamessages.styles":"ready","ext.visualEditor.desktopArticleTarget.noscript":"ready","ext.uls.pt":"ready"};RLPAGEMODULES=["ext.xLab","ext.cite.wikiEditor","ext.wikiEditor","ext.wikiEditor.realtimepreview","ext.CodeMirror.WikiEditor","ext.templateData.templateDiscovery","ext.TemplateWizard","ext.disambiguator","mediawiki.action.edit","mediawiki.action.edit.editWarning","wikibase.client.action.edit.collapsibleFooter","mediawiki.action.edit.collapsibleFooter","site","mediawiki.page.ready","skins.vector.html","ext.centralNotice.geoIP","ext.charinsert","ext.gadget.Slideshow","ext.gadget.ZoomViewer","ext.gadget.CollapsibleTemplates","ext.gadget.Stockphoto","ext.gadget.WatchlistNotice","ext.gadget.AjaxQuickDelete","ext.gadget.WikiMiniAtlas","ext.gadget.LanguageSelect","ext.gadget.PictureOfTheYearEnhancements","ext.urlShortener.toolbar","ext.centralauth.centralautologin","mmv.bootstrap","ext.visualEditor.desktopArticleTarget.init","ext.visualEditor.targetLoader","ext.echo.centralauth","ext.eventLogging","ext.wikimediaEvents","ext.wikimediaEvents.wikibase","ext.navigationTiming","ext.uls.compactlinks","ext.uls.interface","ext.checkUser.clientHints"];</script>
<script>(RLQ=window.RLQ||[]).push(function(){mw.loader.impl(function(){return["user.options@12s5i",function($,jQuery,require,module){mw.user.tokens.set({"patrolToken":"+\\","watchToken":"+\\","csrfToken":"+\\"});
}];});});</script>
<link rel="stylesheet" href="load1ab8.css?lang=en&amp;modules=ext.charinsert.styles%7Cext.uls.pt%7Cext.visualEditor.desktopArticleTarget.noscript%7Cext.wikimediamessages.styles%7Cfilepage%7Cmediawiki.action.edit.styles%7Cmediawiki.codex.messagebox.styles%7Cmediawiki.editfont.styles%7Cmediawiki.interface.helpers.styles%7Cmediawiki.widgets.styles%7Coojs-ui-core.icons%2Cstyles%7Coojs-ui.styles.indicators%7Cskins.vector.icons%2Cstyles%7Cskins.vector.search.codex.styles&amp;only=styles&amp;skin=vector-2022">
<link rel="stylesheet" href="load6be4.css?lang=en&amp;modules=ext.wikiEditor.styles&amp;only=styles&amp;skin=vector-2022">
<script async="" src="load9565.php?lang=en&amp;modules=startup&amp;only=scripts&amp;raw=1&amp;skin=vector-2022"></script>
<meta name="ResourceLoaderDynamicStyles" content="">
<link rel="stylesheet" href="load3e3b.css?lang=en&amp;modules=ext.gadget.Long-Image-Names-in-Categories&amp;only=styles&amp;skin=vector-2022">
<link rel="stylesheet" href="loada24d.css?lang=en&amp;modules=site.styles&amp;only=styles&amp;skin=vector-2022">
<meta name="generator" content="MediaWiki 1.45.0-wmf.12">
<meta name="referrer" content="origin">
<meta name="referrer" content="origin-when-cross-origin">
<meta name="robots" content="noindex,nofollow,max-image-preview:standard">
<meta name="format-detection" content="telephone=no">
<meta property="og:image" content="https://upload.wikimedia.org/wikipedia/commons/thumb/5/5f/Madafu-chopping.jpg/1200px-Madafu-chopping.jpg">
<meta property="og:image:width" content="1200">
<meta property="og:image:height" content="904">
<meta property="og:image" content="https://upload.wikimedia.org/wikipedia/commons/thumb/5/5f/Madafu-chopping.jpg/960px-Madafu-chopping.jpg">
<meta property="og:image:width" content="800">
<meta property="og:image:height" content="603">
<meta property="og:image:width" content="640">
<meta property="og:image:height" content="482">
<meta name="viewport" content="width=1120">
<meta property="og:title" content="Editing File:Madafu-chopping.jpg - Wikimedia Commons">
<meta property="og:type" content="website">
<link rel="alternate" media="only screen and (max-width: 640px)" href="http://commons.m.wikimedia.org/wiki/File:Madafu-chopping.jpg">
<link rel="alternate" type="application/x-wiki" title="Edit" href="https://commons.wikimedia.org/w/index.php?title=File:Madafu-chopping.jpg&amp;action=edit">
<link rel="apple-touch-icon" href="https://commons.wikimedia.org/static/apple-touch/commons.png">
<link rel="icon" href="https://commons.wikimedia.org/static/favicon/commons.ico">
<link rel="search" type="application/opensearchdescription+xml" href="https://commons.wikimedia.org/w/rest.php/v1/search" title="Wikimedia Commons">
<link rel="EditURI" type="application/rsd+xml" href="http://commons.wikimedia.org/w/api.php?action=rsd">
<link rel="canonical" href="https://commons.wikimedia.org/wiki/File:Madafu-chopping.jpg">
<link rel="license" href="https://creativecommons.org/licenses/by-sa/4.0/">
<link rel="alternate" type="application/atom+xml" title="Wikimedia Commons Atom feed" href="https://commons.wikimedia.org/w/index.php?title=Special:RecentChanges&amp;feed=atom">
<link rel="dns-prefetch" href="https://commons.wikimedia.org/w/auth.wikimedia.org">
</head>
<body class="skin--responsive skin-vector skin-vector-search-vue mediawiki ltr sitedir-ltr mw-hide-empty-elt ns-6 ns-subject mw-editable page-File_Madafu-chopping_jpg rootpage-File_Madafu-chopping_jpg skin-vector-2022 action-edit"><a class="mw-jump-link" href="#bodyContent">Jump to content</a>
<div class="vector-header-container">
	<header class="vector-header mw-header no-font-mode-scale">
		<div class="vector-header-start">
			<nav class="vector-main-menu-landmark" aria-label="Site">
				
<div id="vector-main-menu-dropdown" class="vector-dropdown vector-main-menu-dropdown vector-button-flush-left vector-button-flush-right"  title="Main menu" >
	<input type="checkbox" id="vector-main-menu-dropdown-checkbox" role="button" aria-haspopup="true" data-event-name="ui.dropdown-vector-main-menu-dropdown" class="vector-dropdown-checkbox "  aria-label="Main menu"  >
	<label id="vector-main-menu-dropdown-label" for="vector-main-menu-dropdown-checkbox" class="vector-dropdown-label cdx-button cdx-button--fake-button cdx-button--fake-button--enabled cdx-button--weight-quiet cdx-button--icon-only " aria-hidden="true"  ><span class="vector-icon mw-ui-icon-menu mw-ui-icon-wikimedia-menu"></span>

<span class="vector-dropdown-label-text">Main menu</span>
	</label>
	<div class="vector-dropdown-content">


				<div id="vector-main-menu-unpinned-container" class="vector-unpinned-container">
		
<div id="vector-main-menu" class="vector-main-menu vector-pinnable-element">
	<div
	class="vector-pinnable-header vector-main-menu-pinnable-header vector-pinnable-header-unpinned"
	data-feature-name="main-menu-pinned"
	data-pinnable-element-id="vector-main-menu"
	data-pinned-container-id="vector-main-menu-pinned-container"
	data-unpinned-container-id="vector-main-menu-unpinned-container"
>
	<div class="vector-pinnable-header-label">Main menu</div>
	<button class="vector-pinnable-header-toggle-button vector-pinnable-header-pin-button" data-event-name="pinnable-header.vector-main-menu.pin">move to sidebar</button>
	<button class="vector-pinnable-header-toggle-button vector-pinnable-header-unpin-button" data-event-name="pinnable-header.vector-main-menu.unpin">hide</button>
</div>

	
<div id="p-navigation" class="vector-menu mw-portlet mw-portlet-navigation"  >
	<div class="vector-menu-heading">
		Navigate
	</div>
	<div class="vector-menu-content">
		
		<ul class="vector-menu-content-list">
			
			<li id="n-mainpage-description" class="mw-list-item"><a href="https://commons.wikimedia.org/wiki/Main_Page" title="Visit the main page [z]" accesskey="z"><span>Main page</span></a></li><li id="n-welcome" class="mw-list-item"><a href="https://commons.wikimedia.org/wiki/Commons:Welcome"><span>Welcome</span></a></li><li id="n-portal" class="mw-list-item"><a href="https://commons.wikimedia.org/wiki/Commons:Community_portal" title="About the project, what you can do, where to find things"><span>Community portal</span></a></li><li id="n-village-pump" class="mw-list-item"><a href="https://commons.wikimedia.org/wiki/Commons:Village_pump"><span>Village pump</span></a></li><li id="n-help" class="mw-list-item"><a href="https://commons.wikimedia.org/wiki/Special:MyLanguage/Help:Contents" title="The place to find out"><span>Help center</span></a></li>
		</ul>
		
	</div>
</div>

	
	
<div id="p-participate" class="vector-menu mw-portlet mw-portlet-participate"  >
	<div class="vector-menu-heading">
		Participate
	</div>
	<div class="vector-menu-content">
		
		<ul class="vector-menu-content-list">
			
			<li id="n-uploadbtn" class="mw-list-item"><a href="https://commons.wikimedia.org/wiki/Special:UploadWizard"><span>Upload file</span></a></li><li id="n-recentchanges" class="mw-list-item"><a href="https://commons.wikimedia.org/wiki/Special:RecentChanges" title="A list of recent changes in the wiki [r]" accesskey="r"><span>Recent changes</span></a></li><li id="n-latestfiles" class="mw-list-item"><a href="https://commons.wikimedia.org/wiki/Special:NewFiles"><span>Latest files</span></a></li><li id="n-randomimage" class="mw-list-item"><a href="https://commons.wikimedia.org/wiki/Special:Random/File" title="Load a random file [x]" accesskey="x"><span>Random file</span></a></li><li id="n-contact" class="mw-list-item"><a href="https://commons.wikimedia.org/wiki/Commons:Contact_us"><span>Contact us</span></a></li><li id="n-specialpages" class="mw-list-item"><a href="https://commons.wikimedia.org/wiki/Special:SpecialPages"><span>Special pages</span></a></li>
		</ul>
		
	</div>
</div>

	
<div id="vector-main-menu" class="vector-menu "  >
	<div class="vector-menu-heading">
		
	</div>
	<div class="vector-menu-content">
		
		<ul class="vector-menu-content-list">
			
			
		</ul>
		
	</div>
</div>

</div>

				</div>

	</div>
</div>

		</nav>
			
<a href="https://commons.wikimedia.org/wiki/Main_Page" class="mw-logo">
	<img class="mw-logo-icon" src="https://commons.wikimedia.org/static/images/icons/commonswiki.svg" alt="" aria-hidden="true" height="50" width="50">
	<span class="mw-logo-container skin-invert">
		<img class="mw-logo-wordmark" alt="Wikimedia Commons" src="https://commons.wikimedia.org/static/images/mobile/copyright/commonswiki-wordmark.svg" style="width: 7.1875em; height: 2em;">
	</span>
</a>

		</div>
		<div class="vector-header-end">
			
<div id="p-search" role="search" class="vector-search-box-vue  vector-search-box-collapses vector-search-box">
	<a href="https://commons.wikimedia.org/wiki/Special:MediaSearch" class="cdx-button cdx-button--fake-button cdx-button--fake-button--enabled cdx-button--weight-quiet cdx-button--icon-only search-toggle" title="Search Wikimedia Commons [f]" accesskey="f"><span class="vector-icon mw-ui-icon-search mw-ui-icon-wikimedia-search"></span>

<span>Search</span>
	</a>
	<div class="vector-typeahead-search-container">
		<div class="cdx-typeahead-search">
			<form action="https://commons.wikimedia.org/w/index.php" id="searchform" class="cdx-search-input cdx-search-input--has-end-button">
				<div id="simpleSearch" class="cdx-search-input__input-wrapper"  data-search-loc="header-moved">
					<div class="cdx-text-input cdx-text-input--has-start-icon">
						<input
							class="cdx-text-input__input mw-searchInput" autocomplete="off"
							 type="search" name="search" placeholder="Search Wikimedia Commons" aria-label="Search Wikimedia Commons" autocapitalize="sentences" spellcheck="false" title="Search Wikimedia Commons [f]" accesskey="f" id="searchInput"
							>
						<span class="cdx-text-input__icon cdx-text-input__start-icon"></span>
					</div>
					<input type="hidden" name="title" value="Special:MediaSearch">
				</div>
				<button class="cdx-button cdx-search-input__end-button">Search</button>
			</form>
		</div>
	</div>
</div>

			<nav class="vector-user-links vector-user-links-wide" aria-label="Personal tools">
	<div class="vector-user-links-main">
	
<div id="p-vector-user-menu-preferences" class="vector-menu mw-portlet"  >
	<div class="vector-menu-content">
		
		<ul class="vector-menu-content-list">
			<li id="ca-uls" class="mw-list-item active user-links-collapsible-item"><a data-mw="interface" href="#" class="uls-trigger cdx-button cdx-button--fake-button cdx-button--fake-button--enabled cdx-button--weight-quiet"><span class="vector-icon mw-ui-icon-wikimedia-language mw-ui-icon-wikimedia-wikimedia-language"></span>

<span>English</span></a>
</li>

			
		</ul>
		
	</div>
</div>

	
<div id="p-vector-user-menu-userpage" class="vector-menu mw-portlet emptyPortlet"  >
	<div class="vector-menu-content">
		
		<ul class="vector-menu-content-list">
			
			
		</ul>
		
	</div>
</div>

	<nav class="vector-appearance-landmark" aria-label="Appearance">
		
<div id="vector-appearance-dropdown" class="vector-dropdown "  title="Change the appearance of the page&#039;s font size, width, and color" >
	<input type="checkbox" id="vector-appearance-dropdown-checkbox" role="button" aria-haspopup="true" data-event-name="ui.dropdown-vector-appearance-dropdown" class="vector-dropdown-checkbox "  aria-label="Appearance"  >
	<label id="vector-appearance-dropdown-label" for="vector-appearance-dropdown-checkbox" class="vector-dropdown-label cdx-button cdx-button--fake-button cdx-button--fake-button--enabled cdx-button--weight-quiet cdx-button--icon-only " aria-hidden="true"  ><span class="vector-icon mw-ui-icon-appearance mw-ui-icon-wikimedia-appearance"></span>

<span class="vector-dropdown-label-text">Appearance</span>
	</label>
	<div class="vector-dropdown-content">


			<div id="vector-appearance-unpinned-container" class="vector-unpinned-container">
				
			</div>
		
	</div>
</div>

	</nav>
	
<div id="p-vector-user-menu-notifications" class="vector-menu mw-portlet emptyPortlet"  >
	<div class="vector-menu-content">
		
		<ul class="vector-menu-content-list">
			
			
		</ul>
		
	</div>
</div>

	
<div id="p-vector-user-menu-overflow" class="vector-menu mw-portlet"  >
	<div class="vector-menu-content">
		
		<ul class="vector-menu-content-list">
			<li id="pt-sitesupport-2" class="user-links-collapsible-item mw-list-item user-links-collapsible-item"><a data-mw="interface" href="https://donate.wikimedia.org/?wmf_source=donate&amp;wmf_medium=sidebar&amp;wmf_campaign=commons.wikimedia.org&amp;uselang=en" class=""><span>Donate</span></a>
</li>
<li id="pt-createaccount-2" class="user-links-collapsible-item mw-list-item user-links-collapsible-item"><a data-mw="interface" href="https://commons.wikimedia.org/w/index.php?title=Special:CreateAccount&amp;returnto=File%3AMadafu-chopping.jpg&amp;returntoquery=action%3Dedit" title="You are encouraged to create an account and log in; however, it is not mandatory" class=""><span>Create account</span></a>
</li>
<li id="pt-login-2" class="user-links-collapsible-item mw-list-item user-links-collapsible-item"><a data-mw="interface" href="https://commons.wikimedia.org/w/index.php?title=Special:UserLogin&amp;returnto=File%3AMadafu-chopping.jpg&amp;returntoquery=action%3Dedit" title="You are encouraged to log in; however, it is not mandatory [o]" accesskey="o" class=""><span>Log in</span></a>
</li>

			
		</ul>
		
	</div>
</div>

	</div>
	
<div id="vector-user-links-dropdown" class="vector-dropdown vector-user-menu vector-button-flush-right vector-user-menu-logged-out"  title="More options" >
	<input type="checkbox" id="vector-user-links-dropdown-checkbox" role="button" aria-haspopup="true" data-event-name="ui.dropdown-vector-user-links-dropdown" class="vector-dropdown-checkbox "  aria-label="Personal tools"  >
	<label id="vector-user-links-dropdown-label" for="vector-user-links-dropdown-checkbox" class="vector-dropdown-label cdx-button cdx-button--fake-button cdx-button--fake-button--enabled cdx-button--weight-quiet cdx-button--icon-only " aria-hidden="true"  ><span class="vector-icon mw-ui-icon-ellipsis mw-ui-icon-wikimedia-ellipsis"></span>

<span class="vector-dropdown-label-text">Personal tools</span>
	</label>
	<div class="vector-dropdown-content">


		
<div id="p-personal" class="vector-menu mw-portlet mw-portlet-personal user-links-collapsible-item"  title="User menu" >
	<div class="vector-menu-content">
		
		<ul class="vector-menu-content-list">
			
			<li id="pt-sitesupport" class="user-links-collapsible-item mw-list-item"><a href="https://donate.wikimedia.org/?wmf_source=donate&amp;wmf_medium=sidebar&amp;wmf_campaign=commons.wikimedia.org&amp;uselang=en"><span>Donate</span></a></li><li id="pt-createaccount" class="user-links-collapsible-item mw-list-item"><a href="https://commons.wikimedia.org/w/index.php?title=Special:CreateAccount&amp;returnto=File%3AMadafu-chopping.jpg&amp;returntoquery=action%3Dedit" title="You are encouraged to create an account and log in; however, it is not mandatory"><span class="vector-icon mw-ui-icon-userAdd mw-ui-icon-wikimedia-userAdd"></span> <span>Create account</span></a></li><li id="pt-login" class="user-links-collapsible-item mw-list-item"><a href="https://commons.wikimedia.org/w/index.php?title=Special:UserLogin&amp;returnto=File%3AMadafu-chopping.jpg&amp;returntoquery=action%3Dedit" title="You are encouraged to log in; however, it is not mandatory [o]" accesskey="o"><span class="vector-icon mw-ui-icon-logIn mw-ui-icon-wikimedia-logIn"></span> <span>Log in</span></a></li>
		</ul>
		
	</div>
</div>

<div id="p-user-menu-anon-editor" class="vector-menu mw-portlet mw-portlet-user-menu-anon-editor"  >
	<div class="vector-menu-heading">
		Pages for logged out editors <a href="https://commons.wikimedia.org/wiki/Help:Introduction" aria-label="Learn more about editing"><span>learn more</span></a>
	</div>
	<div class="vector-menu-content">
		
		<ul class="vector-menu-content-list">
			
			<li id="pt-anoncontribs" class="mw-list-item"><a href="https://commons.wikimedia.org/wiki/Special:MyContributions" title="A list of edits made from this IP address [y]" accesskey="y"><span>Contributions</span></a></li><li id="pt-anontalk" class="mw-list-item"><a href="https://commons.wikimedia.org/wiki/Special:MyTalk" title="Discussion about edits from this IP address [n]" accesskey="n"><span>Talk</span></a></li>
		</ul>
		
	</div>
</div>

	
	</div>
</div>

</nav>

		</div>
	</header>
</div>
<div class="mw-page-container">
	<div class="mw-page-container-inner">
		<div class="vector-sitenotice-container">
			<div id="siteNotice"><!-- CentralNotice --></div>
		</div>
		<div class="vector-column-start">
			<div class="vector-main-menu-container">
		<div id="mw-navigation">
			<nav id="mw-panel" class="vector-main-menu-landmark" aria-label="Site">
				<div id="vector-main-menu-pinned-container" class="vector-pinned-container">
				
				</div>
		</nav>
		</div>
	</div>
</div>
		<div class="mw-content-container">
			<main id="content" class="mw-body">
				<header class="mw-body-header vector-page-titlebar no-font-mode-scale">
					<h1 id="firstHeading" class="firstHeading mw-first-heading">Editing <span id="firstHeadingTitle">File:Madafu-chopping.jpg</span></h1>
						<div class="mw-indicators">
		</div>
</header>
				<div class="vector-page-toolbar vector-feature-custom-font-size-clientpref--excluded">
					<div class="vector-page-toolbar-container">
						<div id="left-navigation">
							<nav aria-label="Namespaces">
								
<div id="p-associated-pages" class="vector-menu vector-menu-tabs mw-portlet mw-portlet-associated-pages"  >
	<div class="vector-menu-content">
		
		<ul class="vector-menu-content-list">
			
			<li id="ca-nstab-image" class="selected vector-tab-noicon mw-list-item"><a href="https://commons.wikimedia.org/wiki/File:Madafu-chopping.jpg" title="View the file page [c]" accesskey="c"><span>File</span></a></li><li id="ca-talk" class="new vector-tab-noicon mw-list-item"><a href="https://commons.wikimedia.org/w/index.php?title=File_talk:Madafu-chopping.jpg&amp;action=edit&amp;redlink=1" rel="discussion" class="new" title="Discussion about the content page (page does not exist) [t]" accesskey="t"><span>Discussion</span></a></li>
		</ul>
		
	</div>
</div>

								
<div id="vector-variants-dropdown" class="vector-dropdown emptyPortlet"  >
	<input type="checkbox" id="vector-variants-dropdown-checkbox" role="button" aria-haspopup="true" data-event-name="ui.dropdown-vector-variants-dropdown" class="vector-dropdown-checkbox " aria-label="Change language variant"   >
	<label id="vector-variants-dropdown-label" for="vector-variants-dropdown-checkbox" class="vector-dropdown-label cdx-button cdx-button--fake-button cdx-button--fake-button--enabled cdx-button--weight-quiet" aria-hidden="true"  ><span class="vector-dropdown-label-text">English</span>
	</label>
	<div class="vector-dropdown-content">


					
<div id="p-variants" class="vector-menu mw-portlet mw-portlet-variants emptyPortlet"  >
	<div class="vector-menu-content">
		
		<ul class="vector-menu-content-list">
			
			
		</ul>
		
	</div>
</div>

				
	</div>
</div>

							</nav>
						</div>
						<div id="right-navigation" class="vector-collapsible">
							<nav aria-label="Views">
								
<div id="p-views" class="vector-menu vector-menu-tabs mw-portlet mw-portlet-views"  >
	<div class="vector-menu-content">
		
		<ul class="vector-menu-content-list">
			
			<li id="ca-view" class="vector-tab-noicon mw-list-item"><a href="https://commons.wikimedia.org/wiki/File:Madafu-chopping.jpg"><span>Read</span></a></li><li id="ca-edit" class="selected vector-tab-noicon mw-list-item"><a href="https://commons.wikimedia.org/w/index.php?title=File:Madafu-chopping.jpg&amp;action=edit" title="Edit this page"><span>Edit</span></a></li><li id="ca-history" class="vector-tab-noicon mw-list-item"><a href="https://commons.wikimedia.org/w/index.php?title=File:Madafu-chopping.jpg&amp;action=history" title="Past revisions of this page [h]" accesskey="h"><span>View history</span></a></li>
		</ul>
		
	</div>
</div>

							</nav>
				
							<nav class="vector-page-tools-landmark" aria-label="Page tools">
								
<div id="vector-page-tools-dropdown" class="vector-dropdown vector-page-tools-dropdown"  >
	<input type="checkbox" id="vector-page-tools-dropdown-checkbox" role="button" aria-haspopup="true" data-event-name="ui.dropdown-vector-page-tools-dropdown" class="vector-dropdown-checkbox "  aria-label="Tools"  >
	<label id="vector-page-tools-dropdown-label" for="vector-page-tools-dropdown-checkbox" class="vector-dropdown-label cdx-button cdx-button--fake-button cdx-button--fake-button--enabled cdx-button--weight-quiet" aria-hidden="true"  ><span class="vector-dropdown-label-text">Tools</span>
	</label>
	<div class="vector-dropdown-content">


									<div id="vector-page-tools-unpinned-container" class="vector-unpinned-container">
						
<div id="vector-page-tools" class="vector-page-tools vector-pinnable-element">
	<div
	class="vector-pinnable-header vector-page-tools-pinnable-header vector-pinnable-header-unpinned"
	data-feature-name="page-tools-pinned"
	data-pinnable-element-id="vector-page-tools"
	data-pinned-container-id="vector-page-tools-pinned-container"
	data-unpinned-container-id="vector-page-tools-unpinned-container"
>
	<div class="vector-pinnable-header-label">Tools</div>
	<button class="vector-pinnable-header-toggle-button vector-pinnable-header-pin-button" data-event-name="pinnable-header.vector-page-tools.pin">move to sidebar</button>
	<button class="vector-pinnable-header-toggle-button vector-pinnable-header-unpin-button" data-event-name="pinnable-header.vector-page-tools.unpin">hide</button>
</div>

	
<div id="p-cactions" class="vector-menu mw-portlet mw-portlet-cactions emptyPortlet vector-has-collapsible-items"  title="More options" >
	<div class="vector-menu-heading">
		Actions
	</div>
	<div class="vector-menu-content">
		
		<ul class="vector-menu-content-list">
			
			<li id="ca-more-view" class="vector-more-collapsible-item mw-list-item"><a href="https://commons.wikimedia.org/wiki/File:Madafu-chopping.jpg"><span>Read</span></a></li><li id="ca-more-edit" class="selected vector-more-collapsible-item mw-list-item"><a href="https://commons.wikimedia.org/w/index.php?title=File:Madafu-chopping.jpg&amp;action=edit" title="Edit this page [e]" accesskey="e"><span>Edit</span></a></li><li id="ca-more-history" class="vector-more-collapsible-item mw-list-item"><a href="https://commons.wikimedia.org/w/index.php?title=File:Madafu-chopping.jpg&amp;action=history"><span>View history</span></a></li>
		</ul>
		
	</div>
</div>

<div id="p-tb" class="vector-menu mw-portlet mw-portlet-tb"  >
	<div class="vector-menu-heading">
		General
	</div>
	<div class="vector-menu-content">
		
		<ul class="vector-menu-content-list">
			
			<li id="t-whatlinkshere" class="mw-list-item"><a href="https://commons.wikimedia.org/wiki/Special:WhatLinksHere/File:Madafu-chopping.jpg" title="A list of all wiki pages that link here [j]" accesskey="j"><span>What links here</span></a></li><li id="t-recentchangeslinked" class="mw-list-item"><a href="https://commons.wikimedia.org/wiki/Special:RecentChangesLinked/File:Madafu-chopping.jpg" rel="nofollow" title="Recent changes in pages linked from this page [k]" accesskey="k"><span>Related changes</span></a></li><li id="t-info" class="mw-list-item"><a href="https://commons.wikimedia.org/w/index.php?title=File:Madafu-chopping.jpg&amp;action=info" title="More information about this page"><span>Page information</span></a></li><li id="t-urlshortener" class="mw-list-item"><a href="https://commons.wikimedia.org/w/index.php?title=Special:UrlShortener&amp;url=https%3A%2F%2Fcommons.wikimedia.org%2Fw%2Findex.php%3Ftitle%3DFile%3AMadafu-chopping.jpg%26action%3Dedit"><span>Get shortened URL</span></a></li><li id="t-urlshortener-qrcode" class="mw-list-item"><a href="https://commons.wikimedia.org/w/index.php?title=Special:QrCode&amp;url=https%3A%2F%2Fcommons.wikimedia.org%2Fw%2Findex.php%3Ftitle%3DFile%3AMadafu-chopping.jpg%26action%3Dedit"><span>Download QR code</span></a></li><li id="t-wb-concept-uri" class="mw-list-item"><a href="https://commons.wikimedia.org/entity/M36601495" title="URI that identifies the concept described by this Item"><span>Concept URI</span></a></li>
		</ul>
		
	</div>
</div>

<div id="p-wikibase-otherprojects" class="vector-menu mw-portlet mw-portlet-wikibase-otherprojects emptyPortlet"  >
	<div class="vector-menu-heading">
		In other projects
	</div>
	<div class="vector-menu-content">
		
		<ul class="vector-menu-content-list">
			
			
		</ul>
		
	</div>
</div>

</div>

									</div>
				
	</div>
</div>

							</nav>
						</div>
					</div>
				</div>
				<div class="vector-column-end no-font-mode-scale">
					<div class="vector-sticky-pinned-container">
						<nav class="vector-page-tools-landmark" aria-label="Page tools">
							<div id="vector-page-tools-pinned-container" class="vector-pinned-container">
				
							</div>
		</nav>
						<nav class="vector-appearance-landmark" aria-label="Appearance">
							<div id="vector-appearance-pinned-container" class="vector-pinned-container">
				<div id="vector-appearance" class="vector-appearance vector-pinnable-element">
	<div
	class="vector-pinnable-header vector-appearance-pinnable-header vector-pinnable-header-pinned"
	data-feature-name="appearance-pinned"
	data-pinnable-element-id="vector-appearance"
	data-pinned-container-id="vector-appearance-pinned-container"
	data-unpinned-container-id="vector-appearance-unpinned-container"
>
	<div class="vector-pinnable-header-label">Appearance</div>
	<button class="vector-pinnable-header-toggle-button vector-pinnable-header-pin-button" data-event-name="pinnable-header.vector-appearance.pin">move to sidebar</button>
	<button class="vector-pinnable-header-toggle-button vector-pinnable-header-unpin-button" data-event-name="pinnable-header.vector-appearance.unpin">hide</button>
</div>


</div>

							</div>
		</nav>
					</div>
				</div>
				<div id="bodyContent" class="vector-body" aria-labelledby="firstHeading" data-mw-ve-target-container>
					<div class="vector-body-before-content">
					
						
					</div>
					<div id="contentSub"><div id="mw-content-subtitle"></div></div>
					
					
					<div id="mw-content-text" class="mw-body-content"><div class="cdx-message--warning mw-anon-edit-warning cdx-message cdx-message--block"><span class="cdx-message__icon"></span><div class="cdx-message__content">You are not logged in and your <a href="https://en.wikipedia.org/wiki/IP_address" class="extiw" title="w:IP address">IP address</a> will be publicly visible if you make any edits. <a href="https://commons.wikimedia.org/wiki/Special:UserLogin" title="Special:UserLogin">Logging in</a> or <a href="https://commons.wikimedia.org/wiki/Special:CreateAccount" title="Special:CreateAccount">creating an account</a> will conceal your IP address and provide you with many other <a href="https://en.wikipedia.org/wiki/Wikipedia:Why_create_an_account%3F" class="extiw" title="w:Wikipedia:Why create an account?">benefits</a>. Please do not save test edits. If you want to experiment, please use the <a href="https://commons.wikimedia.org/wiki/Commons:Sandbox" title="Commons:Sandbox">Sandbox</a>.</div></div><div id="wikiPreview" class="ontop" style="display: none;"></div><form class="mw-editform" id="editform" name="editform" method="post" action="https://commons.wikimedia.org/w/index.php?title=File:Madafu-chopping.jpg&amp;action=submit" enctype="multipart/form-data" data-mw-editform-type="initial"><input type="hidden" value="ℳ𝒲♥𝓊𝓃𝒾𝒸ℴ𝒹ℯ" name="wpUnicodeCheck"><div id="antispam-container" style="display: none;"><label for="wpAntispam">Anti-spam check.
Do <strong>not</strong> fill this in!</label><input name="wpAntispam" id="wpAntispam"></div><input id="wikieditorUsed" type="hidden" name="wikieditorUsed"><input id="editingStatsId" type="hidden" value="5752004d2cfffec848b84fc997a73713" name="editingStatsId"><input type="hidden" name="wpSection"><input type="hidden" value="20250803074323" name="wpStarttime"><input type="hidden" value="20250520224137" name="wpEdittime"><input type="hidden" value="1033630111" name="editRevId"><input id="wpScrolltop" type="hidden" name="wpScrolltop"><input type="hidden" value="d41d8cd98f00b204e9800998ecf8427e" name="wpAutoSummary"><input type="hidden" value="0" name="oldid"><input type="hidden" value="1033630111" name="parentRevId"><input type="hidden" value="text/x-wiki" name="format"><input type="hidden" value="wikitext" name="model"><textarea aria-label="Wikitext source editor" tabindex="1" class="mw-editfont-monospace" accesskey="," id="wpTextbox1" cols="80" rows="25" lang="en" dir="ltr" name="wpTextbox1">=={{int:filedesc}}==
{{Information
|description={{en|1=Madafu}}{{ig|1=Akụ oyibo}}{{Wiki Loves Africa 2014 country|KE}}
|date=2014-11-04
|source={{own}}
|author=[[User:Kungu01|kungu irungu]]
|permission=
|other versions=
}}

=={{int:license-header}}==
{{self|cc-by-sa-4.0}}

{{Wiki Loves Africa 2014}}
[[Category:Uploaded via Campaign:wlafrica]]
[[Category:Food preparation in Kenya]]
</textarea><div class='editOptions'>
<div id='wpSummaryLabel' class='mw-summary oo-ui-layout oo-ui-labelElement oo-ui-fieldLayout oo-ui-fieldLayout-align-top'><div class='oo-ui-fieldLayout-body'><span class='oo-ui-fieldLayout-header'><label for='wpSummary' class='oo-ui-labelElement-label'><a href="https://www.mediawiki.org/wiki/Special:MyLanguage/Help:Edit_summary" class="extiw" title="mw:Special:MyLanguage/Help:Edit summary">Edit summary</a>:&#160;</label></span><div class='oo-ui-fieldLayout-field'><div id='wpSummaryWidget' class='oo-ui-widget oo-ui-widget-enabled oo-ui-inputWidget oo-ui-textInputWidget oo-ui-textInputWidget-type-text oo-ui-textInputWidget-php' data-ooui='{"_":"OO.ui.TextInputWidget","maxLength":500,"name":"wpSummary","inputId":"wpSummary","tabIndex":1,"title":"Enter a short summary","accessKey":"b","required":false}'><input type='text' tabindex='1' title='Enter a short summary [b]' accesskey='b' name='wpSummary' value='' id='wpSummary' maxlength='500' spellcheck='true' class='oo-ui-inputWidget-input' /><span class='oo-ui-iconElement-icon oo-ui-iconElement-noIcon'></span><span class='oo-ui-indicatorElement-indicator oo-ui-indicatorElement-noIndicator'></span></div></div></div></div><div class='editCheckboxes'><div class='oo-ui-layout oo-ui-horizontalLayout'></div></div>
<div id="editpage-copywarn">By saving changes, you agree to the <a href="https://foundation.wikimedia.org/wiki/Special:MyLanguage/Policy:Terms_of_Use" class="extiw" title="foundation:Special:MyLanguage/Policy:Terms of Use">Terms of Use</a>, and you irrevocably agree to release your contribution under the <a rel="nofollow" class="external text" href="http://creativecommons.org/licenses/by-sa/4.0/">Creative Commons Attribution-ShareAlike 4.0 license</a> and the <a href="https://commons.wikimedia.org/wiki/Commons:GNU_Free_Documentation_License" title="Commons:GNU Free Documentation License">GFDL</a>. You agree that a hyperlink or URL is sufficient attribution under the Creative Commons license.</div><div class='editButtons'>
<span id='wpSaveWidget' class='oo-ui-widget oo-ui-widget-enabled oo-ui-inputWidget oo-ui-buttonElement oo-ui-buttonElement-framed oo-ui-labelElement oo-ui-flaggedElement-progressive oo-ui-flaggedElement-primary oo-ui-buttonInputWidget' data-ooui='{"_":"OO.ui.ButtonInputWidget","useInputTag":true,"type":"submit","name":"wpSave","inputId":"wpSave","tabIndex":3,"title":"Publish your changes","accessKey":"s","label":"Publish changes","flags":["progressive","primary"]}'><input type='submit' tabindex='3' title='Publish your changes [s]' accesskey='s' name='wpSave' id='wpSave' value='Publish changes' class='oo-ui-inputWidget-input oo-ui-buttonElement-button' /></span>
<span id='wpPreviewWidget' class='oo-ui-widget oo-ui-widget-enabled oo-ui-inputWidget oo-ui-buttonElement oo-ui-buttonElement-framed oo-ui-labelElement oo-ui-buttonInputWidget' data-ooui='{"_":"OO.ui.ButtonInputWidget","useInputTag":true,"type":"submit","formNoValidate":true,"name":"wpPreview","inputId":"wpPreview","tabIndex":4,"title":"Preview your changes. Please use this before publishing.","accessKey":"p","label":"Show preview"}'><input type='submit' tabindex='4' title='Preview your changes. Please use this before publishing. [p]' accesskey='p' name='wpPreview' id='wpPreview' value='Show preview' formnovalidate='formnovalidate' class='oo-ui-inputWidget-input oo-ui-buttonElement-button' /></span>
<span id='wpDiffWidget' class='oo-ui-widget oo-ui-widget-enabled oo-ui-inputWidget oo-ui-buttonElement oo-ui-buttonElement-framed oo-ui-labelElement oo-ui-buttonInputWidget' data-ooui='{"_":"OO.ui.ButtonInputWidget","useInputTag":true,"type":"submit","formNoValidate":true,"name":"wpDiff","inputId":"wpDiff","tabIndex":5,"title":"Show which changes you made to the text","accessKey":"v","label":"Show changes"}'><input type='submit' tabindex='5' title='Show which changes you made to the text [v]' accesskey='v' name='wpDiff' id='wpDiff' value='Show changes' formnovalidate='formnovalidate' class='oo-ui-inputWidget-input oo-ui-buttonElement-button' /></span>
	<span class='cancelLink'><span id='mw-editform-cancel' class='oo-ui-widget oo-ui-widget-enabled oo-ui-buttonElement oo-ui-buttonElement-frameless oo-ui-labelElement oo-ui-flaggedElement-destructive oo-ui-buttonWidget' data-ooui='{"_":"OO.ui.ButtonWidget","href":"\/wiki\/File:Madafu-chopping.jpg","rel":["nofollow"],"framed":false,"label":{"html":"Cancel"},"flags":["destructive"],"tabIndex":5}'><a role='button' tabindex='5' href='https://commons.wikimedia.org/wiki/File:Madafu-chopping.jpg' rel='nofollow' class='oo-ui-buttonElement-button'><span class='oo-ui-iconElement-icon oo-ui-iconElement-noIcon oo-ui-image-destructive'></span><span class='oo-ui-labelElement-label'>Cancel</span><span class='oo-ui-indicatorElement-indicator oo-ui-indicatorElement-noIndicator oo-ui-image-destructive'></span></a></span></span>
	<span class='editHelp'><a href="https://meta.wikimedia.org/wiki/Help:Editing" target="helpwindow">Editing help</a> (opens in new window)</span>
</div><!-- editButtons -->
<span id="templatesandbox-editform"><input id="wpTemplateSandboxTemplate" type="hidden" value="File:Madafu-chopping.jpg" name="wpTemplateSandboxTemplate"><input id="wpTemplateSandboxPage" type="hidden" name="wpTemplateSandboxPage"></span>
</div><!-- editOptions -->

<input type="hidden" value="+\" name="wpEditToken">
<div class="mw-editTools"><div id="specialchars" class="my-buttons" title="Click on the wanted special character.">
<p class="specialbasic" id="Standard">
<a data-mw-charinsert-start="[" data-mw-charinsert-end="]" class="mw-charinsert-item">[]</a>
<a data-mw-charinsert-start="[[" data-mw-charinsert-end="]]" class="mw-charinsert-item">[[]]</a>
<a data-mw-charinsert-start="|" data-mw-charinsert-end="" class="mw-charinsert-item">|</a>
<a data-mw-charinsert-start="{{" data-mw-charinsert-end="}}" class="mw-charinsert-item">{{}}</a>
<a data-mw-charinsert-start="{{{" data-mw-charinsert-end="}}}" class="mw-charinsert-item">{{{}}}</a>
<a data-mw-charinsert-start="–" data-mw-charinsert-end="" class="mw-charinsert-item">–</a>
<a data-mw-charinsert-start="—" data-mw-charinsert-end="" class="mw-charinsert-item">—</a>
<a data-mw-charinsert-start="“" data-mw-charinsert-end="”" class="mw-charinsert-item">“”</a>
<a data-mw-charinsert-start="‘" data-mw-charinsert-end="’" class="mw-charinsert-item">‘’</a>
<a data-mw-charinsert-start="«" data-mw-charinsert-end="»" class="mw-charinsert-item">«»</a>
<a data-mw-charinsert-start="‹" data-mw-charinsert-end="›" class="mw-charinsert-item">‹›</a>
<a data-mw-charinsert-start="„" data-mw-charinsert-end="“" class="mw-charinsert-item">„“</a>
<a data-mw-charinsert-start="‚" data-mw-charinsert-end="‘" class="mw-charinsert-item">‚‘</a>
<a data-mw-charinsert-start="·" data-mw-charinsert-end="" class="mw-charinsert-item">·</a>
<a data-mw-charinsert-start="•" data-mw-charinsert-end="" class="mw-charinsert-item">•</a>
<a data-mw-charinsert-start="×" data-mw-charinsert-end="" class="mw-charinsert-item">×</a>
<a data-mw-charinsert-start="°" data-mw-charinsert-end="" class="mw-charinsert-item">°</a>
<a data-mw-charinsert-start="€" data-mw-charinsert-end="" class="mw-charinsert-item">€</a>
<a data-mw-charinsert-start="←" data-mw-charinsert-end="" class="mw-charinsert-item">←</a>
<a data-mw-charinsert-start="→" data-mw-charinsert-end="" class="mw-charinsert-item">→</a>
<a data-mw-charinsert-start="§" data-mw-charinsert-end="" class="mw-charinsert-item">§</a>
<a data-mw-charinsert-start="~~~~" data-mw-charinsert-end="" class="mw-charinsert-item">~~~~</a>
<a data-mw-charinsert-start="&lt;s&gt;" data-mw-charinsert-end="&lt;/s&gt;" class="mw-charinsert-item">&lt;s&gt;&lt;/s&gt;</a>
<a data-mw-charinsert-start="&lt;small&gt;" data-mw-charinsert-end="&lt;/small&gt;" class="mw-charinsert-item">&lt;small&gt;&lt;/small&gt;</a>
<a data-mw-charinsert-start="&lt;sup&gt;" data-mw-charinsert-end="&lt;/sup&gt;" class="mw-charinsert-item">&lt;sup&gt;&lt;/sup&gt;</a>
<a data-mw-charinsert-start="&lt;sub&gt;" data-mw-charinsert-end="&lt;/sub&gt;" class="mw-charinsert-item">&lt;sub&gt;&lt;/sub&gt;</a>
<a data-mw-charinsert-start="&lt;code&gt;" data-mw-charinsert-end="&lt;/code&gt;" class="mw-charinsert-item">&lt;code&gt;&lt;/code&gt;</a>
<a data-mw-charinsert-start="&lt;pre&gt;" data-mw-charinsert-end="&lt;/pre&gt;" class="mw-charinsert-item">&lt;pre&gt;&lt;/pre&gt;</a>
<a data-mw-charinsert-start="&amp;nbsp;" data-mw-charinsert-end="" class="mw-charinsert-item">&amp;nbsp;</a>
<a data-mw-charinsert-start="[[Category:" data-mw-charinsert-end="]]" class="mw-charinsert-item">[[Category:]]</a>
<a data-mw-charinsert-start="[[:File:" data-mw-charinsert-end="]]" class="mw-charinsert-item">[[:File:]]</a>
<a data-mw-charinsert-start="[[Media:" data-mw-charinsert-end="]]" class="mw-charinsert-item">[[Media:]]</a>
<a data-mw-charinsert-start="{{DEFAULTSORT:" data-mw-charinsert-end="}}" class="mw-charinsert-item">{{DEFAULTSORT:}}</a>
<a data-mw-charinsert-start="{{NAMESPACE}}" data-mw-charinsert-end="" class="mw-charinsert-item">{{NAMESPACE}}</a>
<a data-mw-charinsert-start="{{PAGENAME}}" data-mw-charinsert-end="" class="mw-charinsert-item">{{PAGENAME}}</a>
<a data-mw-charinsert-start="#REDIRECT[[" data-mw-charinsert-end="]]" class="mw-charinsert-item">#REDIRECT[[]]</a>
<a data-mw-charinsert-start="&lt;gallery&gt;" data-mw-charinsert-end="&lt;/gallery&gt;" class="mw-charinsert-item">&lt;gallery&gt;&lt;/gallery&gt;</a>
<a data-mw-charinsert-start="&lt;!-- " data-mw-charinsert-end=" --&gt;" class="mw-charinsert-item">&lt;!--  --&gt;</a>
<a data-mw-charinsert-start="&lt;nowiki&gt;" data-mw-charinsert-end="&lt;/nowiki&gt;" class="mw-charinsert-item">&lt;nowiki&gt;&lt;/nowiki&gt;</a>
<a data-mw-charinsert-start="&lt;code&gt;&lt;nowiki&gt;" data-mw-charinsert-end="&lt;/nowiki&gt;&lt;/code&gt;" class="mw-charinsert-item">&lt;code&gt;&lt;nowiki&gt;&lt;/nowiki&gt;&lt;/code&gt;</a>
<a data-mw-charinsert-start="&lt;includeonly&gt;" data-mw-charinsert-end="&lt;/includeonly&gt;" class="mw-charinsert-item">&lt;includeonly&gt;&lt;/includeonly&gt;</a>
<a data-mw-charinsert-start="&lt;noinclude&gt;" data-mw-charinsert-end="&lt;/noinclude&gt;" class="mw-charinsert-item">&lt;noinclude&gt;&lt;/noinclude&gt;</a>
<a data-mw-charinsert-start="&lt;onlyinclude&gt;" data-mw-charinsert-end="&lt;/onlyinclude&gt;" class="mw-charinsert-item">&lt;onlyinclude&gt;&lt;/onlyinclude&gt;</a>
<a data-mw-charinsert-start="&lt;translate&gt;" data-mw-charinsert-end="&lt;/translate&gt;" class="mw-charinsert-item">&lt;translate&gt;&lt;/translate&gt;</a>
<a data-mw-charinsert-start="&lt;tvar name=&quot;1&quot;&gt;" data-mw-charinsert-end="&lt;/tvar&gt;" class="mw-charinsert-item">&lt;tvar name="1"&gt;&lt;/tvar&gt;</a>
<a data-mw-charinsert-start="Special:MyLanguage/" data-mw-charinsert-end="" class="mw-charinsert-item">Special:MyLanguage/</a>
<a data-mw-charinsert-start="&lt;languages/&gt;" data-mw-charinsert-end="" class="mw-charinsert-item">&lt;languages/&gt;</a>
<a data-mw-charinsert-start="{{rename|" data-mw-charinsert-end="|}}" class="mw-charinsert-item">{{rename||}}</a>
<a data-mw-charinsert-start="{{rotate|" data-mw-charinsert-end="}}" class="mw-charinsert-item">{{rotate|}}</a>
<a data-mw-charinsert-start="== {{int:filedesc}} ==" data-mw-charinsert-end="" class="mw-charinsert-item">== {{int:filedesc}} ==</a>
<a data-mw-charinsert-start="== {{int:license-header}} ==" data-mw-charinsert-end="" class="mw-charinsert-item">== {{int:license-header}} ==</a>
</p>
<p class="specialbasic" id="Symbols" style="display:none">
<a data-mw-charinsert-start="¡" data-mw-charinsert-end="" class="mw-charinsert-item">¡</a>
<a data-mw-charinsert-start="¿" data-mw-charinsert-end="" class="mw-charinsert-item">¿</a>
<a data-mw-charinsert-start="¦" data-mw-charinsert-end="" class="mw-charinsert-item">¦</a>
<a data-mw-charinsert-start="†" data-mw-charinsert-end="" class="mw-charinsert-item">†</a>
<a data-mw-charinsert-start="‡" data-mw-charinsert-end="" class="mw-charinsert-item">‡</a>
<a data-mw-charinsert-start="←" data-mw-charinsert-end="" class="mw-charinsert-item">←</a>
<a data-mw-charinsert-start="→" data-mw-charinsert-end="" class="mw-charinsert-item">→</a>
<a data-mw-charinsert-start="↑" data-mw-charinsert-end="" class="mw-charinsert-item">↑</a>
<a data-mw-charinsert-start="↓" data-mw-charinsert-end="" class="mw-charinsert-item">↓</a>
<a data-mw-charinsert-start="↖" data-mw-charinsert-end="" class="mw-charinsert-item">↖</a>
<a data-mw-charinsert-start="↗" data-mw-charinsert-end="" class="mw-charinsert-item">↗</a>
<a data-mw-charinsert-start="↘" data-mw-charinsert-end="" class="mw-charinsert-item">↘</a>
<a data-mw-charinsert-start="↙" data-mw-charinsert-end="" class="mw-charinsert-item">↙</a>
<a data-mw-charinsert-start="↔" data-mw-charinsert-end="" class="mw-charinsert-item">↔</a>
<a data-mw-charinsert-start="⇐" data-mw-charinsert-end="" class="mw-charinsert-item">⇐</a>
<a data-mw-charinsert-start="⇑" data-mw-charinsert-end="" class="mw-charinsert-item">⇑</a>
<a data-mw-charinsert-start="⇒" data-mw-charinsert-end="" class="mw-charinsert-item">⇒</a>
<a data-mw-charinsert-start="⇓" data-mw-charinsert-end="" class="mw-charinsert-item">⇓</a>
<a data-mw-charinsert-start="⇔" data-mw-charinsert-end="" class="mw-charinsert-item">⇔</a>
<a data-mw-charinsert-start="…" data-mw-charinsert-end="" class="mw-charinsert-item">…</a>
<a data-mw-charinsert-start="•" data-mw-charinsert-end="" class="mw-charinsert-item">•</a>
<a data-mw-charinsert-start="¶" data-mw-charinsert-end="" class="mw-charinsert-item">¶</a>
<a data-mw-charinsert-start="½" data-mw-charinsert-end="" class="mw-charinsert-item">½</a>
<a data-mw-charinsert-start="⅓" data-mw-charinsert-end="" class="mw-charinsert-item">⅓</a>
<a data-mw-charinsert-start="⅔" data-mw-charinsert-end="" class="mw-charinsert-item">⅔</a>
<a data-mw-charinsert-start="¼" data-mw-charinsert-end="" class="mw-charinsert-item">¼</a>
<a data-mw-charinsert-start="¾" data-mw-charinsert-end="" class="mw-charinsert-item">¾</a>
<a data-mw-charinsert-start="⅛" data-mw-charinsert-end="" class="mw-charinsert-item">⅛</a>
<a data-mw-charinsert-start="⅜" data-mw-charinsert-end="" class="mw-charinsert-item">⅜</a>
<a data-mw-charinsert-start="⅝" data-mw-charinsert-end="" class="mw-charinsert-item">⅝</a>
<a data-mw-charinsert-start="⅞" data-mw-charinsert-end="" class="mw-charinsert-item">⅞</a>
<a data-mw-charinsert-start="฿" data-mw-charinsert-end="" class="mw-charinsert-item">฿</a>
<a data-mw-charinsert-start="¢" data-mw-charinsert-end="" class="mw-charinsert-item">¢</a>
<a data-mw-charinsert-start="$" data-mw-charinsert-end="" class="mw-charinsert-item">$</a>
<a data-mw-charinsert-start="€" data-mw-charinsert-end="" class="mw-charinsert-item">€</a>
<a data-mw-charinsert-start="₭" data-mw-charinsert-end="" class="mw-charinsert-item">₭</a>
<a data-mw-charinsert-start="£" data-mw-charinsert-end="" class="mw-charinsert-item">£</a>
<a data-mw-charinsert-start="₦" data-mw-charinsert-end="" class="mw-charinsert-item">₦</a>
<a data-mw-charinsert-start="¥" data-mw-charinsert-end="" class="mw-charinsert-item">¥</a>
<a data-mw-charinsert-start="₩" data-mw-charinsert-end="" class="mw-charinsert-item">₩</a>
<a data-mw-charinsert-start="₪" data-mw-charinsert-end="" class="mw-charinsert-item">₪</a>
<a data-mw-charinsert-start="✓" data-mw-charinsert-end="" class="mw-charinsert-item">✓</a>
<a data-mw-charinsert-start="✗" data-mw-charinsert-end="" class="mw-charinsert-item">✗</a>
<a data-mw-charinsert-start="♀" data-mw-charinsert-end="" class="mw-charinsert-item">♀</a>
<a data-mw-charinsert-start="♂" data-mw-charinsert-end="" class="mw-charinsert-item">♂</a>
<a data-mw-charinsert-start="±" data-mw-charinsert-end="" class="mw-charinsert-item">±</a>
<a data-mw-charinsert-start="−" data-mw-charinsert-end="" class="mw-charinsert-item">−</a>
<a data-mw-charinsert-start="×" data-mw-charinsert-end="" class="mw-charinsert-item">×</a>
<a data-mw-charinsert-start="÷" data-mw-charinsert-end="" class="mw-charinsert-item">÷</a>
<a data-mw-charinsert-start="⋅" data-mw-charinsert-end="" class="mw-charinsert-item">⋅</a>
<a data-mw-charinsert-start="√" data-mw-charinsert-end="" class="mw-charinsert-item">√</a>
<a data-mw-charinsert-start="≠" data-mw-charinsert-end="" class="mw-charinsert-item">≠</a>
<a data-mw-charinsert-start="≤" data-mw-charinsert-end="" class="mw-charinsert-item">≤</a>
<a data-mw-charinsert-start="≥" data-mw-charinsert-end="" class="mw-charinsert-item">≥</a>
<a data-mw-charinsert-start="≡" data-mw-charinsert-end="" class="mw-charinsert-item">≡</a>
<a data-mw-charinsert-start="∼" data-mw-charinsert-end="" class="mw-charinsert-item">∼</a>
<a data-mw-charinsert-start="≈" data-mw-charinsert-end="" class="mw-charinsert-item">≈</a>
<a data-mw-charinsert-start="∞" data-mw-charinsert-end="" class="mw-charinsert-item">∞</a>
<a data-mw-charinsert-start="∅" data-mw-charinsert-end="" class="mw-charinsert-item">∅</a>
<a data-mw-charinsert-start="§" data-mw-charinsert-end="" class="mw-charinsert-item">§</a>
<a data-mw-charinsert-start="©" data-mw-charinsert-end="" class="mw-charinsert-item">©</a>
<a data-mw-charinsert-start="®" data-mw-charinsert-end="" class="mw-charinsert-item">®</a>
<a data-mw-charinsert-start="™" data-mw-charinsert-end="" class="mw-charinsert-item">™</a>
</p>
<p class="specialbasic" id="Latin" style="display:none">
<a data-mw-charinsert-start="Á" data-mw-charinsert-end="" class="mw-charinsert-item">Á</a>
<a data-mw-charinsert-start="á" data-mw-charinsert-end="" class="mw-charinsert-item">á</a>
<a data-mw-charinsert-start="Ć" data-mw-charinsert-end="" class="mw-charinsert-item">Ć</a>
<a data-mw-charinsert-start="ć" data-mw-charinsert-end="" class="mw-charinsert-item">ć</a>
<a data-mw-charinsert-start="É" data-mw-charinsert-end="" class="mw-charinsert-item">É</a>
<a data-mw-charinsert-start="é" data-mw-charinsert-end="" class="mw-charinsert-item">é</a>
<a data-mw-charinsert-start="Í" data-mw-charinsert-end="" class="mw-charinsert-item">Í</a>
<a data-mw-charinsert-start="í" data-mw-charinsert-end="" class="mw-charinsert-item">í</a>
<a data-mw-charinsert-start="Ó" data-mw-charinsert-end="" class="mw-charinsert-item">Ó</a>
<a data-mw-charinsert-start="ó" data-mw-charinsert-end="" class="mw-charinsert-item">ó</a>
<a data-mw-charinsert-start="Ś" data-mw-charinsert-end="" class="mw-charinsert-item">Ś</a>
<a data-mw-charinsert-start="ś" data-mw-charinsert-end="" class="mw-charinsert-item">ś</a>
<a data-mw-charinsert-start="Ú" data-mw-charinsert-end="" class="mw-charinsert-item">Ú</a>
<a data-mw-charinsert-start="ú" data-mw-charinsert-end="" class="mw-charinsert-item">ú</a>
<a data-mw-charinsert-start="Ý" data-mw-charinsert-end="" class="mw-charinsert-item">Ý</a>
<a data-mw-charinsert-start="ý" data-mw-charinsert-end="" class="mw-charinsert-item">ý</a>
<a data-mw-charinsert-start="Ǿ" data-mw-charinsert-end="" class="mw-charinsert-item">Ǿ</a>
<a data-mw-charinsert-start="ǿ" data-mw-charinsert-end="" class="mw-charinsert-item">ǿ</a>
<a data-mw-charinsert-start="À" data-mw-charinsert-end="" class="mw-charinsert-item">À</a>
<a data-mw-charinsert-start="à" data-mw-charinsert-end="" class="mw-charinsert-item">à</a>
<a data-mw-charinsert-start="È" data-mw-charinsert-end="" class="mw-charinsert-item">È</a>
<a data-mw-charinsert-start="è" data-mw-charinsert-end="" class="mw-charinsert-item">è</a>
<a data-mw-charinsert-start="Ì" data-mw-charinsert-end="" class="mw-charinsert-item">Ì</a>
<a data-mw-charinsert-start="ì" data-mw-charinsert-end="" class="mw-charinsert-item">ì</a>
<a data-mw-charinsert-start="Ò" data-mw-charinsert-end="" class="mw-charinsert-item">Ò</a>
<a data-mw-charinsert-start="ò" data-mw-charinsert-end="" class="mw-charinsert-item">ò</a>
<a data-mw-charinsert-start="Ù" data-mw-charinsert-end="" class="mw-charinsert-item">Ù</a>
<a data-mw-charinsert-start="ù" data-mw-charinsert-end="" class="mw-charinsert-item">ù</a>
<a data-mw-charinsert-start="Â" data-mw-charinsert-end="" class="mw-charinsert-item">Â</a>
<a data-mw-charinsert-start="â" data-mw-charinsert-end="" class="mw-charinsert-item">â</a>
<a data-mw-charinsert-start="Ĉ" data-mw-charinsert-end="" class="mw-charinsert-item">Ĉ</a>
<a data-mw-charinsert-start="ĉ" data-mw-charinsert-end="" class="mw-charinsert-item">ĉ</a>
<a data-mw-charinsert-start="Ê" data-mw-charinsert-end="" class="mw-charinsert-item">Ê</a>
<a data-mw-charinsert-start="ê" data-mw-charinsert-end="" class="mw-charinsert-item">ê</a>
<a data-mw-charinsert-start="Ĝ" data-mw-charinsert-end="" class="mw-charinsert-item">Ĝ</a>
<a data-mw-charinsert-start="ĝ" data-mw-charinsert-end="" class="mw-charinsert-item">ĝ</a>
<a data-mw-charinsert-start="Ĥ" data-mw-charinsert-end="" class="mw-charinsert-item">Ĥ</a>
<a data-mw-charinsert-start="ĥ" data-mw-charinsert-end="" class="mw-charinsert-item">ĥ</a>
<a data-mw-charinsert-start="Î" data-mw-charinsert-end="" class="mw-charinsert-item">Î</a>
<a data-mw-charinsert-start="î" data-mw-charinsert-end="" class="mw-charinsert-item">î</a>
<a data-mw-charinsert-start="Ĵ" data-mw-charinsert-end="" class="mw-charinsert-item">Ĵ</a>
<a data-mw-charinsert-start="ĵ" data-mw-charinsert-end="" class="mw-charinsert-item">ĵ</a>
<a data-mw-charinsert-start="Ô" data-mw-charinsert-end="" class="mw-charinsert-item">Ô</a>
<a data-mw-charinsert-start="ô" data-mw-charinsert-end="" class="mw-charinsert-item">ô</a>
<a data-mw-charinsert-start="ŝ" data-mw-charinsert-end="" class="mw-charinsert-item">ŝ</a>
<a data-mw-charinsert-start="Ŝ" data-mw-charinsert-end="" class="mw-charinsert-item">Ŝ</a>
<a data-mw-charinsert-start="Û" data-mw-charinsert-end="" class="mw-charinsert-item">Û</a>
<a data-mw-charinsert-start="û" data-mw-charinsert-end="" class="mw-charinsert-item">û</a>
<a data-mw-charinsert-start="Ä" data-mw-charinsert-end="" class="mw-charinsert-item">Ä</a>
<a data-mw-charinsert-start="ä" data-mw-charinsert-end="" class="mw-charinsert-item">ä</a>
<a data-mw-charinsert-start="Ë" data-mw-charinsert-end="" class="mw-charinsert-item">Ë</a>
<a data-mw-charinsert-start="ë" data-mw-charinsert-end="" class="mw-charinsert-item">ë</a>
<a data-mw-charinsert-start="Ï" data-mw-charinsert-end="" class="mw-charinsert-item">Ï</a>
<a data-mw-charinsert-start="ï" data-mw-charinsert-end="" class="mw-charinsert-item">ï</a>
<a data-mw-charinsert-start="Ö" data-mw-charinsert-end="" class="mw-charinsert-item">Ö</a>
<a data-mw-charinsert-start="ö" data-mw-charinsert-end="" class="mw-charinsert-item">ö</a>
<a data-mw-charinsert-start="Ü" data-mw-charinsert-end="" class="mw-charinsert-item">Ü</a>
<a data-mw-charinsert-start="ü" data-mw-charinsert-end="" class="mw-charinsert-item">ü</a>
<a data-mw-charinsert-start="ÿ" data-mw-charinsert-end="" class="mw-charinsert-item">ÿ</a>
<a data-mw-charinsert-start="Ã" data-mw-charinsert-end="" class="mw-charinsert-item">Ã</a>
<a data-mw-charinsert-start="ã" data-mw-charinsert-end="" class="mw-charinsert-item">ã</a>
<a data-mw-charinsert-start="Ñ" data-mw-charinsert-end="" class="mw-charinsert-item">Ñ</a>
<a data-mw-charinsert-start="ñ" data-mw-charinsert-end="" class="mw-charinsert-item">ñ</a>
<a data-mw-charinsert-start="Õ" data-mw-charinsert-end="" class="mw-charinsert-item">Õ</a>
<a data-mw-charinsert-start="õ" data-mw-charinsert-end="" class="mw-charinsert-item">õ</a>
<a data-mw-charinsert-start="Å" data-mw-charinsert-end="" class="mw-charinsert-item">Å</a>
<a data-mw-charinsert-start="å" data-mw-charinsert-end="" class="mw-charinsert-item">å</a>
<a data-mw-charinsert-start="Ç" data-mw-charinsert-end="" class="mw-charinsert-item">Ç</a>
<a data-mw-charinsert-start="ç" data-mw-charinsert-end="" class="mw-charinsert-item">ç</a>
<a data-mw-charinsert-start="Č" data-mw-charinsert-end="" class="mw-charinsert-item">Č</a>
<a data-mw-charinsert-start="č" data-mw-charinsert-end="" class="mw-charinsert-item">č</a>
<a data-mw-charinsert-start="Š" data-mw-charinsert-end="" class="mw-charinsert-item">Š</a>
<a data-mw-charinsert-start="š" data-mw-charinsert-end="" class="mw-charinsert-item">š</a>
<a data-mw-charinsert-start="ŭ" data-mw-charinsert-end="" class="mw-charinsert-item">ŭ</a>
<a data-mw-charinsert-start="Ł" data-mw-charinsert-end="" class="mw-charinsert-item">Ł</a>
<a data-mw-charinsert-start="ł" data-mw-charinsert-end="" class="mw-charinsert-item">ł</a>
<a data-mw-charinsert-start="Ő" data-mw-charinsert-end="" class="mw-charinsert-item">Ő</a>
<a data-mw-charinsert-start="ő" data-mw-charinsert-end="" class="mw-charinsert-item">ő</a>
<a data-mw-charinsert-start="Ű" data-mw-charinsert-end="" class="mw-charinsert-item">Ű</a>
<a data-mw-charinsert-start="ű" data-mw-charinsert-end="" class="mw-charinsert-item">ű</a>
<a data-mw-charinsert-start="Ø" data-mw-charinsert-end="" class="mw-charinsert-item">Ø</a>
<a data-mw-charinsert-start="ø" data-mw-charinsert-end="" class="mw-charinsert-item">ø</a>
<a data-mw-charinsert-start="Ā" data-mw-charinsert-end="" class="mw-charinsert-item">Ā</a>
<a data-mw-charinsert-start="ā" data-mw-charinsert-end="" class="mw-charinsert-item">ā</a>
<a data-mw-charinsert-start="Ē" data-mw-charinsert-end="" class="mw-charinsert-item">Ē</a>
<a data-mw-charinsert-start="ē" data-mw-charinsert-end="" class="mw-charinsert-item">ē</a>
<a data-mw-charinsert-start="Ī" data-mw-charinsert-end="" class="mw-charinsert-item">Ī</a>
<a data-mw-charinsert-start="ī" data-mw-charinsert-end="" class="mw-charinsert-item">ī</a>
<a data-mw-charinsert-start="Ō" data-mw-charinsert-end="" class="mw-charinsert-item">Ō</a>
<a data-mw-charinsert-start="ō" data-mw-charinsert-end="" class="mw-charinsert-item">ō</a>
<a data-mw-charinsert-start="Ū" data-mw-charinsert-end="" class="mw-charinsert-item">Ū</a>
<a data-mw-charinsert-start="ū" data-mw-charinsert-end="" class="mw-charinsert-item">ū</a>
<a data-mw-charinsert-start="ß" data-mw-charinsert-end="" class="mw-charinsert-item">ß</a>
<a data-mw-charinsert-start="Æ" data-mw-charinsert-end="" class="mw-charinsert-item">Æ</a>
<a data-mw-charinsert-start="æ" data-mw-charinsert-end="" class="mw-charinsert-item">æ</a>
<a data-mw-charinsert-start="Œ" data-mw-charinsert-end="" class="mw-charinsert-item">Œ</a>
<a data-mw-charinsert-start="œ" data-mw-charinsert-end="" class="mw-charinsert-item">œ</a>
<a data-mw-charinsert-start="Ð" data-mw-charinsert-end="" class="mw-charinsert-item">Ð</a>
<a data-mw-charinsert-start="ð" data-mw-charinsert-end="" class="mw-charinsert-item">ð</a>
<a data-mw-charinsert-start="Þ" data-mw-charinsert-end="" class="mw-charinsert-item">Þ</a>
<a data-mw-charinsert-start="þ" data-mw-charinsert-end="" class="mw-charinsert-item">þ</a>
<a data-mw-charinsert-start="|" data-mw-charinsert-end="" class="mw-charinsert-item">|</a>
</p>
<p class="specialbasic" id="Greek" style="display:none">
<a data-mw-charinsert-start="Α" data-mw-charinsert-end="" class="mw-charinsert-item">Α</a>
<a data-mw-charinsert-start="Ά" data-mw-charinsert-end="" class="mw-charinsert-item">Ά</a>
<a data-mw-charinsert-start="Β" data-mw-charinsert-end="" class="mw-charinsert-item">Β</a>
<a data-mw-charinsert-start="Γ" data-mw-charinsert-end="" class="mw-charinsert-item">Γ</a>
<a data-mw-charinsert-start="Δ" data-mw-charinsert-end="" class="mw-charinsert-item">Δ</a>
<a data-mw-charinsert-start="Ε" data-mw-charinsert-end="" class="mw-charinsert-item">Ε</a>
<a data-mw-charinsert-start="Έ" data-mw-charinsert-end="" class="mw-charinsert-item">Έ</a>
<a data-mw-charinsert-start="Ζ" data-mw-charinsert-end="" class="mw-charinsert-item">Ζ</a>
<a data-mw-charinsert-start="Η" data-mw-charinsert-end="" class="mw-charinsert-item">Η</a>
<a data-mw-charinsert-start="Ή" data-mw-charinsert-end="" class="mw-charinsert-item">Ή</a>
<a data-mw-charinsert-start="Θ" data-mw-charinsert-end="" class="mw-charinsert-item">Θ</a>
<a data-mw-charinsert-start="Ι" data-mw-charinsert-end="" class="mw-charinsert-item">Ι</a>
<a data-mw-charinsert-start="Ί" data-mw-charinsert-end="" class="mw-charinsert-item">Ί</a>
<a data-mw-charinsert-start="Κ" data-mw-charinsert-end="" class="mw-charinsert-item">Κ</a>
<a data-mw-charinsert-start="Λ" data-mw-charinsert-end="" class="mw-charinsert-item">Λ</a>
<a data-mw-charinsert-start="Μ" data-mw-charinsert-end="" class="mw-charinsert-item">Μ</a>
<a data-mw-charinsert-start="Ν" data-mw-charinsert-end="" class="mw-charinsert-item">Ν</a>
<a data-mw-charinsert-start="Ξ" data-mw-charinsert-end="" class="mw-charinsert-item">Ξ</a>
<a data-mw-charinsert-start="Ο" data-mw-charinsert-end="" class="mw-charinsert-item">Ο</a>
<a data-mw-charinsert-start="Ό" data-mw-charinsert-end="" class="mw-charinsert-item">Ό</a>
<a data-mw-charinsert-start="Π" data-mw-charinsert-end="" class="mw-charinsert-item">Π</a>
<a data-mw-charinsert-start="Ρ" data-mw-charinsert-end="" class="mw-charinsert-item">Ρ</a>
<a data-mw-charinsert-start="Σ" data-mw-charinsert-end="" class="mw-charinsert-item">Σ</a>
<a data-mw-charinsert-start="Τ" data-mw-charinsert-end="" class="mw-charinsert-item">Τ</a>
<a data-mw-charinsert-start="Υ" data-mw-charinsert-end="" class="mw-charinsert-item">Υ</a>
<a data-mw-charinsert-start="Ύ" data-mw-charinsert-end="" class="mw-charinsert-item">Ύ</a>
<a data-mw-charinsert-start="Φ" data-mw-charinsert-end="" class="mw-charinsert-item">Φ</a>
<a data-mw-charinsert-start="Χ" data-mw-charinsert-end="" class="mw-charinsert-item">Χ</a>
<a data-mw-charinsert-start="Ψ" data-mw-charinsert-end="" class="mw-charinsert-item">Ψ</a>
<a data-mw-charinsert-start="Ω" data-mw-charinsert-end="" class="mw-charinsert-item">Ω</a>
<a data-mw-charinsert-start="Ώ" data-mw-charinsert-end="" class="mw-charinsert-item">Ώ</a>
<a data-mw-charinsert-start="α" data-mw-charinsert-end="" class="mw-charinsert-item">α</a>
<a data-mw-charinsert-start="ά" data-mw-charinsert-end="" class="mw-charinsert-item">ά</a>
<a data-mw-charinsert-start="β" data-mw-charinsert-end="" class="mw-charinsert-item">β</a>
<a data-mw-charinsert-start="γ" data-mw-charinsert-end="" class="mw-charinsert-item">γ</a>
<a data-mw-charinsert-start="δ" data-mw-charinsert-end="" class="mw-charinsert-item">δ</a>
<a data-mw-charinsert-start="ε" data-mw-charinsert-end="" class="mw-charinsert-item">ε</a>
<a data-mw-charinsert-start="έ" data-mw-charinsert-end="" class="mw-charinsert-item">έ</a>
<a data-mw-charinsert-start="ζ" data-mw-charinsert-end="" class="mw-charinsert-item">ζ</a>
<a data-mw-charinsert-start="η" data-mw-charinsert-end="" class="mw-charinsert-item">η</a>
<a data-mw-charinsert-start="ή" data-mw-charinsert-end="" class="mw-charinsert-item">ή</a>
<a data-mw-charinsert-start="θ" data-mw-charinsert-end="" class="mw-charinsert-item">θ</a>
<a data-mw-charinsert-start="ι" data-mw-charinsert-end="" class="mw-charinsert-item">ι</a>
<a data-mw-charinsert-start="ί" data-mw-charinsert-end="" class="mw-charinsert-item">ί</a>
<a data-mw-charinsert-start="κ" data-mw-charinsert-end="" class="mw-charinsert-item">κ</a>
<a data-mw-charinsert-start="λ" data-mw-charinsert-end="" class="mw-charinsert-item">λ</a>
<a data-mw-charinsert-start="μ" data-mw-charinsert-end="" class="mw-charinsert-item">μ</a>
<a data-mw-charinsert-start="ν" data-mw-charinsert-end="" class="mw-charinsert-item">ν</a>
<a data-mw-charinsert-start="ξ" data-mw-charinsert-end="" class="mw-charinsert-item">ξ</a>
<a data-mw-charinsert-start="ο" data-mw-charinsert-end="" class="mw-charinsert-item">ο</a>
<a data-mw-charinsert-start="ό" data-mw-charinsert-end="" class="mw-charinsert-item">ό</a>
<a data-mw-charinsert-start="π" data-mw-charinsert-end="" class="mw-charinsert-item">π</a>
<a data-mw-charinsert-start="ρ" data-mw-charinsert-end="" class="mw-charinsert-item">ρ</a>
<a data-mw-charinsert-start="σ" data-mw-charinsert-end="" class="mw-charinsert-item">σ</a>
<a data-mw-charinsert-start="ς" data-mw-charinsert-end="" class="mw-charinsert-item">ς</a>
<a data-mw-charinsert-start="τ" data-mw-charinsert-end="" class="mw-charinsert-item">τ</a>
<a data-mw-charinsert-start="υ" data-mw-charinsert-end="" class="mw-charinsert-item">υ</a>
<a data-mw-charinsert-start="ύ" data-mw-charinsert-end="" class="mw-charinsert-item">ύ</a>
<a data-mw-charinsert-start="φ" data-mw-charinsert-end="" class="mw-charinsert-item">φ</a>
<a data-mw-charinsert-start="χ" data-mw-charinsert-end="" class="mw-charinsert-item">χ</a>
<a data-mw-charinsert-start="ψ" data-mw-charinsert-end="" class="mw-charinsert-item">ψ</a>
<a data-mw-charinsert-start="ω" data-mw-charinsert-end="" class="mw-charinsert-item">ω</a>
<a data-mw-charinsert-start="ώ" data-mw-charinsert-end="" class="mw-charinsert-item">ώ</a>
</p>
<p class="specialbasic" id="Cyrillic" style="display:none">
<a data-mw-charinsert-start="А" data-mw-charinsert-end="" class="mw-charinsert-item">А</a>
<a data-mw-charinsert-start="Б" data-mw-charinsert-end="" class="mw-charinsert-item">Б</a>
<a data-mw-charinsert-start="В" data-mw-charinsert-end="" class="mw-charinsert-item">В</a>
<a data-mw-charinsert-start="Г" data-mw-charinsert-end="" class="mw-charinsert-item">Г</a>
<a data-mw-charinsert-start="Ґ" data-mw-charinsert-end="" class="mw-charinsert-item">Ґ</a>
<a data-mw-charinsert-start="Ѓ" data-mw-charinsert-end="" class="mw-charinsert-item">Ѓ</a>
<a data-mw-charinsert-start="Д" data-mw-charinsert-end="" class="mw-charinsert-item">Д</a>
<a data-mw-charinsert-start="Ђ" data-mw-charinsert-end="" class="mw-charinsert-item">Ђ</a>
<a data-mw-charinsert-start="Е" data-mw-charinsert-end="" class="mw-charinsert-item">Е</a>
<a data-mw-charinsert-start="Ѐ" data-mw-charinsert-end="" class="mw-charinsert-item">Ѐ</a>
<a data-mw-charinsert-start="Ё" data-mw-charinsert-end="" class="mw-charinsert-item">Ё</a>
<a data-mw-charinsert-start="Є" data-mw-charinsert-end="" class="mw-charinsert-item">Є</a>
<a data-mw-charinsert-start="Ж" data-mw-charinsert-end="" class="mw-charinsert-item">Ж</a>
<a data-mw-charinsert-start="З" data-mw-charinsert-end="" class="mw-charinsert-item">З</a>
<a data-mw-charinsert-start="Ѕ" data-mw-charinsert-end="" class="mw-charinsert-item">Ѕ</a>
<a data-mw-charinsert-start="И" data-mw-charinsert-end="" class="mw-charinsert-item">И</a>
<a data-mw-charinsert-start="Ѝ" data-mw-charinsert-end="" class="mw-charinsert-item">Ѝ</a>
<a data-mw-charinsert-start="Й" data-mw-charinsert-end="" class="mw-charinsert-item">Й</a>
<a data-mw-charinsert-start="І" data-mw-charinsert-end="" class="mw-charinsert-item">І</a>
<a data-mw-charinsert-start="Ї" data-mw-charinsert-end="" class="mw-charinsert-item">Ї</a>
<a data-mw-charinsert-start="Ј" data-mw-charinsert-end="" class="mw-charinsert-item">Ј</a>
<a data-mw-charinsert-start="К" data-mw-charinsert-end="" class="mw-charinsert-item">К</a>
<a data-mw-charinsert-start="Ќ" data-mw-charinsert-end="" class="mw-charinsert-item">Ќ</a>
<a data-mw-charinsert-start="Л" data-mw-charinsert-end="" class="mw-charinsert-item">Л</a>
<a data-mw-charinsert-start="Љ" data-mw-charinsert-end="" class="mw-charinsert-item">Љ</a>
<a data-mw-charinsert-start="М" data-mw-charinsert-end="" class="mw-charinsert-item">М</a>
<a data-mw-charinsert-start="Н" data-mw-charinsert-end="" class="mw-charinsert-item">Н</a>
<a data-mw-charinsert-start="Њ" data-mw-charinsert-end="" class="mw-charinsert-item">Њ</a>
<a data-mw-charinsert-start="О" data-mw-charinsert-end="" class="mw-charinsert-item">О</a>
<a data-mw-charinsert-start="П" data-mw-charinsert-end="" class="mw-charinsert-item">П</a>
<a data-mw-charinsert-start="Р" data-mw-charinsert-end="" class="mw-charinsert-item">Р</a>
<a data-mw-charinsert-start="С" data-mw-charinsert-end="" class="mw-charinsert-item">С</a>
<a data-mw-charinsert-start="Т" data-mw-charinsert-end="" class="mw-charinsert-item">Т</a>
<a data-mw-charinsert-start="Ћ" data-mw-charinsert-end="" class="mw-charinsert-item">Ћ</a>
<a data-mw-charinsert-start="У" data-mw-charinsert-end="" class="mw-charinsert-item">У</a>
<a data-mw-charinsert-start="Ў" data-mw-charinsert-end="" class="mw-charinsert-item">Ў</a>
<a data-mw-charinsert-start="Ф" data-mw-charinsert-end="" class="mw-charinsert-item">Ф</a>
<a data-mw-charinsert-start="Х" data-mw-charinsert-end="" class="mw-charinsert-item">Х</a>
<a data-mw-charinsert-start="Ц" data-mw-charinsert-end="" class="mw-charinsert-item">Ц</a>
<a data-mw-charinsert-start="Ч" data-mw-charinsert-end="" class="mw-charinsert-item">Ч</a>
<a data-mw-charinsert-start="Џ" data-mw-charinsert-end="" class="mw-charinsert-item">Џ</a>
<a data-mw-charinsert-start="Ш" data-mw-charinsert-end="" class="mw-charinsert-item">Ш</a>
<a data-mw-charinsert-start="Щ" data-mw-charinsert-end="" class="mw-charinsert-item">Щ</a>
<a data-mw-charinsert-start="Ъ" data-mw-charinsert-end="" class="mw-charinsert-item">Ъ</a>
<a data-mw-charinsert-start="Ы" data-mw-charinsert-end="" class="mw-charinsert-item">Ы</a>
<a data-mw-charinsert-start="Ь" data-mw-charinsert-end="" class="mw-charinsert-item">Ь</a>
<a data-mw-charinsert-start="Э" data-mw-charinsert-end="" class="mw-charinsert-item">Э</a>
<a data-mw-charinsert-start="Ю" data-mw-charinsert-end="" class="mw-charinsert-item">Ю</a>
<a data-mw-charinsert-start="Я" data-mw-charinsert-end="" class="mw-charinsert-item">Я</a>
<a data-mw-charinsert-start="а" data-mw-charinsert-end="" class="mw-charinsert-item">а</a>
<a data-mw-charinsert-start="б" data-mw-charinsert-end="" class="mw-charinsert-item">б</a>
<a data-mw-charinsert-start="в" data-mw-charinsert-end="" class="mw-charinsert-item">в</a>
<a data-mw-charinsert-start="г" data-mw-charinsert-end="" class="mw-charinsert-item">г</a>
<a data-mw-charinsert-start="ґ" data-mw-charinsert-end="" class="mw-charinsert-item">ґ</a>
<a data-mw-charinsert-start="ѓ" data-mw-charinsert-end="" class="mw-charinsert-item">ѓ</a>
<a data-mw-charinsert-start="д" data-mw-charinsert-end="" class="mw-charinsert-item">д</a>
<a data-mw-charinsert-start="ђ" data-mw-charinsert-end="" class="mw-charinsert-item">ђ</a>
<a data-mw-charinsert-start="е" data-mw-charinsert-end="" class="mw-charinsert-item">е</a>
<a data-mw-charinsert-start="ѐ" data-mw-charinsert-end="" class="mw-charinsert-item">ѐ</a>
<a data-mw-charinsert-start="ё" data-mw-charinsert-end="" class="mw-charinsert-item">ё</a>
<a data-mw-charinsert-start="є" data-mw-charinsert-end="" class="mw-charinsert-item">є</a>
<a data-mw-charinsert-start="ж" data-mw-charinsert-end="" class="mw-charinsert-item">ж</a>
<a data-mw-charinsert-start="з" data-mw-charinsert-end="" class="mw-charinsert-item">з</a>
<a data-mw-charinsert-start="ѕ" data-mw-charinsert-end="" class="mw-charinsert-item">ѕ</a>
<a data-mw-charinsert-start="и" data-mw-charinsert-end="" class="mw-charinsert-item">и</a>
<a data-mw-charinsert-start="ѝ" data-mw-charinsert-end="" class="mw-charinsert-item">ѝ</a>
<a data-mw-charinsert-start="й" data-mw-charinsert-end="" class="mw-charinsert-item">й</a>
<a data-mw-charinsert-start="і" data-mw-charinsert-end="" class="mw-charinsert-item">і</a>
<a data-mw-charinsert-start="ї" data-mw-charinsert-end="" class="mw-charinsert-item">ї</a>
<a data-mw-charinsert-start="ј" data-mw-charinsert-end="" class="mw-charinsert-item">ј</a>
<a data-mw-charinsert-start="к" data-mw-charinsert-end="" class="mw-charinsert-item">к</a>
<a data-mw-charinsert-start="ќ" data-mw-charinsert-end="" class="mw-charinsert-item">ќ</a>
<a data-mw-charinsert-start="л" data-mw-charinsert-end="" class="mw-charinsert-item">л</a>
<a data-mw-charinsert-start="љ" data-mw-charinsert-end="" class="mw-charinsert-item">љ</a>
<a data-mw-charinsert-start="м" data-mw-charinsert-end="" class="mw-charinsert-item">м</a>
<a data-mw-charinsert-start="н" data-mw-charinsert-end="" class="mw-charinsert-item">н</a>
<a data-mw-charinsert-start="њ" data-mw-charinsert-end="" class="mw-charinsert-item">њ</a>
<a data-mw-charinsert-start="о" data-mw-charinsert-end="" class="mw-charinsert-item">о</a>
<a data-mw-charinsert-start="п" data-mw-charinsert-end="" class="mw-charinsert-item">п</a>
<a data-mw-charinsert-start="р" data-mw-charinsert-end="" class="mw-charinsert-item">р</a>
<a data-mw-charinsert-start="с" data-mw-charinsert-end="" class="mw-charinsert-item">с</a>
<a data-mw-charinsert-start="т" data-mw-charinsert-end="" class="mw-charinsert-item">т</a>
<a data-mw-charinsert-start="ћ" data-mw-charinsert-end="" class="mw-charinsert-item">ћ</a>
<a data-mw-charinsert-start="у" data-mw-charinsert-end="" class="mw-charinsert-item">у</a>
<a data-mw-charinsert-start="ў" data-mw-charinsert-end="" class="mw-charinsert-item">ў</a>
<a data-mw-charinsert-start="ф" data-mw-charinsert-end="" class="mw-charinsert-item">ф</a>
<a data-mw-charinsert-start="х" data-mw-charinsert-end="" class="mw-charinsert-item">х</a>
<a data-mw-charinsert-start="ц" data-mw-charinsert-end="" class="mw-charinsert-item">ц</a>
<a data-mw-charinsert-start="ч" data-mw-charinsert-end="" class="mw-charinsert-item">ч</a>
<a data-mw-charinsert-start="џ" data-mw-charinsert-end="" class="mw-charinsert-item">џ</a>
<a data-mw-charinsert-start="ш" data-mw-charinsert-end="" class="mw-charinsert-item">ш</a>
<a data-mw-charinsert-start="щ" data-mw-charinsert-end="" class="mw-charinsert-item">щ</a>
<a data-mw-charinsert-start="ъ" data-mw-charinsert-end="" class="mw-charinsert-item">ъ</a>
<a data-mw-charinsert-start="ы" data-mw-charinsert-end="" class="mw-charinsert-item">ы</a>
<a data-mw-charinsert-start="ь" data-mw-charinsert-end="" class="mw-charinsert-item">ь</a>
<a data-mw-charinsert-start="э" data-mw-charinsert-end="" class="mw-charinsert-item">э</a>
<a data-mw-charinsert-start="ю" data-mw-charinsert-end="" class="mw-charinsert-item">ю</a>
<a data-mw-charinsert-start="я" data-mw-charinsert-end="" class="mw-charinsert-item">я</a>
</p>
<p class="specialbasic" id="IPA" style="display:none">
<a data-mw-charinsert-start="ʈ" data-mw-charinsert-end="" class="mw-charinsert-item">ʈ</a>
<a data-mw-charinsert-start="ɖ" data-mw-charinsert-end="" class="mw-charinsert-item">ɖ</a>
<a data-mw-charinsert-start="ɟ" data-mw-charinsert-end="" class="mw-charinsert-item">ɟ</a>
<a data-mw-charinsert-start="ɡ" data-mw-charinsert-end="" class="mw-charinsert-item">ɡ</a>
<a data-mw-charinsert-start="ɢ" data-mw-charinsert-end="" class="mw-charinsert-item">ɢ</a>
<a data-mw-charinsert-start="ʡ" data-mw-charinsert-end="" class="mw-charinsert-item">ʡ</a>
<a data-mw-charinsert-start="ʔ" data-mw-charinsert-end="" class="mw-charinsert-item">ʔ</a>
<a data-mw-charinsert-start="ɸ" data-mw-charinsert-end="" class="mw-charinsert-item">ɸ</a>
<a data-mw-charinsert-start="ʃ" data-mw-charinsert-end="" class="mw-charinsert-item">ʃ</a>
<a data-mw-charinsert-start="ʒ" data-mw-charinsert-end="" class="mw-charinsert-item">ʒ</a>
<a data-mw-charinsert-start="ɕ" data-mw-charinsert-end="" class="mw-charinsert-item">ɕ</a>
<a data-mw-charinsert-start="ʑ" data-mw-charinsert-end="" class="mw-charinsert-item">ʑ</a>
<a data-mw-charinsert-start="ʂ" data-mw-charinsert-end="" class="mw-charinsert-item">ʂ</a>
<a data-mw-charinsert-start="ʐ" data-mw-charinsert-end="" class="mw-charinsert-item">ʐ</a>
<a data-mw-charinsert-start="ʝ" data-mw-charinsert-end="" class="mw-charinsert-item">ʝ</a>
<a data-mw-charinsert-start="ɣ" data-mw-charinsert-end="" class="mw-charinsert-item">ɣ</a>
<a data-mw-charinsert-start="ʁ" data-mw-charinsert-end="" class="mw-charinsert-item">ʁ</a>
<a data-mw-charinsert-start="ʕ" data-mw-charinsert-end="" class="mw-charinsert-item">ʕ</a>
<a data-mw-charinsert-start="ʜ" data-mw-charinsert-end="" class="mw-charinsert-item">ʜ</a>
<a data-mw-charinsert-start="ʢ" data-mw-charinsert-end="" class="mw-charinsert-item">ʢ</a>
<a data-mw-charinsert-start="ɦ" data-mw-charinsert-end="" class="mw-charinsert-item">ɦ</a>
<a data-mw-charinsert-start="ɱ" data-mw-charinsert-end="" class="mw-charinsert-item">ɱ</a>
<a data-mw-charinsert-start="ɳ" data-mw-charinsert-end="" class="mw-charinsert-item">ɳ</a>
<a data-mw-charinsert-start="ɲ" data-mw-charinsert-end="" class="mw-charinsert-item">ɲ</a>
<a data-mw-charinsert-start="ŋ" data-mw-charinsert-end="" class="mw-charinsert-item">ŋ</a>
<a data-mw-charinsert-start="ɴ" data-mw-charinsert-end="" class="mw-charinsert-item">ɴ</a>
<a data-mw-charinsert-start="ʋ" data-mw-charinsert-end="" class="mw-charinsert-item">ʋ</a>
<a data-mw-charinsert-start="ɹ" data-mw-charinsert-end="" class="mw-charinsert-item">ɹ</a>
<a data-mw-charinsert-start="ɻ" data-mw-charinsert-end="" class="mw-charinsert-item">ɻ</a>
<a data-mw-charinsert-start="ɰ" data-mw-charinsert-end="" class="mw-charinsert-item">ɰ</a>
<a data-mw-charinsert-start="ʙ" data-mw-charinsert-end="" class="mw-charinsert-item">ʙ</a>
<a data-mw-charinsert-start="ʀ" data-mw-charinsert-end="" class="mw-charinsert-item">ʀ</a>
<a data-mw-charinsert-start="ɾ" data-mw-charinsert-end="" class="mw-charinsert-item">ɾ</a>
<a data-mw-charinsert-start="ɽ" data-mw-charinsert-end="" class="mw-charinsert-item">ɽ</a>
<a data-mw-charinsert-start="ɫ" data-mw-charinsert-end="" class="mw-charinsert-item">ɫ</a>
<a data-mw-charinsert-start="ɬ" data-mw-charinsert-end="" class="mw-charinsert-item">ɬ</a>
<a data-mw-charinsert-start="ɮ" data-mw-charinsert-end="" class="mw-charinsert-item">ɮ</a>
<a data-mw-charinsert-start="ɺ" data-mw-charinsert-end="" class="mw-charinsert-item">ɺ</a>
<a data-mw-charinsert-start="ɭ" data-mw-charinsert-end="" class="mw-charinsert-item">ɭ</a>
<a data-mw-charinsert-start="ʎ" data-mw-charinsert-end="" class="mw-charinsert-item">ʎ</a>
<a data-mw-charinsert-start="ʟ" data-mw-charinsert-end="" class="mw-charinsert-item">ʟ</a>
<a data-mw-charinsert-start="ɥ" data-mw-charinsert-end="" class="mw-charinsert-item">ɥ</a>
<a data-mw-charinsert-start="ʍ" data-mw-charinsert-end="" class="mw-charinsert-item">ʍ</a>
<a data-mw-charinsert-start="ɧ" data-mw-charinsert-end="" class="mw-charinsert-item">ɧ</a>
<a data-mw-charinsert-start="ɓ" data-mw-charinsert-end="" class="mw-charinsert-item">ɓ</a>
<a data-mw-charinsert-start="ɗ" data-mw-charinsert-end="" class="mw-charinsert-item">ɗ</a>
<a data-mw-charinsert-start="ʄ" data-mw-charinsert-end="" class="mw-charinsert-item">ʄ</a>
<a data-mw-charinsert-start="ɠ" data-mw-charinsert-end="" class="mw-charinsert-item">ɠ</a>
<a data-mw-charinsert-start="ʛ" data-mw-charinsert-end="" class="mw-charinsert-item">ʛ</a>
<a data-mw-charinsert-start="ʘ" data-mw-charinsert-end="" class="mw-charinsert-item">ʘ</a>
<a data-mw-charinsert-start="ǀ" data-mw-charinsert-end="" class="mw-charinsert-item">ǀ</a>
<a data-mw-charinsert-start="ǃ" data-mw-charinsert-end="" class="mw-charinsert-item">ǃ</a>
<a data-mw-charinsert-start="ǂ" data-mw-charinsert-end="" class="mw-charinsert-item">ǂ</a>
<a data-mw-charinsert-start="ǁ" data-mw-charinsert-end="" class="mw-charinsert-item">ǁ</a>
<a data-mw-charinsert-start="ɨ" data-mw-charinsert-end="" class="mw-charinsert-item">ɨ</a>
<a data-mw-charinsert-start="ʉ" data-mw-charinsert-end="" class="mw-charinsert-item">ʉ</a>
<a data-mw-charinsert-start="ɯ" data-mw-charinsert-end="" class="mw-charinsert-item">ɯ</a>
<a data-mw-charinsert-start="ɪ" data-mw-charinsert-end="" class="mw-charinsert-item">ɪ</a>
<a data-mw-charinsert-start="ʏ" data-mw-charinsert-end="" class="mw-charinsert-item">ʏ</a>
<a data-mw-charinsert-start="ʊ" data-mw-charinsert-end="" class="mw-charinsert-item">ʊ</a>
<a data-mw-charinsert-start="ɘ" data-mw-charinsert-end="" class="mw-charinsert-item">ɘ</a>
<a data-mw-charinsert-start="ɵ" data-mw-charinsert-end="" class="mw-charinsert-item">ɵ</a>
<a data-mw-charinsert-start="ɤ" data-mw-charinsert-end="" class="mw-charinsert-item">ɤ</a>
<a data-mw-charinsert-start="ɚ" data-mw-charinsert-end="" class="mw-charinsert-item">ɚ</a>
<a data-mw-charinsert-start="ɛ" data-mw-charinsert-end="" class="mw-charinsert-item">ɛ</a>
<a data-mw-charinsert-start="ɜ" data-mw-charinsert-end="" class="mw-charinsert-item">ɜ</a>
<a data-mw-charinsert-start="ɝ" data-mw-charinsert-end="" class="mw-charinsert-item">ɝ</a>
<a data-mw-charinsert-start="ɞ" data-mw-charinsert-end="" class="mw-charinsert-item">ɞ</a>
<a data-mw-charinsert-start="ʌ" data-mw-charinsert-end="" class="mw-charinsert-item">ʌ</a>
<a data-mw-charinsert-start="ɔ" data-mw-charinsert-end="" class="mw-charinsert-item">ɔ</a>
<a data-mw-charinsert-start="ɐ" data-mw-charinsert-end="" class="mw-charinsert-item">ɐ</a>
<a data-mw-charinsert-start="ɶ" data-mw-charinsert-end="" class="mw-charinsert-item">ɶ</a>
<a data-mw-charinsert-start="ɑ" data-mw-charinsert-end="" class="mw-charinsert-item">ɑ</a>
<a data-mw-charinsert-start="ɒ" data-mw-charinsert-end="" class="mw-charinsert-item">ɒ</a>
<a data-mw-charinsert-start="ʰ" data-mw-charinsert-end="" class="mw-charinsert-item">ʰ</a>
<a data-mw-charinsert-start="ʷ" data-mw-charinsert-end="" class="mw-charinsert-item">ʷ</a>
<a data-mw-charinsert-start="ʲ" data-mw-charinsert-end="" class="mw-charinsert-item">ʲ</a>
<a data-mw-charinsert-start="ˠ" data-mw-charinsert-end="" class="mw-charinsert-item">ˠ</a>
<a data-mw-charinsert-start="ˤ" data-mw-charinsert-end="" class="mw-charinsert-item">ˤ</a>
<a data-mw-charinsert-start="ⁿ" data-mw-charinsert-end="" class="mw-charinsert-item">ⁿ</a>
<a data-mw-charinsert-start="ˡ" data-mw-charinsert-end="" class="mw-charinsert-item">ˡ</a>
<a data-mw-charinsert-start="ˈ" data-mw-charinsert-end="" class="mw-charinsert-item">ˈ</a>
<a data-mw-charinsert-start="ˌ" data-mw-charinsert-end="" class="mw-charinsert-item">ˌ</a>
<a data-mw-charinsert-start="ː" data-mw-charinsert-end="" class="mw-charinsert-item">ː</a>
<a data-mw-charinsert-start="ˑ" data-mw-charinsert-end="" class="mw-charinsert-item">ˑ</a>
</p>
<p class="specialbasic" id="Arabic" style="display:none; font-size:120%;" dir="rtl">
<a data-mw-charinsert-start="ا" data-mw-charinsert-end="" class="mw-charinsert-item">ا</a>
<a data-mw-charinsert-start="ب" data-mw-charinsert-end="" class="mw-charinsert-item">ب</a>
<a data-mw-charinsert-start="ت" data-mw-charinsert-end="" class="mw-charinsert-item">ت</a>
<a data-mw-charinsert-start="ث" data-mw-charinsert-end="" class="mw-charinsert-item">ث</a>
<a data-mw-charinsert-start="ج" data-mw-charinsert-end="" class="mw-charinsert-item">ج</a>
<a data-mw-charinsert-start="ح" data-mw-charinsert-end="" class="mw-charinsert-item">ح</a>
<a data-mw-charinsert-start="خ" data-mw-charinsert-end="" class="mw-charinsert-item">خ</a>
<a data-mw-charinsert-start="د" data-mw-charinsert-end="" class="mw-charinsert-item">د</a>
<a data-mw-charinsert-start="ذ" data-mw-charinsert-end="" class="mw-charinsert-item">ذ</a>
<a data-mw-charinsert-start="ر" data-mw-charinsert-end="" class="mw-charinsert-item">ر</a>
<a data-mw-charinsert-start="ز" data-mw-charinsert-end="" class="mw-charinsert-item">ز</a>
<a data-mw-charinsert-start="س" data-mw-charinsert-end="" class="mw-charinsert-item">س</a>
<a data-mw-charinsert-start="ش" data-mw-charinsert-end="" class="mw-charinsert-item">ش</a>
<a data-mw-charinsert-start="ص" data-mw-charinsert-end="" class="mw-charinsert-item">ص</a>
<a data-mw-charinsert-start="ض" data-mw-charinsert-end="" class="mw-charinsert-item">ض</a>
<a data-mw-charinsert-start="ط" data-mw-charinsert-end="" class="mw-charinsert-item">ط</a>
<a data-mw-charinsert-start="ظ" data-mw-charinsert-end="" class="mw-charinsert-item">ظ</a>
<a data-mw-charinsert-start="ع" data-mw-charinsert-end="" class="mw-charinsert-item">ع</a>
<a data-mw-charinsert-start="غ" data-mw-charinsert-end="" class="mw-charinsert-item">غ</a>
<a data-mw-charinsert-start="ف" data-mw-charinsert-end="" class="mw-charinsert-item">ف</a>
<a data-mw-charinsert-start="ق" data-mw-charinsert-end="" class="mw-charinsert-item">ق</a>
<a data-mw-charinsert-start="ك" data-mw-charinsert-end="" class="mw-charinsert-item">ك</a>
<a data-mw-charinsert-start="ل" data-mw-charinsert-end="" class="mw-charinsert-item">ل</a>
<a data-mw-charinsert-start="م" data-mw-charinsert-end="" class="mw-charinsert-item">م</a>
<a data-mw-charinsert-start="ن" data-mw-charinsert-end="" class="mw-charinsert-item">ن</a>
<a data-mw-charinsert-start="ه" data-mw-charinsert-end="" class="mw-charinsert-item">ه</a>
<a data-mw-charinsert-start="و" data-mw-charinsert-end="" class="mw-charinsert-item">و</a>
<a data-mw-charinsert-start="ي" data-mw-charinsert-end="" class="mw-charinsert-item">ي</a>
<a data-mw-charinsert-start="ﺍ" data-mw-charinsert-end="" class="mw-charinsert-item">ﺍ</a>
<a data-mw-charinsert-start="ﺑ" data-mw-charinsert-end="" class="mw-charinsert-item">ﺑ</a>
<a data-mw-charinsert-start="ﺗ" data-mw-charinsert-end="" class="mw-charinsert-item">ﺗ</a>
<a data-mw-charinsert-start="ﺛ" data-mw-charinsert-end="" class="mw-charinsert-item">ﺛ</a>
<a data-mw-charinsert-start="ﺟ" data-mw-charinsert-end="" class="mw-charinsert-item">ﺟ</a>
<a data-mw-charinsert-start="ﺣ" data-mw-charinsert-end="" class="mw-charinsert-item">ﺣ</a>
<a data-mw-charinsert-start="ﺧ" data-mw-charinsert-end="" class="mw-charinsert-item">ﺧ</a>
<a data-mw-charinsert-start="ﺩ" data-mw-charinsert-end="" class="mw-charinsert-item">ﺩ</a>
<a data-mw-charinsert-start="ﺫ" data-mw-charinsert-end="" class="mw-charinsert-item">ﺫ</a>
<a data-mw-charinsert-start="ﺭ" data-mw-charinsert-end="" class="mw-charinsert-item">ﺭ</a>
<a data-mw-charinsert-start="ﺯ" data-mw-charinsert-end="" class="mw-charinsert-item">ﺯ</a>
<a data-mw-charinsert-start="ﺳ" data-mw-charinsert-end="" class="mw-charinsert-item">ﺳ</a>
<a data-mw-charinsert-start="ﺷ" data-mw-charinsert-end="" class="mw-charinsert-item">ﺷ</a>
<a data-mw-charinsert-start="ﺻ" data-mw-charinsert-end="" class="mw-charinsert-item">ﺻ</a>
<a data-mw-charinsert-start="ﺿ" data-mw-charinsert-end="" class="mw-charinsert-item">ﺿ</a>
<a data-mw-charinsert-start="ﻃ" data-mw-charinsert-end="" class="mw-charinsert-item">ﻃ</a>
<a data-mw-charinsert-start="ﻇ" data-mw-charinsert-end="" class="mw-charinsert-item">ﻇ</a>
<a data-mw-charinsert-start="ﻋ" data-mw-charinsert-end="" class="mw-charinsert-item">ﻋ</a>
<a data-mw-charinsert-start="ﻏ" data-mw-charinsert-end="" class="mw-charinsert-item">ﻏ</a>
<a data-mw-charinsert-start="ﻓ" data-mw-charinsert-end="" class="mw-charinsert-item">ﻓ</a>
<a data-mw-charinsert-start="ﻗ" data-mw-charinsert-end="" class="mw-charinsert-item">ﻗ</a>
<a data-mw-charinsert-start="ﻛ" data-mw-charinsert-end="" class="mw-charinsert-item">ﻛ</a>
<a data-mw-charinsert-start="ﻟ" data-mw-charinsert-end="" class="mw-charinsert-item">ﻟ</a>
<a data-mw-charinsert-start="ﻣ" data-mw-charinsert-end="" class="mw-charinsert-item">ﻣ</a>
<a data-mw-charinsert-start="ﻧ" data-mw-charinsert-end="" class="mw-charinsert-item">ﻧ</a>
<a data-mw-charinsert-start="ﻫ" data-mw-charinsert-end="" class="mw-charinsert-item">ﻫ</a>
<a data-mw-charinsert-start="ﻭ" data-mw-charinsert-end="" class="mw-charinsert-item">ﻭ</a>
<a data-mw-charinsert-start="ﻳ" data-mw-charinsert-end="" class="mw-charinsert-item">ﻳ</a>
<a data-mw-charinsert-start="ﺍ" data-mw-charinsert-end="" class="mw-charinsert-item">ﺍ</a>
<a data-mw-charinsert-start="ﺒ" data-mw-charinsert-end="" class="mw-charinsert-item">ﺒ</a>
<a data-mw-charinsert-start="ﺘ" data-mw-charinsert-end="" class="mw-charinsert-item">ﺘ</a>
<a data-mw-charinsert-start="ﺜ" data-mw-charinsert-end="" class="mw-charinsert-item">ﺜ</a>
<a data-mw-charinsert-start="ﺠ" data-mw-charinsert-end="" class="mw-charinsert-item">ﺠ</a>
<a data-mw-charinsert-start="ﺤ" data-mw-charinsert-end="" class="mw-charinsert-item">ﺤ</a>
<a data-mw-charinsert-start="ﺨ" data-mw-charinsert-end="" class="mw-charinsert-item">ﺨ</a>
<a data-mw-charinsert-start="ﺪ" data-mw-charinsert-end="" class="mw-charinsert-item">ﺪ</a>
<a data-mw-charinsert-start="ﺬ" data-mw-charinsert-end="" class="mw-charinsert-item">ﺬ</a>
<a data-mw-charinsert-start="ﺮ" data-mw-charinsert-end="" class="mw-charinsert-item">ﺮ</a>
<a data-mw-charinsert-start="ﺰ" data-mw-charinsert-end="" class="mw-charinsert-item">ﺰ</a>
<a data-mw-charinsert-start="ﺴ" data-mw-charinsert-end="" class="mw-charinsert-item">ﺴ</a>
<a data-mw-charinsert-start="ﺸ" data-mw-charinsert-end="" class="mw-charinsert-item">ﺸ</a>
<a data-mw-charinsert-start="ﺼ" data-mw-charinsert-end="" class="mw-charinsert-item">ﺼ</a>
<a data-mw-charinsert-start="ﻀ" data-mw-charinsert-end="" class="mw-charinsert-item">ﻀ</a>
<a data-mw-charinsert-start="ﻄ" data-mw-charinsert-end="" class="mw-charinsert-item">ﻄ</a>
<a data-mw-charinsert-start="ﻈ" data-mw-charinsert-end="" class="mw-charinsert-item">ﻈ</a>
<a data-mw-charinsert-start="ﻌ" data-mw-charinsert-end="" class="mw-charinsert-item">ﻌ</a>
<a data-mw-charinsert-start="ﻐ" data-mw-charinsert-end="" class="mw-charinsert-item">ﻐ</a>
<a data-mw-charinsert-start="ﻔ" data-mw-charinsert-end="" class="mw-charinsert-item">ﻔ</a>
<a data-mw-charinsert-start="ﻘ" data-mw-charinsert-end="" class="mw-charinsert-item">ﻘ</a>
<a data-mw-charinsert-start="ﻜ" data-mw-charinsert-end="" class="mw-charinsert-item">ﻜ</a>
<a data-mw-charinsert-start="ﻠ" data-mw-charinsert-end="" class="mw-charinsert-item">ﻠ</a>
<a data-mw-charinsert-start="ﻤ" data-mw-charinsert-end="" class="mw-charinsert-item">ﻤ</a>
<a data-mw-charinsert-start="ﻨ" data-mw-charinsert-end="" class="mw-charinsert-item">ﻨ</a>
<a data-mw-charinsert-start="ﻬ" data-mw-charinsert-end="" class="mw-charinsert-item">ﻬ</a>
<a data-mw-charinsert-start="ﻮ" data-mw-charinsert-end="" class="mw-charinsert-item">ﻮ</a>
<a data-mw-charinsert-start="ﻴ" data-mw-charinsert-end="" class="mw-charinsert-item">ﻴ</a>
<a data-mw-charinsert-start="ﺎ" data-mw-charinsert-end="" class="mw-charinsert-item">ﺎ</a>
<a data-mw-charinsert-start="ﺐ" data-mw-charinsert-end="" class="mw-charinsert-item">ﺐ</a>
<a data-mw-charinsert-start="ﺖ" data-mw-charinsert-end="" class="mw-charinsert-item">ﺖ</a>
<a data-mw-charinsert-start="ﺚ" data-mw-charinsert-end="" class="mw-charinsert-item">ﺚ</a>
<a data-mw-charinsert-start="ﺞ" data-mw-charinsert-end="" class="mw-charinsert-item">ﺞ</a>
<a data-mw-charinsert-start="ﺢ" data-mw-charinsert-end="" class="mw-charinsert-item">ﺢ</a>
<a data-mw-charinsert-start="ﺦ" data-mw-charinsert-end="" class="mw-charinsert-item">ﺦ</a>
<a data-mw-charinsert-start="ﺪ" data-mw-charinsert-end="" class="mw-charinsert-item">ﺪ</a>
<a data-mw-charinsert-start="ﺬ" data-mw-charinsert-end="" class="mw-charinsert-item">ﺬ</a>
<a data-mw-charinsert-start="ﺮ" data-mw-charinsert-end="" class="mw-charinsert-item">ﺮ</a>
<a data-mw-charinsert-start="ﺰ" data-mw-charinsert-end="" class="mw-charinsert-item">ﺰ</a>
<a data-mw-charinsert-start="ﺲ" data-mw-charinsert-end="" class="mw-charinsert-item">ﺲ</a>
<a data-mw-charinsert-start="ﺶ" data-mw-charinsert-end="" class="mw-charinsert-item">ﺶ</a>
<a data-mw-charinsert-start="ﺺ" data-mw-charinsert-end="" class="mw-charinsert-item">ﺺ</a>
<a data-mw-charinsert-start="ﺾ" data-mw-charinsert-end="" class="mw-charinsert-item">ﺾ</a>
<a data-mw-charinsert-start="ﻂ" data-mw-charinsert-end="" class="mw-charinsert-item">ﻂ</a>
<a data-mw-charinsert-start="ﻆ" data-mw-charinsert-end="" class="mw-charinsert-item">ﻆ</a>
<a data-mw-charinsert-start="ﻊ" data-mw-charinsert-end="" class="mw-charinsert-item">ﻊ</a>
<a data-mw-charinsert-start="ﻎ" data-mw-charinsert-end="" class="mw-charinsert-item">ﻎ</a>
<a data-mw-charinsert-start="ﻒ" data-mw-charinsert-end="" class="mw-charinsert-item">ﻒ</a>
<a data-mw-charinsert-start="ﻖ" data-mw-charinsert-end="" class="mw-charinsert-item">ﻖ</a>
<a data-mw-charinsert-start="ﻚ" data-mw-charinsert-end="" class="mw-charinsert-item">ﻚ</a>
<a data-mw-charinsert-start="ﻞ" data-mw-charinsert-end="" class="mw-charinsert-item">ﻞ</a>
<a data-mw-charinsert-start="ﻢ" data-mw-charinsert-end="" class="mw-charinsert-item">ﻢ</a>
<a data-mw-charinsert-start="ﻦ" data-mw-charinsert-end="" class="mw-charinsert-item">ﻦ</a>
<a data-mw-charinsert-start="ﻪ" data-mw-charinsert-end="" class="mw-charinsert-item">ﻪ</a>
<a data-mw-charinsert-start="ﻮ" data-mw-charinsert-end="" class="mw-charinsert-item">ﻮ</a>
<a data-mw-charinsert-start="ﻲ" data-mw-charinsert-end="" class="mw-charinsert-item">ﻲ</a>
<a data-mw-charinsert-start="ء-" data-mw-charinsert-end="" class="mw-charinsert-item">ء-</a>
<a data-mw-charinsert-start="ّ-" data-mw-charinsert-end="" class="mw-charinsert-item">ّ-</a>
<a data-mw-charinsert-start="ْ-" data-mw-charinsert-end="" class="mw-charinsert-item">ْ-</a>
<a data-mw-charinsert-start="ً-" data-mw-charinsert-end="" class="mw-charinsert-item">ً-</a>
<a data-mw-charinsert-start="ِ-" data-mw-charinsert-end="" class="mw-charinsert-item">ِ-</a>
<a data-mw-charinsert-start="آ" data-mw-charinsert-end="" class="mw-charinsert-item">آ</a>
<a data-mw-charinsert-start="أ" data-mw-charinsert-end="" class="mw-charinsert-item">أ</a>
<a data-mw-charinsert-start="إ" data-mw-charinsert-end="" class="mw-charinsert-item">إ</a>
<a data-mw-charinsert-start="ة" data-mw-charinsert-end="" class="mw-charinsert-item">ة</a>
<a data-mw-charinsert-start="ؤ" data-mw-charinsert-end="" class="mw-charinsert-item">ؤ</a>
<a data-mw-charinsert-start="ئ" data-mw-charinsert-end="" class="mw-charinsert-item">ئ</a>
<a data-mw-charinsert-start="ى" data-mw-charinsert-end="" class="mw-charinsert-item">ى</a>
<a data-mw-charinsert-start="پ" data-mw-charinsert-end="" class="mw-charinsert-item">پ</a>
<a data-mw-charinsert-start="چ" data-mw-charinsert-end="" class="mw-charinsert-item">چ</a>
<a data-mw-charinsert-start="ژ" data-mw-charinsert-end="" class="mw-charinsert-item">ژ</a>
<a data-mw-charinsert-start="گ" data-mw-charinsert-end="" class="mw-charinsert-item">گ</a>
<a data-mw-charinsert-start="ﭪ" data-mw-charinsert-end="" class="mw-charinsert-item">ﭪ</a>
<a data-mw-charinsert-start="ڠ" data-mw-charinsert-end="" class="mw-charinsert-item">ڠ</a>
<a data-mw-charinsert-start="۰" data-mw-charinsert-end="" class="mw-charinsert-item">۰</a>
<a data-mw-charinsert-start="۱" data-mw-charinsert-end="" class="mw-charinsert-item">۱</a>
<a data-mw-charinsert-start="۲" data-mw-charinsert-end="" class="mw-charinsert-item">۲</a>
<a data-mw-charinsert-start="۳" data-mw-charinsert-end="" class="mw-charinsert-item">۳</a>
<a data-mw-charinsert-start="٤" data-mw-charinsert-end="" class="mw-charinsert-item">٤</a>
<a data-mw-charinsert-start="٥" data-mw-charinsert-end="" class="mw-charinsert-item">٥</a>
<a data-mw-charinsert-start="٦" data-mw-charinsert-end="" class="mw-charinsert-item">٦</a>
<a data-mw-charinsert-start="٧" data-mw-charinsert-end="" class="mw-charinsert-item">٧</a>
<a data-mw-charinsert-start="۸" data-mw-charinsert-end="" class="mw-charinsert-item">۸</a>
<a data-mw-charinsert-start="۹" data-mw-charinsert-end="" class="mw-charinsert-item">۹</a>
</p>
<p class="specialbasic" id="Catalan" style="display:none">
<a data-mw-charinsert-start="À" data-mw-charinsert-end="" class="mw-charinsert-item">À</a>
<a data-mw-charinsert-start="à" data-mw-charinsert-end="" class="mw-charinsert-item">à</a>
<a data-mw-charinsert-start="Ç" data-mw-charinsert-end="" class="mw-charinsert-item">Ç</a>
<a data-mw-charinsert-start="ç" data-mw-charinsert-end="" class="mw-charinsert-item">ç</a>
<a data-mw-charinsert-start="É" data-mw-charinsert-end="" class="mw-charinsert-item">É</a>
<a data-mw-charinsert-start="é" data-mw-charinsert-end="" class="mw-charinsert-item">é</a>
<a data-mw-charinsert-start="È" data-mw-charinsert-end="" class="mw-charinsert-item">È</a>
<a data-mw-charinsert-start="è" data-mw-charinsert-end="" class="mw-charinsert-item">è</a>
<a data-mw-charinsert-start="Í" data-mw-charinsert-end="" class="mw-charinsert-item">Í</a>
<a data-mw-charinsert-start="í" data-mw-charinsert-end="" class="mw-charinsert-item">í</a>
<a data-mw-charinsert-start="Ï" data-mw-charinsert-end="" class="mw-charinsert-item">Ï</a>
<a data-mw-charinsert-start="ï" data-mw-charinsert-end="" class="mw-charinsert-item">ï</a>
<a data-mw-charinsert-start="Ò" data-mw-charinsert-end="" class="mw-charinsert-item">Ò</a>
<a data-mw-charinsert-start="ò" data-mw-charinsert-end="" class="mw-charinsert-item">ò</a>
<a data-mw-charinsert-start="Ó" data-mw-charinsert-end="" class="mw-charinsert-item">Ó</a>
<a data-mw-charinsert-start="ó" data-mw-charinsert-end="" class="mw-charinsert-item">ó</a>
<a data-mw-charinsert-start="Ú" data-mw-charinsert-end="" class="mw-charinsert-item">Ú</a>
<a data-mw-charinsert-start="ú" data-mw-charinsert-end="" class="mw-charinsert-item">ú</a>
<a data-mw-charinsert-start="Ü" data-mw-charinsert-end="" class="mw-charinsert-item">Ü</a>
<a data-mw-charinsert-start="ü" data-mw-charinsert-end="" class="mw-charinsert-item">ü</a>
<a data-mw-charinsert-start="Ŀ" data-mw-charinsert-end="" class="mw-charinsert-item">Ŀ</a>
<a data-mw-charinsert-start="ŀ" data-mw-charinsert-end="" class="mw-charinsert-item">ŀ</a>
</p>
<p class="specialbasic" id="Czech" style="display:none">
<a data-mw-charinsert-start="Á" data-mw-charinsert-end="" class="mw-charinsert-item">Á</a>
<a data-mw-charinsert-start="á" data-mw-charinsert-end="" class="mw-charinsert-item">á</a>
<a data-mw-charinsert-start="Č" data-mw-charinsert-end="" class="mw-charinsert-item">Č</a>
<a data-mw-charinsert-start="č" data-mw-charinsert-end="" class="mw-charinsert-item">č</a>
<a data-mw-charinsert-start="Ď" data-mw-charinsert-end="" class="mw-charinsert-item">Ď</a>
<a data-mw-charinsert-start="ď" data-mw-charinsert-end="" class="mw-charinsert-item">ď</a>
<a data-mw-charinsert-start="É" data-mw-charinsert-end="" class="mw-charinsert-item">É</a>
<a data-mw-charinsert-start="é" data-mw-charinsert-end="" class="mw-charinsert-item">é</a>
<a data-mw-charinsert-start="Ě" data-mw-charinsert-end="" class="mw-charinsert-item">Ě</a>
<a data-mw-charinsert-start="ě" data-mw-charinsert-end="" class="mw-charinsert-item">ě</a>
<a data-mw-charinsert-start="Í" data-mw-charinsert-end="" class="mw-charinsert-item">Í</a>
<a data-mw-charinsert-start="í" data-mw-charinsert-end="" class="mw-charinsert-item">í</a>
<a data-mw-charinsert-start="Ň" data-mw-charinsert-end="" class="mw-charinsert-item">Ň</a>
<a data-mw-charinsert-start="ň" data-mw-charinsert-end="" class="mw-charinsert-item">ň</a>
<a data-mw-charinsert-start="Ó" data-mw-charinsert-end="" class="mw-charinsert-item">Ó</a>
<a data-mw-charinsert-start="ó" data-mw-charinsert-end="" class="mw-charinsert-item">ó</a>
<a data-mw-charinsert-start="Ř" data-mw-charinsert-end="" class="mw-charinsert-item">Ř</a>
<a data-mw-charinsert-start="ř" data-mw-charinsert-end="" class="mw-charinsert-item">ř</a>
<a data-mw-charinsert-start="Š" data-mw-charinsert-end="" class="mw-charinsert-item">Š</a>
<a data-mw-charinsert-start="š" data-mw-charinsert-end="" class="mw-charinsert-item">š</a>
<a data-mw-charinsert-start="Ť" data-mw-charinsert-end="" class="mw-charinsert-item">Ť</a>
<a data-mw-charinsert-start="ť" data-mw-charinsert-end="" class="mw-charinsert-item">ť</a>
<a data-mw-charinsert-start="Ú" data-mw-charinsert-end="" class="mw-charinsert-item">Ú</a>
<a data-mw-charinsert-start="ú" data-mw-charinsert-end="" class="mw-charinsert-item">ú</a>
<a data-mw-charinsert-start="Ů" data-mw-charinsert-end="" class="mw-charinsert-item">Ů</a>
<a data-mw-charinsert-start="ů" data-mw-charinsert-end="" class="mw-charinsert-item">ů</a>
<a data-mw-charinsert-start="Ý" data-mw-charinsert-end="" class="mw-charinsert-item">Ý</a>
<a data-mw-charinsert-start="ý" data-mw-charinsert-end="" class="mw-charinsert-item">ý</a>
<a data-mw-charinsert-start="Ž" data-mw-charinsert-end="" class="mw-charinsert-item">Ž</a>
<a data-mw-charinsert-start="ž" data-mw-charinsert-end="" class="mw-charinsert-item">ž</a>
</p>
<p class="specialbasic" id="Devanāgarī" style="display:none">
<a data-mw-charinsert-start="ँ" data-mw-charinsert-end="" class="mw-charinsert-item">ँ</a>
<a data-mw-charinsert-start="ं" data-mw-charinsert-end="" class="mw-charinsert-item">ं</a>
<a data-mw-charinsert-start="ः" data-mw-charinsert-end="" class="mw-charinsert-item">ः</a>
<a data-mw-charinsert-start="अ" data-mw-charinsert-end="" class="mw-charinsert-item">अ</a>
<a data-mw-charinsert-start="आ" data-mw-charinsert-end="" class="mw-charinsert-item">आ</a>
<a data-mw-charinsert-start="इ" data-mw-charinsert-end="" class="mw-charinsert-item">इ</a>
<a data-mw-charinsert-start="ई" data-mw-charinsert-end="" class="mw-charinsert-item">ई</a>
<a data-mw-charinsert-start="उ" data-mw-charinsert-end="" class="mw-charinsert-item">उ</a>
<a data-mw-charinsert-start="ऊ" data-mw-charinsert-end="" class="mw-charinsert-item">ऊ</a>
<a data-mw-charinsert-start="ऋ" data-mw-charinsert-end="" class="mw-charinsert-item">ऋ</a>
<a data-mw-charinsert-start="ऌ" data-mw-charinsert-end="" class="mw-charinsert-item">ऌ</a>
<a data-mw-charinsert-start="ऍ" data-mw-charinsert-end="" class="mw-charinsert-item">ऍ</a>
<a data-mw-charinsert-start="ऎ" data-mw-charinsert-end="" class="mw-charinsert-item">ऎ</a>
<a data-mw-charinsert-start="ए" data-mw-charinsert-end="" class="mw-charinsert-item">ए</a>
<a data-mw-charinsert-start="ऐ" data-mw-charinsert-end="" class="mw-charinsert-item">ऐ</a>
<a data-mw-charinsert-start="ऑ" data-mw-charinsert-end="" class="mw-charinsert-item">ऑ</a>
<a data-mw-charinsert-start="ऒ" data-mw-charinsert-end="" class="mw-charinsert-item">ऒ</a>
<a data-mw-charinsert-start="ओ" data-mw-charinsert-end="" class="mw-charinsert-item">ओ</a>
<a data-mw-charinsert-start="औ" data-mw-charinsert-end="" class="mw-charinsert-item">औ</a>
<a data-mw-charinsert-start="क" data-mw-charinsert-end="" class="mw-charinsert-item">क</a>
<a data-mw-charinsert-start="क़" data-mw-charinsert-end="" class="mw-charinsert-item">क़</a>
<a data-mw-charinsert-start="ख" data-mw-charinsert-end="" class="mw-charinsert-item">ख</a>
<a data-mw-charinsert-start="ख़" data-mw-charinsert-end="" class="mw-charinsert-item">ख़</a>
<a data-mw-charinsert-start="ग" data-mw-charinsert-end="" class="mw-charinsert-item">ग</a>
<a data-mw-charinsert-start="ग़" data-mw-charinsert-end="" class="mw-charinsert-item">ग़</a>
<a data-mw-charinsert-start="घ" data-mw-charinsert-end="" class="mw-charinsert-item">घ</a>
<a data-mw-charinsert-start="ङ" data-mw-charinsert-end="" class="mw-charinsert-item">ङ</a>
<a data-mw-charinsert-start="च" data-mw-charinsert-end="" class="mw-charinsert-item">च</a>
<a data-mw-charinsert-start="छ" data-mw-charinsert-end="" class="mw-charinsert-item">छ</a>
<a data-mw-charinsert-start="ज" data-mw-charinsert-end="" class="mw-charinsert-item">ज</a>
<a data-mw-charinsert-start="ज़" data-mw-charinsert-end="" class="mw-charinsert-item">ज़</a>
<a data-mw-charinsert-start="झ" data-mw-charinsert-end="" class="mw-charinsert-item">झ</a>
<a data-mw-charinsert-start="ञ" data-mw-charinsert-end="" class="mw-charinsert-item">ञ</a>
<a data-mw-charinsert-start="ट" data-mw-charinsert-end="" class="mw-charinsert-item">ट</a>
<a data-mw-charinsert-start="ठ" data-mw-charinsert-end="" class="mw-charinsert-item">ठ</a>
<a data-mw-charinsert-start="ड" data-mw-charinsert-end="" class="mw-charinsert-item">ड</a>
<a data-mw-charinsert-start="ड़" data-mw-charinsert-end="" class="mw-charinsert-item">ड़</a>
<a data-mw-charinsert-start="द" data-mw-charinsert-end="" class="mw-charinsert-item">द</a>
<a data-mw-charinsert-start="ढ" data-mw-charinsert-end="" class="mw-charinsert-item">ढ</a>
<a data-mw-charinsert-start="ढ़" data-mw-charinsert-end="" class="mw-charinsert-item">ढ़</a>
<a data-mw-charinsert-start="ण" data-mw-charinsert-end="" class="mw-charinsert-item">ण</a>
<a data-mw-charinsert-start="त" data-mw-charinsert-end="" class="mw-charinsert-item">त</a>
<a data-mw-charinsert-start="थ" data-mw-charinsert-end="" class="mw-charinsert-item">थ</a>
<a data-mw-charinsert-start="ध" data-mw-charinsert-end="" class="mw-charinsert-item">ध</a>
<a data-mw-charinsert-start="न" data-mw-charinsert-end="" class="mw-charinsert-item">न</a>
<a data-mw-charinsert-start="ऩ" data-mw-charinsert-end="" class="mw-charinsert-item">ऩ</a>
<a data-mw-charinsert-start="प" data-mw-charinsert-end="" class="mw-charinsert-item">प</a>
<a data-mw-charinsert-start="फ" data-mw-charinsert-end="" class="mw-charinsert-item">फ</a>
<a data-mw-charinsert-start="फ़" data-mw-charinsert-end="" class="mw-charinsert-item">फ़</a>
<a data-mw-charinsert-start="ब" data-mw-charinsert-end="" class="mw-charinsert-item">ब</a>
<a data-mw-charinsert-start="भ" data-mw-charinsert-end="" class="mw-charinsert-item">भ</a>
<a data-mw-charinsert-start="म" data-mw-charinsert-end="" class="mw-charinsert-item">म</a>
<a data-mw-charinsert-start="य" data-mw-charinsert-end="" class="mw-charinsert-item">य</a>
<a data-mw-charinsert-start="य़" data-mw-charinsert-end="" class="mw-charinsert-item">य़</a>
<a data-mw-charinsert-start="र" data-mw-charinsert-end="" class="mw-charinsert-item">र</a>
<a data-mw-charinsert-start="ऱ" data-mw-charinsert-end="" class="mw-charinsert-item">ऱ</a>
<a data-mw-charinsert-start="ल" data-mw-charinsert-end="" class="mw-charinsert-item">ल</a>
<a data-mw-charinsert-start="ळ" data-mw-charinsert-end="" class="mw-charinsert-item">ळ</a>
<a data-mw-charinsert-start="ऴ" data-mw-charinsert-end="" class="mw-charinsert-item">ऴ</a>
<a data-mw-charinsert-start="व" data-mw-charinsert-end="" class="mw-charinsert-item">व</a>
<a data-mw-charinsert-start="श" data-mw-charinsert-end="" class="mw-charinsert-item">श</a>
<a data-mw-charinsert-start="ष" data-mw-charinsert-end="" class="mw-charinsert-item">ष</a>
<a data-mw-charinsert-start="स" data-mw-charinsert-end="" class="mw-charinsert-item">स</a>
<a data-mw-charinsert-start="ह" data-mw-charinsert-end="" class="mw-charinsert-item">ह</a>
<a data-mw-charinsert-start="़" data-mw-charinsert-end="" class="mw-charinsert-item">़</a>
<a data-mw-charinsert-start="ऽ" data-mw-charinsert-end="" class="mw-charinsert-item">ऽ</a>
<a data-mw-charinsert-start="ा" data-mw-charinsert-end="" class="mw-charinsert-item">ा</a>
<a data-mw-charinsert-start="ि" data-mw-charinsert-end="" class="mw-charinsert-item">ि</a>
<a data-mw-charinsert-start="ॊ" data-mw-charinsert-end="" class="mw-charinsert-item">ॊ</a>
<a data-mw-charinsert-start="ो" data-mw-charinsert-end="" class="mw-charinsert-item">ो</a>
<a data-mw-charinsert-start="ौ" data-mw-charinsert-end="" class="mw-charinsert-item">ौ</a>
<a data-mw-charinsert-start="्" data-mw-charinsert-end="" class="mw-charinsert-item">्</a>
<a data-mw-charinsert-start="ी" data-mw-charinsert-end="" class="mw-charinsert-item">ी</a>
<a data-mw-charinsert-start="ु" data-mw-charinsert-end="" class="mw-charinsert-item">ु</a>
<a data-mw-charinsert-start="ू" data-mw-charinsert-end="" class="mw-charinsert-item">ू</a>
<a data-mw-charinsert-start="ृ" data-mw-charinsert-end="" class="mw-charinsert-item">ृ</a>
<a data-mw-charinsert-start="ॄ" data-mw-charinsert-end="" class="mw-charinsert-item">ॄ</a>
<a data-mw-charinsert-start="ॅ" data-mw-charinsert-end="" class="mw-charinsert-item">ॅ</a>
<a data-mw-charinsert-start="ॆ" data-mw-charinsert-end="" class="mw-charinsert-item">ॆ</a>
<a data-mw-charinsert-start="े" data-mw-charinsert-end="" class="mw-charinsert-item">े</a>
<a data-mw-charinsert-start="ै" data-mw-charinsert-end="" class="mw-charinsert-item">ै</a>
<a data-mw-charinsert-start="ॉ" data-mw-charinsert-end="" class="mw-charinsert-item">ॉ</a>
<a data-mw-charinsert-start="ॐ" data-mw-charinsert-end="" class="mw-charinsert-item">ॐ</a>
<a data-mw-charinsert-start="॑" data-mw-charinsert-end="" class="mw-charinsert-item">॑</a>
<a data-mw-charinsert-start="॒" data-mw-charinsert-end="" class="mw-charinsert-item">॒</a>
<a data-mw-charinsert-start="॓" data-mw-charinsert-end="" class="mw-charinsert-item">॓</a>
<a data-mw-charinsert-start="॔" data-mw-charinsert-end="" class="mw-charinsert-item">॔</a>
<a data-mw-charinsert-start="ॠ" data-mw-charinsert-end="" class="mw-charinsert-item">ॠ</a>
<a data-mw-charinsert-start="ॡ" data-mw-charinsert-end="" class="mw-charinsert-item">ॡ</a>
<a data-mw-charinsert-start="ॢ" data-mw-charinsert-end="" class="mw-charinsert-item">ॢ</a>
<a data-mw-charinsert-start="ॣ" data-mw-charinsert-end="" class="mw-charinsert-item">ॣ</a>
<a data-mw-charinsert-start="।" data-mw-charinsert-end="" class="mw-charinsert-item">।</a>
<a data-mw-charinsert-start="॥" data-mw-charinsert-end="" class="mw-charinsert-item">॥</a>
<a data-mw-charinsert-start="॰" data-mw-charinsert-end="" class="mw-charinsert-item">॰</a>
</p>
<p class="specialbasic" id="Esperanto" style="display:none">
<a data-mw-charinsert-start="Ĉ" data-mw-charinsert-end="" class="mw-charinsert-item">Ĉ</a>
<a data-mw-charinsert-start="ĉ" data-mw-charinsert-end="" class="mw-charinsert-item">ĉ</a>
<a data-mw-charinsert-start="Ĝ" data-mw-charinsert-end="" class="mw-charinsert-item">Ĝ</a>
<a data-mw-charinsert-start="ĝ" data-mw-charinsert-end="" class="mw-charinsert-item">ĝ</a>
<a data-mw-charinsert-start="Ĥ" data-mw-charinsert-end="" class="mw-charinsert-item">Ĥ</a>
<a data-mw-charinsert-start="ĥ" data-mw-charinsert-end="" class="mw-charinsert-item">ĥ</a>
<a data-mw-charinsert-start="Ĵ" data-mw-charinsert-end="" class="mw-charinsert-item">Ĵ</a>
<a data-mw-charinsert-start="ĵ" data-mw-charinsert-end="" class="mw-charinsert-item">ĵ</a>
<a data-mw-charinsert-start="Ŝ" data-mw-charinsert-end="" class="mw-charinsert-item">Ŝ</a>
<a data-mw-charinsert-start="ŝ" data-mw-charinsert-end="" class="mw-charinsert-item">ŝ</a>
<a data-mw-charinsert-start="Ŭ" data-mw-charinsert-end="" class="mw-charinsert-item">Ŭ</a>
<a data-mw-charinsert-start="ŭ" data-mw-charinsert-end="" class="mw-charinsert-item">ŭ</a>
</p>
<p class="specialbasic" id="Estonian" style="display:none">
<a data-mw-charinsert-start="Č" data-mw-charinsert-end="" class="mw-charinsert-item">Č</a>
<a data-mw-charinsert-start="č" data-mw-charinsert-end="" class="mw-charinsert-item">č</a>
<a data-mw-charinsert-start="Š" data-mw-charinsert-end="" class="mw-charinsert-item">Š</a>
<a data-mw-charinsert-start="š" data-mw-charinsert-end="" class="mw-charinsert-item">š</a>
<a data-mw-charinsert-start="Ž" data-mw-charinsert-end="" class="mw-charinsert-item">Ž</a>
<a data-mw-charinsert-start="ž" data-mw-charinsert-end="" class="mw-charinsert-item">ž</a>
<a data-mw-charinsert-start="Õ" data-mw-charinsert-end="" class="mw-charinsert-item">Õ</a>
<a data-mw-charinsert-start="õ" data-mw-charinsert-end="" class="mw-charinsert-item">õ</a>
<a data-mw-charinsert-start="Ä" data-mw-charinsert-end="" class="mw-charinsert-item">Ä</a>
<a data-mw-charinsert-start="ä" data-mw-charinsert-end="" class="mw-charinsert-item">ä</a>
<a data-mw-charinsert-start="Ö" data-mw-charinsert-end="" class="mw-charinsert-item">Ö</a>
<a data-mw-charinsert-start="ö" data-mw-charinsert-end="" class="mw-charinsert-item">ö</a>
<a data-mw-charinsert-start="Ü" data-mw-charinsert-end="" class="mw-charinsert-item">Ü</a>
<a data-mw-charinsert-start="ü" data-mw-charinsert-end="" class="mw-charinsert-item">ü</a>
</p>
<p class="specialbasic" id="French" style="display:none">
<a data-mw-charinsert-start="À" data-mw-charinsert-end="" class="mw-charinsert-item">À</a>
<a data-mw-charinsert-start="à" data-mw-charinsert-end="" class="mw-charinsert-item">à</a>
<a data-mw-charinsert-start="Â" data-mw-charinsert-end="" class="mw-charinsert-item">Â</a>
<a data-mw-charinsert-start="â" data-mw-charinsert-end="" class="mw-charinsert-item">â</a>
<a data-mw-charinsert-start="Ç" data-mw-charinsert-end="" class="mw-charinsert-item">Ç</a>
<a data-mw-charinsert-start="ç" data-mw-charinsert-end="" class="mw-charinsert-item">ç</a>
<a data-mw-charinsert-start="É" data-mw-charinsert-end="" class="mw-charinsert-item">É</a>
<a data-mw-charinsert-start="é" data-mw-charinsert-end="" class="mw-charinsert-item">é</a>
<a data-mw-charinsert-start="È" data-mw-charinsert-end="" class="mw-charinsert-item">È</a>
<a data-mw-charinsert-start="è" data-mw-charinsert-end="" class="mw-charinsert-item">è</a>
<a data-mw-charinsert-start="Ê" data-mw-charinsert-end="" class="mw-charinsert-item">Ê</a>
<a data-mw-charinsert-start="ê" data-mw-charinsert-end="" class="mw-charinsert-item">ê</a>
<a data-mw-charinsert-start="Ë" data-mw-charinsert-end="" class="mw-charinsert-item">Ë</a>
<a data-mw-charinsert-start="ë" data-mw-charinsert-end="" class="mw-charinsert-item">ë</a>
<a data-mw-charinsert-start="Î" data-mw-charinsert-end="" class="mw-charinsert-item">Î</a>
<a data-mw-charinsert-start="î" data-mw-charinsert-end="" class="mw-charinsert-item">î</a>
<a data-mw-charinsert-start="Ï" data-mw-charinsert-end="" class="mw-charinsert-item">Ï</a>
<a data-mw-charinsert-start="ï" data-mw-charinsert-end="" class="mw-charinsert-item">ï</a>
<a data-mw-charinsert-start="Ô" data-mw-charinsert-end="" class="mw-charinsert-item">Ô</a>
<a data-mw-charinsert-start="ô" data-mw-charinsert-end="" class="mw-charinsert-item">ô</a>
<a data-mw-charinsert-start="Œ" data-mw-charinsert-end="" class="mw-charinsert-item">Œ</a>
<a data-mw-charinsert-start="œ" data-mw-charinsert-end="" class="mw-charinsert-item">œ</a>
<a data-mw-charinsert-start="Ù" data-mw-charinsert-end="" class="mw-charinsert-item">Ù</a>
<a data-mw-charinsert-start="ù" data-mw-charinsert-end="" class="mw-charinsert-item">ù</a>
<a data-mw-charinsert-start="Û" data-mw-charinsert-end="" class="mw-charinsert-item">Û</a>
<a data-mw-charinsert-start="û" data-mw-charinsert-end="" class="mw-charinsert-item">û</a>
<a data-mw-charinsert-start="Ü" data-mw-charinsert-end="" class="mw-charinsert-item">Ü</a>
<a data-mw-charinsert-start="ü" data-mw-charinsert-end="" class="mw-charinsert-item">ü</a>
<a data-mw-charinsert-start="Ÿ" data-mw-charinsert-end="" class="mw-charinsert-item">Ÿ</a>
<a data-mw-charinsert-start="ÿ" data-mw-charinsert-end="" class="mw-charinsert-item">ÿ</a>
</p>
<p class="specialbasic" id="Georgian" style="display:none">
<a data-mw-charinsert-start="ა" data-mw-charinsert-end="" class="mw-charinsert-item">ა</a>
<a data-mw-charinsert-start="ბ" data-mw-charinsert-end="" class="mw-charinsert-item">ბ</a>
<a data-mw-charinsert-start="გ" data-mw-charinsert-end="" class="mw-charinsert-item">გ</a>
<a data-mw-charinsert-start="დ" data-mw-charinsert-end="" class="mw-charinsert-item">დ</a>
<a data-mw-charinsert-start="ე" data-mw-charinsert-end="" class="mw-charinsert-item">ე</a>
<a data-mw-charinsert-start="ვ" data-mw-charinsert-end="" class="mw-charinsert-item">ვ</a>
<a data-mw-charinsert-start="ზ" data-mw-charinsert-end="" class="mw-charinsert-item">ზ</a>
<a data-mw-charinsert-start="თ" data-mw-charinsert-end="" class="mw-charinsert-item">თ</a>
<a data-mw-charinsert-start="ი" data-mw-charinsert-end="" class="mw-charinsert-item">ი</a>
<a data-mw-charinsert-start="კ" data-mw-charinsert-end="" class="mw-charinsert-item">კ</a>
<a data-mw-charinsert-start="ლ" data-mw-charinsert-end="" class="mw-charinsert-item">ლ</a>
<a data-mw-charinsert-start="მ" data-mw-charinsert-end="" class="mw-charinsert-item">მ</a>
<a data-mw-charinsert-start="ნ" data-mw-charinsert-end="" class="mw-charinsert-item">ნ</a>
<a data-mw-charinsert-start="ო" data-mw-charinsert-end="" class="mw-charinsert-item">ო</a>
<a data-mw-charinsert-start="პ" data-mw-charinsert-end="" class="mw-charinsert-item">პ</a>
<a data-mw-charinsert-start="ჟ" data-mw-charinsert-end="" class="mw-charinsert-item">ჟ</a>
<a data-mw-charinsert-start="რ" data-mw-charinsert-end="" class="mw-charinsert-item">რ</a>
<a data-mw-charinsert-start="ს" data-mw-charinsert-end="" class="mw-charinsert-item">ს</a>
<a data-mw-charinsert-start="ტ" data-mw-charinsert-end="" class="mw-charinsert-item">ტ</a>
<a data-mw-charinsert-start="უ" data-mw-charinsert-end="" class="mw-charinsert-item">უ</a>
<a data-mw-charinsert-start="ფ" data-mw-charinsert-end="" class="mw-charinsert-item">ფ</a>
<a data-mw-charinsert-start="ქ" data-mw-charinsert-end="" class="mw-charinsert-item">ქ</a>
<a data-mw-charinsert-start="ღ" data-mw-charinsert-end="" class="mw-charinsert-item">ღ</a>
<a data-mw-charinsert-start="ყ" data-mw-charinsert-end="" class="mw-charinsert-item">ყ</a>
<a data-mw-charinsert-start="შ" data-mw-charinsert-end="" class="mw-charinsert-item">შ</a>
<a data-mw-charinsert-start="ჩ" data-mw-charinsert-end="" class="mw-charinsert-item">ჩ</a>
<a data-mw-charinsert-start="ც" data-mw-charinsert-end="" class="mw-charinsert-item">ც</a>
<a data-mw-charinsert-start="ძ" data-mw-charinsert-end="" class="mw-charinsert-item">ძ</a>
<a data-mw-charinsert-start="წ" data-mw-charinsert-end="" class="mw-charinsert-item">წ</a>
<a data-mw-charinsert-start="ჭ" data-mw-charinsert-end="" class="mw-charinsert-item">ჭ</a>
<a data-mw-charinsert-start="ხ" data-mw-charinsert-end="" class="mw-charinsert-item">ხ</a>
<a data-mw-charinsert-start="ჯ" data-mw-charinsert-end="" class="mw-charinsert-item">ჯ</a>
<a data-mw-charinsert-start="ჰ" data-mw-charinsert-end="" class="mw-charinsert-item">ჰ</a>
<a data-mw-charinsert-start="ჱ" data-mw-charinsert-end="" class="mw-charinsert-item">ჱ</a>
<a data-mw-charinsert-start="ჲ" data-mw-charinsert-end="" class="mw-charinsert-item">ჲ</a>
<a data-mw-charinsert-start="ჳ" data-mw-charinsert-end="" class="mw-charinsert-item">ჳ</a>
<a data-mw-charinsert-start="ჴ" data-mw-charinsert-end="" class="mw-charinsert-item">ჴ</a>
<a data-mw-charinsert-start="ჵ" data-mw-charinsert-end="" class="mw-charinsert-item">ჵ</a>
<a data-mw-charinsert-start="ჶ" data-mw-charinsert-end="" class="mw-charinsert-item">ჶ</a>
<a data-mw-charinsert-start="ჷ" data-mw-charinsert-end="" class="mw-charinsert-item">ჷ</a>
<a data-mw-charinsert-start="ჸ" data-mw-charinsert-end="" class="mw-charinsert-item">ჸ</a>
<a data-mw-charinsert-start="ჹ" data-mw-charinsert-end="" class="mw-charinsert-item">ჹ</a>
<a data-mw-charinsert-start="ჺ" data-mw-charinsert-end="" class="mw-charinsert-item">ჺ</a>
<a data-mw-charinsert-start="჻" data-mw-charinsert-end="" class="mw-charinsert-item">჻</a>
<a data-mw-charinsert-start="ჼ" data-mw-charinsert-end="" class="mw-charinsert-item">ჼ</a>
<a data-mw-charinsert-start="Ⴀ" data-mw-charinsert-end="" class="mw-charinsert-item">Ⴀ</a>
<a data-mw-charinsert-start="Ⴁ" data-mw-charinsert-end="" class="mw-charinsert-item">Ⴁ</a>
<a data-mw-charinsert-start="Ⴂ" data-mw-charinsert-end="" class="mw-charinsert-item">Ⴂ</a>
<a data-mw-charinsert-start="Ⴃ" data-mw-charinsert-end="" class="mw-charinsert-item">Ⴃ</a>
<a data-mw-charinsert-start="Ⴄ" data-mw-charinsert-end="" class="mw-charinsert-item">Ⴄ</a>
<a data-mw-charinsert-start="Ⴅ" data-mw-charinsert-end="" class="mw-charinsert-item">Ⴅ</a>
<a data-mw-charinsert-start="Ⴆ" data-mw-charinsert-end="" class="mw-charinsert-item">Ⴆ</a>
<a data-mw-charinsert-start="Ⴡ" data-mw-charinsert-end="" class="mw-charinsert-item">Ⴡ</a>
<a data-mw-charinsert-start="Ⴇ" data-mw-charinsert-end="" class="mw-charinsert-item">Ⴇ</a>
<a data-mw-charinsert-start="Ⴈ" data-mw-charinsert-end="" class="mw-charinsert-item">Ⴈ</a>
<a data-mw-charinsert-start="Ⴉ" data-mw-charinsert-end="" class="mw-charinsert-item">Ⴉ</a>
<a data-mw-charinsert-start="Ⴊ" data-mw-charinsert-end="" class="mw-charinsert-item">Ⴊ</a>
<a data-mw-charinsert-start="Ⴋ" data-mw-charinsert-end="" class="mw-charinsert-item">Ⴋ</a>
<a data-mw-charinsert-start="Ⴌ" data-mw-charinsert-end="" class="mw-charinsert-item">Ⴌ</a>
<a data-mw-charinsert-start="Ⴢ" data-mw-charinsert-end="" class="mw-charinsert-item">Ⴢ</a>
<a data-mw-charinsert-start="Ⴍ" data-mw-charinsert-end="" class="mw-charinsert-item">Ⴍ</a>
<a data-mw-charinsert-start="Ⴎ" data-mw-charinsert-end="" class="mw-charinsert-item">Ⴎ</a>
<a data-mw-charinsert-start="Ⴏ" data-mw-charinsert-end="" class="mw-charinsert-item">Ⴏ</a>
<a data-mw-charinsert-start="Ⴐ" data-mw-charinsert-end="" class="mw-charinsert-item">Ⴐ</a>
<a data-mw-charinsert-start="Ⴑ" data-mw-charinsert-end="" class="mw-charinsert-item">Ⴑ</a>
<a data-mw-charinsert-start="Ⴒ" data-mw-charinsert-end="" class="mw-charinsert-item">Ⴒ</a>
<a data-mw-charinsert-start="Ⴣ" data-mw-charinsert-end="" class="mw-charinsert-item">Ⴣ</a>
<a data-mw-charinsert-start="Ⴓ" data-mw-charinsert-end="" class="mw-charinsert-item">Ⴓ</a>
<a data-mw-charinsert-start="Ⴔ" data-mw-charinsert-end="" class="mw-charinsert-item">Ⴔ</a>
<a data-mw-charinsert-start="Ⴕ" data-mw-charinsert-end="" class="mw-charinsert-item">Ⴕ</a>
<a data-mw-charinsert-start="Ⴖ" data-mw-charinsert-end="" class="mw-charinsert-item">Ⴖ</a>
<a data-mw-charinsert-start="Ⴗ" data-mw-charinsert-end="" class="mw-charinsert-item">Ⴗ</a>
<a data-mw-charinsert-start="Ⴘ" data-mw-charinsert-end="" class="mw-charinsert-item">Ⴘ</a>
<a data-mw-charinsert-start="Ⴙ" data-mw-charinsert-end="" class="mw-charinsert-item">Ⴙ</a>
<a data-mw-charinsert-start="Ⴚ" data-mw-charinsert-end="" class="mw-charinsert-item">Ⴚ</a>
<a data-mw-charinsert-start="Ⴛ" data-mw-charinsert-end="" class="mw-charinsert-item">Ⴛ</a>
<a data-mw-charinsert-start="Ⴜ" data-mw-charinsert-end="" class="mw-charinsert-item">Ⴜ</a>
<a data-mw-charinsert-start="Ⴝ" data-mw-charinsert-end="" class="mw-charinsert-item">Ⴝ</a>
<a data-mw-charinsert-start="Ⴞ" data-mw-charinsert-end="" class="mw-charinsert-item">Ⴞ</a>
<a data-mw-charinsert-start="Ⴤ" data-mw-charinsert-end="" class="mw-charinsert-item">Ⴤ</a>
<a data-mw-charinsert-start="Ⴟ" data-mw-charinsert-end="" class="mw-charinsert-item">Ⴟ</a>
<a data-mw-charinsert-start="Ⴠ" data-mw-charinsert-end="" class="mw-charinsert-item">Ⴠ</a>
<a data-mw-charinsert-start="Ⴥ" data-mw-charinsert-end="" class="mw-charinsert-item">Ⴥ</a>
</p>
<p class="specialbasic" id="German" style="display:none">
<a data-mw-charinsert-start="Ä" data-mw-charinsert-end="" class="mw-charinsert-item">Ä</a>
<a data-mw-charinsert-start="ä" data-mw-charinsert-end="" class="mw-charinsert-item">ä</a>
<a data-mw-charinsert-start="Ö" data-mw-charinsert-end="" class="mw-charinsert-item">Ö</a>
<a data-mw-charinsert-start="ö" data-mw-charinsert-end="" class="mw-charinsert-item">ö</a>
<a data-mw-charinsert-start="Ü" data-mw-charinsert-end="" class="mw-charinsert-item">Ü</a>
<a data-mw-charinsert-start="ü" data-mw-charinsert-end="" class="mw-charinsert-item">ü</a>
<a data-mw-charinsert-start="ß" data-mw-charinsert-end="" class="mw-charinsert-item">ß</a>
</p>
<p class="specialbasic" id="Hawaiian" style="display:none">
<a data-mw-charinsert-start="Ā" data-mw-charinsert-end="" class="mw-charinsert-item">Ā</a>
<a data-mw-charinsert-start="ā" data-mw-charinsert-end="" class="mw-charinsert-item">ā</a>
<a data-mw-charinsert-start="Ē" data-mw-charinsert-end="" class="mw-charinsert-item">Ē</a>
<a data-mw-charinsert-start="ē" data-mw-charinsert-end="" class="mw-charinsert-item">ē</a>
<a data-mw-charinsert-start="Ī" data-mw-charinsert-end="" class="mw-charinsert-item">Ī</a>
<a data-mw-charinsert-start="ī" data-mw-charinsert-end="" class="mw-charinsert-item">ī</a>
<a data-mw-charinsert-start="Ō" data-mw-charinsert-end="" class="mw-charinsert-item">Ō</a>
<a data-mw-charinsert-start="ō" data-mw-charinsert-end="" class="mw-charinsert-item">ō</a>
<a data-mw-charinsert-start="Ū" data-mw-charinsert-end="" class="mw-charinsert-item">Ū</a>
<a data-mw-charinsert-start="ū" data-mw-charinsert-end="" class="mw-charinsert-item">ū</a>
<a data-mw-charinsert-start="ʻ" data-mw-charinsert-end="" class="mw-charinsert-item">ʻ</a>
</p>
<p class="specialbasic" id="Hebrew" style="display:none">
<a data-mw-charinsert-start="א" data-mw-charinsert-end="" class="mw-charinsert-item">א</a>
<a data-mw-charinsert-start="ב" data-mw-charinsert-end="" class="mw-charinsert-item">ב</a>
<a data-mw-charinsert-start="ג" data-mw-charinsert-end="" class="mw-charinsert-item">ג</a>
<a data-mw-charinsert-start="ד" data-mw-charinsert-end="" class="mw-charinsert-item">ד</a>
<a data-mw-charinsert-start="ה" data-mw-charinsert-end="" class="mw-charinsert-item">ה</a>
<a data-mw-charinsert-start="ו" data-mw-charinsert-end="" class="mw-charinsert-item">ו</a>
<a data-mw-charinsert-start="ז" data-mw-charinsert-end="" class="mw-charinsert-item">ז</a>
<a data-mw-charinsert-start="ח" data-mw-charinsert-end="" class="mw-charinsert-item">ח</a>
<a data-mw-charinsert-start="ט" data-mw-charinsert-end="" class="mw-charinsert-item">ט</a>
<a data-mw-charinsert-start="י" data-mw-charinsert-end="" class="mw-charinsert-item">י</a>
<a data-mw-charinsert-start="כ" data-mw-charinsert-end="" class="mw-charinsert-item">כ</a>
<a data-mw-charinsert-start="ך" data-mw-charinsert-end="" class="mw-charinsert-item">ך</a>
<a data-mw-charinsert-start="ל" data-mw-charinsert-end="" class="mw-charinsert-item">ל</a>
<a data-mw-charinsert-start="מ" data-mw-charinsert-end="" class="mw-charinsert-item">מ</a>
<a data-mw-charinsert-start="ם" data-mw-charinsert-end="" class="mw-charinsert-item">ם</a>
<a data-mw-charinsert-start="נ" data-mw-charinsert-end="" class="mw-charinsert-item">נ</a>
<a data-mw-charinsert-start="ן" data-mw-charinsert-end="" class="mw-charinsert-item">ן</a>
<a data-mw-charinsert-start="ס" data-mw-charinsert-end="" class="mw-charinsert-item">ס</a>
<a data-mw-charinsert-start="ע" data-mw-charinsert-end="" class="mw-charinsert-item">ע</a>
<a data-mw-charinsert-start="פ" data-mw-charinsert-end="" class="mw-charinsert-item">פ</a>
<a data-mw-charinsert-start="ף" data-mw-charinsert-end="" class="mw-charinsert-item">ף</a>
<a data-mw-charinsert-start="צ" data-mw-charinsert-end="" class="mw-charinsert-item">צ</a>
<a data-mw-charinsert-start="ץ" data-mw-charinsert-end="" class="mw-charinsert-item">ץ</a>
<a data-mw-charinsert-start="ק" data-mw-charinsert-end="" class="mw-charinsert-item">ק</a>
<a data-mw-charinsert-start="ר" data-mw-charinsert-end="" class="mw-charinsert-item">ר</a>
<a data-mw-charinsert-start="ש" data-mw-charinsert-end="" class="mw-charinsert-item">ש</a>
<a data-mw-charinsert-start="ת" data-mw-charinsert-end="" class="mw-charinsert-item">ת</a>
<a data-mw-charinsert-start="־" data-mw-charinsert-end="" class="mw-charinsert-item">־</a>
<a data-mw-charinsert-start="״" data-mw-charinsert-end="" class="mw-charinsert-item">״</a>
<a data-mw-charinsert-start="׳" data-mw-charinsert-end="" class="mw-charinsert-item">׳</a>
</p>
<p class="specialbasic" id="Hungarian" style="display:none">
<a data-mw-charinsert-start="Ő" data-mw-charinsert-end="" class="mw-charinsert-item">Ő</a>
<a data-mw-charinsert-start="ő" data-mw-charinsert-end="" class="mw-charinsert-item">ő</a>
<a data-mw-charinsert-start="Ű" data-mw-charinsert-end="" class="mw-charinsert-item">Ű</a>
<a data-mw-charinsert-start="ű" data-mw-charinsert-end="" class="mw-charinsert-item">ű</a>
</p>
<p class="specialbasic" id="Icelandic" style="display:none">
<a data-mw-charinsert-start="Á" data-mw-charinsert-end="" class="mw-charinsert-item">Á</a>
<a data-mw-charinsert-start="á" data-mw-charinsert-end="" class="mw-charinsert-item">á</a>
<a data-mw-charinsert-start="Ð" data-mw-charinsert-end="" class="mw-charinsert-item">Ð</a>
<a data-mw-charinsert-start="ð" data-mw-charinsert-end="" class="mw-charinsert-item">ð</a>
<a data-mw-charinsert-start="É" data-mw-charinsert-end="" class="mw-charinsert-item">É</a>
<a data-mw-charinsert-start="é" data-mw-charinsert-end="" class="mw-charinsert-item">é</a>
<a data-mw-charinsert-start="Í" data-mw-charinsert-end="" class="mw-charinsert-item">Í</a>
<a data-mw-charinsert-start="í" data-mw-charinsert-end="" class="mw-charinsert-item">í</a>
<a data-mw-charinsert-start="Ó" data-mw-charinsert-end="" class="mw-charinsert-item">Ó</a>
<a data-mw-charinsert-start="ó" data-mw-charinsert-end="" class="mw-charinsert-item">ó</a>
<a data-mw-charinsert-start="Ú" data-mw-charinsert-end="" class="mw-charinsert-item">Ú</a>
<a data-mw-charinsert-start="ú" data-mw-charinsert-end="" class="mw-charinsert-item">ú</a>
<a data-mw-charinsert-start="Ý" data-mw-charinsert-end="" class="mw-charinsert-item">Ý</a>
<a data-mw-charinsert-start="ý" data-mw-charinsert-end="" class="mw-charinsert-item">ý</a>
<a data-mw-charinsert-start="Þ" data-mw-charinsert-end="" class="mw-charinsert-item">Þ</a>
<a data-mw-charinsert-start="þ" data-mw-charinsert-end="" class="mw-charinsert-item">þ</a>
<a data-mw-charinsert-start="Æ" data-mw-charinsert-end="" class="mw-charinsert-item">Æ</a>
<a data-mw-charinsert-start="æ" data-mw-charinsert-end="" class="mw-charinsert-item">æ</a>
<a data-mw-charinsert-start="Ö" data-mw-charinsert-end="" class="mw-charinsert-item">Ö</a>
<a data-mw-charinsert-start="ö" data-mw-charinsert-end="" class="mw-charinsert-item">ö</a>
</p>
<p class="specialbasic" id="Italian" style="display:none">
<a data-mw-charinsert-start="Á" data-mw-charinsert-end="" class="mw-charinsert-item">Á</a>
<a data-mw-charinsert-start="á" data-mw-charinsert-end="" class="mw-charinsert-item">á</a>
<a data-mw-charinsert-start="À" data-mw-charinsert-end="" class="mw-charinsert-item">À</a>
<a data-mw-charinsert-start="à" data-mw-charinsert-end="" class="mw-charinsert-item">à</a>
<a data-mw-charinsert-start="É" data-mw-charinsert-end="" class="mw-charinsert-item">É</a>
<a data-mw-charinsert-start="é" data-mw-charinsert-end="" class="mw-charinsert-item">é</a>
<a data-mw-charinsert-start="È" data-mw-charinsert-end="" class="mw-charinsert-item">È</a>
<a data-mw-charinsert-start="è" data-mw-charinsert-end="" class="mw-charinsert-item">è</a>
<a data-mw-charinsert-start="Í" data-mw-charinsert-end="" class="mw-charinsert-item">Í</a>
<a data-mw-charinsert-start="í" data-mw-charinsert-end="" class="mw-charinsert-item">í</a>
<a data-mw-charinsert-start="Ì" data-mw-charinsert-end="" class="mw-charinsert-item">Ì</a>
<a data-mw-charinsert-start="ì" data-mw-charinsert-end="" class="mw-charinsert-item">ì</a>
<a data-mw-charinsert-start="Ó" data-mw-charinsert-end="" class="mw-charinsert-item">Ó</a>
<a data-mw-charinsert-start="ó" data-mw-charinsert-end="" class="mw-charinsert-item">ó</a>
<a data-mw-charinsert-start="Ò" data-mw-charinsert-end="" class="mw-charinsert-item">Ò</a>
<a data-mw-charinsert-start="ò" data-mw-charinsert-end="" class="mw-charinsert-item">ò</a>
<a data-mw-charinsert-start="Ú" data-mw-charinsert-end="" class="mw-charinsert-item">Ú</a>
<a data-mw-charinsert-start="ú" data-mw-charinsert-end="" class="mw-charinsert-item">ú</a>
<a data-mw-charinsert-start="Ù" data-mw-charinsert-end="" class="mw-charinsert-item">Ù</a>
<a data-mw-charinsert-start="ù" data-mw-charinsert-end="" class="mw-charinsert-item">ù</a>
</p>
<p class="specialbasic" id="Latvian" style="display:none">
<a data-mw-charinsert-start="Ā" data-mw-charinsert-end="" class="mw-charinsert-item">Ā</a>
<a data-mw-charinsert-start="ā" data-mw-charinsert-end="" class="mw-charinsert-item">ā</a>
<a data-mw-charinsert-start="Č" data-mw-charinsert-end="" class="mw-charinsert-item">Č</a>
<a data-mw-charinsert-start="č" data-mw-charinsert-end="" class="mw-charinsert-item">č</a>
<a data-mw-charinsert-start="Ē" data-mw-charinsert-end="" class="mw-charinsert-item">Ē</a>
<a data-mw-charinsert-start="ē" data-mw-charinsert-end="" class="mw-charinsert-item">ē</a>
<a data-mw-charinsert-start="Ģ" data-mw-charinsert-end="" class="mw-charinsert-item">Ģ</a>
<a data-mw-charinsert-start="ģ" data-mw-charinsert-end="" class="mw-charinsert-item">ģ</a>
<a data-mw-charinsert-start="Ī" data-mw-charinsert-end="" class="mw-charinsert-item">Ī</a>
<a data-mw-charinsert-start="ī" data-mw-charinsert-end="" class="mw-charinsert-item">ī</a>
<a data-mw-charinsert-start="Ķ" data-mw-charinsert-end="" class="mw-charinsert-item">Ķ</a>
<a data-mw-charinsert-start="ķ" data-mw-charinsert-end="" class="mw-charinsert-item">ķ</a>
<a data-mw-charinsert-start="Ļ" data-mw-charinsert-end="" class="mw-charinsert-item">Ļ</a>
<a data-mw-charinsert-start="ļ" data-mw-charinsert-end="" class="mw-charinsert-item">ļ</a>
<a data-mw-charinsert-start="Ņ" data-mw-charinsert-end="" class="mw-charinsert-item">Ņ</a>
<a data-mw-charinsert-start="ņ" data-mw-charinsert-end="" class="mw-charinsert-item">ņ</a>
<a data-mw-charinsert-start="Š" data-mw-charinsert-end="" class="mw-charinsert-item">Š</a>
<a data-mw-charinsert-start="š" data-mw-charinsert-end="" class="mw-charinsert-item">š</a>
<a data-mw-charinsert-start="Ū" data-mw-charinsert-end="" class="mw-charinsert-item">Ū</a>
<a data-mw-charinsert-start="ū" data-mw-charinsert-end="" class="mw-charinsert-item">ū</a>
<a data-mw-charinsert-start="Ž" data-mw-charinsert-end="" class="mw-charinsert-item">Ž</a>
<a data-mw-charinsert-start="ž" data-mw-charinsert-end="" class="mw-charinsert-item">ž</a>
</p>
<p class="specialbasic" id="Lithuanian" style="display:none">
<a data-mw-charinsert-start="Ą" data-mw-charinsert-end="" class="mw-charinsert-item">Ą</a>
<a data-mw-charinsert-start="ą" data-mw-charinsert-end="" class="mw-charinsert-item">ą</a>
<a data-mw-charinsert-start="Č" data-mw-charinsert-end="" class="mw-charinsert-item">Č</a>
<a data-mw-charinsert-start="č" data-mw-charinsert-end="" class="mw-charinsert-item">č</a>
<a data-mw-charinsert-start="Ę" data-mw-charinsert-end="" class="mw-charinsert-item">Ę</a>
<a data-mw-charinsert-start="ę" data-mw-charinsert-end="" class="mw-charinsert-item">ę</a>
<a data-mw-charinsert-start="Ė" data-mw-charinsert-end="" class="mw-charinsert-item">Ė</a>
<a data-mw-charinsert-start="ė" data-mw-charinsert-end="" class="mw-charinsert-item">ė</a>
<a data-mw-charinsert-start="Į" data-mw-charinsert-end="" class="mw-charinsert-item">Į</a>
<a data-mw-charinsert-start="į" data-mw-charinsert-end="" class="mw-charinsert-item">į</a>
<a data-mw-charinsert-start="Š" data-mw-charinsert-end="" class="mw-charinsert-item">Š</a>
<a data-mw-charinsert-start="š" data-mw-charinsert-end="" class="mw-charinsert-item">š</a>
<a data-mw-charinsert-start="Ų" data-mw-charinsert-end="" class="mw-charinsert-item">Ų</a>
<a data-mw-charinsert-start="ų" data-mw-charinsert-end="" class="mw-charinsert-item">ų</a>
<a data-mw-charinsert-start="Ū" data-mw-charinsert-end="" class="mw-charinsert-item">Ū</a>
<a data-mw-charinsert-start="ū" data-mw-charinsert-end="" class="mw-charinsert-item">ū</a>
<a data-mw-charinsert-start="Ž" data-mw-charinsert-end="" class="mw-charinsert-item">Ž</a>
<a data-mw-charinsert-start="ž" data-mw-charinsert-end="" class="mw-charinsert-item">ž</a>
</p>
<p class="specialbasic" id="Maltese" style="display:none">
<a data-mw-charinsert-start="Ċ" data-mw-charinsert-end="" class="mw-charinsert-item">Ċ</a>
<a data-mw-charinsert-start="ċ" data-mw-charinsert-end="" class="mw-charinsert-item">ċ</a>
<a data-mw-charinsert-start="Ġ" data-mw-charinsert-end="" class="mw-charinsert-item">Ġ</a>
<a data-mw-charinsert-start="ġ" data-mw-charinsert-end="" class="mw-charinsert-item">ġ</a>
<a data-mw-charinsert-start="Ħ" data-mw-charinsert-end="" class="mw-charinsert-item">Ħ</a>
<a data-mw-charinsert-start="ħ" data-mw-charinsert-end="" class="mw-charinsert-item">ħ</a>
<a data-mw-charinsert-start="Ż" data-mw-charinsert-end="" class="mw-charinsert-item">Ż</a>
<a data-mw-charinsert-start="ż" data-mw-charinsert-end="" class="mw-charinsert-item">ż</a>
</p>
<p class="specialbasic" id="Old-English" style="display:none">
<a data-mw-charinsert-start="Ā" data-mw-charinsert-end="" class="mw-charinsert-item">Ā</a>
<a data-mw-charinsert-start="ā" data-mw-charinsert-end="" class="mw-charinsert-item">ā</a>
<a data-mw-charinsert-start="Æ" data-mw-charinsert-end="" class="mw-charinsert-item">Æ</a>
<a data-mw-charinsert-start="æ" data-mw-charinsert-end="" class="mw-charinsert-item">æ</a>
<a data-mw-charinsert-start="Ǣ" data-mw-charinsert-end="" class="mw-charinsert-item">Ǣ</a>
<a data-mw-charinsert-start="ǣ" data-mw-charinsert-end="" class="mw-charinsert-item">ǣ</a>
<a data-mw-charinsert-start="Ǽ" data-mw-charinsert-end="" class="mw-charinsert-item">Ǽ</a>
<a data-mw-charinsert-start="ǽ" data-mw-charinsert-end="" class="mw-charinsert-item">ǽ</a>
<a data-mw-charinsert-start="Ċ" data-mw-charinsert-end="" class="mw-charinsert-item">Ċ</a>
<a data-mw-charinsert-start="ċ" data-mw-charinsert-end="" class="mw-charinsert-item">ċ</a>
<a data-mw-charinsert-start="Ð" data-mw-charinsert-end="" class="mw-charinsert-item">Ð</a>
<a data-mw-charinsert-start="ð" data-mw-charinsert-end="" class="mw-charinsert-item">ð</a>
<a data-mw-charinsert-start="Ē" data-mw-charinsert-end="" class="mw-charinsert-item">Ē</a>
<a data-mw-charinsert-start="ē" data-mw-charinsert-end="" class="mw-charinsert-item">ē</a>
<a data-mw-charinsert-start="Ġ" data-mw-charinsert-end="" class="mw-charinsert-item">Ġ</a>
<a data-mw-charinsert-start="ġ" data-mw-charinsert-end="" class="mw-charinsert-item">ġ</a>
<a data-mw-charinsert-start="Ī" data-mw-charinsert-end="" class="mw-charinsert-item">Ī</a>
<a data-mw-charinsert-start="ī" data-mw-charinsert-end="" class="mw-charinsert-item">ī</a>
<a data-mw-charinsert-start="Ō" data-mw-charinsert-end="" class="mw-charinsert-item">Ō</a>
<a data-mw-charinsert-start="ō" data-mw-charinsert-end="" class="mw-charinsert-item">ō</a>
<a data-mw-charinsert-start="Ū" data-mw-charinsert-end="" class="mw-charinsert-item">Ū</a>
<a data-mw-charinsert-start="ū" data-mw-charinsert-end="" class="mw-charinsert-item">ū</a>
<a data-mw-charinsert-start="Ƿ" data-mw-charinsert-end="" class="mw-charinsert-item">Ƿ</a>
<a data-mw-charinsert-start="ƿ" data-mw-charinsert-end="" class="mw-charinsert-item">ƿ</a>
<a data-mw-charinsert-start="Ȳ" data-mw-charinsert-end="" class="mw-charinsert-item">Ȳ</a>
<a data-mw-charinsert-start="ȳ" data-mw-charinsert-end="" class="mw-charinsert-item">ȳ</a>
<a data-mw-charinsert-start="Þ" data-mw-charinsert-end="" class="mw-charinsert-item">Þ</a>
<a data-mw-charinsert-start="þ" data-mw-charinsert-end="" class="mw-charinsert-item">þ</a>
<a data-mw-charinsert-start="Ȝ" data-mw-charinsert-end="" class="mw-charinsert-item">Ȝ</a>
<a data-mw-charinsert-start="ȝ" data-mw-charinsert-end="" class="mw-charinsert-item">ȝ</a>
</p>
<p class="specialbasic" id="Pinyin" style="display:none">
<a data-mw-charinsert-start="Á" data-mw-charinsert-end="" class="mw-charinsert-item">Á</a>
<a data-mw-charinsert-start="á" data-mw-charinsert-end="" class="mw-charinsert-item">á</a>
<a data-mw-charinsert-start="À" data-mw-charinsert-end="" class="mw-charinsert-item">À</a>
<a data-mw-charinsert-start="à" data-mw-charinsert-end="" class="mw-charinsert-item">à</a>
<a data-mw-charinsert-start="Ǎ" data-mw-charinsert-end="" class="mw-charinsert-item">Ǎ</a>
<a data-mw-charinsert-start="ǎ" data-mw-charinsert-end="" class="mw-charinsert-item">ǎ</a>
<a data-mw-charinsert-start="Ā" data-mw-charinsert-end="" class="mw-charinsert-item">Ā</a>
<a data-mw-charinsert-start="ā" data-mw-charinsert-end="" class="mw-charinsert-item">ā</a>
<a data-mw-charinsert-start="É" data-mw-charinsert-end="" class="mw-charinsert-item">É</a>
<a data-mw-charinsert-start="é" data-mw-charinsert-end="" class="mw-charinsert-item">é</a>
<a data-mw-charinsert-start="È" data-mw-charinsert-end="" class="mw-charinsert-item">È</a>
<a data-mw-charinsert-start="è" data-mw-charinsert-end="" class="mw-charinsert-item">è</a>
<a data-mw-charinsert-start="Ě" data-mw-charinsert-end="" class="mw-charinsert-item">Ě</a>
<a data-mw-charinsert-start="ě" data-mw-charinsert-end="" class="mw-charinsert-item">ě</a>
<a data-mw-charinsert-start="Ē" data-mw-charinsert-end="" class="mw-charinsert-item">Ē</a>
<a data-mw-charinsert-start="ē" data-mw-charinsert-end="" class="mw-charinsert-item">ē</a>
<a data-mw-charinsert-start="Í" data-mw-charinsert-end="" class="mw-charinsert-item">Í</a>
<a data-mw-charinsert-start="í" data-mw-charinsert-end="" class="mw-charinsert-item">í</a>
<a data-mw-charinsert-start="Ì" data-mw-charinsert-end="" class="mw-charinsert-item">Ì</a>
<a data-mw-charinsert-start="ì" data-mw-charinsert-end="" class="mw-charinsert-item">ì</a>
<a data-mw-charinsert-start="Ǐ" data-mw-charinsert-end="" class="mw-charinsert-item">Ǐ</a>
<a data-mw-charinsert-start="ǐ" data-mw-charinsert-end="" class="mw-charinsert-item">ǐ</a>
<a data-mw-charinsert-start="Ī" data-mw-charinsert-end="" class="mw-charinsert-item">Ī</a>
<a data-mw-charinsert-start="ī" data-mw-charinsert-end="" class="mw-charinsert-item">ī</a>
<a data-mw-charinsert-start="Ó" data-mw-charinsert-end="" class="mw-charinsert-item">Ó</a>
<a data-mw-charinsert-start="ó" data-mw-charinsert-end="" class="mw-charinsert-item">ó</a>
<a data-mw-charinsert-start="Ò" data-mw-charinsert-end="" class="mw-charinsert-item">Ò</a>
<a data-mw-charinsert-start="ò" data-mw-charinsert-end="" class="mw-charinsert-item">ò</a>
<a data-mw-charinsert-start="Ǒ" data-mw-charinsert-end="" class="mw-charinsert-item">Ǒ</a>
<a data-mw-charinsert-start="ǒ" data-mw-charinsert-end="" class="mw-charinsert-item">ǒ</a>
<a data-mw-charinsert-start="Ō" data-mw-charinsert-end="" class="mw-charinsert-item">Ō</a>
<a data-mw-charinsert-start="ō" data-mw-charinsert-end="" class="mw-charinsert-item">ō</a>
<a data-mw-charinsert-start="Ú" data-mw-charinsert-end="" class="mw-charinsert-item">Ú</a>
<a data-mw-charinsert-start="ú" data-mw-charinsert-end="" class="mw-charinsert-item">ú</a>
<a data-mw-charinsert-start="Ù" data-mw-charinsert-end="" class="mw-charinsert-item">Ù</a>
<a data-mw-charinsert-start="ù" data-mw-charinsert-end="" class="mw-charinsert-item">ù</a>
<a data-mw-charinsert-start="Ü" data-mw-charinsert-end="" class="mw-charinsert-item">Ü</a>
<a data-mw-charinsert-start="ü" data-mw-charinsert-end="" class="mw-charinsert-item">ü</a>
<a data-mw-charinsert-start="Ǔ" data-mw-charinsert-end="" class="mw-charinsert-item">Ǔ</a>
<a data-mw-charinsert-start="ǔ" data-mw-charinsert-end="" class="mw-charinsert-item">ǔ</a>
<a data-mw-charinsert-start="Ū" data-mw-charinsert-end="" class="mw-charinsert-item">Ū</a>
<a data-mw-charinsert-start="ū" data-mw-charinsert-end="" class="mw-charinsert-item">ū</a>
<a data-mw-charinsert-start="Ǘ" data-mw-charinsert-end="" class="mw-charinsert-item">Ǘ</a>
<a data-mw-charinsert-start="ǘ" data-mw-charinsert-end="" class="mw-charinsert-item">ǘ</a>
<a data-mw-charinsert-start="Ǜ" data-mw-charinsert-end="" class="mw-charinsert-item">Ǜ</a>
<a data-mw-charinsert-start="ǜ" data-mw-charinsert-end="" class="mw-charinsert-item">ǜ</a>
<a data-mw-charinsert-start="Ǚ" data-mw-charinsert-end="" class="mw-charinsert-item">Ǚ</a>
<a data-mw-charinsert-start="ǚ" data-mw-charinsert-end="" class="mw-charinsert-item">ǚ</a>
<a data-mw-charinsert-start="Ǖ" data-mw-charinsert-end="" class="mw-charinsert-item">Ǖ</a>
<a data-mw-charinsert-start="ǖ" data-mw-charinsert-end="" class="mw-charinsert-item">ǖ</a>
</p>
<p class="specialbasic" id="Polish" style="display:none">
<a data-mw-charinsert-start="ą" data-mw-charinsert-end="" class="mw-charinsert-item">ą</a>
<a data-mw-charinsert-start="Ą" data-mw-charinsert-end="" class="mw-charinsert-item">Ą</a>
<a data-mw-charinsert-start="ć" data-mw-charinsert-end="" class="mw-charinsert-item">ć</a>
<a data-mw-charinsert-start="Ć" data-mw-charinsert-end="" class="mw-charinsert-item">Ć</a>
<a data-mw-charinsert-start="ę" data-mw-charinsert-end="" class="mw-charinsert-item">ę</a>
<a data-mw-charinsert-start="Ę" data-mw-charinsert-end="" class="mw-charinsert-item">Ę</a>
<a data-mw-charinsert-start="ł" data-mw-charinsert-end="" class="mw-charinsert-item">ł</a>
<a data-mw-charinsert-start="Ł" data-mw-charinsert-end="" class="mw-charinsert-item">Ł</a>
<a data-mw-charinsert-start="ń" data-mw-charinsert-end="" class="mw-charinsert-item">ń</a>
<a data-mw-charinsert-start="Ń" data-mw-charinsert-end="" class="mw-charinsert-item">Ń</a>
<a data-mw-charinsert-start="ó" data-mw-charinsert-end="" class="mw-charinsert-item">ó</a>
<a data-mw-charinsert-start="Ó" data-mw-charinsert-end="" class="mw-charinsert-item">Ó</a>
<a data-mw-charinsert-start="ś" data-mw-charinsert-end="" class="mw-charinsert-item">ś</a>
<a data-mw-charinsert-start="Ś" data-mw-charinsert-end="" class="mw-charinsert-item">Ś</a>
<a data-mw-charinsert-start="ź" data-mw-charinsert-end="" class="mw-charinsert-item">ź</a>
<a data-mw-charinsert-start="Ź" data-mw-charinsert-end="" class="mw-charinsert-item">Ź</a>
<a data-mw-charinsert-start="ż" data-mw-charinsert-end="" class="mw-charinsert-item">ż</a>
<a data-mw-charinsert-start="Ż" data-mw-charinsert-end="" class="mw-charinsert-item">Ż</a>
</p>
<p class="specialbasic" id="Portuguese" style="display:none">
<a data-mw-charinsert-start="Á" data-mw-charinsert-end="" class="mw-charinsert-item">Á</a>
<a data-mw-charinsert-start="á" data-mw-charinsert-end="" class="mw-charinsert-item">á</a>
<a data-mw-charinsert-start="À" data-mw-charinsert-end="" class="mw-charinsert-item">À</a>
<a data-mw-charinsert-start="à" data-mw-charinsert-end="" class="mw-charinsert-item">à</a>
<a data-mw-charinsert-start="Â" data-mw-charinsert-end="" class="mw-charinsert-item">Â</a>
<a data-mw-charinsert-start="â" data-mw-charinsert-end="" class="mw-charinsert-item">â</a>
<a data-mw-charinsert-start="Ã" data-mw-charinsert-end="" class="mw-charinsert-item">Ã</a>
<a data-mw-charinsert-start="ã" data-mw-charinsert-end="" class="mw-charinsert-item">ã</a>
<a data-mw-charinsert-start="Ç" data-mw-charinsert-end="" class="mw-charinsert-item">Ç</a>
<a data-mw-charinsert-start="ç" data-mw-charinsert-end="" class="mw-charinsert-item">ç</a>
<a data-mw-charinsert-start="É" data-mw-charinsert-end="" class="mw-charinsert-item">É</a>
<a data-mw-charinsert-start="é" data-mw-charinsert-end="" class="mw-charinsert-item">é</a>
<a data-mw-charinsert-start="Ê" data-mw-charinsert-end="" class="mw-charinsert-item">Ê</a>
<a data-mw-charinsert-start="ê" data-mw-charinsert-end="" class="mw-charinsert-item">ê</a>
<a data-mw-charinsert-start="Í" data-mw-charinsert-end="" class="mw-charinsert-item">Í</a>
<a data-mw-charinsert-start="í" data-mw-charinsert-end="" class="mw-charinsert-item">í</a>
<a data-mw-charinsert-start="Ó" data-mw-charinsert-end="" class="mw-charinsert-item">Ó</a>
<a data-mw-charinsert-start="ó" data-mw-charinsert-end="" class="mw-charinsert-item">ó</a>
<a data-mw-charinsert-start="Ô" data-mw-charinsert-end="" class="mw-charinsert-item">Ô</a>
<a data-mw-charinsert-start="ô" data-mw-charinsert-end="" class="mw-charinsert-item">ô</a>
<a data-mw-charinsert-start="Õ" data-mw-charinsert-end="" class="mw-charinsert-item">Õ</a>
<a data-mw-charinsert-start="õ" data-mw-charinsert-end="" class="mw-charinsert-item">õ</a>
<a data-mw-charinsert-start="Ú" data-mw-charinsert-end="" class="mw-charinsert-item">Ú</a>
<a data-mw-charinsert-start="ú" data-mw-charinsert-end="" class="mw-charinsert-item">ú</a>
<a data-mw-charinsert-start="Ü" data-mw-charinsert-end="" class="mw-charinsert-item">Ü</a>
<a data-mw-charinsert-start="ü" data-mw-charinsert-end="" class="mw-charinsert-item">ü</a>
</p>
<p class="specialbasic" id="Romaji" style="display:none">
<a data-mw-charinsert-start="Ā" data-mw-charinsert-end="" class="mw-charinsert-item">Ā</a>
<a data-mw-charinsert-start="ā" data-mw-charinsert-end="" class="mw-charinsert-item">ā</a>
<a data-mw-charinsert-start="Ē" data-mw-charinsert-end="" class="mw-charinsert-item">Ē</a>
<a data-mw-charinsert-start="ē" data-mw-charinsert-end="" class="mw-charinsert-item">ē</a>
<a data-mw-charinsert-start="Ī" data-mw-charinsert-end="" class="mw-charinsert-item">Ī</a>
<a data-mw-charinsert-start="ī" data-mw-charinsert-end="" class="mw-charinsert-item">ī</a>
<a data-mw-charinsert-start="Ō" data-mw-charinsert-end="" class="mw-charinsert-item">Ō</a>
<a data-mw-charinsert-start="ō" data-mw-charinsert-end="" class="mw-charinsert-item">ō</a>
<a data-mw-charinsert-start="Ū" data-mw-charinsert-end="" class="mw-charinsert-item">Ū</a>
<a data-mw-charinsert-start="ū" data-mw-charinsert-end="" class="mw-charinsert-item">ū</a>
</p>
<p class="specialbasic" id="Romanian" style="display:none">
<a data-mw-charinsert-start="Ă" data-mw-charinsert-end="" class="mw-charinsert-item">Ă</a>
<a data-mw-charinsert-start="ă" data-mw-charinsert-end="" class="mw-charinsert-item">ă</a>
<a data-mw-charinsert-start="Â" data-mw-charinsert-end="" class="mw-charinsert-item">Â</a>
<a data-mw-charinsert-start="â" data-mw-charinsert-end="" class="mw-charinsert-item">â</a>
<a data-mw-charinsert-start="Î" data-mw-charinsert-end="" class="mw-charinsert-item">Î</a>
<a data-mw-charinsert-start="î" data-mw-charinsert-end="" class="mw-charinsert-item">î</a>
<a data-mw-charinsert-start="Ş" data-mw-charinsert-end="" class="mw-charinsert-item">Ş</a>
<a data-mw-charinsert-start="ş" data-mw-charinsert-end="" class="mw-charinsert-item">ş</a>
<a data-mw-charinsert-start="Ţ" data-mw-charinsert-end="" class="mw-charinsert-item">Ţ</a>
<a data-mw-charinsert-start="ţ" data-mw-charinsert-end="" class="mw-charinsert-item">ţ</a>
</p>
<p class="specialbasic" id="Scandinavian" style="display:none">
<a data-mw-charinsert-start="À" data-mw-charinsert-end="" class="mw-charinsert-item">À</a>
<a data-mw-charinsert-start="à" data-mw-charinsert-end="" class="mw-charinsert-item">à</a>
<a data-mw-charinsert-start="É" data-mw-charinsert-end="" class="mw-charinsert-item">É</a>
<a data-mw-charinsert-start="é" data-mw-charinsert-end="" class="mw-charinsert-item">é</a>
<a data-mw-charinsert-start="Å" data-mw-charinsert-end="" class="mw-charinsert-item">Å</a>
<a data-mw-charinsert-start="å" data-mw-charinsert-end="" class="mw-charinsert-item">å</a>
<a data-mw-charinsert-start="Æ" data-mw-charinsert-end="" class="mw-charinsert-item">Æ</a>
<a data-mw-charinsert-start="æ" data-mw-charinsert-end="" class="mw-charinsert-item">æ</a>
<a data-mw-charinsert-start="Ä" data-mw-charinsert-end="" class="mw-charinsert-item">Ä</a>
<a data-mw-charinsert-start="ä" data-mw-charinsert-end="" class="mw-charinsert-item">ä</a>
<a data-mw-charinsert-start="Ø" data-mw-charinsert-end="" class="mw-charinsert-item">Ø</a>
<a data-mw-charinsert-start="ø" data-mw-charinsert-end="" class="mw-charinsert-item">ø</a>
<a data-mw-charinsert-start="Ö" data-mw-charinsert-end="" class="mw-charinsert-item">Ö</a>
<a data-mw-charinsert-start="ö" data-mw-charinsert-end="" class="mw-charinsert-item">ö</a>
</p>
<p class="specialbasic" id="Serbian" style="display:none">
<a data-mw-charinsert-start="А" data-mw-charinsert-end="" class="mw-charinsert-item">А</a>
<a data-mw-charinsert-start="а" data-mw-charinsert-end="" class="mw-charinsert-item">а</a>
<a data-mw-charinsert-start="Б" data-mw-charinsert-end="" class="mw-charinsert-item">Б</a>
<a data-mw-charinsert-start="б" data-mw-charinsert-end="" class="mw-charinsert-item">б</a>
<a data-mw-charinsert-start="В" data-mw-charinsert-end="" class="mw-charinsert-item">В</a>
<a data-mw-charinsert-start="в" data-mw-charinsert-end="" class="mw-charinsert-item">в</a>
<a data-mw-charinsert-start="Г" data-mw-charinsert-end="" class="mw-charinsert-item">Г</a>
<a data-mw-charinsert-start="г" data-mw-charinsert-end="" class="mw-charinsert-item">г</a>
<a data-mw-charinsert-start="Д" data-mw-charinsert-end="" class="mw-charinsert-item">Д</a>
<a data-mw-charinsert-start="д" data-mw-charinsert-end="" class="mw-charinsert-item">д</a>
<a data-mw-charinsert-start="Ђ" data-mw-charinsert-end="" class="mw-charinsert-item">Ђ</a>
<a data-mw-charinsert-start="ђ" data-mw-charinsert-end="" class="mw-charinsert-item">ђ</a>
<a data-mw-charinsert-start="Е" data-mw-charinsert-end="" class="mw-charinsert-item">Е</a>
<a data-mw-charinsert-start="е" data-mw-charinsert-end="" class="mw-charinsert-item">е</a>
<a data-mw-charinsert-start="Ж" data-mw-charinsert-end="" class="mw-charinsert-item">Ж</a>
<a data-mw-charinsert-start="ж" data-mw-charinsert-end="" class="mw-charinsert-item">ж</a>
<a data-mw-charinsert-start="З" data-mw-charinsert-end="" class="mw-charinsert-item">З</a>
<a data-mw-charinsert-start="з" data-mw-charinsert-end="" class="mw-charinsert-item">з</a>
<a data-mw-charinsert-start="И" data-mw-charinsert-end="" class="mw-charinsert-item">И</a>
<a data-mw-charinsert-start="и" data-mw-charinsert-end="" class="mw-charinsert-item">и</a>
<a data-mw-charinsert-start="Ј" data-mw-charinsert-end="" class="mw-charinsert-item">Ј</a>
<a data-mw-charinsert-start="ј" data-mw-charinsert-end="" class="mw-charinsert-item">ј</a>
<a data-mw-charinsert-start="К" data-mw-charinsert-end="" class="mw-charinsert-item">К</a>
<a data-mw-charinsert-start="к" data-mw-charinsert-end="" class="mw-charinsert-item">к</a>
<a data-mw-charinsert-start="Л" data-mw-charinsert-end="" class="mw-charinsert-item">Л</a>
<a data-mw-charinsert-start="л" data-mw-charinsert-end="" class="mw-charinsert-item">л</a>
<a data-mw-charinsert-start="Љ" data-mw-charinsert-end="" class="mw-charinsert-item">Љ</a>
<a data-mw-charinsert-start="љ" data-mw-charinsert-end="" class="mw-charinsert-item">љ</a>
<a data-mw-charinsert-start="М" data-mw-charinsert-end="" class="mw-charinsert-item">М</a>
<a data-mw-charinsert-start="м" data-mw-charinsert-end="" class="mw-charinsert-item">м</a>
<a data-mw-charinsert-start="Н" data-mw-charinsert-end="" class="mw-charinsert-item">Н</a>
<a data-mw-charinsert-start="н" data-mw-charinsert-end="" class="mw-charinsert-item">н</a>
<a data-mw-charinsert-start="Њ" data-mw-charinsert-end="" class="mw-charinsert-item">Њ</a>
<a data-mw-charinsert-start="њ" data-mw-charinsert-end="" class="mw-charinsert-item">њ</a>
<a data-mw-charinsert-start="О" data-mw-charinsert-end="" class="mw-charinsert-item">О</a>
<a data-mw-charinsert-start="о" data-mw-charinsert-end="" class="mw-charinsert-item">о</a>
<a data-mw-charinsert-start="П" data-mw-charinsert-end="" class="mw-charinsert-item">П</a>
<a data-mw-charinsert-start="п" data-mw-charinsert-end="" class="mw-charinsert-item">п</a>
<a data-mw-charinsert-start="Р" data-mw-charinsert-end="" class="mw-charinsert-item">Р</a>
<a data-mw-charinsert-start="р" data-mw-charinsert-end="" class="mw-charinsert-item">р</a>
<a data-mw-charinsert-start="С" data-mw-charinsert-end="" class="mw-charinsert-item">С</a>
<a data-mw-charinsert-start="с" data-mw-charinsert-end="" class="mw-charinsert-item">с</a>
<a data-mw-charinsert-start="Т" data-mw-charinsert-end="" class="mw-charinsert-item">Т</a>
<a data-mw-charinsert-start="т" data-mw-charinsert-end="" class="mw-charinsert-item">т</a>
<a data-mw-charinsert-start="Ћ" data-mw-charinsert-end="" class="mw-charinsert-item">Ћ</a>
<a data-mw-charinsert-start="ћ" data-mw-charinsert-end="" class="mw-charinsert-item">ћ</a>
<a data-mw-charinsert-start="У" data-mw-charinsert-end="" class="mw-charinsert-item">У</a>
<a data-mw-charinsert-start="у" data-mw-charinsert-end="" class="mw-charinsert-item">у</a>
<a data-mw-charinsert-start="Ф" data-mw-charinsert-end="" class="mw-charinsert-item">Ф</a>
<a data-mw-charinsert-start="ф" data-mw-charinsert-end="" class="mw-charinsert-item">ф</a>
<a data-mw-charinsert-start="Х" data-mw-charinsert-end="" class="mw-charinsert-item">Х</a>
<a data-mw-charinsert-start="х" data-mw-charinsert-end="" class="mw-charinsert-item">х</a>
<a data-mw-charinsert-start="Ц" data-mw-charinsert-end="" class="mw-charinsert-item">Ц</a>
<a data-mw-charinsert-start="ц" data-mw-charinsert-end="" class="mw-charinsert-item">ц</a>
<a data-mw-charinsert-start="Ч" data-mw-charinsert-end="" class="mw-charinsert-item">Ч</a>
<a data-mw-charinsert-start="ч" data-mw-charinsert-end="" class="mw-charinsert-item">ч</a>
<a data-mw-charinsert-start="Џ" data-mw-charinsert-end="" class="mw-charinsert-item">Џ</a>
<a data-mw-charinsert-start="џ" data-mw-charinsert-end="" class="mw-charinsert-item">џ</a>
<a data-mw-charinsert-start="Ш" data-mw-charinsert-end="" class="mw-charinsert-item">Ш</a>
<a data-mw-charinsert-start="ш" data-mw-charinsert-end="" class="mw-charinsert-item">ш</a>
</p>
<p class="specialbasic" id="Spanish" style="display:none">
<a data-mw-charinsert-start="Á" data-mw-charinsert-end="" class="mw-charinsert-item">Á</a>
<a data-mw-charinsert-start="á" data-mw-charinsert-end="" class="mw-charinsert-item">á</a>
<a data-mw-charinsert-start="É" data-mw-charinsert-end="" class="mw-charinsert-item">É</a>
<a data-mw-charinsert-start="é" data-mw-charinsert-end="" class="mw-charinsert-item">é</a>
<a data-mw-charinsert-start="Í" data-mw-charinsert-end="" class="mw-charinsert-item">Í</a>
<a data-mw-charinsert-start="í" data-mw-charinsert-end="" class="mw-charinsert-item">í</a>
<a data-mw-charinsert-start="Ñ" data-mw-charinsert-end="" class="mw-charinsert-item">Ñ</a>
<a data-mw-charinsert-start="ñ" data-mw-charinsert-end="" class="mw-charinsert-item">ñ</a>
<a data-mw-charinsert-start="Ó" data-mw-charinsert-end="" class="mw-charinsert-item">Ó</a>
<a data-mw-charinsert-start="ó" data-mw-charinsert-end="" class="mw-charinsert-item">ó</a>
<a data-mw-charinsert-start="Ú" data-mw-charinsert-end="" class="mw-charinsert-item">Ú</a>
<a data-mw-charinsert-start="ú" data-mw-charinsert-end="" class="mw-charinsert-item">ú</a>
<a data-mw-charinsert-start="Ü" data-mw-charinsert-end="" class="mw-charinsert-item">Ü</a>
<a data-mw-charinsert-start="ü" data-mw-charinsert-end="" class="mw-charinsert-item">ü</a>
<a data-mw-charinsert-start="¡" data-mw-charinsert-end="" class="mw-charinsert-item">¡</a>
<a data-mw-charinsert-start="¿" data-mw-charinsert-end="" class="mw-charinsert-item">¿</a>
</p>
<p class="specialbasic" id="Turkish" style="display:none">
<a data-mw-charinsert-start="Ç" data-mw-charinsert-end="" class="mw-charinsert-item">Ç</a>
<a data-mw-charinsert-start="ç" data-mw-charinsert-end="" class="mw-charinsert-item">ç</a>
<a data-mw-charinsert-start="Ğ" data-mw-charinsert-end="" class="mw-charinsert-item">Ğ</a>
<a data-mw-charinsert-start="ğ" data-mw-charinsert-end="" class="mw-charinsert-item">ğ</a>
<a data-mw-charinsert-start="İ" data-mw-charinsert-end="" class="mw-charinsert-item">İ</a>
<a data-mw-charinsert-start="ı" data-mw-charinsert-end="" class="mw-charinsert-item">ı</a>
<a data-mw-charinsert-start="Ö" data-mw-charinsert-end="" class="mw-charinsert-item">Ö</a>
<a data-mw-charinsert-start="ö" data-mw-charinsert-end="" class="mw-charinsert-item">ö</a>
<a data-mw-charinsert-start="Ş" data-mw-charinsert-end="" class="mw-charinsert-item">Ş</a>
<a data-mw-charinsert-start="ş" data-mw-charinsert-end="" class="mw-charinsert-item">ş</a>
<a data-mw-charinsert-start="Ü" data-mw-charinsert-end="" class="mw-charinsert-item">Ü</a>
<a data-mw-charinsert-start="ü" data-mw-charinsert-end="" class="mw-charinsert-item">ü</a>
<a data-mw-charinsert-start="Â" data-mw-charinsert-end="" class="mw-charinsert-item">Â</a>
<a data-mw-charinsert-start="â" data-mw-charinsert-end="" class="mw-charinsert-item">â</a>
<a data-mw-charinsert-start="Î" data-mw-charinsert-end="" class="mw-charinsert-item">Î</a>
<a data-mw-charinsert-start="î" data-mw-charinsert-end="" class="mw-charinsert-item">î</a>
<a data-mw-charinsert-start="Û" data-mw-charinsert-end="" class="mw-charinsert-item">Û</a>
<a data-mw-charinsert-start="û" data-mw-charinsert-end="" class="mw-charinsert-item">û</a>
</p>
<p class="specialbasic" id="Vietnamese" style="display:none">
<a data-mw-charinsert-start="À" data-mw-charinsert-end="" class="mw-charinsert-item">À</a>
<a data-mw-charinsert-start="à" data-mw-charinsert-end="" class="mw-charinsert-item">à</a>
<a data-mw-charinsert-start="Ả" data-mw-charinsert-end="" class="mw-charinsert-item">Ả</a>
<a data-mw-charinsert-start="ả" data-mw-charinsert-end="" class="mw-charinsert-item">ả</a>
<a data-mw-charinsert-start="Á" data-mw-charinsert-end="" class="mw-charinsert-item">Á</a>
<a data-mw-charinsert-start="á" data-mw-charinsert-end="" class="mw-charinsert-item">á</a>
<a data-mw-charinsert-start="Ạ" data-mw-charinsert-end="" class="mw-charinsert-item">Ạ</a>
<a data-mw-charinsert-start="ạ" data-mw-charinsert-end="" class="mw-charinsert-item">ạ</a>
<a data-mw-charinsert-start="Ã" data-mw-charinsert-end="" class="mw-charinsert-item">Ã</a>
<a data-mw-charinsert-start="ã" data-mw-charinsert-end="" class="mw-charinsert-item">ã</a>
<a data-mw-charinsert-start="Ă" data-mw-charinsert-end="" class="mw-charinsert-item">Ă</a>
<a data-mw-charinsert-start="ă" data-mw-charinsert-end="" class="mw-charinsert-item">ă</a>
<a data-mw-charinsert-start="Ằ" data-mw-charinsert-end="" class="mw-charinsert-item">Ằ</a>
<a data-mw-charinsert-start="ằ" data-mw-charinsert-end="" class="mw-charinsert-item">ằ</a>
<a data-mw-charinsert-start="Ẳ" data-mw-charinsert-end="" class="mw-charinsert-item">Ẳ</a>
<a data-mw-charinsert-start="ẳ" data-mw-charinsert-end="" class="mw-charinsert-item">ẳ</a>
<a data-mw-charinsert-start="Ẵ" data-mw-charinsert-end="" class="mw-charinsert-item">Ẵ</a>
<a data-mw-charinsert-start="ẵ" data-mw-charinsert-end="" class="mw-charinsert-item">ẵ</a>
<a data-mw-charinsert-start="Ắ" data-mw-charinsert-end="" class="mw-charinsert-item">Ắ</a>
<a data-mw-charinsert-start="ắ" data-mw-charinsert-end="" class="mw-charinsert-item">ắ</a>
<a data-mw-charinsert-start="Ặ" data-mw-charinsert-end="" class="mw-charinsert-item">Ặ</a>
<a data-mw-charinsert-start="ặ" data-mw-charinsert-end="" class="mw-charinsert-item">ặ</a>
<a data-mw-charinsert-start="Â" data-mw-charinsert-end="" class="mw-charinsert-item">Â</a>
<a data-mw-charinsert-start="â" data-mw-charinsert-end="" class="mw-charinsert-item">â</a>
<a data-mw-charinsert-start="Ầ" data-mw-charinsert-end="" class="mw-charinsert-item">Ầ</a>
<a data-mw-charinsert-start="ầ" data-mw-charinsert-end="" class="mw-charinsert-item">ầ</a>
<a data-mw-charinsert-start="Ẩ" data-mw-charinsert-end="" class="mw-charinsert-item">Ẩ</a>
<a data-mw-charinsert-start="ẩ" data-mw-charinsert-end="" class="mw-charinsert-item">ẩ</a>
<a data-mw-charinsert-start="Ẫ" data-mw-charinsert-end="" class="mw-charinsert-item">Ẫ</a>
<a data-mw-charinsert-start="ẫ" data-mw-charinsert-end="" class="mw-charinsert-item">ẫ</a>
<a data-mw-charinsert-start="Ấ" data-mw-charinsert-end="" class="mw-charinsert-item">Ấ</a>
<a data-mw-charinsert-start="ấ" data-mw-charinsert-end="" class="mw-charinsert-item">ấ</a>
<a data-mw-charinsert-start="Ậ" data-mw-charinsert-end="" class="mw-charinsert-item">Ậ</a>
<a data-mw-charinsert-start="ậ" data-mw-charinsert-end="" class="mw-charinsert-item">ậ</a>
<a data-mw-charinsert-start="Đ" data-mw-charinsert-end="" class="mw-charinsert-item">Đ</a>
<a data-mw-charinsert-start="đ" data-mw-charinsert-end="" class="mw-charinsert-item">đ</a>
<a data-mw-charinsert-start="È" data-mw-charinsert-end="" class="mw-charinsert-item">È</a>
<a data-mw-charinsert-start="è" data-mw-charinsert-end="" class="mw-charinsert-item">è</a>
<a data-mw-charinsert-start="Ẻ" data-mw-charinsert-end="" class="mw-charinsert-item">Ẻ</a>
<a data-mw-charinsert-start="ẻ" data-mw-charinsert-end="" class="mw-charinsert-item">ẻ</a>
<a data-mw-charinsert-start="Ẽ" data-mw-charinsert-end="" class="mw-charinsert-item">Ẽ</a>
<a data-mw-charinsert-start="ẽ" data-mw-charinsert-end="" class="mw-charinsert-item">ẽ</a>
<a data-mw-charinsert-start="É" data-mw-charinsert-end="" class="mw-charinsert-item">É</a>
<a data-mw-charinsert-start="é" data-mw-charinsert-end="" class="mw-charinsert-item">é</a>
<a data-mw-charinsert-start="Ẹ" data-mw-charinsert-end="" class="mw-charinsert-item">Ẹ</a>
<a data-mw-charinsert-start="ẹ" data-mw-charinsert-end="" class="mw-charinsert-item">ẹ</a>
<a data-mw-charinsert-start="Ê" data-mw-charinsert-end="" class="mw-charinsert-item">Ê</a>
<a data-mw-charinsert-start="ê" data-mw-charinsert-end="" class="mw-charinsert-item">ê</a>
<a data-mw-charinsert-start="Ề" data-mw-charinsert-end="" class="mw-charinsert-item">Ề</a>
<a data-mw-charinsert-start="ề" data-mw-charinsert-end="" class="mw-charinsert-item">ề</a>
<a data-mw-charinsert-start="Ể" data-mw-charinsert-end="" class="mw-charinsert-item">Ể</a>
<a data-mw-charinsert-start="ể" data-mw-charinsert-end="" class="mw-charinsert-item">ể</a>
<a data-mw-charinsert-start="Ễ" data-mw-charinsert-end="" class="mw-charinsert-item">Ễ</a>
<a data-mw-charinsert-start="ễ" data-mw-charinsert-end="" class="mw-charinsert-item">ễ</a>
<a data-mw-charinsert-start="Ế" data-mw-charinsert-end="" class="mw-charinsert-item">Ế</a>
<a data-mw-charinsert-start="ế" data-mw-charinsert-end="" class="mw-charinsert-item">ế</a>
<a data-mw-charinsert-start="Ệ" data-mw-charinsert-end="" class="mw-charinsert-item">Ệ</a>
<a data-mw-charinsert-start="ệ" data-mw-charinsert-end="" class="mw-charinsert-item">ệ</a>
<a data-mw-charinsert-start="Ỉ" data-mw-charinsert-end="" class="mw-charinsert-item">Ỉ</a>
<a data-mw-charinsert-start="ỉ" data-mw-charinsert-end="" class="mw-charinsert-item">ỉ</a>
<a data-mw-charinsert-start="Ĩ" data-mw-charinsert-end="" class="mw-charinsert-item">Ĩ</a>
<a data-mw-charinsert-start="ĩ" data-mw-charinsert-end="" class="mw-charinsert-item">ĩ</a>
<a data-mw-charinsert-start="Í" data-mw-charinsert-end="" class="mw-charinsert-item">Í</a>
<a data-mw-charinsert-start="í" data-mw-charinsert-end="" class="mw-charinsert-item">í</a>
<a data-mw-charinsert-start="Ị" data-mw-charinsert-end="" class="mw-charinsert-item">Ị</a>
<a data-mw-charinsert-start="ị" data-mw-charinsert-end="" class="mw-charinsert-item">ị</a>
<a data-mw-charinsert-start="Ì" data-mw-charinsert-end="" class="mw-charinsert-item">Ì</a>
<a data-mw-charinsert-start="ì" data-mw-charinsert-end="" class="mw-charinsert-item">ì</a>
<a data-mw-charinsert-start="Ỏ" data-mw-charinsert-end="" class="mw-charinsert-item">Ỏ</a>
<a data-mw-charinsert-start="ỏ" data-mw-charinsert-end="" class="mw-charinsert-item">ỏ</a>
<a data-mw-charinsert-start="Ó" data-mw-charinsert-end="" class="mw-charinsert-item">Ó</a>
<a data-mw-charinsert-start="ó" data-mw-charinsert-end="" class="mw-charinsert-item">ó</a>
<a data-mw-charinsert-start="Ọ" data-mw-charinsert-end="" class="mw-charinsert-item">Ọ</a>
<a data-mw-charinsert-start="ọ" data-mw-charinsert-end="" class="mw-charinsert-item">ọ</a>
<a data-mw-charinsert-start="Ò" data-mw-charinsert-end="" class="mw-charinsert-item">Ò</a>
<a data-mw-charinsert-start="ò" data-mw-charinsert-end="" class="mw-charinsert-item">ò</a>
<a data-mw-charinsert-start="Õ" data-mw-charinsert-end="" class="mw-charinsert-item">Õ</a>
<a data-mw-charinsert-start="õ" data-mw-charinsert-end="" class="mw-charinsert-item">õ</a>
<a data-mw-charinsert-start="Ô" data-mw-charinsert-end="" class="mw-charinsert-item">Ô</a>
<a data-mw-charinsert-start="ô" data-mw-charinsert-end="" class="mw-charinsert-item">ô</a>
<a data-mw-charinsert-start="Ồ" data-mw-charinsert-end="" class="mw-charinsert-item">Ồ</a>
<a data-mw-charinsert-start="ồ" data-mw-charinsert-end="" class="mw-charinsert-item">ồ</a>
<a data-mw-charinsert-start="Ổ" data-mw-charinsert-end="" class="mw-charinsert-item">Ổ</a>
<a data-mw-charinsert-start="ổ" data-mw-charinsert-end="" class="mw-charinsert-item">ổ</a>
<a data-mw-charinsert-start="Ỗ" data-mw-charinsert-end="" class="mw-charinsert-item">Ỗ</a>
<a data-mw-charinsert-start="ỗ" data-mw-charinsert-end="" class="mw-charinsert-item">ỗ</a>
<a data-mw-charinsert-start="Ố" data-mw-charinsert-end="" class="mw-charinsert-item">Ố</a>
<a data-mw-charinsert-start="ố" data-mw-charinsert-end="" class="mw-charinsert-item">ố</a>
<a data-mw-charinsert-start="Ộ" data-mw-charinsert-end="" class="mw-charinsert-item">Ộ</a>
<a data-mw-charinsert-start="ộ" data-mw-charinsert-end="" class="mw-charinsert-item">ộ</a>
<a data-mw-charinsert-start="Ơ" data-mw-charinsert-end="" class="mw-charinsert-item">Ơ</a>
<a data-mw-charinsert-start="ơ" data-mw-charinsert-end="" class="mw-charinsert-item">ơ</a>
<a data-mw-charinsert-start="Ờ" data-mw-charinsert-end="" class="mw-charinsert-item">Ờ</a>
<a data-mw-charinsert-start="ờ" data-mw-charinsert-end="" class="mw-charinsert-item">ờ</a>
<a data-mw-charinsert-start="Ở" data-mw-charinsert-end="" class="mw-charinsert-item">Ở</a>
<a data-mw-charinsert-start="ở" data-mw-charinsert-end="" class="mw-charinsert-item">ở</a>
<a data-mw-charinsert-start="Ỡ" data-mw-charinsert-end="" class="mw-charinsert-item">Ỡ</a>
<a data-mw-charinsert-start="ỡ" data-mw-charinsert-end="" class="mw-charinsert-item">ỡ</a>
<a data-mw-charinsert-start="Ớ" data-mw-charinsert-end="" class="mw-charinsert-item">Ớ</a>
<a data-mw-charinsert-start="ớ" data-mw-charinsert-end="" class="mw-charinsert-item">ớ</a>
<a data-mw-charinsert-start="Ợ" data-mw-charinsert-end="" class="mw-charinsert-item">Ợ</a>
<a data-mw-charinsert-start="ợ" data-mw-charinsert-end="" class="mw-charinsert-item">ợ</a>
<a data-mw-charinsert-start="Ù" data-mw-charinsert-end="" class="mw-charinsert-item">Ù</a>
<a data-mw-charinsert-start="ù" data-mw-charinsert-end="" class="mw-charinsert-item">ù</a>
<a data-mw-charinsert-start="Ủ" data-mw-charinsert-end="" class="mw-charinsert-item">Ủ</a>
<a data-mw-charinsert-start="ủ" data-mw-charinsert-end="" class="mw-charinsert-item">ủ</a>
<a data-mw-charinsert-start="Ũ" data-mw-charinsert-end="" class="mw-charinsert-item">Ũ</a>
<a data-mw-charinsert-start="ũ" data-mw-charinsert-end="" class="mw-charinsert-item">ũ</a>
<a data-mw-charinsert-start="Ú" data-mw-charinsert-end="" class="mw-charinsert-item">Ú</a>
<a data-mw-charinsert-start="ú" data-mw-charinsert-end="" class="mw-charinsert-item">ú</a>
<a data-mw-charinsert-start="Ụ" data-mw-charinsert-end="" class="mw-charinsert-item">Ụ</a>
<a data-mw-charinsert-start="ụ" data-mw-charinsert-end="" class="mw-charinsert-item">ụ</a>
<a data-mw-charinsert-start="Ư" data-mw-charinsert-end="" class="mw-charinsert-item">Ư</a>
<a data-mw-charinsert-start="ư" data-mw-charinsert-end="" class="mw-charinsert-item">ư</a>
<a data-mw-charinsert-start="Ừ" data-mw-charinsert-end="" class="mw-charinsert-item">Ừ</a>
<a data-mw-charinsert-start="ừ" data-mw-charinsert-end="" class="mw-charinsert-item">ừ</a>
<a data-mw-charinsert-start="Ử" data-mw-charinsert-end="" class="mw-charinsert-item">Ử</a>
<a data-mw-charinsert-start="ử" data-mw-charinsert-end="" class="mw-charinsert-item">ử</a>
<a data-mw-charinsert-start="Ữ" data-mw-charinsert-end="" class="mw-charinsert-item">Ữ</a>
<a data-mw-charinsert-start="ữ" data-mw-charinsert-end="" class="mw-charinsert-item">ữ</a>
<a data-mw-charinsert-start="Ứ" data-mw-charinsert-end="" class="mw-charinsert-item">Ứ</a>
<a data-mw-charinsert-start="ứ" data-mw-charinsert-end="" class="mw-charinsert-item">ứ</a>
<a data-mw-charinsert-start="Ự" data-mw-charinsert-end="" class="mw-charinsert-item">Ự</a>
<a data-mw-charinsert-start="ự" data-mw-charinsert-end="" class="mw-charinsert-item">ự</a>
<a data-mw-charinsert-start="Ỳ" data-mw-charinsert-end="" class="mw-charinsert-item">Ỳ</a>
<a data-mw-charinsert-start="ỳ" data-mw-charinsert-end="" class="mw-charinsert-item">ỳ</a>
<a data-mw-charinsert-start="Ỷ" data-mw-charinsert-end="" class="mw-charinsert-item">Ỷ</a>
<a data-mw-charinsert-start="ỷ" data-mw-charinsert-end="" class="mw-charinsert-item">ỷ</a>
<a data-mw-charinsert-start="Ỹ" data-mw-charinsert-end="" class="mw-charinsert-item">Ỹ</a>
<a data-mw-charinsert-start="ỹ" data-mw-charinsert-end="" class="mw-charinsert-item">ỹ</a>
<a data-mw-charinsert-start="Ỵ" data-mw-charinsert-end="" class="mw-charinsert-item">Ỵ</a>
<a data-mw-charinsert-start="ỵ" data-mw-charinsert-end="" class="mw-charinsert-item">ỵ</a>
<a data-mw-charinsert-start="Ý" data-mw-charinsert-end="" class="mw-charinsert-item">Ý</a>
<a data-mw-charinsert-start="ý" data-mw-charinsert-end="" class="mw-charinsert-item">ý</a>
</p>
<p class="specialbasic" id="Welsh" style="display:none">
<a data-mw-charinsert-start="Á" data-mw-charinsert-end="" class="mw-charinsert-item">Á</a>
<a data-mw-charinsert-start="á" data-mw-charinsert-end="" class="mw-charinsert-item">á</a>
<a data-mw-charinsert-start="À" data-mw-charinsert-end="" class="mw-charinsert-item">À</a>
<a data-mw-charinsert-start="à" data-mw-charinsert-end="" class="mw-charinsert-item">à</a>
<a data-mw-charinsert-start="Â" data-mw-charinsert-end="" class="mw-charinsert-item">Â</a>
<a data-mw-charinsert-start="â" data-mw-charinsert-end="" class="mw-charinsert-item">â</a>
<a data-mw-charinsert-start="Ä" data-mw-charinsert-end="" class="mw-charinsert-item">Ä</a>
<a data-mw-charinsert-start="ä" data-mw-charinsert-end="" class="mw-charinsert-item">ä</a>
<a data-mw-charinsert-start="É" data-mw-charinsert-end="" class="mw-charinsert-item">É</a>
<a data-mw-charinsert-start="é" data-mw-charinsert-end="" class="mw-charinsert-item">é</a>
<a data-mw-charinsert-start="È" data-mw-charinsert-end="" class="mw-charinsert-item">È</a>
<a data-mw-charinsert-start="è" data-mw-charinsert-end="" class="mw-charinsert-item">è</a>
<a data-mw-charinsert-start="Ê" data-mw-charinsert-end="" class="mw-charinsert-item">Ê</a>
<a data-mw-charinsert-start="ê" data-mw-charinsert-end="" class="mw-charinsert-item">ê</a>
<a data-mw-charinsert-start="Ë" data-mw-charinsert-end="" class="mw-charinsert-item">Ë</a>
<a data-mw-charinsert-start="ë" data-mw-charinsert-end="" class="mw-charinsert-item">ë</a>
<a data-mw-charinsert-start="Ì" data-mw-charinsert-end="" class="mw-charinsert-item">Ì</a>
<a data-mw-charinsert-start="ì" data-mw-charinsert-end="" class="mw-charinsert-item">ì</a>
<a data-mw-charinsert-start="Î" data-mw-charinsert-end="" class="mw-charinsert-item">Î</a>
<a data-mw-charinsert-start="î" data-mw-charinsert-end="" class="mw-charinsert-item">î</a>
<a data-mw-charinsert-start="Ï" data-mw-charinsert-end="" class="mw-charinsert-item">Ï</a>
<a data-mw-charinsert-start="ï" data-mw-charinsert-end="" class="mw-charinsert-item">ï</a>
<a data-mw-charinsert-start="Ó" data-mw-charinsert-end="" class="mw-charinsert-item">Ó</a>
<a data-mw-charinsert-start="ó" data-mw-charinsert-end="" class="mw-charinsert-item">ó</a>
<a data-mw-charinsert-start="Ò" data-mw-charinsert-end="" class="mw-charinsert-item">Ò</a>
<a data-mw-charinsert-start="ò" data-mw-charinsert-end="" class="mw-charinsert-item">ò</a>
<a data-mw-charinsert-start="Ô" data-mw-charinsert-end="" class="mw-charinsert-item">Ô</a>
<a data-mw-charinsert-start="ô" data-mw-charinsert-end="" class="mw-charinsert-item">ô</a>
<a data-mw-charinsert-start="Ö" data-mw-charinsert-end="" class="mw-charinsert-item">Ö</a>
<a data-mw-charinsert-start="ö" data-mw-charinsert-end="" class="mw-charinsert-item">ö</a>
<a data-mw-charinsert-start="Ù" data-mw-charinsert-end="" class="mw-charinsert-item">Ù</a>
<a data-mw-charinsert-start="ù" data-mw-charinsert-end="" class="mw-charinsert-item">ù</a>
<a data-mw-charinsert-start="Û" data-mw-charinsert-end="" class="mw-charinsert-item">Û</a>
<a data-mw-charinsert-start="û" data-mw-charinsert-end="" class="mw-charinsert-item">û</a>
<a data-mw-charinsert-start="Ẁ" data-mw-charinsert-end="" class="mw-charinsert-item">Ẁ</a>
<a data-mw-charinsert-start="ẁ" data-mw-charinsert-end="" class="mw-charinsert-item">ẁ</a>
<a data-mw-charinsert-start="Ŵ" data-mw-charinsert-end="" class="mw-charinsert-item">Ŵ</a>
<a data-mw-charinsert-start="ŵ" data-mw-charinsert-end="" class="mw-charinsert-item">ŵ</a>
<a data-mw-charinsert-start="Ẅ" data-mw-charinsert-end="" class="mw-charinsert-item">Ẅ</a>
<a data-mw-charinsert-start="ẅ" data-mw-charinsert-end="" class="mw-charinsert-item">ẅ</a>
<a data-mw-charinsert-start="Ý" data-mw-charinsert-end="" class="mw-charinsert-item">Ý</a>
<a data-mw-charinsert-start="ý" data-mw-charinsert-end="" class="mw-charinsert-item">ý</a>
<a data-mw-charinsert-start="Ỳ" data-mw-charinsert-end="" class="mw-charinsert-item">Ỳ</a>
<a data-mw-charinsert-start="ỳ" data-mw-charinsert-end="" class="mw-charinsert-item">ỳ</a>
<a data-mw-charinsert-start="Ŷ" data-mw-charinsert-end="" class="mw-charinsert-item">Ŷ</a>
<a data-mw-charinsert-start="ŷ" data-mw-charinsert-end="" class="mw-charinsert-item">ŷ</a>
<a data-mw-charinsert-start="Ÿ" data-mw-charinsert-end="" class="mw-charinsert-item">Ÿ</a>
<a data-mw-charinsert-start="ÿ" data-mw-charinsert-end="" class="mw-charinsert-item">ÿ</a>
</p>
<p class="specialbasic" id="Yiddish" style="display:none">
<a data-mw-charinsert-start="א" data-mw-charinsert-end="" class="mw-charinsert-item">א</a>
<a data-mw-charinsert-start="אַ" data-mw-charinsert-end="" class="mw-charinsert-item">אַ</a>
<a data-mw-charinsert-start="אָ" data-mw-charinsert-end="" class="mw-charinsert-item">אָ</a>
<a data-mw-charinsert-start="ב" data-mw-charinsert-end="" class="mw-charinsert-item">ב</a>
<a data-mw-charinsert-start="בֿ" data-mw-charinsert-end="" class="mw-charinsert-item">בֿ</a>
<a data-mw-charinsert-start="ג" data-mw-charinsert-end="" class="mw-charinsert-item">ג</a>
<a data-mw-charinsert-start="ד" data-mw-charinsert-end="" class="mw-charinsert-item">ד</a>
<a data-mw-charinsert-start="ה" data-mw-charinsert-end="" class="mw-charinsert-item">ה</a>
<a data-mw-charinsert-start="ו" data-mw-charinsert-end="" class="mw-charinsert-item">ו</a>
<a data-mw-charinsert-start="וּ" data-mw-charinsert-end="" class="mw-charinsert-item">וּ</a>
<a data-mw-charinsert-start="װ" data-mw-charinsert-end="" class="mw-charinsert-item">װ</a>
<a data-mw-charinsert-start="ױ" data-mw-charinsert-end="" class="mw-charinsert-item">ױ</a>
<a data-mw-charinsert-start="ז" data-mw-charinsert-end="" class="mw-charinsert-item">ז</a>
<a data-mw-charinsert-start="זש" data-mw-charinsert-end="" class="mw-charinsert-item">זש</a>
<a data-mw-charinsert-start="ח" data-mw-charinsert-end="" class="mw-charinsert-item">ח</a>
<a data-mw-charinsert-start="ט" data-mw-charinsert-end="" class="mw-charinsert-item">ט</a>
<a data-mw-charinsert-start="י" data-mw-charinsert-end="" class="mw-charinsert-item">י</a>
<a data-mw-charinsert-start="יִ" data-mw-charinsert-end="" class="mw-charinsert-item">יִ</a>
<a data-mw-charinsert-start="ײ" data-mw-charinsert-end="" class="mw-charinsert-item">ײ</a>
<a data-mw-charinsert-start="ײַ" data-mw-charinsert-end="" class="mw-charinsert-item">ײַ</a>
<a data-mw-charinsert-start="כ" data-mw-charinsert-end="" class="mw-charinsert-item">כ</a>
<a data-mw-charinsert-start="ך" data-mw-charinsert-end="" class="mw-charinsert-item">ך</a>
<a data-mw-charinsert-start="כּ" data-mw-charinsert-end="" class="mw-charinsert-item">כּ</a>
<a data-mw-charinsert-start="ל" data-mw-charinsert-end="" class="mw-charinsert-item">ל</a>
<a data-mw-charinsert-start="ל" data-mw-charinsert-end="" class="mw-charinsert-item">ל</a>
<a data-mw-charinsert-start="מ" data-mw-charinsert-end="" class="mw-charinsert-item">מ</a>
<a data-mw-charinsert-start="ם" data-mw-charinsert-end="" class="mw-charinsert-item">ם</a>
<a data-mw-charinsert-start="נ" data-mw-charinsert-end="" class="mw-charinsert-item">נ</a>
<a data-mw-charinsert-start="ן" data-mw-charinsert-end="" class="mw-charinsert-item">ן</a>
<a data-mw-charinsert-start="ס" data-mw-charinsert-end="" class="mw-charinsert-item">ס</a>
<a data-mw-charinsert-start="ע" data-mw-charinsert-end="" class="mw-charinsert-item">ע</a>
<a data-mw-charinsert-start="ע" data-mw-charinsert-end="" class="mw-charinsert-item">ע</a>
<a data-mw-charinsert-start="פ" data-mw-charinsert-end="" class="mw-charinsert-item">פ</a>
<a data-mw-charinsert-start="פּ" data-mw-charinsert-end="" class="mw-charinsert-item">פּ</a>
<a data-mw-charinsert-start="פֿ" data-mw-charinsert-end="" class="mw-charinsert-item">פֿ</a>
<a data-mw-charinsert-start="ף" data-mw-charinsert-end="" class="mw-charinsert-item">ף</a>
<a data-mw-charinsert-start="צ" data-mw-charinsert-end="" class="mw-charinsert-item">צ</a>
<a data-mw-charinsert-start="ץ" data-mw-charinsert-end="" class="mw-charinsert-item">ץ</a>
<a data-mw-charinsert-start="ק" data-mw-charinsert-end="" class="mw-charinsert-item">ק</a>
<a data-mw-charinsert-start="ר" data-mw-charinsert-end="" class="mw-charinsert-item">ר</a>
<a data-mw-charinsert-start="ש" data-mw-charinsert-end="" class="mw-charinsert-item">ש</a>
<a data-mw-charinsert-start="שׂ" data-mw-charinsert-end="" class="mw-charinsert-item">שׂ</a>
<a data-mw-charinsert-start="תּ" data-mw-charinsert-end="" class="mw-charinsert-item">תּ</a>
<a data-mw-charinsert-start="ת" data-mw-charinsert-end="" class="mw-charinsert-item">ת</a>
<a data-mw-charinsert-start="׳" data-mw-charinsert-end="" class="mw-charinsert-item">׳</a>
<a data-mw-charinsert-start="״" data-mw-charinsert-end="" class="mw-charinsert-item">״</a>
<a data-mw-charinsert-start="־" data-mw-charinsert-end="" class="mw-charinsert-item">־</a>
</p>
</div></div><div class="wikibase-entity-usage"><div class="wikibase-entityusage-explanation"><p>Wikidata entities used in this page
</p></div>
<ul><li><a class="extiw wb-entity-link external" href="https://www.wikidata.org/wiki/Special:EntityPage/M36601495">M36601495</a>: Title, Miscellaneous (e.g. aliases, entity existence)</li><li><a class="extiw wb-entity-link external" href="https://www.wikidata.org/wiki/Special:EntityPage/Q114">Kenya</a>: Sitelink, Some labels, Some descriptions</li></ul></div>
<div class="templatesUsed"><div class="mw-templatesUsedExplanation"><p>Templates used on this page:
</p></div><ul>
<li><a href="https://commons.wikimedia.org/wiki/Template:Cc-by-sa-4.0" title="Template:Cc-by-sa-4.0">Template:Cc-by-sa-4.0</a> (<a href="https://commons.wikimedia.org/w/index.php?title=Template:Cc-by-sa-4.0&amp;action=edit" title="Template:Cc-by-sa-4.0">view source</a>) (protected)</li><li><a href="https://commons.wikimedia.org/wiki/Template:Cc-by-sa-layout" title="Template:Cc-by-sa-layout">Template:Cc-by-sa-layout</a> (<a href="https://commons.wikimedia.org/w/index.php?title=Template:Cc-by-sa-layout&amp;action=edit" title="Template:Cc-by-sa-layout">view source</a>) (protected)</li><li><a href="https://commons.wikimedia.org/wiki/Template:Country" title="Template:Country">Template:Country</a> (<a href="https://commons.wikimedia.org/w/index.php?title=Template:Country&amp;action=edit" title="Template:Country">view source</a>) (template editor protected)</li><li><a href="https://commons.wikimedia.org/wiki/Template:Edit" title="Template:Edit">Template:Edit</a> (<a href="https://commons.wikimedia.org/w/index.php?title=Template:Edit&amp;action=edit" title="Template:Edit">view source</a>) (protected)</li><li><a href="https://commons.wikimedia.org/wiki/Template:En" title="Template:En">Template:En</a> (<a href="https://commons.wikimedia.org/w/index.php?title=Template:En&amp;action=edit" title="Template:En">view source</a>) (protected)</li><li><a href="https://commons.wikimedia.org/wiki/Template:Encodefirst" title="Template:Encodefirst">Template:Encodefirst</a> (<a href="https://commons.wikimedia.org/w/index.php?title=Template:Encodefirst&amp;action=edit" title="Template:Encodefirst">view source</a>) (template editor protected)</li><li><a href="https://commons.wikimedia.org/wiki/Template:Ig" title="Template:Ig">Template:Ig</a> (<a href="https://commons.wikimedia.org/w/index.php?title=Template:Ig&amp;action=edit" title="Template:Ig">edit</a>) </li><li><a href="https://commons.wikimedia.org/wiki/Template:Infobox_template_tag" title="Template:Infobox template tag">Template:Infobox template tag</a> (<a href="https://commons.wikimedia.org/w/index.php?title=Template:Infobox_template_tag&amp;action=edit" title="Template:Infobox template tag">view source</a>) (protected)</li><li><a href="https://commons.wikimedia.org/wiki/Template:Information" title="Template:Information">Template:Information</a> (<a href="https://commons.wikimedia.org/w/index.php?title=Template:Information&amp;action=edit" title="Template:Information">view source</a>) (protected)</li><li><a href="https://commons.wikimedia.org/wiki/Template:Kenya" title="Template:Kenya">Template:Kenya</a> (<a href="https://commons.wikimedia.org/w/index.php?title=Template:Kenya&amp;action=edit" title="Template:Kenya">edit</a>) </li><li><a href="https://commons.wikimedia.org/wiki/Template:Label" title="Template:Label">Template:Label</a> (<a href="https://commons.wikimedia.org/w/index.php?title=Template:Label&amp;action=edit" title="Template:Label">view source</a>) (template editor protected)</li><li><a href="https://commons.wikimedia.org/wiki/Template:Lang" title="Template:Lang">Template:Lang</a> (<a href="https://commons.wikimedia.org/w/index.php?title=Template:Lang&amp;action=edit" title="Template:Lang">view source</a>) (protected)</li><li><a href="https://commons.wikimedia.org/wiki/Template:License_template_tag" title="Template:License template tag">Template:License template tag</a> (<a href="https://commons.wikimedia.org/w/index.php?title=Template:License_template_tag&amp;action=edit" title="Template:License template tag">view source</a>) (protected)</li><li><a href="https://commons.wikimedia.org/wiki/Template:Own" title="Template:Own">Template:Own</a> (<a href="https://commons.wikimedia.org/w/index.php?title=Template:Own&amp;action=edit" title="Template:Own">view source</a>) (protected)</li><li><a href="https://commons.wikimedia.org/wiki/Template:Partnership-Layout" title="Template:Partnership-Layout">Template:Partnership-Layout</a> (<a href="https://commons.wikimedia.org/w/index.php?title=Template:Partnership-Layout&amp;action=edit" title="Template:Partnership-Layout">view source</a>) (protected)</li><li><a href="https://commons.wikimedia.org/wiki/Template:Self" title="Template:Self">Template:Self</a> (<a href="https://commons.wikimedia.org/w/index.php?title=Template:Self&amp;action=edit" title="Template:Self">view source</a>) (protected)</li><li><a href="https://commons.wikimedia.org/wiki/Template:Smartlink" title="Template:Smartlink">Template:Smartlink</a> (<a href="https://commons.wikimedia.org/w/index.php?title=Template:Smartlink&amp;action=edit" title="Template:Smartlink">view source</a>) (template editor protected)</li><li><a href="https://commons.wikimedia.org/wiki/Template:Wiki_Loves_Africa_2014" title="Template:Wiki Loves Africa 2014">Template:Wiki Loves Africa 2014</a> (<a href="https://commons.wikimedia.org/w/index.php?title=Template:Wiki_Loves_Africa_2014&amp;action=edit" title="Template:Wiki Loves Africa 2014">edit</a>) </li><li><a href="https://commons.wikimedia.org/wiki/Template:Wiki_Loves_Africa_2014/en" title="Template:Wiki Loves Africa 2014/en">Template:Wiki Loves Africa 2014/en</a> (<a href="https://commons.wikimedia.org/w/index.php?title=Template:Wiki_Loves_Africa_2014/en&amp;action=edit" title="Template:Wiki Loves Africa 2014/en">edit</a>) </li><li><a href="https://commons.wikimedia.org/wiki/Template:Wiki_Loves_Africa_2014/lang" title="Template:Wiki Loves Africa 2014/lang">Template:Wiki Loves Africa 2014/lang</a> (<a href="https://commons.wikimedia.org/w/index.php?title=Template:Wiki_Loves_Africa_2014/lang&amp;action=edit" title="Template:Wiki Loves Africa 2014/lang">edit</a>) </li><li><a href="https://commons.wikimedia.org/wiki/Template:Wiki_Loves_Africa_2014/layout" title="Template:Wiki Loves Africa 2014/layout">Template:Wiki Loves Africa 2014/layout</a> (<a href="https://commons.wikimedia.org/w/index.php?title=Template:Wiki_Loves_Africa_2014/layout&amp;action=edit" title="Template:Wiki Loves Africa 2014/layout">edit</a>) </li><li><a href="https://commons.wikimedia.org/wiki/Template:Wiki_Loves_Africa_2014_country" title="Template:Wiki Loves Africa 2014 country">Template:Wiki Loves Africa 2014 country</a> (<a href="https://commons.wikimedia.org/w/index.php?title=Template:Wiki_Loves_Africa_2014_country&amp;action=edit" title="Template:Wiki Loves Africa 2014 country">edit</a>) </li><li><a href="https://commons.wikimedia.org/wiki/Template:Wiki_Loves_Africa_2014_country/en" title="Template:Wiki Loves Africa 2014 country/en">Template:Wiki Loves Africa 2014 country/en</a> (<a href="https://commons.wikimedia.org/w/index.php?title=Template:Wiki_Loves_Africa_2014_country/en&amp;action=edit" title="Template:Wiki Loves Africa 2014 country/en">edit</a>) </li><li><a href="https://commons.wikimedia.org/wiki/Template:Wiki_Loves_Africa_2014_country/layout" title="Template:Wiki Loves Africa 2014 country/layout">Template:Wiki Loves Africa 2014 country/layout</a> (<a href="https://commons.wikimedia.org/w/index.php?title=Template:Wiki_Loves_Africa_2014_country/layout&amp;action=edit" title="Template:Wiki Loves Africa 2014 country/layout">edit</a>) </li><li><a href="https://commons.wikimedia.org/wiki/Module:Autotranslate" title="Module:Autotranslate">Module:Autotranslate</a> (<a href="https://commons.wikimedia.org/w/index.php?title=Module:Autotranslate&amp;action=edit" title="Module:Autotranslate">view source</a>) (protected)</li><li><a href="https://commons.wikimedia.org/wiki/Module:Core" title="Module:Core">Module:Core</a> (<a href="https://commons.wikimedia.org/w/index.php?title=Module:Core&amp;action=edit" title="Module:Core">view source</a>) (template editor protected)</li><li><a href="https://commons.wikimedia.org/wiki/Module:DateI18n" title="Module:DateI18n">Module:DateI18n</a> (<a href="https://commons.wikimedia.org/w/index.php?title=Module:DateI18n&amp;action=edit" title="Module:DateI18n">view source</a>) (template editor protected)</li><li><a href="https://commons.wikimedia.org/wiki/Module:Description" title="Module:Description">Module:Description</a> (<a href="https://commons.wikimedia.org/w/index.php?title=Module:Description&amp;action=edit" title="Module:Description">view source</a>) (template editor protected)</li><li><a href="https://commons.wikimedia.org/wiki/Module:ISOdate" title="Module:ISOdate">Module:ISOdate</a> (<a href="https://commons.wikimedia.org/w/index.php?title=Module:ISOdate&amp;action=edit" title="Module:ISOdate">view source</a>) (protected)</li><li><a href="https://commons.wikimedia.org/wiki/Module:Information" title="Module:Information">Module:Information</a> (<a href="https://commons.wikimedia.org/w/index.php?title=Module:Information&amp;action=edit" title="Module:Information">view source</a>) (template editor protected)</li><li><a href="https://commons.wikimedia.org/wiki/Module:MultiReplace" title="Module:MultiReplace">Module:MultiReplace</a> (<a href="https://commons.wikimedia.org/w/index.php?title=Module:MultiReplace&amp;action=edit" title="Module:MultiReplace">view source</a>) (template editor protected)</li><li><a href="https://commons.wikimedia.org/wiki/Module:SDC_tracking" title="Module:SDC tracking">Module:SDC tracking</a> (<a href="https://commons.wikimedia.org/w/index.php?title=Module:SDC_tracking&amp;action=edit" title="Module:SDC tracking">view source</a>) (template editor protected)</li><li><a href="https://commons.wikimedia.org/wiki/Module:Self" title="Module:Self">Module:Self</a> (<a href="https://commons.wikimedia.org/w/index.php?title=Module:Self&amp;action=edit" title="Module:Self">view source</a>) (template editor protected)</li><li><a href="https://commons.wikimedia.org/wiki/Module:Smartlink" title="Module:Smartlink">Module:Smartlink</a> (<a href="https://commons.wikimedia.org/w/index.php?title=Module:Smartlink&amp;action=edit" title="Module:Smartlink">view source</a>) (template editor protected)</li><li><a href="https://commons.wikimedia.org/wiki/Module:Wikidata_label" title="Module:Wikidata label">Module:Wikidata label</a> (<a href="https://commons.wikimedia.org/w/index.php?title=Module:Wikidata_label&amp;action=edit" title="Module:Wikidata label">view source</a>) (template editor protected)</li></ul></div><div class="hiddencats"><div class="mw-hiddenCategoriesExplanation"><p>This page is a member of 5 hidden categories:
</p></div><ul>
<li><a href="https://commons.wikimedia.org/wiki/Category:CC-BY-SA-4.0" title="Category:CC-BY-SA-4.0">Category:CC-BY-SA-4.0</a></li>
<li><a href="https://commons.wikimedia.org/wiki/Category:Images_from_Wiki_Loves_Africa_2014" title="Category:Images from Wiki Loves Africa 2014">Category:Images from Wiki Loves Africa 2014</a></li>
<li><a href="https://commons.wikimedia.org/wiki/Category:Images_from_Wiki_Loves_Africa_2014_in_Kenya" title="Category:Images from Wiki Loves Africa 2014 in Kenya">Category:Images from Wiki Loves Africa 2014 in Kenya</a></li>
<li><a href="https://commons.wikimedia.org/wiki/Category:Self-published_work" title="Category:Self-published work">Category:Self-published work</a></li>
<li><a href="https://commons.wikimedia.org/wiki/Category:Uploaded_via_Campaign:wlafrica" title="Category:Uploaded via Campaign:wlafrica">Category:Uploaded via Campaign:wlafrica</a></li>
</ul></div><div class="limitreport"></div><input id="mw-edit-mode" type="hidden" value="text" name="mode"><input type="hidden" value="1" name="wpUltimateParam">
</form>
<!--esi <esi:include src="/esitest-fa8a495983347898/content" /> --><noscript><img src="https://commons.wikimedia.org/wiki/Special:CentralAutoLogin/start?type=1x1&amp;usesul3=1" alt="" width="1" height="1" style="border: none; position: absolute;"></noscript>
<div class="printfooter" data-nosnippet="">Retrieved from "<a dir="ltr" href="https://commons.wikimedia.org/wiki/File:Madafu-chopping.jpg">https://commons.wikimedia.org/wiki/File:Madafu-chopping.jpg</a>"</div></div>
					<div id="catlinks" class="catlinks catlinks-allhidden" data-mw="interface"></div>
				</div>
			</main>
			
		</div>
		<div class="mw-footer-container">
			
<footer id="footer" class="mw-footer" >
	<ul id="footer-info">
</ul>

	<ul id="footer-places">
	<li id="footer-places-privacy"><a href="https://foundation.wikimedia.org/wiki/Special:MyLanguage/Policy:Privacy_policy">Privacy policy</a></li>
	<li id="footer-places-about"><a href="https://commons.wikimedia.org/wiki/Commons:Welcome">About Wikimedia Commons</a></li>
	<li id="footer-places-disclaimers"><a href="https://commons.wikimedia.org/wiki/Commons:General_disclaimer">Disclaimers</a></li>
	<li id="footer-places-wm-codeofconduct"><a href="https://foundation.wikimedia.org/wiki/Special:MyLanguage/Policy:Universal_Code_of_Conduct">Code of Conduct</a></li>
	<li id="footer-places-developers"><a href="https://developer.wikimedia.org/">Developers</a></li>
	<li id="footer-places-statslink"><a href="https://stats.wikimedia.org/#/commons.wikimedia.org">Statistics</a></li>
	<li id="footer-places-cookiestatement"><a href="https://foundation.wikimedia.org/wiki/Special:MyLanguage/Policy:Cookie_statement">Cookie statement</a></li>
	<li id="footer-places-mobileview"><a href="http://commons.m.wikimedia.org/w/index.php?title=File:Madafu-chopping.jpg&amp;action=edit&amp;mobileaction=toggle_view_mobile" class="noprint stopMobileRedirectToggle">Mobile view</a></li>
</ul>

	<ul id="footer-icons" class="noprint">
	<li id="footer-copyrightico"><a href="https://www.wikimedia.org/" class="cdx-button cdx-button--fake-button cdx-button--size-large cdx-button--fake-button--enabled"><picture><source media="(min-width: 500px)" srcset="/static/images/footer/wikimedia-button.svg" width="84" height="29"><img src="https://commons.wikimedia.org/static/images/footer/wikimedia.svg" width="25" height="25" alt="Wikimedia Foundation" lang="en" loading="lazy"></picture></a></li>
	<li id="footer-poweredbyico"><a href="https://www.mediawiki.org/" class="cdx-button cdx-button--fake-button cdx-button--size-large cdx-button--fake-button--enabled"><picture><source media="(min-width: 500px)" srcset="/w/resources/assets/poweredby_mediawiki.svg" width="88" height="31"><img src="https://commons.wikimedia.org/w/resources/assets/mediawiki_compact.svg" alt="Powered by MediaWiki" lang="en" width="25" height="25" loading="lazy"></picture></a></li>
</ul>

</footer>

		</div>
	</div> 
</div> 
<div class="vector-header-container vector-sticky-header-container no-font-mode-scale">
	<div id="vector-sticky-header" class="vector-sticky-header">
		<div class="vector-sticky-header-start">
			<div class="vector-sticky-header-icon-start vector-button-flush-left vector-button-flush-right" aria-hidden="true">
				<button class="cdx-button cdx-button--weight-quiet cdx-button--icon-only vector-sticky-header-search-toggle" tabindex="-1" data-event-name="ui.vector-sticky-search-form.icon"><span class="vector-icon mw-ui-icon-search mw-ui-icon-wikimedia-search"></span>

<span>Search</span>
			</button>
		</div>
			
		<div role="search" class="vector-search-box-vue vector-search-box">
			<div class="vector-typeahead-search-container">
				<div class="cdx-typeahead-search">
					<form action="https://commons.wikimedia.org/w/index.php" id="vector-sticky-search-form" class="cdx-search-input cdx-search-input--has-end-button">
						<div  class="cdx-search-input__input-wrapper"  data-search-loc="header-moved">
							<div class="cdx-text-input cdx-text-input--has-start-icon">
								<input
									class="cdx-text-input__input mw-searchInput" autocomplete="off"
									
									type="search" name="search" placeholder="Search Wikimedia Commons">
								<span class="cdx-text-input__icon cdx-text-input__start-icon"></span>
							</div>
							<input type="hidden" name="title" value="Special:MediaSearch">
						</div>
						<button class="cdx-button cdx-search-input__end-button">Search</button>
					</form>
				</div>
			</div>
		</div>
		<div class="vector-sticky-header-context-bar">
				<div class="vector-sticky-header-context-bar-primary" aria-hidden="true" >Editing <span id="firstHeadingTitle">File:Madafu-chopping.jpg</span></div>
			</div>
		</div>
		<div class="vector-sticky-header-end" aria-hidden="true">
			<div class="vector-sticky-header-icons">
				<a href="#" class="cdx-button cdx-button--fake-button cdx-button--fake-button--enabled cdx-button--weight-quiet cdx-button--icon-only" id="ca-talk-sticky-header" tabindex="-1" data-event-name="talk-sticky-header"><span class="vector-icon mw-ui-icon-speechBubbles mw-ui-icon-wikimedia-speechBubbles"></span>

<span></span>
			</a>
			<a href="#" class="cdx-button cdx-button--fake-button cdx-button--fake-button--enabled cdx-button--weight-quiet cdx-button--icon-only" id="ca-subject-sticky-header" tabindex="-1" data-event-name="subject-sticky-header"><span class="vector-icon mw-ui-icon-article mw-ui-icon-wikimedia-article"></span>

<span></span>
			</a>
			<a href="#" class="cdx-button cdx-button--fake-button cdx-button--fake-button--enabled cdx-button--weight-quiet cdx-button--icon-only" id="ca-history-sticky-header" tabindex="-1" data-event-name="history-sticky-header"><span class="vector-icon mw-ui-icon-wikimedia-history mw-ui-icon-wikimedia-wikimedia-history"></span>

<span></span>
			</a>
			<a href="#" class="cdx-button cdx-button--fake-button cdx-button--fake-button--enabled cdx-button--weight-quiet cdx-button--icon-only mw-watchlink" id="ca-watchstar-sticky-header" tabindex="-1" data-event-name="watch-sticky-header"><span class="vector-icon mw-ui-icon-wikimedia-star mw-ui-icon-wikimedia-wikimedia-star"></span>

<span></span>
			</a>
			<a href="#" class="cdx-button cdx-button--fake-button cdx-button--fake-button--enabled cdx-button--weight-quiet cdx-button--icon-only" id="ca-edit-sticky-header" tabindex="-1" data-event-name="wikitext-edit-sticky-header"><span class="vector-icon mw-ui-icon-wikimedia-wikiText mw-ui-icon-wikimedia-wikimedia-wikiText"></span>

<span></span>
			</a>
			<a href="#" class="cdx-button cdx-button--fake-button cdx-button--fake-button--enabled cdx-button--weight-quiet cdx-button--icon-only" id="ca-ve-edit-sticky-header" tabindex="-1" data-event-name="ve-edit-sticky-header"><span class="vector-icon mw-ui-icon-wikimedia-edit mw-ui-icon-wikimedia-wikimedia-edit"></span>

<span></span>
			</a>
			<a href="#" class="cdx-button cdx-button--fake-button cdx-button--fake-button--enabled cdx-button--weight-quiet cdx-button--icon-only" id="ca-viewsource-sticky-header" tabindex="-1" data-event-name="ve-edit-protected-sticky-header"><span class="vector-icon mw-ui-icon-wikimedia-editLock mw-ui-icon-wikimedia-wikimedia-editLock"></span>

<span></span>
			</a>
		</div>
			<div class="vector-sticky-header-buttons">
				<a href="#" class="cdx-button cdx-button--fake-button cdx-button--fake-button--enabled cdx-button--weight-quiet cdx-button--action-progressive" id="ca-addsection-sticky-header" tabindex="-1" data-event-name="addsection-sticky-header"><span class="vector-icon mw-ui-icon-speechBubbleAdd-progressive mw-ui-icon-wikimedia-speechBubbleAdd-progressive"></span>

<span>Add topic</span>
			</a>
		</div>
			<div class="vector-sticky-header-icon-end">
				<div class="vector-user-links">
				</div>
			</div>
		</div>
	</div>
</div>
<div class="mw-portlet mw-portlet-dock-bottom emptyPortlet" id="p-dock-bottom">
	<ul>
		
	</ul>
</div>
<script>(RLQ=window.RLQ||[]).push(function(){mw.config.set({"wgHostname":"mw-web.eqiad.main-7b48b5fb74-rns6h","wgBackendResponseTime":318});});</script>
<script type="application/ld+json">{"@context":"https:\/\/schema.org","@type":"ImageObject","contentUrl":"https:\/\/upload.wikimedia.org\/wikipedia\/commons\/5\/5f\/Madafu-chopping.jpg","license":"https:\/\/creativecommons.org\/licenses\/by-sa\/4.0","acquireLicensePage":"\/\/commons.wikimedia.org\/wiki\/File:Madafu-chopping.jpg","uploadDate":"2014-11-04 11:09:41"}</script>
</body>

<!-- Mirrored from commons.wikimedia.org/w/index.php?title=File:Madafu-chopping.jpg&action=edit by HTTrack Website Copier/3.x [XR&CO'2014], Sun, 03 Aug 2025 08:17:54 GMT -->
</html>