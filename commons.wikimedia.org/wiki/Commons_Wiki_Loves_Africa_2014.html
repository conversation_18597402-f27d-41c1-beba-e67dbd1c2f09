<!DOCTYPE html>
<html class="client-nojs vector-feature-language-in-header-disabled vector-feature-language-in-main-page-header-disabled vector-feature-page-tools-pinned-disabled vector-feature-toc-pinned-clientpref-1 vector-feature-main-menu-pinned-disabled vector-feature-limited-width-clientpref-1 vector-feature-limited-width-content-enabled vector-feature-custom-font-size-clientpref-1 vector-feature-appearance-pinned-clientpref-1 vector-feature-night-mode-enabled skin-theme-clientpref-day vector-sticky-header-enabled vector-toc-available" lang="en" dir="ltr">

<!-- Mirrored from commons.wikimedia.org/wiki/Commons:Wiki_Loves_Africa_2014 by HTTrack Website Copier/3.x [XR&CO'2014], Sun, 03 Aug 2025 08:53:28 GMT -->
<!-- Added by HTTrack --><meta http-equiv="content-type" content="text/html;charset=UTF-8" /><!-- /Added by HTTrack -->
<head>
<meta charset="UTF-8">
<title>Commons:Wiki Loves Africa 2014 - Wikimedia Commons</title>
<script>(function(){var className="client-js vector-feature-language-in-header-disabled vector-feature-language-in-main-page-header-disabled vector-feature-page-tools-pinned-disabled vector-feature-toc-pinned-clientpref-1 vector-feature-main-menu-pinned-disabled vector-feature-limited-width-clientpref-1 vector-feature-limited-width-content-enabled vector-feature-custom-font-size-clientpref-1 vector-feature-appearance-pinned-clientpref-1 vector-feature-night-mode-enabled skin-theme-clientpref-day vector-sticky-header-enabled vector-toc-available";var cookie=document.cookie.match(/(?:^|; )commonswikimwclientpreferences=([^;]+)/);if(cookie){cookie[1].split('%2C').forEach(function(pref){className=className.replace(new RegExp('(^| )'+pref.replace(/-clientpref-\w+$|[^\w-]+/g,'')+'-clientpref-\\w+( |$)'),'$1'+pref+'$2');});}document.documentElement.className=className;}());RLCONF={"wgBreakFrames":false,"wgSeparatorTransformTable":["",""],"wgDigitTransformTable":["",""],"wgDefaultDateFormat":"dmy","wgMonthNames":["","January","February","March","April","May","June","July","August","September","October","November","December"],"wgRequestId":"f931e2ef-b761-4d8a-aa19-82aed8fa5362","wgCanonicalNamespace":"Project","wgCanonicalSpecialPageName":false,"wgNamespaceNumber":4,"wgPageName":"Commons:Wiki_Loves_Africa_2014","wgTitle":"Wiki Loves Africa 2014","wgCurRevisionId":917904392,"wgRevisionId":917904392,"wgArticleId":35043849,"wgIsArticle":true,"wgIsRedirect":false,"wgAction":"view","wgUserName":null,"wgUserGroups":["*"],"wgCategories":["Wiki Loves Africa templates","Wiki Loves Africa 2014"],"wgPageViewLanguage":"en","wgPageContentLanguage":"en","wgPageContentModel":"wikitext","wgRelevantPageName":"Commons:Wiki_Loves_Africa_2014","wgRelevantArticleId":35043849,"wgIsProbablyEditable":true,"wgRelevantPageIsProbablyEditable":true,"wgRestrictionEdit":[],"wgRestrictionMove":[],"wgNoticeProject":"commons","wgCiteReferencePreviewsActive":true,"wgMediaViewerOnClick":true,"wgMediaViewerEnabledByDefault":false,"wgVisualEditor":{"pageLanguageCode":"en","pageLanguageDir":"ltr","pageVariantFallbacks":"en"},"wgMFDisplayWikibaseDescriptions":{"search":true,"watchlist":true,"tagline":true,"nearby":true},"wgWMESchemaEditAttemptStepOversample":false,"wgWMEPageLength":5000,"wgMetricsPlatformUserExperiments":{"active_experiments":[],"overrides":[],"enrolled":[],"assigned":[],"subject_ids":[],"sampling_units":[]},"wgTranslatePageTranslation":"source","upwizPropertyTitles":{"P180":"Main subjects visible in this work"},"upwizPropertyPlaceholders":{"P180":"e.g., Angolan giraffe"},"upwizPropertyCopyLabels":{"P180":"Main subjects"},"wgEditSubmitButtonLabelPublish":true,"wgDiscussionToolsFeaturesEnabled":{"replytool":true,"newtopictool":true,"sourcemodetoolbar":true,"topicsubscription":false,"autotopicsub":false,"visualenhancements":false,"visualenhancements_reply":false,"visualenhancements_pageframe":false},"wgDiscussionToolsFallbackEditMode":"source","wgULSPosition":"personal","wgULSisCompactLinksEnabled":true,"wgVector2022LanguageInHeader":false,"wgULSisLanguageSelectorEmpty":false,"wbmiDefaultProperties":["P180"],"wbmiPropertyTitles":{"P180":"Items portrayed in this file"},"wbmiPropertyTypes":{"P180":"wikibase-item"},"wbmiRepoApiUrl":"/w/api.php","wbmiHelpUrls":{"P180":"https://commons.wikimedia.org/wiki/Special:MyLanguage/Commons:Depicts"},"wbmiExternalEntitySearchBaseUri":"https://www.wikidata.org/w/api.php","wbmiSupportedDataTypes":["wikibase-item","string","quantity","time","monolingualtext","external-id","globe-coordinate","url"],"wgCheckUserClientHintsHeadersJsApi":["brands","architecture","bitness","fullVersionList","mobile","model","platform","platformVersion"]};
RLSTATE={"ext.gadget.Long-Image-Names-in-Categories":"ready","ext.globalCssJs.user.styles":"ready","site.styles":"ready","user.styles":"ready","ext.globalCssJs.user":"ready","user":"ready","user.options":"loading","ext.translate.tag.languages":"ready","mediawiki.page.gallery.styles":"ready","ext.discussionTools.init.styles":"ready","oojs-ui-core.styles":"ready","oojs-ui.styles.indicators":"ready","mediawiki.widgets.styles":"ready","oojs-ui-core.icons":"ready","skins.vector.search.codex.styles":"ready","skins.vector.styles":"ready","skins.vector.icons":"ready","ext.translate.edit.documentation.styles":"ready","ext.translate":"ready","ext.wikimediamessages.styles":"ready","ext.visualEditor.desktopArticleTarget.noscript":"ready","ext.uls.pt":"ready","wikibase.client.init":"ready"};RLPAGEMODULES=["ext.xLab","mediawiki.page.gallery","site","mediawiki.page.ready","Skins.vector.html","ext.centralNotice.geoIP","ext.centralNotice.startUp","Ext.translate.pagetranslation.html","ext.gadget.Slideshow","ext.gadget.ZoomViewer","ext.gadget.CollapsibleTemplates","ext.gadget.Stockphoto","ext.gadget.WatchlistNotice","ext.gadget.AjaxQuickDelete","ext.gadget.WikiMiniAtlas","ext.gadget.LanguageSelect","ext.gadget.PictureOfTheYearEnhancements","ext.urlShortener.toolbar","ext.centralauth.centralautologin","mmv.bootstrap","ext.visualEditor.desktopArticleTarget.init","ext.visualEditor.targetLoader","ext.echo.centralauth","ext.discussionTools.init","ext.eventLogging","ext.wikimediaEvents","ext.wikimediaEvents.wikibase","ext.navigationTiming","ext.uls.compactlinks","ext.uls.interface","ext.checkUser.clientHints"];</script>
<script>(RLQ=window.RLQ||[]).push(function(){mw.loader.impl(function(){return["user.options@12s5i",function($,jQuery,require,module){mw.user.tokens.set({"patrolToken":"+\\","watchToken":"+\\","csrfToken":"+\\"});
}];});});</script>
<link rel="stylesheet" href="../w/load25e6.css?lang=en&amp;modules=ext.discussionTools.init.styles%7Cext.translate%7Cext.translate.edit.documentation.styles%7Cext.translate.tag.languages%7Cext.uls.pt%7Cext.visualEditor.desktopArticleTarget.noscript%7Cext.wikimediamessages.styles%7Cmediawiki.page.gallery.styles%7Cmediawiki.widgets.styles%7Coojs-ui-core.icons%2Cstyles%7Coojs-ui.styles.indicators%7Cskins.vector.icons%2Cstyles%7Cskins.vector.search.codex.styles%7Cwikibase.client.init&amp;only=styles&amp;skin=vector-2022">
<script async="" src="../w/load9565.php?lang=en&amp;modules=startup&amp;only=scripts&amp;raw=1&amp;skin=vector-2022"></script>
<meta name="ResourceLoaderDynamicStyles" content="">
<link rel="stylesheet" href="../w/load3e3b.css?lang=en&amp;modules=ext.gadget.Long-Image-Names-in-Categories&amp;only=styles&amp;skin=vector-2022">
<link rel="stylesheet" href="../w/loada24d.css?lang=en&amp;modules=site.styles&amp;only=styles&amp;skin=vector-2022">
<meta name="generator" content="MediaWiki 1.45.0-wmf.12">
<meta name="referrer" content="origin">
<meta name="referrer" content="origin-when-cross-origin">
<meta name="robots" content="max-image-preview:standard">
<meta name="format-detection" content="telephone=no">
<meta name="viewport" content="width=1120">
<meta property="og:title" content="Commons:Wiki Loves Africa 2014 - Wikimedia Commons">
<meta property="og:type" content="website">
<link rel="preconnect" href="../../upload.wikimedia.org/index.html">
<link rel="alternate" media="only screen and (max-width: 640px)" href="../../commons.m.wikimedia.org/wiki/Commons_Wiki_Loves_Africa_2014.html">
<link rel="alternate" type="application/x-wiki" title="Edit" href="../w/index31e8.html?title=Commons:Wiki_Loves_Africa_2014&amp;action=edit">
<link rel="apple-touch-icon" href="../static/apple-touch/commons.png">
<link rel="icon" href="../static/favicon/commons.ico">
<link rel="search" type="application/opensearchdescription+xml" href="../w/rest.php/v1/search" title="Wikimedia Commons">
<link rel="EditURI" type="application/rsd+xml" href="https://commons.wikimedia.org/w/api.php?action=rsd">
<link rel="canonical" href="Commons_Wiki_Loves_Africa_2014.html">
<link rel="license" href="../../creativecommons.org/licenses/by-sa/4.0/index.html">
<link rel="alternate" type="application/atom+xml" title="Wikimedia Commons Atom feed" href="https://commons.wikimedia.org/w/api.php?hidebots=1&amp;hidecategorization=1&amp;hideWikibase=1&amp;translations=filter&amp;urlversion=1&amp;days=7&amp;limit=50&amp;action=feedrecentchanges&amp;feedformat=atom">
<link rel="dns-prefetch" href="../../meta.wikimedia.org/index.html" />
<link rel="dns-prefetch" href="Auth.wikimedia.html">
</head>
<body class="ext-discussiontools-replytool-enabled ext-discussiontools-newtopictool-enabled ext-discussiontools-sourcemodetoolbar-enabled skin--responsive skin-vector skin-vector-search-vue mediawiki ltr sitedir-ltr mw-hide-empty-elt ns-4 ns-subject mw-editable page-Commons_Wiki_Loves_Africa_2014 rootpage-Commons_Wiki_Loves_Africa_2014 skin-vector-2022 action-view"><a class="mw-jump-link" href="#bodyContent">Jump to content</a>
<div class="vector-header-container">
	<header class="vector-header mw-header no-font-mode-scale">
		<div class="vector-header-start">
			<nav class="vector-main-menu-landmark" aria-label="Site">
				
<div id="vector-main-menu-dropdown" class="vector-dropdown vector-main-menu-dropdown vector-button-flush-left vector-button-flush-right"  title="Main menu" >
	<input type="checkbox" id="vector-main-menu-dropdown-checkbox" role="button" aria-haspopup="true" data-event-name="ui.dropdown-vector-main-menu-dropdown" class="vector-dropdown-checkbox "  aria-label="Main menu"  >
	<label id="vector-main-menu-dropdown-label" for="vector-main-menu-dropdown-checkbox" class="vector-dropdown-label cdx-button cdx-button--fake-button cdx-button--fake-button--enabled cdx-button--weight-quiet cdx-button--icon-only " aria-hidden="true"  ><span class="vector-icon mw-ui-icon-menu mw-ui-icon-wikimedia-menu"></span>

<span class="vector-dropdown-label-text">Main menu</span>
	</label>
	<div class="vector-dropdown-content">


				<div id="vector-main-menu-unpinned-container" class="vector-unpinned-container">
		
<div id="vector-main-menu" class="vector-main-menu vector-pinnable-element">
	<div
	class="vector-pinnable-header vector-main-menu-pinnable-header vector-pinnable-header-unpinned"
	data-feature-name="main-menu-pinned"
	data-pinnable-element-id="vector-main-menu"
	data-pinned-container-id="vector-main-menu-pinned-container"
	data-unpinned-container-id="vector-main-menu-unpinned-container"
>
	<div class="vector-pinnable-header-label">Main menu</div>
	<button class="vector-pinnable-header-toggle-button vector-pinnable-header-pin-button" data-event-name="pinnable-header.vector-main-menu.pin">move to sidebar</button>
	<button class="vector-pinnable-header-toggle-button vector-pinnable-header-unpin-button" data-event-name="pinnable-header.vector-main-menu.unpin">hide</button>
</div>

	
<div id="p-navigation" class="vector-menu mw-portlet mw-portlet-navigation"  >
	<div class="vector-menu-heading">
		Navigate
	</div>
	<div class="vector-menu-content">
		
		<ul class="vector-menu-content-list">
			
			<li id="n-mainpage-description" class="mw-list-item"><a href="Main_Page.html" title="Visit the main page [z]" accesskey="z"><span>Main page</span></a></li><li id="n-welcome" class="mw-list-item"><a href="Commons_Welcome.html"><span>Welcome</span></a></li><li id="n-portal" class="mw-list-item"><a href="Commons_Community_portal.html" title="About the project, what you can do, where to find things"><span>Community portal</span></a></li><li id="n-village-pump" class="mw-list-item"><a href="Commons_Village_pump.html"><span>Village pump</span></a></li><li id="n-help" class="mw-list-item"><a href="Special_MyLanguage/Help_Contents.html" title="The place to find out"><span>Help center</span></a></li>
		</ul>
		
	</div>
</div>

	
	
<div id="p-participate" class="vector-menu mw-portlet mw-portlet-participate"  >
	<div class="vector-menu-heading">
		Participate
	</div>
	<div class="vector-menu-content">
		
		<ul class="vector-menu-content-list">
			
			<li id="n-uploadbtn" class="mw-list-item"><a href="Special_UploadWizard.html"><span>Upload file</span></a></li><li id="n-recentchanges" class="mw-list-item"><a href="Special_RecentChanges.html" title="A list of recent changes in the wiki [r]" accesskey="r"><span>Recent changes</span></a></li><li id="n-latestfiles" class="mw-list-item"><a href="Special_NewFiles.html"><span>Latest files</span></a></li><li id="n-randomimage" class="mw-list-item"><a href="File_Corliss_valvegear%2c_Gordon%27s_improved_(New_Catechism_of_the_Steam_Engine%2c_1904).html" title="Load a random file [x]" accesskey="x"><span>Random file</span></a></li><li id="n-contact" class="mw-list-item"><a href="Commons_Contact_us.html"><span>Contact us</span></a></li><li id="n-specialpages" class="mw-list-item"><a href="Special_SpecialPages.html"><span>Special pages</span></a></li>
		</ul>
		
	</div>
</div>

	
<div id="p-lang" class="vector-menu mw-portlet mw-portlet-lang"  >
	<div class="vector-menu-heading">
		In Wikipedia
	</div>
	<div class="vector-menu-content">
		
		<ul class="vector-menu-content-list">
			
			
		</ul>
		<div class="after-portlet after-portlet-lang"><span class="wb-langlinks-add wb-langlinks-link"><a href="https://www.wikidata.org/wiki/Special:NewItem?site=commonswiki&amp;page=Commons%3AWiki+Loves+Africa+2014" title="Add interlanguage links" class="wbc-editpage">Add links</a></span></div>
	</div>
</div>

</div>

				</div>

	</div>
</div>

		</nav>
			
<a href="Main_Page.html" class="mw-logo">
	<img class="mw-logo-icon" src="../static/images/icons/commonswiki.svg" alt="" aria-hidden="true" height="50" width="50">
	<span class="mw-logo-container skin-invert">
		<img class="mw-logo-wordmark" alt="Wikimedia Commons" src="../static/images/mobile/copyright/commonswiki-wordmark.svg" style="width: 7.1875em; height: 2em;">
	</span>
</a>

		</div>
		<div class="vector-header-end">
			
<div id="p-search" role="search" class="vector-search-box-vue  vector-search-box-collapses vector-search-box">
	<a href="Special_MediaSearch.html" class="cdx-button cdx-button--fake-button cdx-button--fake-button--enabled cdx-button--weight-quiet cdx-button--icon-only search-toggle" title="Search Wikimedia Commons [f]" accesskey="f"><span class="vector-icon mw-ui-icon-search mw-ui-icon-wikimedia-search"></span>

<span>Search</span>
	</a>
	<div class="vector-typeahead-search-container">
		<div class="cdx-typeahead-search">
			<form action="https://commons.wikimedia.org/w/index.php" id="searchform" class="cdx-search-input cdx-search-input--has-end-button">
				<div id="simpleSearch" class="cdx-search-input__input-wrapper"  data-search-loc="header-moved">
					<div class="cdx-text-input cdx-text-input--has-start-icon">
						<input
							class="cdx-text-input__input mw-searchInput" autocomplete="off"
							 type="search" name="search" placeholder="Search Wikimedia Commons" aria-label="Search Wikimedia Commons" autocapitalize="sentences" spellcheck="false" title="Search Wikimedia Commons [f]" accesskey="f" id="searchInput"
							>
						<span class="cdx-text-input__icon cdx-text-input__start-icon"></span>
					</div>
					<input type="hidden" name="title" value="Special:MediaSearch">
				</div>
				<button class="cdx-button cdx-search-input__end-button">Search</button>
			</form>
		</div>
	</div>
</div>

			<nav class="vector-user-links vector-user-links-wide" aria-label="Personal tools">
	<div class="vector-user-links-main">
	
<div id="p-vector-user-menu-preferences" class="vector-menu mw-portlet"  >
	<div class="vector-menu-content">
		
		<ul class="vector-menu-content-list">
			<li id="ca-uls" class="mw-list-item active user-links-collapsible-item"><a data-mw="interface" href="#" class="uls-trigger cdx-button cdx-button--fake-button cdx-button--fake-button--enabled cdx-button--weight-quiet"><span class="vector-icon mw-ui-icon-wikimedia-language mw-ui-icon-wikimedia-wikimedia-language"></span>

<span>English</span></a>
</li>

			
		</ul>
		
	</div>
</div>

	
<div id="p-vector-user-menu-userpage" class="vector-menu mw-portlet emptyPortlet"  >
	<div class="vector-menu-content">
		
		<ul class="vector-menu-content-list">
			
			
		</ul>
		
	</div>
</div>

	<nav class="vector-appearance-landmark" aria-label="Appearance">
		
<div id="vector-appearance-dropdown" class="vector-dropdown "  title="Change the appearance of the page&#039;s font size, width, and color" >
	<input type="checkbox" id="vector-appearance-dropdown-checkbox" role="button" aria-haspopup="true" data-event-name="ui.dropdown-vector-appearance-dropdown" class="vector-dropdown-checkbox "  aria-label="Appearance"  >
	<label id="vector-appearance-dropdown-label" for="vector-appearance-dropdown-checkbox" class="vector-dropdown-label cdx-button cdx-button--fake-button cdx-button--fake-button--enabled cdx-button--weight-quiet cdx-button--icon-only " aria-hidden="true"  ><span class="vector-icon mw-ui-icon-appearance mw-ui-icon-wikimedia-appearance"></span>

<span class="vector-dropdown-label-text">Appearance</span>
	</label>
	<div class="vector-dropdown-content">


			<div id="vector-appearance-unpinned-container" class="vector-unpinned-container">
				
			</div>
		
	</div>
</div>

	</nav>
	
<div id="p-vector-user-menu-notifications" class="vector-menu mw-portlet emptyPortlet"  >
	<div class="vector-menu-content">
		
		<ul class="vector-menu-content-list">
			
			
		</ul>
		
	</div>
</div>

	
<div id="p-vector-user-menu-overflow" class="vector-menu mw-portlet"  >
	<div class="vector-menu-content">
		
		<ul class="vector-menu-content-list">
			<li id="pt-sitesupport-2" class="user-links-collapsible-item mw-list-item user-links-collapsible-item"><a data-mw="interface" href="https://donate.wikimedia.org/?wmf_source=donate&amp;wmf_medium=sidebar&amp;wmf_campaign=commons.wikimedia.org&amp;uselang=en" class=""><span>Donate</span></a>
</li>
<li id="pt-createaccount-2" class="user-links-collapsible-item mw-list-item user-links-collapsible-item"><a data-mw="interface" href="https://commons.wikimedia.org/w/index.php?title=Special:CreateAccount&amp;returnto=Commons%3AWiki+Loves+Africa+2014" title="You are encouraged to create an account and log in; however, it is not mandatory" class=""><span>Create account</span></a>
</li>
<li id="pt-login-2" class="user-links-collapsible-item mw-list-item user-links-collapsible-item"><a data-mw="interface" href="https://commons.wikimedia.org/w/index.php?title=Special:UserLogin&amp;returnto=Commons%3AWiki+Loves+Africa+2014" title="You are encouraged to log in; however, it is not mandatory [o]" accesskey="o" class=""><span>Log in</span></a>
</li>

			
		</ul>
		
	</div>
</div>

	</div>
	
<div id="vector-user-links-dropdown" class="vector-dropdown vector-user-menu vector-button-flush-right vector-user-menu-logged-out"  title="More options" >
	<input type="checkbox" id="vector-user-links-dropdown-checkbox" role="button" aria-haspopup="true" data-event-name="ui.dropdown-vector-user-links-dropdown" class="vector-dropdown-checkbox "  aria-label="Personal tools"  >
	<label id="vector-user-links-dropdown-label" for="vector-user-links-dropdown-checkbox" class="vector-dropdown-label cdx-button cdx-button--fake-button cdx-button--fake-button--enabled cdx-button--weight-quiet cdx-button--icon-only " aria-hidden="true"  ><span class="vector-icon mw-ui-icon-ellipsis mw-ui-icon-wikimedia-ellipsis"></span>

<span class="vector-dropdown-label-text">Personal tools</span>
	</label>
	<div class="vector-dropdown-content">


		
<div id="p-personal" class="vector-menu mw-portlet mw-portlet-personal user-links-collapsible-item"  title="User menu" >
	<div class="vector-menu-content">
		
		<ul class="vector-menu-content-list">
			
			<li id="pt-sitesupport" class="user-links-collapsible-item mw-list-item"><a href="https://donate.wikimedia.org/?wmf_source=donate&amp;wmf_medium=sidebar&amp;wmf_campaign=commons.wikimedia.org&amp;uselang=en"><span>Donate</span></a></li><li id="pt-createaccount" class="user-links-collapsible-item mw-list-item"><a href="https://commons.wikimedia.org/w/index.php?title=Special:CreateAccount&amp;returnto=Commons%3AWiki+Loves+Africa+2014" title="You are encouraged to create an account and log in; however, it is not mandatory"><span class="vector-icon mw-ui-icon-userAdd mw-ui-icon-wikimedia-userAdd"></span> <span>Create account</span></a></li><li id="pt-login" class="user-links-collapsible-item mw-list-item"><a href="https://commons.wikimedia.org/w/index.php?title=Special:UserLogin&amp;returnto=Commons%3AWiki+Loves+Africa+2014" title="You are encouraged to log in; however, it is not mandatory [o]" accesskey="o"><span class="vector-icon mw-ui-icon-logIn mw-ui-icon-wikimedia-logIn"></span> <span>Log in</span></a></li>
		</ul>
		
	</div>
</div>

<div id="p-user-menu-anon-editor" class="vector-menu mw-portlet mw-portlet-user-menu-anon-editor"  >
	<div class="vector-menu-heading">
		Pages for logged out editors <a href="Help_Introduction.html" aria-label="Learn more about editing"><span>learn more</span></a>
	</div>
	<div class="vector-menu-content">
		
		<ul class="vector-menu-content-list">
			
			<li id="pt-anoncontribs" class="mw-list-item"><a href="Special_MyContributions.html" title="A list of edits made from this IP address [y]" accesskey="y"><span>Contributions</span></a></li><li id="pt-anontalk" class="mw-list-item"><a href="Special_MyTalk.html" title="Discussion about edits from this IP address [n]" accesskey="n"><span>Talk</span></a></li>
		</ul>
		
	</div>
</div>

	
	</div>
</div>

</nav>

		</div>
	</header>
</div>
<div class="mw-page-container">
	<div class="mw-page-container-inner">
		<div class="vector-sitenotice-container">
			<div id="siteNotice"><!-- CentralNotice --></div>
		</div>
		<div class="vector-column-start">
			<div class="vector-main-menu-container">
		<div id="mw-navigation">
			<nav id="mw-panel" class="vector-main-menu-landmark" aria-label="Site">
				<div id="vector-main-menu-pinned-container" class="vector-pinned-container">
				
				</div>
		</nav>
		</div>
	</div>
	<div class="vector-sticky-pinned-container">
				<nav id="mw-panel-toc" aria-label="Contents" data-event-name="ui.sidebar-toc" class="mw-table-of-contents-container vector-toc-landmark">
					<div id="vector-toc-pinned-container" class="vector-pinned-container">
					<div id="vector-toc" class="vector-toc vector-pinnable-element">
	<div
	class="vector-pinnable-header vector-toc-pinnable-header vector-pinnable-header-pinned"
	data-feature-name="toc-pinned"
	data-pinnable-element-id="vector-toc"
	
	
>
	<h2 class="vector-pinnable-header-label">Contents</h2>
	<button class="vector-pinnable-header-toggle-button vector-pinnable-header-pin-button" data-event-name="pinnable-header.vector-toc.pin">move to sidebar</button>
	<button class="vector-pinnable-header-toggle-button vector-pinnable-header-unpin-button" data-event-name="pinnable-header.vector-toc.unpin">hide</button>
</div>


	<ul class="vector-toc-contents" id="mw-panel-toc-list">
		<li id="toc-mw-content-text"
			class="vector-toc-list-item vector-toc-level-1">
			<a href="#" class="vector-toc-link">
				<div class="vector-toc-text">Beginning</div>
			</a>
		</li>
		<li id="toc-Links"
		class="vector-toc-list-item vector-toc-level-1 vector-toc-list-item-expanded">
		<a class="vector-toc-link" href="#Links">
			<div class="vector-toc-text">
				<span class="vector-toc-numb">1</span>
				<span>Links</span>
			</div>
		</a>
		
		<ul id="toc-Links-sublist" class="vector-toc-list">
		</ul>
	</li>
</ul>
</div>

					</div>
		</nav>
			</div>
		</div>
		<div class="mw-content-container">
			<main id="content" class="mw-body">
				<header class="mw-body-header vector-page-titlebar no-font-mode-scale">
					<nav aria-label="Contents" class="vector-toc-landmark">
						
<div id="vector-page-titlebar-toc" class="vector-dropdown vector-page-titlebar-toc vector-button-flush-left"  title="Table of Contents" >
	<input type="checkbox" id="vector-page-titlebar-toc-checkbox" role="button" aria-haspopup="true" data-event-name="ui.dropdown-vector-page-titlebar-toc" class="vector-dropdown-checkbox "  aria-label="Toggle the table of contents"  >
	<label id="vector-page-titlebar-toc-label" for="vector-page-titlebar-toc-checkbox" class="vector-dropdown-label cdx-button cdx-button--fake-button cdx-button--fake-button--enabled cdx-button--weight-quiet cdx-button--icon-only " aria-hidden="true"  ><span class="vector-icon mw-ui-icon-listBullet mw-ui-icon-wikimedia-listBullet"></span>

<span class="vector-dropdown-label-text">Toggle the table of contents</span>
	</label>
	<div class="vector-dropdown-content">


							<div id="vector-page-titlebar-toc-unpinned-container" class="vector-unpinned-container">
			</div>
		
	</div>
</div>

					</nav>
					<h1 id="firstHeading" class="firstHeading mw-first-heading"><span class="mw-page-title-namespace">Commons</span><span class="mw-page-title-separator">:</span><span class="mw-page-title-main">Wiki Loves Africa 2014</span></h1>
						<div class="mw-indicators">
		</div>
</header>
				<div class="vector-page-toolbar vector-feature-custom-font-size-clientpref--excluded">
					<div class="vector-page-toolbar-container">
						<div id="left-navigation">
							<nav aria-label="Namespaces">
								
<div id="p-associated-pages" class="vector-menu vector-menu-tabs mw-portlet mw-portlet-associated-pages"  >
	<div class="vector-menu-content">
		
		<ul class="vector-menu-content-list">
			
			<li id="ca-nstab-project" class="selected vector-tab-noicon mw-list-item"><a href="Commons_Wiki_Loves_Africa_2014.html" title="View the project page [c]" accesskey="c"><span>Project page</span></a></li><li id="ca-talk" class="vector-tab-noicon mw-list-item"><a href="Commons_talk_Wiki_Loves_Africa_2014.html" rel="discussion" title="Discussion about the content page [t]" accesskey="t"><span>Discussion</span></a></li>
		</ul>
		
	</div>
</div>

								
<div id="vector-variants-dropdown" class="vector-dropdown emptyPortlet"  >
	<input type="checkbox" id="vector-variants-dropdown-checkbox" role="button" aria-haspopup="true" data-event-name="ui.dropdown-vector-variants-dropdown" class="vector-dropdown-checkbox " aria-label="Change language variant"   >
	<label id="vector-variants-dropdown-label" for="vector-variants-dropdown-checkbox" class="vector-dropdown-label cdx-button cdx-button--fake-button cdx-button--fake-button--enabled cdx-button--weight-quiet" aria-hidden="true"  ><span class="vector-dropdown-label-text">English</span>
	</label>
	<div class="vector-dropdown-content">


					
<div id="p-variants" class="vector-menu mw-portlet mw-portlet-variants emptyPortlet"  >
	<div class="vector-menu-content">
		
		<ul class="vector-menu-content-list">
			
			
		</ul>
		
	</div>
</div>

				
	</div>
</div>

							</nav>
						</div>
						<div id="right-navigation" class="vector-collapsible">
							<nav aria-label="Views">
								
<div id="p-views" class="vector-menu vector-menu-tabs mw-portlet mw-portlet-views"  >
	<div class="vector-menu-content">
		
		<ul class="vector-menu-content-list">
			
			<li id="ca-view" class="selected vector-tab-noicon mw-list-item"><a href="Commons_Wiki_Loves_Africa_2014.html"><span>Read</span></a></li><li id="ca-edit" class="vector-tab-noicon mw-list-item"><a href="../w/index31e8.html?title=Commons:Wiki_Loves_Africa_2014&amp;action=edit" title="Edit this page [e]" accesskey="e"><span>Edit</span></a></li><li id="ca-history" class="vector-tab-noicon mw-list-item"><a href="https://commons.wikimedia.org/w/index.php?title=Commons:Wiki_Loves_Africa_2014&amp;action=history" title="Past revisions of this page [h]" accesskey="h"><span>View history</span></a></li>
		</ul>
		
	</div>
</div>

							</nav>
				
							<nav class="vector-page-tools-landmark" aria-label="Page tools">
								
<div id="vector-page-tools-dropdown" class="vector-dropdown vector-page-tools-dropdown"  >
	<input type="checkbox" id="vector-page-tools-dropdown-checkbox" role="button" aria-haspopup="true" data-event-name="ui.dropdown-vector-page-tools-dropdown" class="vector-dropdown-checkbox "  aria-label="Tools"  >
	<label id="vector-page-tools-dropdown-label" for="vector-page-tools-dropdown-checkbox" class="vector-dropdown-label cdx-button cdx-button--fake-button cdx-button--fake-button--enabled cdx-button--weight-quiet" aria-hidden="true"  ><span class="vector-dropdown-label-text">Tools</span>
	</label>
	<div class="vector-dropdown-content">


									<div id="vector-page-tools-unpinned-container" class="vector-unpinned-container">
						
<div id="vector-page-tools" class="vector-page-tools vector-pinnable-element">
	<div
	class="vector-pinnable-header vector-page-tools-pinnable-header vector-pinnable-header-unpinned"
	data-feature-name="page-tools-pinned"
	data-pinnable-element-id="vector-page-tools"
	data-pinned-container-id="vector-page-tools-pinned-container"
	data-unpinned-container-id="vector-page-tools-unpinned-container"
>
	<div class="vector-pinnable-header-label">Tools</div>
	<button class="vector-pinnable-header-toggle-button vector-pinnable-header-pin-button" data-event-name="pinnable-header.vector-page-tools.pin">move to sidebar</button>
	<button class="vector-pinnable-header-toggle-button vector-pinnable-header-unpin-button" data-event-name="pinnable-header.vector-page-tools.unpin">hide</button>
</div>

	
<div id="p-cactions" class="vector-menu mw-portlet mw-portlet-cactions emptyPortlet vector-has-collapsible-items"  title="More options" >
	<div class="vector-menu-heading">
		Actions
	</div>
	<div class="vector-menu-content">
		
		<ul class="vector-menu-content-list">
			
			<li id="ca-more-view" class="selected vector-more-collapsible-item mw-list-item"><a href="Commons_Wiki_Loves_Africa_2014.html"><span>Read</span></a></li><li id="ca-more-edit" class="vector-more-collapsible-item mw-list-item"><a href="../w/index31e8.html?title=Commons:Wiki_Loves_Africa_2014&amp;action=edit" title="Edit this page [e]" accesskey="e"><span>Edit</span></a></li><li id="ca-more-history" class="vector-more-collapsible-item mw-list-item"><a href="https://commons.wikimedia.org/w/index.php?title=Commons:Wiki_Loves_Africa_2014&amp;action=history"><span>View history</span></a></li>
		</ul>
		
	</div>
</div>

<div id="p-tb" class="vector-menu mw-portlet mw-portlet-tb"  >
	<div class="vector-menu-heading">
		General
	</div>
	<div class="vector-menu-content">
		
		<ul class="vector-menu-content-list">
			
			<li id="t-whatlinkshere" class="mw-list-item"><a href="Special_WhatLinksHere/Commons_Wiki_Loves_Africa_2014.html" title="A list of all wiki pages that link here [j]" accesskey="j"><span>What links here</span></a></li><li id="t-recentchangeslinked" class="mw-list-item"><a href="Special_RecentChangesLinked/Commons_Wiki_Loves_Africa_2014.html" rel="nofollow" title="Recent changes in pages linked from this page [k]" accesskey="k"><span>Related changes</span></a></li><li id="t-permalink" class="mw-list-item"><a href="https://commons.wikimedia.org/w/index.php?title=Commons:Wiki_Loves_Africa_2014&amp;oldid=917904392" title="Permanent link to this revision of this page"><span>Permanent link</span></a></li><li id="t-info" class="mw-list-item"><a href="https://commons.wikimedia.org/w/index.php?title=Commons:Wiki_Loves_Africa_2014&amp;action=info" title="More information about this page"><span>Page information</span></a></li><li id="t-urlshortener" class="mw-list-item"><a href="https://commons.wikimedia.org/w/index.php?title=Special:UrlShortener&amp;url=https%3A%2F%2Fcommons.wikimedia.org%2Fwiki%2FCommons%3AWiki_Loves_Africa_2014"><span>Get shortened URL</span></a></li><li id="t-urlshortener-qrcode" class="mw-list-item"><a href="https://commons.wikimedia.org/w/index.php?title=Special:QrCode&amp;url=https%3A%2F%2Fcommons.wikimedia.org%2Fwiki%2FCommons%3AWiki_Loves_Africa_2014"><span>Download QR code</span></a></li>
		</ul>
		
	</div>
</div>

<div id="p-coll-print_export" class="vector-menu mw-portlet mw-portlet-coll-print_export"  >
	<div class="vector-menu-heading">
		Print/export
	</div>
	<div class="vector-menu-content">
		
		<ul class="vector-menu-content-list">
			
			<li id="coll-create_a_book" class="mw-list-item"><a href="https://commons.wikimedia.org/w/index.php?title=Special:Book&amp;bookcmd=book_creator&amp;referer=Commons%3AWiki+Loves+Africa+2014"><span>Create a book</span></a></li><li id="coll-download-as-rl" class="mw-list-item"><a href="https://commons.wikimedia.org/w/index.php?title=Special:DownloadAsPdf&amp;page=Commons%3AWiki_Loves_Africa_2014&amp;action=show-download-screen"><span>Download as PDF</span></a></li><li id="t-print" class="mw-list-item"><a href="https://commons.wikimedia.org/w/index.php?title=Commons:Wiki_Loves_Africa_2014&amp;printable=yes" title="Printable version of this page [p]" accesskey="p"><span>Printable version</span></a></li>
		</ul>
		
	</div>
</div>

<div id="p-wikibase-otherprojects" class="vector-menu mw-portlet mw-portlet-wikibase-otherprojects emptyPortlet"  >
	<div class="vector-menu-heading">
		In other projects
	</div>
	<div class="vector-menu-content">
		
		<ul class="vector-menu-content-list">
			
			
		</ul>
		
	</div>
</div>

</div>

									</div>
				
	</div>
</div>

							</nav>
						</div>
					</div>
				</div>
				<div class="vector-column-end no-font-mode-scale">
					<div class="vector-sticky-pinned-container">
						<nav class="vector-page-tools-landmark" aria-label="Page tools">
							<div id="vector-page-tools-pinned-container" class="vector-pinned-container">
				
							</div>
		</nav>
						<nav class="vector-appearance-landmark" aria-label="Appearance">
							<div id="vector-appearance-pinned-container" class="vector-pinned-container">
				<div id="vector-appearance" class="vector-appearance vector-pinnable-element">
	<div
	class="vector-pinnable-header vector-appearance-pinnable-header vector-pinnable-header-pinned"
	data-feature-name="appearance-pinned"
	data-pinnable-element-id="vector-appearance"
	data-pinned-container-id="vector-appearance-pinned-container"
	data-unpinned-container-id="vector-appearance-unpinned-container"
>
	<div class="vector-pinnable-header-label">Appearance</div>
	<button class="vector-pinnable-header-toggle-button vector-pinnable-header-pin-button" data-event-name="pinnable-header.vector-appearance.pin">move to sidebar</button>
	<button class="vector-pinnable-header-toggle-button vector-pinnable-header-unpin-button" data-event-name="pinnable-header.vector-appearance.unpin">hide</button>
</div>


</div>

							</div>
		</nav>
					</div>
				</div>
				<div id="bodyContent" class="vector-body" aria-labelledby="firstHeading" data-mw-ve-target-container>
					<div class="vector-body-before-content">
					
						<div id="siteSub" class="noprint">From Wikimedia Commons, the free media repository</div>
					</div>
					<div id="contentSub"><div id="mw-content-subtitle"></div></div>
					
					
					<div id="mw-content-text" class="mw-body-content"><div class="mw-pt-translate-header noprint nomobile" dir="ltr" lang="en"><a href="https://commons.wikimedia.org/w/index.php?title=Special:Translate&amp;group=page-Commons%3AWiki+Loves+Africa+2014&amp;action=page&amp;filter=&amp;action_source=translate_page" title="Special:Translate">Translate this page</a></div><div class="mw-content-ltr mw-parser-output" lang="en" dir="ltr"><div class="mw-pt-languages noprint navigation-not-searchable" lang="en" dir="ltr"><div class="mw-pt-languages-label">Other languages:</div><ul class="mw-pt-languages-list"><li><a href="Commons_Wiki_Loves_Africa_2014/de.html" class="mw-pt-progress mw-pt-progress--complete" title="Commons:Wiki Loves Africa 2014 (100% translated)" lang="de" dir="ltr">Deutsch</a></li>
<li><span class="mw-pt-languages-ui mw-pt-languages-selected mw-pt-progress mw-pt-progress--complete" lang="en" dir="ltr">English</span></li>
<li><a href="Commons_Wiki_Loves_Africa_2014/eo.html" class="mw-pt-progress mw-pt-progress--low" title="Commons:Wiki Loves Africa 2014/eo (3% translated)" lang="eo" dir="ltr">Esperanto</a></li>
<li><a href="Commons_Wiki_Loves_Africa_2014/tr.html" class="mw-pt-progress mw-pt-progress--low" title="Commons:Viki Afrika'yı seviyor 2014 (14% translated)" lang="tr" dir="ltr">Türkçe</a></li>
<li><a href="Commons_Wiki_Loves_Africa_2014/da.html" class="mw-pt-progress mw-pt-progress--med" title="Commons:Wiki elsker Afrika 2014 (17% translated)" lang="da" dir="ltr">dansk</a></li>
<li><a href="Commons_Wiki_Loves_Africa_2014/fr.html" class="mw-pt-progress mw-pt-progress--complete" title="Commons:Wiki Loves Africa 2014 (100% translated)" lang="fr" dir="ltr">français</a></li>
<li><a href="Commons_Wiki_Loves_Africa_2014/gl.html" class="mw-pt-progress mw-pt-progress--low" title="Commons:Wiki Loves Africa 2014/gl (3% translated)" lang="gl" dir="ltr">galego</a></li>
<li><a href="Commons_Wiki_Loves_Africa_2014/it.html" class="mw-pt-progress mw-pt-progress--low" title="Commons:Wiki Loves Africa 2014/it (3% translated)" lang="it" dir="ltr">italiano</a></li>
<li><a href="Commons_Wiki_Loves_Africa_2014/pl.html" class="mw-pt-progress mw-pt-progress--low" title="Commons:Wiki Lubi Afrykę 2014 (6% translated)" lang="pl" dir="ltr">polski</a></li>
<li><a href="Commons_Wiki_Loves_Africa_2014/pt-br.html" class="mw-pt-progress mw-pt-progress--low" title="Commons:Wiki Loves Africa 2014/pt-br (11% translated)" lang="pt-BR" dir="ltr">português do Brasil</a></li>
<li><a href="Commons_Wiki_Loves_Africa_2014/ru.html" class="mw-pt-progress mw-pt-progress--med" title="Commons:Вики любит Африку 2014 (49% translated)" lang="ru" dir="ltr">русский</a></li>
<li><a href="Commons_Wiki_Loves_Africa_2014/uk.html" class="mw-pt-progress mw-pt-progress--high" title="Вікісховище:Вікі любить Африку 2014 (94% translated)" lang="uk" dir="ltr">українська</a></li>
<li><a href="Commons_Wiki_Loves_Africa_2014/ar.html" class="mw-pt-progress mw-pt-progress--complete" title="كومنز:ويكي تهوى أفريقيا 2014 (100% translated)" lang="ar" dir="rtl">العربية</a></li>
<li><a href="Commons_Wiki_Loves_Africa_2014/arz.html" class="mw-pt-progress mw-pt-progress--low" title="Commons:Wiki Loves Africa 2014/arz (3% translated)" lang="arz" dir="rtl">مصرى</a></li>
<li><a href="Commons_Wiki_Loves_Africa_2014/ja.html" class="mw-pt-progress mw-pt-progress--low" title="Commons:Wiki Loves Africa 2014/ja (9% translated)" lang="ja" dir="ltr">日本語</a></li></ul></div>
<dl><dd><dl><dd><dl><dd><style data-mw-deduplicate="TemplateStyles:r586028894">.mw-parser-output .wla-menu{display:flex;flex-wrap:wrap;column-gap:3px;margin:0.5em 0 1em 0;list-style:none}.mw-parser-output .wla-menu li{flex:auto;border-top:.5em #CD5C5C solid;background:#ffffff;padding:10px;text-align:center;white-space:nowrap}.mw-parser-output .wla-menu li a{color:#32AFAF;font-weight:bold}.mw-parser-output .wla-menu .wlam-currentYear{border-color:#f15348}.mw-parser-output .wla-menu .wlam-currentYear a{color:#f15348}.mw-parser-output .wla-menu .wlam-currentPage{background:#eaecf0}</style></dd></dl></dd></dl></dd></dl>
<ul class="wla-menu" dir="ltr" lang="en">
<li class=""><a href="Commons_Wiki_Loves_Africa.html" title="Commons:Wiki Loves Africa">WLA HOME</a></li>
<li class="wlam-currentYear"><a href="Commons_Wiki_Loves_Africa_2023.html" title="Commons:Wiki Loves Africa 2023">WLA 2023</a></li>
<li class=""><a href="Commons_Wiki_Loves_Africa_2022.html" title="Commons:Wiki Loves Africa 2022">WLA 2022</a></li>
<li class=""><a href="Commons_Wiki_Loves_Africa_2021.html" title="Commons:Wiki Loves Africa 2021">WLA 2021</a></li>
<li class=""><a href="Commons_Wiki_Loves_Africa_2020.html" title="Commons:Wiki Loves Africa 2020">WLA 2020</a></li>
<li class=""><a href="Commons_Wiki_Loves_Africa_2019.html" title="Commons:Wiki Loves Africa 2019">WLA 2019</a></li>
<li class=""><a href="Commons_Wiki_Loves_Africa_2017/en.html" title="Commons:Wiki Loves Africa 2017/en">WLA 2017</a></li>
<li class=""><a href="Commons_Wiki_Loves_Africa_2016.html" title="Commons:Wiki Loves Africa 2016">WLA 2016</a></li>
<li class=""><a href="Commons_Wiki_Loves_Africa_2015.html" title="Commons:Wiki Loves Africa 2015">WLA 2015</a></li>
<li class="wlam-currentPage"><a class="mw-selflink selflink">WLA 2014</a></li>
<li><a href="https://meta.wikimedia.org/wiki/Wiki_Loves_Africa" class="extiw" title="meta:Wiki Loves Africa">Organizers</a></li>
<li><a href="https://iw.toolforge.org/isa/%3Flang%3Den" class="extiw" title="toolforge:isa/?lang=en">Play ISA!</a></li>
</ul>
<table style="background-color:#fdf4d8; border:1px solid #A3B1BF; margin:0 0 5px 10px; padding:5px;" align="right">
<caption><small><a class="external text" href="https://commons.wikimedia.org/w/index.php?title=Template:WLA_2014_menu&amp;action=edit">edit me +/-</a></small>
</caption>
<tbody><tr>
<td><b><span typeof="mw:File"><a href="File_WIKI_LOVES_AFRICA.html" class="mw-file-description"><img src="../../upload.wikimedia.org/wikipedia/commons/thumb/7/71/WIKI_LOVES_AFRICA.png/60px-WIKI_LOVES_AFRICA.png" decoding="async" width="50" height="28" class="mw-file-element" srcset="https://upload.wikimedia.org/wikipedia/commons/thumb/7/71/WIKI_LOVES_AFRICA.png/120px-WIKI_LOVES_AFRICA.png 1.5x" data-file-width="708" data-file-height="392"></a></span> Wiki Loves Africa</b>
<p><b>For competitors</b>
</p>
<ul><li><a href="Commons_Wiki_Loves_Africa_2014/Participate.html" title="Commons:Wiki Loves Africa 2014/Participate">Enter images here</a></li>
<li><a rel="nofollow" class="external text" href="http://www.wikilovesafrica.org/">Main competition website</a></li>
<li><a href="Commons_Wiki_Loves_Africa_2014/Competition_rules.html" title="Commons:Wiki Loves Africa 2014/Competition rules">Competition rules</a></li>
<li><a href="Commons_Wiki_Loves_Africa_2014/FAQ.html" title="Commons:Wiki Loves Africa 2014/FAQ">Competitors' FAQ</a></li>
<li><a href="Commons_Wiki_Loves_Africa_2014/Competitors%27_help_desk.html" title="Commons:Wiki Loves Africa 2014/Competitors' help desk">Competitors' help desk</a></li>
<li><a href="Category_Images_from_Wiki_Loves_Africa_2014.html" title="Category:Images from Wiki Loves Africa 2014">Uploaded images</a></li></ul>
<p><b>For organizers &amp; helpers</b>
</p>
<ul><li><a href="Commons_Wiki_Loves_Africa.html" title="Commons:Wiki Loves Africa">Main page on Commons</a></li>
<li><a href="Commons_Wiki_Loves_Africa_2014/People.html" title="Commons:Wiki Loves Africa 2014/People">Organizers &amp; helpers</a></li>
<li><a href="Commons_Wiki_Loves_Africa_2014/Organizers%27_FAQ.html" title="Commons:Wiki Loves Africa 2014/Organizers' FAQ">Organizers' FAQ</a></li></ul>
<p><b>For media</b>
</p>
<ul><li><a href="Commons_Wiki_Loves_Africa_2014/Media_Information.html" title="Commons:Wiki Loves Africa 2014/Media Information">Media information</a></li>
<li><a href="Commons_Wiki_Loves_Africa_2014/Media_mentions.html" title="Commons:Wiki Loves Africa 2014/Media mentions">Media coverage</a></li></ul>
<p><b>Other links</b>
</p>
<ul><li><a href="Commons_talk_Wiki_Loves_Africa.html" title="Commons talk:Wiki Loves Africa">Discuss</a></li></ul>
</td></tr></tbody></table>
<figure class="mw-halign-left" typeof="mw:File"><a href="File_Wiki_Loves_Africa_Logo.html" class="mw-file-description"><img src="../../upload.wikimedia.org/wikipedia/commons/thumb/d/d3/Wiki_Loves_Africa_Logo.jpg/330px-Wiki_Loves_Africa_Logo.jpg" decoding="async" width="300" height="170" class="mw-file-element" srcset="https://upload.wikimedia.org/wikipedia/commons/thumb/d/d3/Wiki_Loves_Africa_Logo.jpg/500px-Wiki_Loves_Africa_Logo.jpg 1.5x, https://upload.wikimedia.org/wikipedia/commons/thumb/d/d3/Wiki_Loves_Africa_Logo.jpg/600px-Wiki_Loves_Africa_Logo.jpg 2x" data-file-width="709" data-file-height="401"></a><figcaption></figcaption></figure>
<p>Please note: This page documents the Wiki Loves Africa competition in <b>2014</b>. To view the page for <b>2015</b>, <a href="Commons_Wiki_Loves_Africa_2015.html" title="Commons:Wiki Loves Africa 2015">click here</a>. To view the voting and nomination process that resulted in the 2015 theme, <a href="Commons_Wiki_Loves_Africa/2015_theme_ideas.html" title="Commons:Wiki Loves Africa/2015 theme ideas">click here</a>.
</p><p><a href="Wiki_Loves_Africa_2014/Winners.html" title="Wiki Loves Africa 2014/Winners"><big><big><b>View the 4 winning photos for Wiki Loves Africa 2014 here.</b></big></big></a>
</p><p><b>What is Wiki Loves Africa?</b>
</p>
<dl><dd>An annual photo competition in Africa</dd></dl>
<p>This project is possible due to funding from the <a rel="nofollow" class="external text" href="http://www.fondationorange.com/?lang=en">Orange Foundation</a> and the <a href="Wikimedia_Foundation.html" title="Wikimedia Foundation">Wikimedia Foundation</a>, via a <a href="https://meta.wikimedia.org/wiki/Grants:PEG/Africa_Centre/Wiki_Loves_Africa_2014" class="extiw" title="m:Grants:PEG/Africa Centre/Wiki Loves Africa 2014">PEG grant</a>.
</p><p><b>When does it take place? </b>
</p>
<dl><dd>WLA is a two-month competition. In 2014 it started on the 1st October and ended on the 30th November.</dd></dl>
<p><b>What's the focus theme&nbsp;?</b>
</p>
<dl><dd>The theme for the 2014 photo contest was Wiki Loves Africa Cuisine. In 2015 the community selected a <a href="Commons_Wiki_Loves_Africa/2015_theme_ideas.html" title="Commons:Wiki Loves Africa/2015 theme ideas">theme</a> in June.</dd></dl>
<p><b>Some examples of pictures you could propose in 2014...</b>
</p>
<ul class="gallery mw-gallery-packed">
		<li class="gallerybox" style="width: 142.66666666667px">
			<div class="thumb" style="width: 140.66666666667px;"><span typeof="mw:File"><a href="File_South_Africa1.html" class="mw-file-description" title="Nando's Restaurant in South Africa"><img alt="Nando's Restaurant in South Africa" src="../../upload.wikimedia.org/wikipedia/commons/thumb/5/52/South_Africa1.jpg/250px-South_Africa1.jpg" decoding="async" width="141" height="200" class="mw-file-element" srcset="https://upload.wikimedia.org/wikipedia/commons/thumb/5/52/South_Africa1.jpg/330px-South_Africa1.jpg 1.5x, https://upload.wikimedia.org/wikipedia/commons/thumb/5/52/South_Africa1.jpg/500px-South_Africa1.jpg 2x" data-file-width="721" data-file-height="1024"></a></span></div>
			<div class="gallerytext">Nando's Restaurant in South Africa</div>
		</li>
		<li class="gallerybox" style="width: 302.66666666667px">
			<div class="thumb" style="width: 300.66666666667px;"><span typeof="mw:File"><a href="File_Africa_Food_Security_11_(10665081134).html" class="mw-file-description" title="A Malawian woman husks corn with a group of women in her village on the outskirts of Lilongwe, Malawi"><img alt="A Malawian woman husks corn with a group of women in her village on the outskirts of Lilongwe, Malawi" src="../../upload.wikimedia.org/wikipedia/commons/thumb/2/24/Africa_Food_Security_11_(10665081134).jpg/500px-Africa_Food_Security_11_(10665081134).jpg" decoding="async" width="301" height="200" class="mw-file-element" srcset="https://upload.wikimedia.org/wikipedia/commons/thumb/2/24/Africa_Food_Security_11_%2810665081134%29.jpg/960px-Africa_Food_Security_11_%2810665081134%29.jpg 1.5x" data-file-width="3504" data-file-height="2332"></a></span></div>
			<div class="gallerytext">A Malawian woman husks corn with a group of women in her village on the outskirts of Lilongwe, Malawi</div>
		</li>
		<li class="gallerybox" style="width: 282px">
			<div class="thumb" style="width: 280px;"><span typeof="mw:File"><a href="File_Root44_3_cropped.html" class="mw-file-description" title="Gatsby sandwich filled with calamari and chips for sale at The Fish Stop stall at Root44 Market, Stellenbosch, South Africa"><img alt="Gatsby sandwich filled with calamari and chips for sale at The Fish Stop stall at Root44 Market, Stellenbosch, South Africa" src="../../upload.wikimedia.org/wikipedia/commons/thumb/6/6e/Root44_3_cropped.jpg/500px-Root44_3_cropped.jpg" decoding="async" width="280" height="200" class="mw-file-element" srcset="https://upload.wikimedia.org/wikipedia/commons/thumb/6/6e/Root44_3_cropped.jpg/960px-Root44_3_cropped.jpg 1.5x" data-file-width="1289" data-file-height="921"></a></span></div>
			<div class="gallerytext">Gatsby sandwich filled with calamari and chips for sale at The Fish Stop stall at Root44 Market, Stellenbosch, South Africa</div>
		</li>
		<li class="gallerybox" style="width: 302px">
			<div class="thumb" style="width: 300px;"><span typeof="mw:File"><a href="File_Root44_10.html" class="mw-file-description" title="Saville's Eastern Delights stall at Root44 Market, Stellenbosch, South Africa"><img alt="Saville's Eastern Delights stall at Root44 Market, Stellenbosch, South Africa" src="../../upload.wikimedia.org/wikipedia/commons/thumb/4/44/Root44_10.jpg/500px-Root44_10.jpg" decoding="async" width="300" height="200" class="mw-file-element" srcset="https://upload.wikimedia.org/wikipedia/commons/thumb/4/44/Root44_10.jpg/960px-Root44_10.jpg 1.5x" data-file-width="5184" data-file-height="3456"></a></span></div>
			<div class="gallerytext">Saville's Eastern Delights stall at Root44 Market, Stellenbosch, South Africa</div>
		</li>
		<li class="gallerybox" style="width: 303.33333333333px">
			<div class="thumb" style="width: 301.33333333333px;"><span typeof="mw:File"><a href="File_2008_Veg_Egypt_3140277021.html" class="mw-file-description" title="Cauliflowers in Egypt"><img alt="Cauliflowers in Egypt" src="../../upload.wikimedia.org/wikipedia/commons/thumb/c/c5/2008_Veg_Egypt_3140277021.jpg/500px-2008_Veg_Egypt_3140277021.jpg" decoding="async" width="302" height="200" class="mw-file-element" srcset="https://upload.wikimedia.org/wikipedia/commons/thumb/c/c5/2008_Veg_Egypt_3140277021.jpg/960px-2008_Veg_Egypt_3140277021.jpg 1.5x" data-file-width="2717" data-file-height="1803"></a></span></div>
			<div class="gallerytext">Cauliflowers in Egypt</div>
		</li>
		<li class="gallerybox" style="width: 268.66666666667px">
			<div class="thumb" style="width: 266.66666666667px;"><span typeof="mw:File"><a href="File_KororimaGround.html" class="mw-file-description" title="Kororima. The dried fruits have been pulverized so that the seeds may be removed. The ground seed is used as an ingredient in berbere. (Shashamane, Ethiopia)"><img alt="Kororima. The dried fruits have been pulverized so that the seeds may be removed. The ground seed is used as an ingredient in berbere. (Shashamane, Ethiopia)" src="../../upload.wikimedia.org/wikipedia/commons/thumb/a/a5/KororimaGround.jpg/500px-KororimaGround.jpg" decoding="async" width="267" height="200" class="mw-file-element" srcset="https://upload.wikimedia.org/wikipedia/commons/thumb/a/a5/KororimaGround.jpg/960px-KororimaGround.jpg 1.5x" data-file-width="2592" data-file-height="1944"></a></span></div>
			<div class="gallerytext">Kororima. The dried fruits have been pulverized so that the seeds may be removed. The ground seed is used as an ingredient in berbere. (Shashamane, Ethiopia)</div>
		</li>
		<li class="gallerybox" style="width: 135.33333333333px">
			<div class="thumb" style="width: 133.33333333333px;"><span typeof="mw:File"><a href="File_Sandrine_dole_restorue03.html" class="mw-file-description" title="Cooking equipment in ceramics, bamboo. Production Cameroon. Self-initiative with institutional grants."><img alt="Cooking equipment in ceramics, bamboo. Production Cameroon. Self-initiative with institutional grants." src="../../upload.wikimedia.org/wikipedia/commons/thumb/f/f3/Sandrine_dole_restorue03.jpg/250px-Sandrine_dole_restorue03.jpg" decoding="async" width="134" height="200" class="mw-file-element" srcset="https://upload.wikimedia.org/wikipedia/commons/thumb/f/f3/Sandrine_dole_restorue03.jpg/330px-Sandrine_dole_restorue03.jpg 1.5x, https://upload.wikimedia.org/wikipedia/commons/thumb/f/f3/Sandrine_dole_restorue03.jpg/500px-Sandrine_dole_restorue03.jpg 2x" data-file-width="900" data-file-height="1350"></a></span></div>
			<div class="gallerytext">Cooking equipment in ceramics, bamboo. Production Cameroon. Self-initiative with institutional grants.</div>
		</li>
		<li class="gallerybox" style="width: 302.66666666667px">
			<div class="thumb" style="width: 300.66666666667px;"><span typeof="mw:File"><a href="File_Attieke_en_sac.html" class="mw-file-description" title="Attiéké, couscous of cassava. Culinary speciality from Côte d'Ivoire"><img alt="Attiéké, couscous of cassava. Culinary speciality from Côte d'Ivoire" src="../../upload.wikimedia.org/wikipedia/commons/thumb/8/88/Attieke_en_sac.JPG/500px-Attieke_en_sac.jpg" decoding="async" width="301" height="200" class="mw-file-element" srcset="https://upload.wikimedia.org/wikipedia/commons/thumb/8/88/Attieke_en_sac.JPG/960px-Attieke_en_sac.JPG 1.5x" data-file-width="4000" data-file-height="2664"></a></span></div>
			<div class="gallerytext">Attiéké, couscous of cassava. Culinary speciality from Côte d'Ivoire</div>
		</li>
		<li class="gallerybox" style="width: 272.66666666667px">
			<div class="thumb" style="width: 270.66666666667px;"><span typeof="mw:File"><a href="File_Pot_en_terre_cuite_pour_huile_d%27olive%2c_Lamta_Tunisie_mai_2013.html" class="mw-file-description" title="Pottery to contain olive oil exposed in Lamta Ribat, Tunisia"><img alt="Pottery to contain olive oil exposed in Lamta Ribat, Tunisia" src="../../upload.wikimedia.org/wikipedia/commons/thumb/0/06/Pot_en_terre_cuite_pour_huile_d%27olive%2c_Lamta_Tunisie_mai_2013.jpg/500px-Pot_en_terre_cuite_pour_huile_d%27olive%2c_Lamta_Tunisie_mai_2013.jpg" decoding="async" width="271" height="200" class="mw-file-element" srcset="https://upload.wikimedia.org/wikipedia/commons/thumb/0/06/Pot_en_terre_cuite_pour_huile_d%27olive%2C_Lamta_Tunisie_mai_2013.jpg/960px-Pot_en_terre_cuite_pour_huile_d%27olive%2C_Lamta_Tunisie_mai_2013.jpg 1.5x" data-file-width="3856" data-file-height="2853"></a></span></div>
			<div class="gallerytext">Pottery to contain olive oil exposed in Lamta Ribat, Tunisia</div>
		</li>
		<li class="gallerybox" style="width: 268.66666666667px">
			<div class="thumb" style="width: 266.66666666667px;"><span typeof="mw:File"><a href="File_Guellala-59a-olaria.html" class="mw-file-description" title="Local pottery in Guellala shop in Tunisia"><img alt="Local pottery in Guellala shop in Tunisia" src="../../upload.wikimedia.org/wikipedia/commons/thumb/0/0f/Guellala-59a-olaria.jpg/500px-Guellala-59a-olaria.jpg" decoding="async" width="267" height="200" class="mw-file-element" srcset="https://upload.wikimedia.org/wikipedia/commons/thumb/0/0f/Guellala-59a-olaria.jpg/600px-Guellala-59a-olaria.jpg 1.5x, https://upload.wikimedia.org/wikipedia/commons/thumb/0/0f/Guellala-59a-olaria.jpg/800px-Guellala-59a-olaria.jpg 2x" data-file-width="853" data-file-height="640"></a></span></div>
			<div class="gallerytext">Local pottery in Guellala shop in Tunisia</div>
		</li>
		<li class="gallerybox" style="width: 302px">
			<div class="thumb" style="width: 300px;"><span typeof="mw:File"><a href="File_Coffee_Bean_Roasting%2c_Ethiopia_(11815309196).html" class="mw-file-description" title="Coffee Bean Roasting, Ethiopia"><img alt="Coffee Bean Roasting, Ethiopia" src="../../upload.wikimedia.org/wikipedia/commons/thumb/9/98/Coffee_Bean_Roasting%2c_Ethiopia_(11815309196).jpg/500px-Coffee_Bean_Roasting%2c_Ethiopia_(11815309196).jpg" decoding="async" width="300" height="200" class="mw-file-element" srcset="https://upload.wikimedia.org/wikipedia/commons/thumb/9/98/Coffee_Bean_Roasting%2C_Ethiopia_%2811815309196%29.jpg/960px-Coffee_Bean_Roasting%2C_Ethiopia_%2811815309196%29.jpg 1.5x" data-file-width="6016" data-file-height="4016"></a></span></div>
			<div class="gallerytext">Coffee Bean Roasting, Ethiopia</div>
		</li>
		<li class="gallerybox" style="width: 302px">
			<div class="thumb" style="width: 300px;"><span typeof="mw:File"><a href="File_Ethiopian_Coffee_Ceremony_011.html" class="mw-file-description" title="Ethiopian coffee ceremony"><img alt="Ethiopian coffee ceremony" src="../../upload.wikimedia.org/wikipedia/commons/thumb/e/e6/Ethiopian_Coffee_Ceremony_011.jpg/500px-Ethiopian_Coffee_Ceremony_011.jpg" decoding="async" width="300" height="200" class="mw-file-element" srcset="https://upload.wikimedia.org/wikipedia/commons/thumb/e/e6/Ethiopian_Coffee_Ceremony_011.jpg/960px-Ethiopian_Coffee_Ceremony_011.jpg 1.5x" data-file-width="2496" data-file-height="1664"></a></span></div>
			<div class="gallerytext">Ethiopian coffee ceremony</div>
		</li>
		<li class="gallerybox" style="width: 302.66666666667px">
			<div class="thumb" style="width: 300.66666666667px;"><span typeof="mw:File"><a href="File_Cassava_on_the_Sawla_market.html" class="mw-file-description" title="Chips of Cassava plant (Manihot esculents) at Sawla market in Ghana's Northern Region."><img alt="Chips of Cassava plant (Manihot esculents) at Sawla market in Ghana's Northern Region." src="../../upload.wikimedia.org/wikipedia/commons/thumb/a/a2/Cassava_on_the_Sawla_market.jpg/500px-Cassava_on_the_Sawla_market.jpg" decoding="async" width="301" height="200" class="mw-file-element" srcset="https://upload.wikimedia.org/wikipedia/commons/thumb/a/a2/Cassava_on_the_Sawla_market.jpg/960px-Cassava_on_the_Sawla_market.jpg 1.5x" data-file-width="4256" data-file-height="2832"></a></span></div>
			<div class="gallerytext">Chips of Cassava plant (Manihot esculents) at Sawla market in Ghana's Northern Region.</div>
		</li>
		<li class="gallerybox" style="width: 352.66666666667px">
			<div class="thumb" style="width: 350.66666666667px;"><span typeof="mw:File"><a href="File_Ghana_pineapple_field.html" class="mw-file-description" title="Pineapple field in Ghana"><img alt="Pineapple field in Ghana" src="../../upload.wikimedia.org/wikipedia/commons/thumb/d/da/Ghana_pineapple_field.jpg/960px-Ghana_pineapple_field.jpg" decoding="async" width="351" height="200" class="mw-file-element" srcset="https://upload.wikimedia.org/wikipedia/commons/thumb/d/da/Ghana_pineapple_field.jpg/1052px-Ghana_pineapple_field.jpg 2x" data-file-width="1795" data-file-height="1024"></a></span></div>
			<div class="gallerytext">Pineapple field in Ghana</div>
		</li>
		<li class="gallerybox" style="width: 268.66666666667px">
			<div class="thumb" style="width: 266.66666666667px;"><span typeof="mw:File"><a href="File_Tea_plantation_near_Mulanje.html" class="mw-file-description" title="Tea plantation and harvest near Mulanje Plateau, Malawi"><img alt="Tea plantation and harvest near Mulanje Plateau, Malawi" src="../../upload.wikimedia.org/wikipedia/commons/thumb/2/29/Tea_plantation_near_Mulanje.JPG/500px-Tea_plantation_near_Mulanje.jpg" decoding="async" width="267" height="200" class="mw-file-element" srcset="https://upload.wikimedia.org/wikipedia/commons/thumb/2/29/Tea_plantation_near_Mulanje.JPG/960px-Tea_plantation_near_Mulanje.JPG 1.5x" data-file-width="3648" data-file-height="2736"></a></span></div>
			<div class="gallerytext">Tea plantation and harvest near Mulanje Plateau, Malawi</div>
		</li>
		<li class="gallerybox" style="width: 268.66666666667px">
			<div class="thumb" style="width: 266.66666666667px;"><span typeof="mw:File"><a href="File_Tajine_022.html" class="mw-file-description" title="Tajine"><img alt="Tajine" src="../../upload.wikimedia.org/wikipedia/commons/thumb/9/9e/Tajine_022.JPG/500px-Tajine_022.jpg" decoding="async" width="267" height="200" class="mw-file-element" srcset="https://upload.wikimedia.org/wikipedia/commons/thumb/9/9e/Tajine_022.JPG/960px-Tajine_022.JPG 1.5x" data-file-width="1600" data-file-height="1200"></a></span></div>
			<div class="gallerytext">Tajine</div>
		</li>
		<li class="gallerybox" style="width: 269.33333333333px">
			<div class="thumb" style="width: 267.33333333333px;"><span typeof="mw:File"><a href="File_Pastilla4.html" class="mw-file-description" title="Etape de la réalisation de la pastilla"><img alt="Etape de la réalisation de la pastilla" src="../../upload.wikimedia.org/wikipedia/commons/b/b3/Pastilla4.jpg" decoding="async" width="268" height="200" class="mw-file-element" data-file-width="227" data-file-height="170"></a></span></div>
			<div class="gallerytext">Etape de la réalisation de la pastilla</div>
		</li>
		<li class="gallerybox" style="width: 274.66666666667px">
			<div class="thumb" style="width: 272.66666666667px;"><span typeof="mw:File"><a href="File_Praia_market_potatoes_manioc.html" class="mw-file-description" title="Marché à Praia, la capitale du Cap-Vert"><img alt="Marché à Praia, la capitale du Cap-Vert" src="../../upload.wikimedia.org/wikipedia/commons/thumb/6/64/Praia_market_potatoes_manioc.jpg/500px-Praia_market_potatoes_manioc.jpg" decoding="async" width="273" height="200" class="mw-file-element" srcset="https://upload.wikimedia.org/wikipedia/commons/thumb/6/64/Praia_market_potatoes_manioc.jpg/960px-Praia_market_potatoes_manioc.jpg 1.5x" data-file-width="3768" data-file-height="2763"></a></span></div>
			<div class="gallerytext">Marché à Praia, la capitale du Cap-Vert</div>
		</li>
		<li class="gallerybox" style="width: 142px">
			<div class="thumb" style="width: 140px;"><span typeof="mw:File"><a href="File_Culinary_exchange%2c_Arta%2c_Djibouti%2c_March_2011_(5537026630).html" class="mw-file-description" title="Arta Culinary School student washes dishes after the exchange of homemade recipes. Arta, Djibouti"><img alt="Arta Culinary School student washes dishes after the exchange of homemade recipes. Arta, Djibouti" src="../../upload.wikimedia.org/wikipedia/commons/thumb/9/95/Culinary_exchange%2c_Arta%2c_Djibouti%2c_March_2011_(5537026630).jpg/250px-Culinary_exchange%2c_Arta%2c_Djibouti%2c_March_2011_(5537026630).jpg" decoding="async" width="140" height="200" class="mw-file-element" srcset="https://upload.wikimedia.org/wikipedia/commons/thumb/9/95/Culinary_exchange%2C_Arta%2C_Djibouti%2C_March_2011_%285537026630%29.jpg/330px-Culinary_exchange%2C_Arta%2C_Djibouti%2C_March_2011_%285537026630%29.jpg 1.5x, https://upload.wikimedia.org/wikipedia/commons/thumb/9/95/Culinary_exchange%2C_Arta%2C_Djibouti%2C_March_2011_%285537026630%29.jpg/500px-Culinary_exchange%2C_Arta%2C_Djibouti%2C_March_2011_%285537026630%29.jpg 2x" data-file-width="1050" data-file-height="1500"></a></span></div>
			<div class="gallerytext">Arta Culinary School student washes dishes after the exchange of homemade recipes. Arta, Djibouti</div>
		</li>
		<li class="gallerybox" style="width: 302.66666666667px">
			<div class="thumb" style="width: 300.66666666667px;"><span typeof="mw:File"><a href="File_Liboke_entrouvert_03.html" class="mw-file-description" title="« Liboke » type de cuisson congolaise : la nourriture (le plus souvent du poisson) est cuite sur des braises à l'étouffée à l'intérieur de larges feuilles de bananier ou de citronnier liées par le haut."><img alt="« Liboke » type de cuisson congolaise : la nourriture (le plus souvent du poisson) est cuite sur des braises à l'étouffée à l'intérieur de larges feuilles de bananier ou de citronnier liées par le haut." src="../../upload.wikimedia.org/wikipedia/commons/thumb/5/52/Liboke_entrouvert_03.JPG/500px-Liboke_entrouvert_03.jpg" decoding="async" width="301" height="200" class="mw-file-element" srcset="https://upload.wikimedia.org/wikipedia/commons/thumb/5/52/Liboke_entrouvert_03.JPG/960px-Liboke_entrouvert_03.JPG 1.5x" data-file-width="4000" data-file-height="2664"></a></span></div>
			<div class="gallerytext">«&nbsp;Liboke&nbsp;» type de cuisson congolaise&nbsp;: la nourriture (le plus souvent du poisson) est cuite sur des braises à l'étouffée à l'intérieur de larges feuilles de bananier ou de citronnier liées par le haut. </div>
		</li>
		<li class="gallerybox" style="width: 302px">
			<div class="thumb" style="width: 300px;"><span typeof="mw:File"><a href="File_Aloegrove_Dining_Room_(3684697507).html" class="mw-file-description" title="Aloegrove Dining Room. Namibia"><img alt="Aloegrove Dining Room. Namibia" src="../../upload.wikimedia.org/wikipedia/commons/thumb/3/36/Aloegrove_Dining_Room_(3684697507).jpg/500px-Aloegrove_Dining_Room_(3684697507).jpg" decoding="async" width="300" height="200" class="mw-file-element" srcset="https://upload.wikimedia.org/wikipedia/commons/thumb/3/36/Aloegrove_Dining_Room_%283684697507%29.jpg/960px-Aloegrove_Dining_Room_%283684697507%29.jpg 1.5x" data-file-width="1600" data-file-height="1067"></a></span></div>
			<div class="gallerytext">Aloegrove Dining Room. Namibia</div>
		</li>
		<li class="gallerybox" style="width: 152px">
			<div class="thumb" style="width: 150px;"><span typeof="mw:File"><a href="File_Un_festival_c%c3%a9l%c3%a8bre_la_tradition_culinaire_tunisienne_(5238660948).html" class="mw-file-description" title="Festival culinaire en Tunisie."><img alt="Festival culinaire en Tunisie." src="../../upload.wikimedia.org/wikipedia/commons/thumb/1/1c/Un_festival_c%c3%a9l%c3%a8bre_la_tradition_culinaire_tunisienne_(5238660948).jpg/250px-Un_festival_c%c3%a9l%c3%a8bre_la_tradition_culinaire_tunisienne_(5238660948).jpg" decoding="async" width="150" height="200" class="mw-file-element" srcset="https://upload.wikimedia.org/wikipedia/commons/thumb/1/1c/Un_festival_c%C3%A9l%C3%A8bre_la_tradition_culinaire_tunisienne_%285238660948%29.jpg/500px-Un_festival_c%C3%A9l%C3%A8bre_la_tradition_culinaire_tunisienne_%285238660948%29.jpg 1.5x" data-file-width="3000" data-file-height="4000"></a></span></div>
			<div class="gallerytext">Festival culinaire en Tunisie.</div>
		</li>
</ul>
<div class="mw-heading mw-heading2 ext-discussiontools-init-section"><h2 id="Links" data-mw-thread-id="h-Links"><span data-mw-comment-start="" id="h-Links"></span>Links<span data-mw-comment-end="h-Links"></span></h2><span class="mw-editsection"><span class="mw-editsection-bracket">[</span><a href="https://commons.wikimedia.org/w/index.php?title=Commons:Wiki_Loves_Africa_2014&amp;action=edit&amp;section=1" title="Edit section: Links"><span>edit</span></a><span class="mw-editsection-bracket">]</span></span></div>
<ul><li><a rel="nofollow" class="external text" href="http://web.archive.org/web/20141208191144/http://wikilovesafrica.org/">Wiki Loves Africa Website (Internet Archive)</a></li>
<li><a class="external text" href="../../meta.wikimedia.org/wiki/File_Wiki_Loves_Africa.html">Early presentation of the contest, during WikiIndaba</a></li>
<li><a href="Commons_Wiki_Loves_Africa_2014/Results_and_best_practices.html" title="Commons:Wiki Loves Africa 2014/Results and best practices">Commons:Wiki Loves Africa 2014/Results and best practices</a></li></ul>
<div role="navigation" class="navbox" aria-labelledby="Wiki_Loves_Africa" style="padding:3px"><table class="nowraplinks collapsible autocollapse navbox-inner" style="border-spacing:0;background:transparent;color:inherit"><tbody><tr><th scope="col" class="navbox-title" colspan="3"><div class="plainlinks hlist navbar mini"><ul><li class="nv-view"><a href="Template_Wiki_Loves_Africa.html" class="mw-redirect" title="Template:Wiki Loves Africa"><span title="View this template" style=";;background:none transparent;border:none;-moz-box-shadow:none;-webkit-box-shadow:none;box-shadow:none; padding:0;">v</span></a></li><li class="nv-talk"><a href="https://commons.wikimedia.org/w/index.php?title=Template_talk:Wiki_Loves_Africa&amp;action=edit&amp;redlink=1" class="new" title="Template talk:Wiki Loves Africa (page does not exist)"><span title="Discuss this template" style=";;background:none transparent;border:none;-moz-box-shadow:none;-webkit-box-shadow:none;box-shadow:none; padding:0;">t</span></a></li><li class="nv-edit"><a class="external text" href="https://commons.wikimedia.org/w/index.php?title=Template:Wiki_Loves_Africa&amp;action=edit"><span title="Edit this template" style=";;background:none transparent;border:none;-moz-box-shadow:none;-webkit-box-shadow:none;box-shadow:none; padding:0;">e</span></a></li></ul></div><div id="Wiki_Loves_Africa" style="font-size:114%;margin:0 4em"><a href="Commons_Wiki_Loves_Africa.html" title="Commons:Wiki Loves Africa">Wiki Loves Africa</a></div></th></tr><tr><th scope="row" class="navbox-group" style="width:1%">WLA home competition by year</th><td class="navbox-list navbox-odd hlist" style="text-align:left;border-left-width:2px;border-left-style:solid;width:auto;padding:0px"><div style="padding:0em 0.25em">Competition pages on Commons&nbsp;: <a href="Commons_Wiki_Loves_Africa.html" title="Commons:Wiki Loves Africa">Wiki Loves Africa</a> - <a class="mw-selflink selflink">2014</a> - <a href="Commons_Wiki_Loves_Africa_2015.html" title="Commons:Wiki Loves Africa 2015">2015</a> - <a href="Commons_Wiki_Loves_Africa_2016.html" title="Commons:Wiki Loves Africa 2016">2016</a> - <a href="Commons_Wiki_Loves_Africa_2017.html" class="mw-redirect" title="Commons:Wiki Loves Africa 2017">2016</a> - <a href="Commons_Wiki_Loves_Africa_2019.html" title="Commons:Wiki Loves Africa 2019">2019</a> - <a href="Commons_Wiki_Loves_Africa_2020.html" title="Commons:Wiki Loves Africa 2020">2020</a> - <a href="Commons_Wiki_Loves_Africa_2021.html" title="Commons:Wiki Loves Africa 2021">2021</a> - <a href="Commons_Wiki_Loves_Africa_2022.html" title="Commons:Wiki Loves Africa 2022">2022</a> - <a href="Commons_Wiki_Loves_Africa_2023.html" title="Commons:Wiki Loves Africa 2023">2023</a> - <a href="Commons_Wiki_Loves_Africa_2024.html" title="Commons:Wiki Loves Africa 2024">2024</a><br>
<hr></div></td><td class="navbox-image" rowspan="7" style="width:1px;padding:0 0 0 2px"><div><span typeof="mw:File"><a href="File_Wiki_Loves_Africa_logo_2024.html" class="mw-file-description"><img src="../../upload.wikimedia.org/wikipedia/commons/thumb/5/5d/Wiki_Loves_Africa_logo_2024.svg/250px-Wiki_Loves_Africa_logo_2024.svg.png" decoding="async" width="150" height="94" class="mw-file-element" srcset="https://upload.wikimedia.org/wikipedia/commons/thumb/5/5d/Wiki_Loves_Africa_logo_2024.svg/330px-Wiki_Loves_Africa_logo_2024.svg.png 2x" data-file-width="512" data-file-height="322"></a></span></div></td></tr><tr><th scope="row" class="navbox-group" style="width:1%">Winners</th><td class="navbox-list navbox-even hlist" style="text-align:left;border-left-width:2px;border-left-style:solid;width:auto;padding:0px"><div style="padding:0em 0.25em">In 2024: <b><a href="Commons_Wiki_Loves_Africa_2024/Winners.html" title="Commons:Wiki Loves Africa 2024/Winners">International winners</a></b> - <a href="Commons_Wiki_Loves_Africa_2024/National_winners.html" title="Commons:Wiki Loves Africa 2024/National winners">Results of the National Winners</a>  <br>
<p>In 2023: <b><a href="Commons_Wiki_Loves_Africa_2023/Winners.html" title="Commons:Wiki Loves Africa 2023/Winners">International winners</a></b> - <a href="Commons_Wiki_Loves_Africa_2023/National_winners.html" title="Commons:Wiki Loves Africa 2023/National winners">Results of the National Winners</a>  <br>
In 2022: <b><a href="Commons_Wiki_Loves_Africa_2022/Winners.html" title="Commons:Wiki Loves Africa 2022/Winners">International winners</a></b> - <a href="Commons_Wiki_Loves_Africa_2022/National_winners.html" title="Commons:Wiki Loves Africa 2022/National winners">Results of the National Winners</a>  <br>
In 2021: <b><a href="Commons_Wiki_Loves_Africa_2021/Winners.html" title="Commons:Wiki Loves Africa 2021/Winners">International winners</a></b> - <a href="Commons_Wiki_Loves_Africa_2021/National_winners.html" title="Commons:Wiki Loves Africa 2021/National winners">Results of the National Winners</a>  <br>
In 2020: <b><a href="Commons_Wiki_Loves_Africa_2020/Winners.html" title="Commons:Wiki Loves Africa 2020/Winners">International winners</a></b> -  <a href="Commons_Wiki_Loves_Africa_2020/National_winners.html" title="Commons:Wiki Loves Africa 2020/National winners">Results of the National Winners</a> <br>
In 2019: <b><a href="Commons_Wiki_Loves_Africa_2019/Winners.html" title="Commons:Wiki Loves Africa 2019/Winners">International winners</a></b> -  <a href="Commons_Wiki_Loves_Africa_2019_in_Algeria.html" title="Commons:Wiki Loves Africa 2019 in Algeria">In Algeria</a> - <a href="Commons_Wiki_Loves_Africa_2019/Cameroon.html" title="Commons:Wiki Loves Africa 2019/Cameroon">In Cameroon</a> - <a href="Commons_Wiki_Loves_Africa_2019/Nigeria.html" title="Commons:Wiki Loves Africa 2019/Nigeria">In Nigeria</a> - <a href="Commons_Wiki_Loves_Africa_2019/Benin.html" title="Commons:Wiki Loves Africa 2019/Benin">In Benin</a> - <a href="Commons_Wiki_Loves_Africa_2019/Zambia.html" title="Commons:Wiki Loves Africa 2019/Zambia">in Zambia</a> <br>
In 2017: <b><a href="Commons_Wiki_Loves_Africa_2017/Winners.html" title="Commons:Wiki Loves Africa 2017/Winners">International winners</a></b> - <a href="Commons_Wiki_Loves_Africa_2017/Cameroon.html" title="Commons:Wiki Loves Africa 2017/Cameroon">In Cameroon</a> - <a href="Commons_Wiki_Loves_Africa_2017/Nigeria.html" title="Commons:Wiki Loves Africa 2017/Nigeria">in Nigeria</a> - <a href="Category_Wiki_Loves_Africa_Tunisia_2017_Close_Ceremony.html" title="Category:Wiki Loves Africa Tunisia 2017 Close Ceremony">In Tunisia</a> <br>
In 2016: <b><a href="Commons_Wiki_Loves_Africa_2016/Winners.html" title="Commons:Wiki Loves Africa 2016/Winners">International winners</a></b> - <a href="Commons_Wiki_Loves_Africa_2016/Community_Prize_Selection.html" title="Commons:Wiki Loves Africa 2016/Community Prize Selection">Community Prize Selection</a> - <a href="Commons_Wiki_Loves_Africa_2016_in_Algeria.html" title="Commons:Wiki Loves Africa 2016 in Algeria">In Algeria</a> - <a href="Commons_Wiki_Loves_Africa_2016/Winners_in_Nigeria.html" title="Commons:Wiki Loves Africa 2016/Winners in Nigeria">In Nigeria</a> - <a href="Commons_Wiki_Loves_Africa_2016/Winners_in_Egypt.html" title="Commons:Wiki Loves Africa 2016/Winners in Egypt">In Egypt</a> - <a href="Commons_Wiki_Loves_Africa_2016/Winners_in_Tunisia.html" title="Commons:Wiki Loves Africa 2016/Winners in Tunisia">In Tunisia</a> <br>
In 2015: <b><a href="Commons_Wiki_Loves_Africa_2015/Winners.html" title="Commons:Wiki Loves Africa 2015/Winners">International winners</a></b> - <a href="Commons_Wiki_Loves_Africa_2015/Community_Prize_Selection.html" title="Commons:Wiki Loves Africa 2015/Community Prize Selection">Community Prize Selection</a> - <a href="Commons_Wiki_Loves_Africa_2015_in_Algeria.html" title="Commons:Wiki Loves Africa 2015 in Algeria">In Algeria</a> <br>
In 2014: <b><a href="Wiki_Loves_Africa_2014/Winners.html" title="Wiki Loves Africa 2014/Winners">International winners</a></b><br>
</p>
<hr></div></td></tr><tr><th scope="row" class="navbox-group" style="width:1%">Logistics and operations</th><td class="navbox-list navbox-odd hlist" style="text-align:left;border-left-width:2px;border-left-style:solid;width:auto;padding:0px"><div style="padding:0em 0.25em"><b>Best practices</b>&nbsp;: <a href="Commons_Wiki_Loves_Africa_2014/Results_and_best_practices.html" title="Commons:Wiki Loves Africa 2014/Results and best practices">in 2014</a> - <a href="Commons_Wiki_Loves_Africa_2015/Results_and_best_practices.html" title="Commons:Wiki Loves Africa 2015/Results and best practices">in 2015</a> - <a href="Commons_Wiki_Loves_Africa_2016/Results_and_best_practices.html" title="Commons:Wiki Loves Africa 2016/Results and best practices">in 2016</a> - <a href="Commons_Wiki_Loves_Africa_2017/Results_and_best_practices.html" title="Commons:Wiki Loves Africa 2017/Results and best practices">in 2017</a> - <a href="Commons_Wiki_Loves_Africa_2019/Results_and_best_practices.html" title="Commons:Wiki Loves Africa 2019/Results and best practices">in 2019</a> - <a href="Commons_Wiki_Loves_Africa_2020/Results_and_best_practices.html" title="Commons:Wiki Loves Africa 2020/Results and best practices">in 2020</a> - <a href="Commons_Wiki_Loves_Africa_2021/Results_and_best_practices.html" title="Commons:Wiki Loves Africa 2021/Results and best practices">in 2021</a> - <a href="Commons_Wiki_Loves_Africa_2022/Results_and_best_practices.html" title="Commons:Wiki Loves Africa 2022/Results and best practices">in 2022</a> - <a href="Commons_Wiki_Loves_Africa_2023/Results_and_best_practices.html" title="Commons:Wiki Loves Africa 2023/Results and best practices">in 2023</a> - <a href="Commons_Wiki_Loves_Africa_2024/Results_and_best_practices.html" title="Commons:Wiki Loves Africa 2024/Results and best practices">in 2024</a><br>
<p><b>Organizing pages on Meta</b>&nbsp;: <a href="https://meta.wikimedia.org/wiki/Wiki_Loves_Africa" class="extiw" title="meta:Wiki Loves Africa">on meta</a> - <a href="https://meta.wikimedia.org/wiki/Wiki_Loves_Africa_2014" class="extiw" title="meta:Wiki Loves Africa 2014">2014</a> - <a href="https://meta.wikimedia.org/wiki/Wiki_Loves_Africa_2015" class="extiw" title="meta:Wiki Loves Africa 2015">2015</a> - <a href="https://meta.wikimedia.org/wiki/Wiki_Loves_Africa_2016" class="extiw" title="meta:Wiki Loves Africa 2016">2016</a> - <a href="https://meta.wikimedia.org/wiki/Wiki_Loves_Africa_2017" class="extiw" title="meta:Wiki Loves Africa 2017">2017</a> - <a href="https://meta.wikimedia.org/wiki/Wiki_Loves_Africa_2019" class="extiw" title="meta:Wiki Loves Africa 2019">2019</a> - <a href="https://meta.wikimedia.org/wiki/Wiki_Loves_Africa_2020" class="extiw" title="meta:Wiki Loves Africa 2020">2020</a> - <a href="https://meta.wikimedia.org/wiki/Wiki_Loves_Africa_2021" class="extiw" title="meta:Wiki Loves Africa 2021">2021</a> - <a href="https://meta.wikimedia.org/wiki/Wiki_Loves_Africa_2022" class="extiw" title="meta:Wiki Loves Africa 2022">2022</a> - <a href="https://meta.wikimedia.org/wiki/Wiki_Loves_Africa_2023" class="extiw" title="meta:Wiki Loves Africa 2023">2023</a> - <a href="https://meta.wikimedia.org/wiki/Wiki_Loves_Africa_2024" class="extiw" title="meta:Wiki Loves Africa 2024">2024</a><br>
<b>Evaluation and training</b>&nbsp;: <a href="Category_Wiki_Loves_Africa_training_material.html" title="Category:Wiki Loves Africa training material">Training material</a> - <a href="Commons_Wiki_Loves_Africa/WLA_images_under_evaluation.html" title="Commons:Wiki Loves Africa/WLA images under evaluation">WLA images under evaluation</a> - <a href="Commons_Wiki_Loves_Africa_2021/Images_checking.html" title="Commons:Wiki Loves Africa 2021/Images checking">Images checking</a>
</p>
<hr></div></td></tr><tr><th scope="row" class="navbox-group" style="width:1%">Best of the best</th><td class="navbox-list navbox-even hlist" style="text-align:left;border-left-width:2px;border-left-style:solid;width:auto;padding:0px"><div style="padding:0em 0.25em">- Selections of pictures&nbsp;: <a href="Commons_Wiki_Loves_Africa/First_selection.html" title="Commons:Wiki Loves Africa/First selection">2014 First selection</a> - <a href="https://commons.wikimedia.org/w/index.php?title=Category:Best_of_Images_from_Wiki_Loves_Africa_2014&amp;action=edit&amp;redlink=1" class="new" title="Category:Best of Images from Wiki Loves Africa 2014 (page does not exist)">2014 Best Images</a> - <a href="Wiki_Loves_Africa_2014/Community_Prize_Selection.html" class="mw-redirect" title="Wiki Loves Africa 2014/Community Prize Selection">2014 Community Prize Selection</a> - <a href="https://commons.wikimedia.org/w/index.php?title=Category:Best_of_Images_from_Wiki_Loves_Africa_2015&amp;action=edit&amp;redlink=1" class="new" title="Category:Best of Images from Wiki Loves Africa 2015 (page does not exist)">Best Images</a> - <a href="Category_Best_images_from_Wiki_Loves_Africa_2016.html" title="Category:Best images from Wiki Loves Africa 2016">2016 Best Images</a> - <a href="Category_Best_images_from_Wiki_Loves_Africa_2017.html" title="Category:Best images from Wiki Loves Africa 2017">2017 Best images</a> - <a href="Category_Best_images_from_Wiki_Loves_Africa_2019.html" title="Category:Best images from Wiki Loves Africa 2019">2019 Best images</a> - <a href="Category_Best_images_from_Wiki_Loves_Africa_2020.html" title="Category:Best images from Wiki Loves Africa 2020">2020 Best images</a> - <a href="Category_Best_images_from_Wiki_Loves_Africa_2021.html" title="Category:Best images from Wiki Loves Africa 2021">2021 Best images</a> - <a href="Category_Best_images_from_Wiki_Loves_Africa_2022.html" title="Category:Best images from Wiki Loves Africa 2022">2022 Best images</a> - <a href="Category_Best_images_from_Wiki_Loves_Africa_2023.html" title="Category:Best images from Wiki Loves Africa 2023">2023 Best images</a> - <a href="Category_Best_images_from_Wiki_Loves_Africa_2024.html" title="Category:Best images from Wiki Loves Africa 2024">2024 Best images</a><br>
<p>- Selections of videos&nbsp;: <a href="Category_Best_videos_from_Wiki_Loves_Africa_2020.html" title="Category:Best videos from Wiki Loves Africa 2020">2020 Best videos</a> - <a href="Category_Best_videos_from_Wiki_Loves_Africa_2021.html" title="Category:Best videos from Wiki Loves Africa 2021">2021 Best videos</a> - <a href="Category_Best_videos_from_Wiki_Loves_Africa_2022.html" title="Category:Best videos from Wiki Loves Africa 2022">2022 Best videos</a> - <a href="Category_Best_videos_from_Wiki_Loves_Africa_2023.html" title="Category:Best videos from Wiki Loves Africa 2023">2023 Best videos</a> - <a href="https://commons.wikimedia.org/w/index.php?title=Category:Best_videos_from_Wiki_Loves_Africa_2024&amp;action=edit&amp;redlink=1" class="new" title="Category:Best videos from Wiki Loves Africa 2024 (page does not exist)">2024 Best videos</a><br>
</p>
<hr></div></td></tr><tr><th scope="row" class="navbox-group" style="width:1%">Media and outreach</th><td class="navbox-list navbox-odd hlist" style="text-align:left;border-left-width:2px;border-left-style:solid;width:auto;padding:0px"><div style="padding:0em 0.25em"><b>Website</b>&nbsp;: <a rel="nofollow" class="external free" href="http://www.wikilovesafrica.net/">http://www.wikilovesafrica.net</a><br>
<p><b>Social media</b>: <a rel="nofollow" class="external text" href="https://twitter.com/WikiLovesAfrica">WLA on Twitter</a> - <a rel="nofollow" class="external text" href="https://www.facebook.com/WikiLovesAfrica">WLA on Facebook</a> - <a rel="nofollow" class="external text" href="https://www.instagram.com/wikilovesafrica/">WLA on Instagram</a> - <a rel="nofollow" class="external text" href="https://www.youtube.com/playlist?list=PLvyE0yEt-BKgEUM5QlcQOwsMtpbWCbsnA">WLA on YouTube</a><br>
</p>
<hr></div></td></tr><tr><th scope="row" class="navbox-group" style="width:1%">Statistics</th><td class="navbox-list navbox-even hlist" style="text-align:left;border-left-width:2px;border-left-style:solid;width:auto;padding:0px"><div style="padding:0em 0.25em"><b>Wiki Loves X statistics on Toolforge</b>&nbsp;: <a class="external text" href="https://tools.wmflabs.org/wikiloves/africa/2014">2014</a> – <a class="external text" href="https://tools.wmflabs.org/wikiloves/africa/2015">2015</a> – <a class="external text" href="https://tools.wmflabs.org/wikiloves/africa/2016">2016</a> – <a class="external text" href="https://tools.wmflabs.org/wikiloves/africa/2017">2017</a> – <a class="external text" href="https://tools.wmflabs.org/wikiloves/africa/2019">2019</a> – <a class="external text" href="https://tools.wmflabs.org/wikiloves/africa/2020">2020</a> – <a class="external text" href="https://wikiloves.toolforge.org/africa/2021">2021</a> – 
<p><a class="external text" href="https://wikiloves.toolforge.org/africa/2022">2022</a> – <a class="external text" href="https://wikiloves.toolforge.org/africa/2023">2023</a> – <a class="external text" href="https://wikiloves.toolforge.org/africa/2023">2024</a>  <br>
<b>Baglama2 statistics on Toolforge&nbsp;:</b> <a class="external text" href="https://glamtools.toolforge.org/baglama2/#gid=285">2014</a> – <a class="external text" href="https://glamtools.toolforge.org/baglama2/#gid=286">2015</a> – <a class="external text" href="https://glamtools.toolforge.org/baglama2/#gid=287">2016</a> – <a class="external text" href="https://glamtools.toolforge.org/baglama2/#gid=333">2017</a> – <a class="external text" href="https://glamtools.toolforge.org/baglama2/#gid=340">2019</a> – <a class="external text" href="https://glamtools.toolforge.org/baglama2/#gid">2020</a> – <a class="external text" href="https://glamtools.toolforge.org/baglama2/#gid=777">2021</a> - <a class="external text" href="https://glamtools.toolforge.org/baglama2/#gid=1002&amp;month=202212">2022</a> <br>
</p>
<hr></div></td></tr><tr><th scope="row" class="navbox-group" style="width:1%">Lead organisation</th><td class="navbox-list navbox-odd hlist" style="text-align:left;border-left-width:2px;border-left-style:solid;width:auto;padding:0px"><div style="padding:0em 0.25em"><b>Website</b>&nbsp;: <a rel="nofollow" class="external free" href="http://www.wikiinafrica.org/">http://www.wikiinafrica.org</a><br>
<b>On Meta</b>: <a href="https://meta.wikimedia.org/wiki/Wiki_In_Africa" class="extiw" title="meta:Wiki In Africa">Wiki In Africa</a></div></td></tr></tbody></table></div>
<p>[[Category:Commons projects{{#translation}}]]
</p>
<!-- 
NewPP limit report
Parsed by mw‐web.codfw.main‐67f757d97d‐k7qvn
Cached time: 20250714074821
Cache expiry: 2592000
Reduced expiry: false
Complications: []
DiscussionTools time usage: 0.020 seconds
CPU time usage: 0.218 seconds
Real time usage: 0.401 seconds
Preprocessor visited node count: 1074/1000000
Revision size: 5232/2097152 bytes
Post‐expand include size: 39729/2097152 bytes
Template argument size: 3608/2097152 bytes
Highest expansion depth: 10/100
Expensive parser function count: 19/500
Unstrip recursion depth: 0/20
Unstrip post‐expand size: 23913/5000000 bytes
Lua time usage: 0.036/10.000 seconds
Lua memory usage: 1301374/52428800 bytes
Number of Wikibase entities loaded: 0/400
-->
<!--
Transclusion expansion time report (%,ms,calls,template)
100.00%  164.875      1 -total
 45.25%   74.603      1 Commons:Wiki_Loves_Africa/menu
 32.60%   53.741     19 Template:Pg
 20.18%   33.265      1 Template:TNT
  9.85%   16.248      1 Template:Wiki_Loves_Africa_navbox
  6.77%   11.160      1 Template:Navbox
  1.96%    3.233     19 Template:Str_left
-->

<!-- Saved in parser cache with key commonswiki:pcache:35043849:|#|:idhash:canonical and timestamp 20250714074821 and revision id 917904392. Rendering was triggered because: page-view
 -->
</div><!--esi <esi:include src="/esitest-fa8a495983347898/content" /> --><noscript><img src="https://auth.wikimedia.org/loginwiki/wiki/Special:CentralAutoLogin/checkLoggedIn?useformat=desktop&amp;wikiid=commonswiki&amp;usesul3=1&amp;type=1x1" alt="" width="1" height="1" style="border: none; position: absolute;"></noscript>
<div class="printfooter" data-nosnippet="">Retrieved from "<a dir="ltr" href="https://commons.wikimedia.org/w/index.php?title=Commons:Wiki_Loves_Africa_2014&amp;oldid=917904392">https://commons.wikimedia.org/w/index.php?title=Commons:Wiki_Loves_Africa_2014&amp;oldid=917904392</a>"</div></div>
					<div id="catlinks" class="catlinks" data-mw="interface"><div id="mw-normal-catlinks" class="mw-normal-catlinks"><a href="Special_Categories.html" title="Special:Categories">Categories</a>: <ul><li><a href="Category_Wiki_Loves_Africa_templates.html" title="Category:Wiki Loves Africa templates">Wiki Loves Africa templates</a></li><li><a href="Category_Wiki_Loves_Africa_2014.html" title="Category:Wiki Loves Africa 2014">Wiki Loves Africa 2014</a></li></ul></div></div>
				</div>
			</main>
			
		</div>
		<div class="mw-footer-container">
			
<footer id="footer" class="mw-footer" >
	<ul id="footer-info">
	<li id="footer-info-lastmod"> This page was last edited on 31 August 2024, at 11:14.</li>
	<li id="footer-info-copyright">Files are available under licenses specified on their description page. All structured data from the file namespace is available under the <a rel="nofollow" class="external text" href="https://creativecommons.org/publicdomain/zero/1.0/">Creative Commons CC0 License</a>; all unstructured text is available under the <a rel="nofollow" class="external text" href="../../creativecommons.org/licenses/by-sa/4.0/index.html">Creative Commons Attribution-ShareAlike License</a>;
additional terms may apply.
By using this site, you agree to the <a class="external text" href="https://foundation.wikimedia.org/wiki/Special:MyLanguage/Policy:Terms_of_Use">Terms of Use</a> and the <a class="external text" href="https://foundation.wikimedia.org/wiki/Special:MyLanguage/Policy:Privacy_policy">Privacy Policy</a>.</li>
</ul>

	<ul id="footer-places">
	<li id="footer-places-privacy"><a href="https://foundation.wikimedia.org/wiki/Special:MyLanguage/Policy:Privacy_policy">Privacy policy</a></li>
	<li id="footer-places-about"><a href="Commons_Welcome.html">About Wikimedia Commons</a></li>
	<li id="footer-places-disclaimers"><a href="Commons_General_disclaimer.html">Disclaimers</a></li>
	<li id="footer-places-wm-codeofconduct"><a href="https://foundation.wikimedia.org/wiki/Special:MyLanguage/Policy:Universal_Code_of_Conduct">Code of Conduct</a></li>
	<li id="footer-places-developers"><a href="https://developer.wikimedia.org/">Developers</a></li>
	<li id="footer-places-statslink"><a href="https://stats.wikimedia.org/#/commons.wikimedia.org">Statistics</a></li>
	<li id="footer-places-cookiestatement"><a href="https://foundation.wikimedia.org/wiki/Special:MyLanguage/Policy:Cookie_statement">Cookie statement</a></li>
	<li id="footer-places-mobileview"><a href="http://commons.m.wikimedia.org/w/index.php?title=Commons:Wiki_Loves_Africa_2014&amp;mobileaction=toggle_view_mobile" class="noprint stopMobileRedirectToggle">Mobile view</a></li>
</ul>

	<ul id="footer-icons" class="noprint">
	<li id="footer-copyrightico"><a href="https://www.wikimedia.org/" class="cdx-button cdx-button--fake-button cdx-button--size-large cdx-button--fake-button--enabled"><picture><source media="(min-width: 500px)" srcset="/static/images/footer/wikimedia-button.svg" width="84" height="29"><img src="../static/images/footer/wikimedia.svg" width="25" height="25" alt="Wikimedia Foundation" lang="en" loading="lazy"></picture></a></li>
	<li id="footer-poweredbyico"><a href="https://www.mediawiki.org/" class="cdx-button cdx-button--fake-button cdx-button--size-large cdx-button--fake-button--enabled"><picture><source media="(min-width: 500px)" srcset="/w/resources/assets/poweredby_mediawiki.svg" width="88" height="31"><img src="../w/resources/assets/mediawiki_compact.svg" alt="Powered by MediaWiki" lang="en" width="25" height="25" loading="lazy"></picture></a></li>
</ul>

</footer>

		</div>
	</div> 
</div> 
<div class="vector-header-container vector-sticky-header-container no-font-mode-scale">
	<div id="vector-sticky-header" class="vector-sticky-header">
		<div class="vector-sticky-header-start">
			<div class="vector-sticky-header-icon-start vector-button-flush-left vector-button-flush-right" aria-hidden="true">
				<button class="cdx-button cdx-button--weight-quiet cdx-button--icon-only vector-sticky-header-search-toggle" tabindex="-1" data-event-name="ui.vector-sticky-search-form.icon"><span class="vector-icon mw-ui-icon-search mw-ui-icon-wikimedia-search"></span>

<span>Search</span>
			</button>
		</div>
			
		<div role="search" class="vector-search-box-vue vector-search-box">
			<div class="vector-typeahead-search-container">
				<div class="cdx-typeahead-search">
					<form action="https://commons.wikimedia.org/w/index.php" id="vector-sticky-search-form" class="cdx-search-input cdx-search-input--has-end-button">
						<div  class="cdx-search-input__input-wrapper"  data-search-loc="header-moved">
							<div class="cdx-text-input cdx-text-input--has-start-icon">
								<input
									class="cdx-text-input__input mw-searchInput" autocomplete="off"
									
									type="search" name="search" placeholder="Search Wikimedia Commons">
								<span class="cdx-text-input__icon cdx-text-input__start-icon"></span>
							</div>
							<input type="hidden" name="title" value="Special:MediaSearch">
						</div>
						<button class="cdx-button cdx-search-input__end-button">Search</button>
					</form>
				</div>
			</div>
		</div>
		<div class="vector-sticky-header-context-bar">
				<nav aria-label="Contents" class="vector-toc-landmark">
						
					<div id="vector-sticky-header-toc" class="vector-dropdown mw-portlet mw-portlet-sticky-header-toc vector-sticky-header-toc vector-button-flush-left"  >
						<input type="checkbox" id="vector-sticky-header-toc-checkbox" role="button" aria-haspopup="true" data-event-name="ui.dropdown-vector-sticky-header-toc" class="vector-dropdown-checkbox "  aria-label="Toggle the table of contents"  >
						<label id="vector-sticky-header-toc-label" for="vector-sticky-header-toc-checkbox" class="vector-dropdown-label cdx-button cdx-button--fake-button cdx-button--fake-button--enabled cdx-button--weight-quiet cdx-button--icon-only " aria-hidden="true"  ><span class="vector-icon mw-ui-icon-listBullet mw-ui-icon-wikimedia-listBullet"></span>

<span class="vector-dropdown-label-text">Toggle the table of contents</span>
						</label>
						<div class="vector-dropdown-content">
					
						<div id="vector-sticky-header-toc-unpinned-container" class="vector-unpinned-container">
						</div>
					
						</div>
					</div>
			</nav>
				<div class="vector-sticky-header-context-bar-primary" aria-hidden="true" ><span class="mw-page-title-namespace">Commons</span><span class="mw-page-title-separator">:</span><span class="mw-page-title-main">Wiki Loves Africa 2014</span></div>
			</div>
		</div>
		<div class="vector-sticky-header-end" aria-hidden="true">
			<div class="vector-sticky-header-icons">
				<a href="#" class="cdx-button cdx-button--fake-button cdx-button--fake-button--enabled cdx-button--weight-quiet cdx-button--icon-only" id="ca-talk-sticky-header" tabindex="-1" data-event-name="talk-sticky-header"><span class="vector-icon mw-ui-icon-speechBubbles mw-ui-icon-wikimedia-speechBubbles"></span>

<span></span>
			</a>
			<a href="#" class="cdx-button cdx-button--fake-button cdx-button--fake-button--enabled cdx-button--weight-quiet cdx-button--icon-only" id="ca-subject-sticky-header" tabindex="-1" data-event-name="subject-sticky-header"><span class="vector-icon mw-ui-icon-article mw-ui-icon-wikimedia-article"></span>

<span></span>
			</a>
			<a href="#" class="cdx-button cdx-button--fake-button cdx-button--fake-button--enabled cdx-button--weight-quiet cdx-button--icon-only" id="ca-history-sticky-header" tabindex="-1" data-event-name="history-sticky-header"><span class="vector-icon mw-ui-icon-wikimedia-history mw-ui-icon-wikimedia-wikimedia-history"></span>

<span></span>
			</a>
			<a href="#" class="cdx-button cdx-button--fake-button cdx-button--fake-button--enabled cdx-button--weight-quiet cdx-button--icon-only mw-watchlink" id="ca-watchstar-sticky-header" tabindex="-1" data-event-name="watch-sticky-header"><span class="vector-icon mw-ui-icon-wikimedia-star mw-ui-icon-wikimedia-wikimedia-star"></span>

<span></span>
			</a>
			<a href="#" class="cdx-button cdx-button--fake-button cdx-button--fake-button--enabled cdx-button--weight-quiet cdx-button--icon-only" id="ca-edit-sticky-header" tabindex="-1" data-event-name="wikitext-edit-sticky-header"><span class="vector-icon mw-ui-icon-wikimedia-wikiText mw-ui-icon-wikimedia-wikimedia-wikiText"></span>

<span></span>
			</a>
			<a href="#" class="cdx-button cdx-button--fake-button cdx-button--fake-button--enabled cdx-button--weight-quiet cdx-button--icon-only" id="ca-ve-edit-sticky-header" tabindex="-1" data-event-name="ve-edit-sticky-header"><span class="vector-icon mw-ui-icon-wikimedia-edit mw-ui-icon-wikimedia-wikimedia-edit"></span>

<span></span>
			</a>
			<a href="#" class="cdx-button cdx-button--fake-button cdx-button--fake-button--enabled cdx-button--weight-quiet cdx-button--icon-only" id="ca-viewsource-sticky-header" tabindex="-1" data-event-name="ve-edit-protected-sticky-header"><span class="vector-icon mw-ui-icon-wikimedia-editLock mw-ui-icon-wikimedia-wikimedia-editLock"></span>

<span></span>
			</a>
		</div>
			<div class="vector-sticky-header-buttons">
				<a href="#" class="cdx-button cdx-button--fake-button cdx-button--fake-button--enabled cdx-button--weight-quiet cdx-button--action-progressive" id="ca-addsection-sticky-header" tabindex="-1" data-event-name="addsection-sticky-header"><span class="vector-icon mw-ui-icon-speechBubbleAdd-progressive mw-ui-icon-wikimedia-speechBubbleAdd-progressive"></span>

<span>Add topic</span>
			</a>
		</div>
			<div class="vector-sticky-header-icon-end">
				<div class="vector-user-links">
				</div>
			</div>
		</div>
	</div>
</div>
<div class="mw-portlet mw-portlet-dock-bottom emptyPortlet" id="p-dock-bottom">
	<ul>
		
	</ul>
</div>
<script>(RLQ=window.RLQ||[]).push(function(){mw.config.set({"wgHostname":"mw-web.eqiad.main-7b48b5fb74-c9j84","wgBackendResponseTime":130,"wgDiscussionToolsPageThreads":[{"headingLevel":2,"name":"h-","type":"heading","level":0,"id":"h-Links","replies":[]}],"wgPageParseReport":{"discussiontools":{"limitreport-timeusage":"0.020"},"limitreport":{"cputime":"0.218","walltime":"0.401","ppvisitednodes":{"value":1074,"limit":1000000},"revisionsize":{"value":5232,"limit":2097152},"postexpandincludesize":{"value":39729,"limit":2097152},"templateargumentsize":{"value":3608,"limit":2097152},"expansiondepth":{"value":10,"limit":100},"expensivefunctioncount":{"value":19,"limit":500},"unstrip-depth":{"value":0,"limit":20},"unstrip-size":{"value":23913,"limit":5000000},"entityaccesscount":{"value":0,"limit":400},"timingprofile":["100.00%  164.875      1 -total"," 45.25%   74.603      1 Commons:Wiki_Loves_Africa/menu"," 32.60%   53.741     19 Template:Pg"," 20.18%   33.265      1 Template:TNT","  9.85%   16.248      1 Template:Wiki_Loves_Africa_navbox","  6.77%   11.160      1 Template:Navbox","  1.96%    3.233     19 Template:Str_left"]},"scribunto":{"limitreport-timeusage":{"value":"0.036","limit":"10.000"},"limitreport-memusage":{"value":1301374,"limit":52428800}},"cachereport":{"origin":"mw-web.codfw.main-67f757d97d-k7qvn","timestamp":"20250714074821","ttl":2592000,"transientcontent":false}}});});</script>
</body>

<!-- Mirrored from commons.wikimedia.org/wiki/Commons:Wiki_Loves_Africa_2014 by HTTrack Website Copier/3.x [XR&CO'2014], Sun, 03 Aug 2025 08:53:35 GMT -->
</html>