<!DOCTYPE html>
<html class="client-nojs vector-feature-language-in-header-disabled vector-feature-language-in-main-page-header-disabled vector-feature-page-tools-pinned-disabled vector-feature-toc-pinned-clientpref-1 vector-feature-main-menu-pinned-disabled vector-feature-limited-width-clientpref-1 vector-feature-limited-width-content-enabled vector-feature-custom-font-size-clientpref--excluded vector-feature-appearance-pinned-clientpref-1 vector-feature-night-mode-enabled skin-theme-clientpref-day vector-sticky-header-enabled vector-toc-available" lang="en" dir="ltr">

<!-- Mirrored from commons.wikimedia.org/wiki/File:Emoji_u1f35c.svg by HTTrack Website Copier/3.x [XR&CO'2014], Sun, 03 Aug 2025 08:46:23 GMT -->
<!-- Added by HTTrack --><meta http-equiv="content-type" content="text/html;charset=UTF-8" /><!-- /Added by HTTrack -->
<head>
<meta charset="UTF-8">
<title>File:Emoji u1f35c.svg - Wikimedia Commons</title>
<script>(function(){var className="client-js vector-feature-language-in-header-disabled vector-feature-language-in-main-page-header-disabled vector-feature-page-tools-pinned-disabled vector-feature-toc-pinned-clientpref-1 vector-feature-main-menu-pinned-disabled vector-feature-limited-width-clientpref-1 vector-feature-limited-width-content-enabled vector-feature-custom-font-size-clientpref--excluded vector-feature-appearance-pinned-clientpref-1 vector-feature-night-mode-enabled skin-theme-clientpref-day vector-sticky-header-enabled vector-toc-available";var cookie=document.cookie.match(/(?:^|; )commonswikimwclientpreferences=([^;]+)/);if(cookie){cookie[1].split('%2C').forEach(function(pref){className=className.replace(new RegExp('(^| )'+pref.replace(/-clientpref-\w+$|[^\w-]+/g,'')+'-clientpref-\\w+( |$)'),'$1'+pref+'$2');});}document.documentElement.className=className;}());RLCONF={"wgBreakFrames":true,"wgSeparatorTransformTable":["",""],"wgDigitTransformTable":["",""],"wgDefaultDateFormat":"dmy","wgMonthNames":["","January","February","March","April","May","June","July","August","September","October","November","December"],"wgRequestId":"367f2465-1219-4c73-a098-d58c2388a2e2","wgCanonicalNamespace":"File","wgCanonicalSpecialPageName":false,"wgNamespaceNumber":6,"wgPageName":"File:Emoji_u1f35c.svg","wgTitle":"Emoji u1f35c.svg","wgCurRevisionId":**********,"wgRevisionId":**********,"wgArticleId":34839638,"wgIsArticle":true,"wgIsRedirect":false,"wgAction":"view","wgUserName":null,"wgUserGroups":["*"],"wgCategories":["Valid SVG created with Adobe Illustrator: Emoji Noto-Nougat","Apache License","SVG egg icons","Sliced eggs","Steam in art","Noto Color Emoji Nougat","U+1F35C"],"wgPageViewLanguage":"en","wgPageContentLanguage":"en","wgPageContentModel":"wikitext","wgRelevantPageName":"File:Emoji_u1f35c.svg","wgRelevantArticleId":34839638,"wgIsProbablyEditable":true,"wgRelevantPageIsProbablyEditable":true,"wgRestrictionEdit":[],"wgRestrictionMove":[],"wgRestrictionUpload":[],"wgNoticeProject":"commons","wgCiteReferencePreviewsActive":true,"wgMediaViewerOnClick":true,"wgMediaViewerEnabledByDefault":false,"wgVisualEditor":{"pageLanguageCode":"en","pageLanguageDir":"ltr","pageVariantFallbacks":"en"},"wgMFDisplayWikibaseDescriptions":{"search":true,"watchlist":true,"tagline":true,"nearby":true},"wgWMESchemaEditAttemptStepOversample":false,"wgWMEPageLength":700,"wbUserPreferredContentLanguages":["en"],"wbUserSpecifiedLanguages":["en"],"wbCopyright":{"version":"wikibase-1","messageHtml":"By clicking \"publish\", you agree to the \u003Ca href=\"/wiki/Commons:Copyrights\" class=\"mw-redirect\" title=\"Commons:Copyrights\"\u003Eterms of use\u003C/a\u003E, and you irrevocably agree to release your contribution under the \u003Ca rel=\"nofollow\" class=\"external text\" href=\"https://creativecommons.org/publicdomain/zero/1.0/\"\u003ECreative Commons CC0 License\u003C/a\u003E."},"wbBadgeItems":[],"wbMultiLingualStringLimit":250,"wbTaintedReferencesEnabled":false,"wgMetricsPlatformUserExperiments":{"active_experiments":[],"overrides":[],"enrolled":[],"assigned":[],"subject_ids":[],"sampling_units":[]},"wbEntityId":"*********","wgEditSubmitButtonLabelPublish":true,"upwizPropertyTitles":{"P180":"Main subjects visible in this work"},"upwizPropertyPlaceholders":{"P180":"e.g., Angolan giraffe"},"upwizPropertyCopyLabels":{"P180":"Main subjects"},"wgULSPosition":"personal","wgULSisCompactLinksEnabled":true,"wgVector2022LanguageInHeader":false,"wgULSisLanguageSelectorEmpty":false,"wbmiDefaultProperties":["P180"],"wbmiPropertyTitles":{"P180":"Items portrayed in this file"},"wbmiPropertyTypes":{"P180":"wikibase-item","P571":"time","P3575":"quantity","P1163":"string","P4092":"string","P459":"wikibase-item"},"wbmiRepoApiUrl":"/w/api.php","wbmiHelpUrls":{"P180":"https://commons.wikimedia.org/wiki/Special:MyLanguage/Commons:Depicts"},"wbmiExternalEntitySearchBaseUri":"https://www.wikidata.org/w/api.php","wbmiSupportedDataTypes":["wikibase-item","string","quantity","time","monolingualtext","external-id","globe-coordinate","url"],"wbCurrentRevision":**********,"wbEntity":{"type":"mediainfo","id":"*********","labels":[],"descriptions":[],"statements":{"P571":[{"mainsnak":{"snaktype":"value","property":"P571","hash":"13274f4f34cbbb11c76c440faf9c1c3b7a01b13c","datavalue":{"value":{"time":"+2017-05-23T00:00:00Z","timezone":0,"before":0,"after":0,"precision":11,"calendarmodel":"http://www.wikidata.org/entity/Q1985727"},"type":"time"}},"type":"statement","id":"*********$87455C05-45BE-439D-B8D7-E93A41BDEABE","rank":"normal"}],"P3575":[{"mainsnak":{"snaktype":"value","property":"P3575","hash":"6e72f015055360bf9d2b2c4ebc2a28abb733f947","datavalue":{"value":{"amount":"+14349","unit":"http://www.wikidata.org/entity/Q8799"},"type":"quantity"}},"type":"statement","id":"*********$E3DCBF3E-FB95-4C13-BE8C-D205F09482CA","rank":"normal"}],"P1163":[{"mainsnak":{"snaktype":"value","property":"P1163","hash":"636199d1806c7cdbae57b4d4298f38fade12d052","datavalue":{"value":"image/svg+xml","type":"string"}},"type":"statement","id":"*********$FCD2AFDC-D208-4429-ADD6-427693287943","rank":"normal"}],"P4092":[{"mainsnak":{"snaktype":"value","property":"P4092","hash":"26ff034003268a46c32d582493d97e40f26214df","datavalue":{"value":"ceadc1634b02a17d67524b7c59e56acdc7278f27","type":"string"}},"type":"statement","qualifiers":{"P459":[{"snaktype":"value","property":"P459","hash":"75dff03c151b13fbab93742164121c16a6aa0de1","datavalue":{"value":{"entity-type":"item","numeric-id":13414952,"id":"Q13414952"},"type":"wikibase-entityid"}}]},"qualifiers-order":["P459"],"id":"*********$706B59BB-490A-455F-9061-957D23B0AC24","rank":"normal"}]}},"wbmiMinCaptionLength":5,"wbmiMaxCaptionLength":250,"wbmiParsedMessageAnonEditWarning":"\u003Cp\u003EYou are not logged in and your \u003Ca href=\"https://en.wikipedia.org/wiki/IP_address\" class=\"extiw\" title=\"w:IP address\"\u003EIP address\u003C/a\u003E will be publicly visible if you make any edits. \u003Ca href=\"/wiki/Special:UserLogin\" title=\"Special:UserLogin\"\u003ELogging in\u003C/a\u003E or \u003Ca href=\"/wiki/Special:CreateAccount\" title=\"Special:CreateAccount\"\u003Ecreating an account\u003C/a\u003E will conceal your IP address and provide you with many other \u003Ca href=\"https://en.wikipedia.org/wiki/Wikipedia:Why_create_an_account%3F\" class=\"extiw\" title=\"w:Wikipedia:Why create an account?\"\u003Ebenefits\u003C/a\u003E. Please do not save test edits. If you want to experiment, please use the \u003Ca href=\"/wiki/Commons:Sandbox\" title=\"Commons:Sandbox\"\u003ESandbox\u003C/a\u003E.\n\u003C/p\u003E","wbmiProtectionMsg":null,"wbmiShowIPEditingWarning":true,"wgCheckUserClientHintsHeadersJsApi":["brands","architecture","bitness","fullVersionList","mobile","model","platform","platformVersion"]};
RLSTATE={"ext.gadget.Long-Image-Names-in-Categories":"ready","ext.globalCssJs.user.styles":"ready","site.styles":"ready","user.styles":"ready","ext.globalCssJs.user":"ready","user":"ready","user.options":"loading","wikibase.alltargets":"ready","wikibase.desktop":"ready","jquery.wikibase.toolbar.styles":"ready","mediawiki.interface.helpers.styles":"ready","mediawiki.interface.helpers.linker.styles":"ready","mediawiki.action.view.filepage":"ready","skins.vector.search.codex.styles":"ready","skins.vector.styles":"ready","skins.vector.icons":"ready","filepage":"ready","ext.wikimediamessages.styles":"ready","ext.visualEditor.desktopArticleTarget.noscript":"ready","ext.uls.pt":"ready","wikibase.mediainfo.filepage.styles":"ready","wikibase.mediainfo.statements.styles":"ready"};RLPAGEMODULES=["ext.xLab","wikibase.entityPage.entityLoaded","wikibase.ui.entityViewInit","mediawiki.action.view.metadata","site","mediawiki.page.ready","Skins.vector.html","ext.centralNotice.geoIP","ext.centralNotice.startUp","ext.gadget.Slideshow","ext.gadget.ZoomViewer","ext.gadget.CollapsibleTemplates","ext.gadget.Stockphoto","ext.gadget.WatchlistNotice","ext.gadget.AjaxQuickDelete","ext.gadget.WikiMiniAtlas","ext.gadget.LanguageSelect","ext.gadget.PictureOfTheYearEnhancements","ext.urlShortener.toolbar","ext.centralauth.centralautologin","mmv.bootstrap","ext.visualEditor.desktopArticleTarget.init","ext.visualEditor.targetLoader","ext.echo.centralauth","ext.eventLogging","ext.wikimediaEvents","ext.wikimediaEvents.wikibase","ext.navigationTiming","ext.uls.compactlinks","ext.uls.interface","wikibase.mediainfo.filePageDisplay","ext.checkUser.clientHints"];</script>
<script>(RLQ=window.RLQ||[]).push(function(){mw.loader.impl(function(){return["user.options@12s5i",function($,jQuery,require,module){mw.user.tokens.set({"patrolToken":"+\\","watchToken":"+\\","csrfToken":"+\\"});
}];});});</script>
<link rel="stylesheet" href="../w/load02c7.css?lang=en&amp;modules=ext.uls.pt%7Cext.visualEditor.desktopArticleTarget.noscript%7Cext.wikimediamessages.styles%7Cfilepage%7Cjquery.wikibase.toolbar.styles%7Cmediawiki.action.view.filepage%7Cmediawiki.interface.helpers.linker.styles%7Cmediawiki.interface.helpers.styles%7Cskins.vector.icons%2Cstyles%7Cskins.vector.search.codex.styles%7Cwikibase.alltargets%2Cdesktop%7Cwikibase.mediainfo.filepage.styles%7Cwikibase.mediainfo.statements.styles&amp;only=styles&amp;skin=vector-2022">
<script async="" src="../w/load9565.php?lang=en&amp;modules=startup&amp;only=scripts&amp;raw=1&amp;skin=vector-2022"></script>
<meta name="ResourceLoaderDynamicStyles" content="">
<link rel="stylesheet" href="../w/load3e3b.css?lang=en&amp;modules=ext.gadget.Long-Image-Names-in-Categories&amp;only=styles&amp;skin=vector-2022">
<link rel="stylesheet" href="../w/loada24d.css?lang=en&amp;modules=site.styles&amp;only=styles&amp;skin=vector-2022">
<meta name="generator" content="MediaWiki 1.45.0-wmf.12">
<meta name="referrer" content="origin">
<meta name="referrer" content="origin-when-cross-origin">
<meta name="robots" content="max-image-preview:standard">
<meta name="format-detection" content="telephone=no">
<meta property="og:image" content="https://upload.wikimedia.org/wikipedia/commons/thumb/7/77/Emoji_u1f35c.svg/1200px-Emoji_u1f35c.svg.png">
<meta property="og:image:width" content="1200">
<meta property="og:image:height" content="1200">
<meta property="og:image" content="https://upload.wikimedia.org/wikipedia/commons/thumb/7/77/Emoji_u1f35c.svg/800px-Emoji_u1f35c.svg.png">
<meta property="og:image:width" content="800">
<meta property="og:image:height" content="800">
<meta property="og:image" content="https://upload.wikimedia.org/wikipedia/commons/thumb/7/77/Emoji_u1f35c.svg/640px-Emoji_u1f35c.svg.png">
<meta property="og:image:width" content="640">
<meta property="og:image:height" content="640">
<meta name="viewport" content="width=1120">
<meta property="og:title" content="File:Emoji u1f35c.svg - Wikimedia Commons">
<meta property="og:type" content="website">
<link rel="preconnect" href="../../upload.wikimedia.org/index.html">
<link rel="alternate" href="Special_EntityData/*********.json" type="application/json">
<link rel="alternate" href="Special_EntityData/*********.php" type="application/vnd.php.serialized">
<link rel="alternate" href="Special_EntityData/*********.n3" type="text/n3">
<link rel="alternate" href="Special_EntityData/*********.ttl" type="text/turtle">
<link rel="alternate" href="Special_EntityData/*********.nt" type="application/n-triples">
<link rel="alternate" href="Special_EntityData/*********.rdf" type="application/rdf+xml">
<link rel="alternate" href="Special_EntityData/*********.jsonld" type="application/ld+json">
<link rel="alternate" media="only screen and (max-width: 640px)" href="../../commons.m.wikimedia.org/wiki/File_Emoji_u1f35c.html">
<link rel="alternate" type="application/x-wiki" title="Edit" href="../w/indexdf2d.html?title=File:Emoji_u1f35c.svg&amp;action=edit">
<link rel="apple-touch-icon" href="../static/apple-touch/commons.png">
<link rel="icon" href="../static/favicon/commons.ico">
<link rel="search" type="application/opensearchdescription+xml" href="../w/rest.php/v1/search" title="Wikimedia Commons">
<link rel="EditURI" type="application/rsd+xml" href="https://commons.wikimedia.org/w/api.php?action=rsd">
<link rel="canonical" href="File_Emoji_u1f35c.html">
<link rel="license" href="../../creativecommons.org/licenses/by-sa/4.0/index.html">
<link rel="alternate" type="application/atom+xml" title="Wikimedia Commons Atom feed" href="https://commons.wikimedia.org/w/api.php?hidebots=1&amp;hidecategorization=1&amp;hideWikibase=1&amp;translations=filter&amp;urlversion=1&amp;days=7&amp;limit=50&amp;action=feedrecentchanges&amp;feedformat=atom">
<link rel="dns-prefetch" href="../../meta.wikimedia.org/index.html" />
<link rel="dns-prefetch" href="Auth.wikimedia.html">
</head>
<body class="skin--responsive skin-vector skin-vector-search-vue mediawiki ltr sitedir-ltr mw-hide-empty-elt ns-6 ns-subject mw-editable page-File_Emoji_u1f35c_svg rootpage-File_Emoji_u1f35c_svg skin-vector-2022 action-view wb-entitypage wb-mediainfopage wb-mediainfopage-*********"><a class="mw-jump-link" href="#bodyContent">Jump to content</a>
<div class="vector-header-container">
	<header class="vector-header mw-header no-font-mode-scale">
		<div class="vector-header-start">
			<nav class="vector-main-menu-landmark" aria-label="Site">
				
<div id="vector-main-menu-dropdown" class="vector-dropdown vector-main-menu-dropdown vector-button-flush-left vector-button-flush-right"  title="Main menu" >
	<input type="checkbox" id="vector-main-menu-dropdown-checkbox" role="button" aria-haspopup="true" data-event-name="ui.dropdown-vector-main-menu-dropdown" class="vector-dropdown-checkbox "  aria-label="Main menu"  >
	<label id="vector-main-menu-dropdown-label" for="vector-main-menu-dropdown-checkbox" class="vector-dropdown-label cdx-button cdx-button--fake-button cdx-button--fake-button--enabled cdx-button--weight-quiet cdx-button--icon-only " aria-hidden="true"  ><span class="vector-icon mw-ui-icon-menu mw-ui-icon-wikimedia-menu"></span>

<span class="vector-dropdown-label-text">Main menu</span>
	</label>
	<div class="vector-dropdown-content">


				<div id="vector-main-menu-unpinned-container" class="vector-unpinned-container">
		
<div id="vector-main-menu" class="vector-main-menu vector-pinnable-element">
	<div
	class="vector-pinnable-header vector-main-menu-pinnable-header vector-pinnable-header-unpinned"
	data-feature-name="main-menu-pinned"
	data-pinnable-element-id="vector-main-menu"
	data-pinned-container-id="vector-main-menu-pinned-container"
	data-unpinned-container-id="vector-main-menu-unpinned-container"
>
	<div class="vector-pinnable-header-label">Main menu</div>
	<button class="vector-pinnable-header-toggle-button vector-pinnable-header-pin-button" data-event-name="pinnable-header.vector-main-menu.pin">move to sidebar</button>
	<button class="vector-pinnable-header-toggle-button vector-pinnable-header-unpin-button" data-event-name="pinnable-header.vector-main-menu.unpin">hide</button>
</div>

	
<div id="p-navigation" class="vector-menu mw-portlet mw-portlet-navigation"  >
	<div class="vector-menu-heading">
		Navigate
	</div>
	<div class="vector-menu-content">
		
		<ul class="vector-menu-content-list">
			
			<li id="n-mainpage-description" class="mw-list-item"><a href="Main_Page.html" title="Visit the main page [z]" accesskey="z"><span>Main page</span></a></li><li id="n-welcome" class="mw-list-item"><a href="Commons_Welcome.html"><span>Welcome</span></a></li><li id="n-portal" class="mw-list-item"><a href="Commons_Community_portal.html" title="About the project, what you can do, where to find things"><span>Community portal</span></a></li><li id="n-village-pump" class="mw-list-item"><a href="Commons_Village_pump.html"><span>Village pump</span></a></li><li id="n-help" class="mw-list-item"><a href="Special_MyLanguage/Help_Contents.html" title="The place to find out"><span>Help center</span></a></li>
		</ul>
		
	</div>
</div>

	
	
<div id="p-participate" class="vector-menu mw-portlet mw-portlet-participate"  >
	<div class="vector-menu-heading">
		Participate
	</div>
	<div class="vector-menu-content">
		
		<ul class="vector-menu-content-list">
			
			<li id="n-uploadbtn" class="mw-list-item"><a href="Special_UploadWizard.html"><span>Upload file</span></a></li><li id="n-recentchanges" class="mw-list-item"><a href="Special_RecentChanges.html" title="A list of recent changes in the wiki [r]" accesskey="r"><span>Recent changes</span></a></li><li id="n-latestfiles" class="mw-list-item"><a href="Special_NewFiles.html"><span>Latest files</span></a></li><li id="n-randomimage" class="mw-list-item"><a href="File_Corliss_valvegear%2c_Gordon%27s_improved_(New_Catechism_of_the_Steam_Engine%2c_1904).html" title="Load a random file [x]" accesskey="x"><span>Random file</span></a></li><li id="n-contact" class="mw-list-item"><a href="Commons_Contact_us.html"><span>Contact us</span></a></li><li id="n-specialpages" class="mw-list-item"><a href="Special_SpecialPages.html"><span>Special pages</span></a></li>
		</ul>
		
	</div>
</div>

	
<div id="vector-main-menu" class="vector-menu "  >
	<div class="vector-menu-heading">
		
	</div>
	<div class="vector-menu-content">
		
		<ul class="vector-menu-content-list">
			
			
		</ul>
		
	</div>
</div>

</div>

				</div>

	</div>
</div>

		</nav>
			
<a href="Main_Page.html" class="mw-logo">
	<img class="mw-logo-icon" src="../static/images/icons/commonswiki.svg" alt="" aria-hidden="true" height="50" width="50">
	<span class="mw-logo-container skin-invert">
		<img class="mw-logo-wordmark" alt="Wikimedia Commons" src="../static/images/mobile/copyright/commonswiki-wordmark.svg" style="width: 7.1875em; height: 2em;">
	</span>
</a>

		</div>
		<div class="vector-header-end">
			
<div id="p-search" role="search" class="vector-search-box-vue  vector-search-box-collapses vector-search-box">
	<a href="Special_MediaSearch.html" class="cdx-button cdx-button--fake-button cdx-button--fake-button--enabled cdx-button--weight-quiet cdx-button--icon-only search-toggle" title="Search Wikimedia Commons [f]" accesskey="f"><span class="vector-icon mw-ui-icon-search mw-ui-icon-wikimedia-search"></span>

<span>Search</span>
	</a>
	<div class="vector-typeahead-search-container">
		<div class="cdx-typeahead-search">
			<form action="https://commons.wikimedia.org/w/index.php" id="searchform" class="cdx-search-input cdx-search-input--has-end-button">
				<div id="simpleSearch" class="cdx-search-input__input-wrapper"  data-search-loc="header-moved">
					<div class="cdx-text-input cdx-text-input--has-start-icon">
						<input
							class="cdx-text-input__input mw-searchInput" autocomplete="off"
							 type="search" name="search" placeholder="Search Wikimedia Commons" aria-label="Search Wikimedia Commons" autocapitalize="sentences" spellcheck="false" title="Search Wikimedia Commons [f]" accesskey="f" id="searchInput"
							>
						<span class="cdx-text-input__icon cdx-text-input__start-icon"></span>
					</div>
					<input type="hidden" name="title" value="Special:MediaSearch">
				</div>
				<button class="cdx-button cdx-search-input__end-button">Search</button>
			</form>
		</div>
	</div>
</div>

			<nav class="vector-user-links vector-user-links-wide" aria-label="Personal tools">
	<div class="vector-user-links-main">
	
<div id="p-vector-user-menu-preferences" class="vector-menu mw-portlet"  >
	<div class="vector-menu-content">
		
		<ul class="vector-menu-content-list">
			<li id="ca-uls" class="mw-list-item active user-links-collapsible-item"><a data-mw="interface" href="#" class="uls-trigger cdx-button cdx-button--fake-button cdx-button--fake-button--enabled cdx-button--weight-quiet"><span class="vector-icon mw-ui-icon-wikimedia-language mw-ui-icon-wikimedia-wikimedia-language"></span>

<span>English</span></a>
</li>

			
		</ul>
		
	</div>
</div>

	
<div id="p-vector-user-menu-userpage" class="vector-menu mw-portlet emptyPortlet"  >
	<div class="vector-menu-content">
		
		<ul class="vector-menu-content-list">
			
			
		</ul>
		
	</div>
</div>

	<nav class="vector-appearance-landmark" aria-label="Appearance">
		
<div id="vector-appearance-dropdown" class="vector-dropdown "  title="Change the appearance of the page&#039;s font size, width, and color" >
	<input type="checkbox" id="vector-appearance-dropdown-checkbox" role="button" aria-haspopup="true" data-event-name="ui.dropdown-vector-appearance-dropdown" class="vector-dropdown-checkbox "  aria-label="Appearance"  >
	<label id="vector-appearance-dropdown-label" for="vector-appearance-dropdown-checkbox" class="vector-dropdown-label cdx-button cdx-button--fake-button cdx-button--fake-button--enabled cdx-button--weight-quiet cdx-button--icon-only " aria-hidden="true"  ><span class="vector-icon mw-ui-icon-appearance mw-ui-icon-wikimedia-appearance"></span>

<span class="vector-dropdown-label-text">Appearance</span>
	</label>
	<div class="vector-dropdown-content">


			<div id="vector-appearance-unpinned-container" class="vector-unpinned-container">
				
			</div>
		
	</div>
</div>

	</nav>
	
<div id="p-vector-user-menu-notifications" class="vector-menu mw-portlet emptyPortlet"  >
	<div class="vector-menu-content">
		
		<ul class="vector-menu-content-list">
			
			
		</ul>
		
	</div>
</div>

	
<div id="p-vector-user-menu-overflow" class="vector-menu mw-portlet"  >
	<div class="vector-menu-content">
		
		<ul class="vector-menu-content-list">
			<li id="pt-sitesupport-2" class="user-links-collapsible-item mw-list-item user-links-collapsible-item"><a data-mw="interface" href="https://donate.wikimedia.org/?wmf_source=donate&amp;wmf_medium=sidebar&amp;wmf_campaign=commons.wikimedia.org&amp;uselang=en" class=""><span>Donate</span></a>
</li>
<li id="pt-createaccount-2" class="user-links-collapsible-item mw-list-item user-links-collapsible-item"><a data-mw="interface" href="https://commons.wikimedia.org/w/index.php?title=Special:CreateAccount&amp;returnto=File%3AEmoji+u1f35c.svg" title="You are encouraged to create an account and log in; however, it is not mandatory" class=""><span>Create account</span></a>
</li>
<li id="pt-login-2" class="user-links-collapsible-item mw-list-item user-links-collapsible-item"><a data-mw="interface" href="https://commons.wikimedia.org/w/index.php?title=Special:UserLogin&amp;returnto=File%3AEmoji+u1f35c.svg" title="You are encouraged to log in; however, it is not mandatory [o]" accesskey="o" class=""><span>Log in</span></a>
</li>

			
		</ul>
		
	</div>
</div>

	</div>
	
<div id="vector-user-links-dropdown" class="vector-dropdown vector-user-menu vector-button-flush-right vector-user-menu-logged-out"  title="More options" >
	<input type="checkbox" id="vector-user-links-dropdown-checkbox" role="button" aria-haspopup="true" data-event-name="ui.dropdown-vector-user-links-dropdown" class="vector-dropdown-checkbox "  aria-label="Personal tools"  >
	<label id="vector-user-links-dropdown-label" for="vector-user-links-dropdown-checkbox" class="vector-dropdown-label cdx-button cdx-button--fake-button cdx-button--fake-button--enabled cdx-button--weight-quiet cdx-button--icon-only " aria-hidden="true"  ><span class="vector-icon mw-ui-icon-ellipsis mw-ui-icon-wikimedia-ellipsis"></span>

<span class="vector-dropdown-label-text">Personal tools</span>
	</label>
	<div class="vector-dropdown-content">


		
<div id="p-personal" class="vector-menu mw-portlet mw-portlet-personal user-links-collapsible-item"  title="User menu" >
	<div class="vector-menu-content">
		
		<ul class="vector-menu-content-list">
			
			<li id="pt-sitesupport" class="user-links-collapsible-item mw-list-item"><a href="https://donate.wikimedia.org/?wmf_source=donate&amp;wmf_medium=sidebar&amp;wmf_campaign=commons.wikimedia.org&amp;uselang=en"><span>Donate</span></a></li><li id="pt-createaccount" class="user-links-collapsible-item mw-list-item"><a href="https://commons.wikimedia.org/w/index.php?title=Special:CreateAccount&amp;returnto=File%3AEmoji+u1f35c.svg" title="You are encouraged to create an account and log in; however, it is not mandatory"><span class="vector-icon mw-ui-icon-userAdd mw-ui-icon-wikimedia-userAdd"></span> <span>Create account</span></a></li><li id="pt-login" class="user-links-collapsible-item mw-list-item"><a href="https://commons.wikimedia.org/w/index.php?title=Special:UserLogin&amp;returnto=File%3AEmoji+u1f35c.svg" title="You are encouraged to log in; however, it is not mandatory [o]" accesskey="o"><span class="vector-icon mw-ui-icon-logIn mw-ui-icon-wikimedia-logIn"></span> <span>Log in</span></a></li>
		</ul>
		
	</div>
</div>

<div id="p-user-menu-anon-editor" class="vector-menu mw-portlet mw-portlet-user-menu-anon-editor"  >
	<div class="vector-menu-heading">
		Pages for logged out editors <a href="Help_Introduction.html" aria-label="Learn more about editing"><span>learn more</span></a>
	</div>
	<div class="vector-menu-content">
		
		<ul class="vector-menu-content-list">
			
			<li id="pt-anoncontribs" class="mw-list-item"><a href="Special_MyContributions.html" title="A list of edits made from this IP address [y]" accesskey="y"><span>Contributions</span></a></li><li id="pt-anontalk" class="mw-list-item"><a href="Special_MyTalk.html" title="Discussion about edits from this IP address [n]" accesskey="n"><span>Talk</span></a></li>
		</ul>
		
	</div>
</div>

	
	</div>
</div>

</nav>

		</div>
	</header>
</div>
<div class="mw-page-container">
	<div class="mw-page-container-inner">
		<div class="vector-sitenotice-container">
			<div id="siteNotice"><!-- CentralNotice --></div>
		</div>
		<div class="vector-column-start">
			<div class="vector-main-menu-container">
		<div id="mw-navigation">
			<nav id="mw-panel" class="vector-main-menu-landmark" aria-label="Site">
				<div id="vector-main-menu-pinned-container" class="vector-pinned-container">
				
				</div>
		</nav>
		</div>
	</div>
	<div class="vector-sticky-pinned-container">
				<nav id="mw-panel-toc" aria-label="Contents" data-event-name="ui.sidebar-toc" class="mw-table-of-contents-container vector-toc-landmark">
					<div id="vector-toc-pinned-container" class="vector-pinned-container">
					<div id="vector-toc" class="vector-toc vector-pinnable-element">
	<div
	class="vector-pinnable-header vector-toc-pinnable-header vector-pinnable-header-pinned"
	data-feature-name="toc-pinned"
	data-pinnable-element-id="vector-toc"
	
	
>
	<h2 class="vector-pinnable-header-label">Contents</h2>
	<button class="vector-pinnable-header-toggle-button vector-pinnable-header-pin-button" data-event-name="pinnable-header.vector-toc.pin">move to sidebar</button>
	<button class="vector-pinnable-header-toggle-button vector-pinnable-header-unpin-button" data-event-name="pinnable-header.vector-toc.unpin">hide</button>
</div>


	<ul class="vector-toc-contents" id="mw-panel-toc-list">
		<li id="toc-mw-content-text"
			class="vector-toc-list-item vector-toc-level-1">
			<a href="#" class="vector-toc-link">
				<div class="vector-toc-text">Beginning</div>
			</a>
		</li>
		<li id="toc-Summary"
		class="vector-toc-list-item vector-toc-level-1 vector-toc-list-item-expanded">
		<a class="vector-toc-link" href="#Summary">
			<div class="vector-toc-text">
				<span class="vector-toc-numb">1</span>
				<span>Summary</span>
			</div>
		</a>
		
		<ul id="toc-Summary-sublist" class="vector-toc-list">
		</ul>
	</li>
	<li id="toc-Licensing"
		class="vector-toc-list-item vector-toc-level-1 vector-toc-list-item-expanded">
		<a class="vector-toc-link" href="#Licensing">
			<div class="vector-toc-text">
				<span class="vector-toc-numb">2</span>
				<span>Licensing</span>
			</div>
		</a>
		
		<ul id="toc-Licensing-sublist" class="vector-toc-list">
		</ul>
	</li>
</ul>
</div>

					</div>
		</nav>
			</div>
		</div>
		<div class="mw-content-container">
			<main id="content" class="mw-body">
				<header class="mw-body-header vector-page-titlebar no-font-mode-scale">
					<nav aria-label="Contents" class="vector-toc-landmark">
						
<div id="vector-page-titlebar-toc" class="vector-dropdown vector-page-titlebar-toc vector-button-flush-left"  title="Table of Contents" >
	<input type="checkbox" id="vector-page-titlebar-toc-checkbox" role="button" aria-haspopup="true" data-event-name="ui.dropdown-vector-page-titlebar-toc" class="vector-dropdown-checkbox "  aria-label="Toggle the table of contents"  >
	<label id="vector-page-titlebar-toc-label" for="vector-page-titlebar-toc-checkbox" class="vector-dropdown-label cdx-button cdx-button--fake-button cdx-button--fake-button--enabled cdx-button--weight-quiet cdx-button--icon-only " aria-hidden="true"  ><span class="vector-icon mw-ui-icon-listBullet mw-ui-icon-wikimedia-listBullet"></span>

<span class="vector-dropdown-label-text">Toggle the table of contents</span>
	</label>
	<div class="vector-dropdown-content">


							<div id="vector-page-titlebar-toc-unpinned-container" class="vector-unpinned-container">
			</div>
		
	</div>
</div>

					</nav>
					<h1 id="firstHeading" class="firstHeading mw-first-heading"><span class="mw-page-title-namespace">File</span><span class="mw-page-title-separator">:</span><span class="mw-page-title-main">Emoji u1f35c.svg</span></h1>
						<div class="mw-indicators">
		</div>
</header>
				<div class="vector-page-toolbar vector-feature-custom-font-size-clientpref--excluded">
					<div class="vector-page-toolbar-container">
						<div id="left-navigation">
							<nav aria-label="Namespaces">
								
<div id="p-associated-pages" class="vector-menu vector-menu-tabs mw-portlet mw-portlet-associated-pages"  >
	<div class="vector-menu-content">
		
		<ul class="vector-menu-content-list">
			
			<li id="ca-nstab-image" class="selected vector-tab-noicon mw-list-item"><a href="File_Emoji_u1f35c.html" title="View the file page [c]" accesskey="c"><span>File</span></a></li><li id="ca-talk" class="new vector-tab-noicon mw-list-item"><a href="https://commons.wikimedia.org/w/index.php?title=File_talk:Emoji_u1f35c.svg&amp;action=edit&amp;redlink=1" rel="discussion" class="new" title="Discussion about the content page (page does not exist) [t]" accesskey="t"><span>Discussion</span></a></li>
		</ul>
		
	</div>
</div>

								
<div id="vector-variants-dropdown" class="vector-dropdown emptyPortlet"  >
	<input type="checkbox" id="vector-variants-dropdown-checkbox" role="button" aria-haspopup="true" data-event-name="ui.dropdown-vector-variants-dropdown" class="vector-dropdown-checkbox " aria-label="Change language variant"   >
	<label id="vector-variants-dropdown-label" for="vector-variants-dropdown-checkbox" class="vector-dropdown-label cdx-button cdx-button--fake-button cdx-button--fake-button--enabled cdx-button--weight-quiet" aria-hidden="true"  ><span class="vector-dropdown-label-text">English</span>
	</label>
	<div class="vector-dropdown-content">


					
<div id="p-variants" class="vector-menu mw-portlet mw-portlet-variants emptyPortlet"  >
	<div class="vector-menu-content">
		
		<ul class="vector-menu-content-list">
			
			
		</ul>
		
	</div>
</div>

				
	</div>
</div>

							</nav>
						</div>
						<div id="right-navigation" class="vector-collapsible">
							<nav aria-label="Views">
								
<div id="p-views" class="vector-menu vector-menu-tabs mw-portlet mw-portlet-views"  >
	<div class="vector-menu-content">
		
		<ul class="vector-menu-content-list">
			
			<li id="ca-view" class="selected vector-tab-noicon mw-list-item"><a href="File_Emoji_u1f35c.html"><span>Read</span></a></li><li id="ca-edit" class="vector-tab-noicon mw-list-item"><a href="../w/indexdf2d.html?title=File:Emoji_u1f35c.svg&amp;action=edit" title="Edit this page [e]" accesskey="e"><span>Edit</span></a></li><li id="ca-history" class="vector-tab-noicon mw-list-item"><a href="https://commons.wikimedia.org/w/index.php?title=File:Emoji_u1f35c.svg&amp;action=history" title="Past revisions of this page [h]" accesskey="h"><span>View history</span></a></li>
		</ul>
		
	</div>
</div>

							</nav>
				
							<nav class="vector-page-tools-landmark" aria-label="Page tools">
								
<div id="vector-page-tools-dropdown" class="vector-dropdown vector-page-tools-dropdown"  >
	<input type="checkbox" id="vector-page-tools-dropdown-checkbox" role="button" aria-haspopup="true" data-event-name="ui.dropdown-vector-page-tools-dropdown" class="vector-dropdown-checkbox "  aria-label="Tools"  >
	<label id="vector-page-tools-dropdown-label" for="vector-page-tools-dropdown-checkbox" class="vector-dropdown-label cdx-button cdx-button--fake-button cdx-button--fake-button--enabled cdx-button--weight-quiet" aria-hidden="true"  ><span class="vector-dropdown-label-text">Tools</span>
	</label>
	<div class="vector-dropdown-content">


									<div id="vector-page-tools-unpinned-container" class="vector-unpinned-container">
						
<div id="vector-page-tools" class="vector-page-tools vector-pinnable-element">
	<div
	class="vector-pinnable-header vector-page-tools-pinnable-header vector-pinnable-header-unpinned"
	data-feature-name="page-tools-pinned"
	data-pinnable-element-id="vector-page-tools"
	data-pinned-container-id="vector-page-tools-pinned-container"
	data-unpinned-container-id="vector-page-tools-unpinned-container"
>
	<div class="vector-pinnable-header-label">Tools</div>
	<button class="vector-pinnable-header-toggle-button vector-pinnable-header-pin-button" data-event-name="pinnable-header.vector-page-tools.pin">move to sidebar</button>
	<button class="vector-pinnable-header-toggle-button vector-pinnable-header-unpin-button" data-event-name="pinnable-header.vector-page-tools.unpin">hide</button>
</div>

	
<div id="p-cactions" class="vector-menu mw-portlet mw-portlet-cactions emptyPortlet vector-has-collapsible-items"  title="More options" >
	<div class="vector-menu-heading">
		Actions
	</div>
	<div class="vector-menu-content">
		
		<ul class="vector-menu-content-list">
			
			<li id="ca-more-view" class="selected vector-more-collapsible-item mw-list-item"><a href="File_Emoji_u1f35c.html"><span>Read</span></a></li><li id="ca-more-edit" class="vector-more-collapsible-item mw-list-item"><a href="../w/indexdf2d.html?title=File:Emoji_u1f35c.svg&amp;action=edit" title="Edit this page [e]" accesskey="e"><span>Edit</span></a></li><li id="ca-more-history" class="vector-more-collapsible-item mw-list-item"><a href="https://commons.wikimedia.org/w/index.php?title=File:Emoji_u1f35c.svg&amp;action=history"><span>View history</span></a></li>
		</ul>
		
	</div>
</div>

<div id="p-tb" class="vector-menu mw-portlet mw-portlet-tb"  >
	<div class="vector-menu-heading">
		General
	</div>
	<div class="vector-menu-content">
		
		<ul class="vector-menu-content-list">
			
			<li id="t-whatlinkshere" class="mw-list-item"><a href="Special_WhatLinksHere/File_Emoji_u1f35c.html" title="A list of all wiki pages that link here [j]" accesskey="j"><span>What links here</span></a></li><li id="t-recentchangeslinked" class="mw-list-item"><a href="Special_RecentChangesLinked/File_Emoji_u1f35c.html" rel="nofollow" title="Recent changes in pages linked from this page [k]" accesskey="k"><span>Related changes</span></a></li><li id="t-permalink" class="mw-list-item"><a href="https://commons.wikimedia.org/w/index.php?title=File:Emoji_u1f35c.svg&amp;oldid=**********" title="Permanent link to this revision of this page"><span>Permanent link</span></a></li><li id="t-info" class="mw-list-item"><a href="https://commons.wikimedia.org/w/index.php?title=File:Emoji_u1f35c.svg&amp;action=info" title="More information about this page"><span>Page information</span></a></li><li id="t-cite" class="mw-list-item"><a href="https://commons.wikimedia.org/w/index.php?title=Special:CiteThisPage&amp;page=File%3AEmoji_u1f35c.svg&amp;id=**********&amp;wpFormIdentifier=titleform" title="Information on how to cite this page"><span>Cite this page</span></a></li><li id="t-urlshortener" class="mw-list-item"><a href="https://commons.wikimedia.org/w/index.php?title=Special:UrlShortener&amp;url=https%3A%2F%2Fcommons.wikimedia.org%2Fwiki%2FFile%3AEmoji_u1f35c.svg"><span>Get shortened URL</span></a></li><li id="t-urlshortener-qrcode" class="mw-list-item"><a href="https://commons.wikimedia.org/w/index.php?title=Special:QrCode&amp;url=https%3A%2F%2Fcommons.wikimedia.org%2Fwiki%2FFile%3AEmoji_u1f35c.svg"><span>Download QR code</span></a></li><li id="t-wb-concept-uri" class="mw-list-item"><a href="https://commons.wikimedia.org/entity/*********" title="URI that identifies the concept described by this Item"><span>Concept URI</span></a></li>
		</ul>
		
	</div>
</div>

<div id="p-electronpdfservice-sidebar-portlet-heading" class="vector-menu mw-portlet mw-portlet-electronpdfservice-sidebar-portlet-heading"  >
	<div class="vector-menu-heading">
		Print/export
	</div>
	<div class="vector-menu-content">
		
		<ul class="vector-menu-content-list">
			
			<li id="electron-print_pdf" class="mw-list-item"><a href="https://commons.wikimedia.org/w/index.php?title=Special:DownloadAsPdf&amp;page=File%3AEmoji_u1f35c.svg&amp;action=show-download-screen"><span>Download as PDF</span></a></li><li id="t-print" class="mw-list-item"><a href="javascript:print();" rel="alternate" title="Printable version of this page [p]" accesskey="p"><span>Printable version</span></a></li>
		</ul>
		
	</div>
</div>

<div id="p-wikibase-otherprojects" class="vector-menu mw-portlet mw-portlet-wikibase-otherprojects emptyPortlet"  >
	<div class="vector-menu-heading">
		In other projects
	</div>
	<div class="vector-menu-content">
		
		<ul class="vector-menu-content-list">
			
			
		</ul>
		
	</div>
</div>

</div>

									</div>
				
	</div>
</div>

							</nav>
						</div>
					</div>
				</div>
				<div class="vector-column-end no-font-mode-scale">
					<div class="vector-sticky-pinned-container">
						<nav class="vector-page-tools-landmark" aria-label="Page tools">
							<div id="vector-page-tools-pinned-container" class="vector-pinned-container">
				
							</div>
		</nav>
						<nav class="vector-appearance-landmark" aria-label="Appearance">
							<div id="vector-appearance-pinned-container" class="vector-pinned-container">
				<div id="vector-appearance" class="vector-appearance vector-pinnable-element">
	<div
	class="vector-pinnable-header vector-appearance-pinnable-header vector-pinnable-header-pinned"
	data-feature-name="appearance-pinned"
	data-pinnable-element-id="vector-appearance"
	data-pinned-container-id="vector-appearance-pinned-container"
	data-unpinned-container-id="vector-appearance-unpinned-container"
>
	<div class="vector-pinnable-header-label">Appearance</div>
	<button class="vector-pinnable-header-toggle-button vector-pinnable-header-pin-button" data-event-name="pinnable-header.vector-appearance.pin">move to sidebar</button>
	<button class="vector-pinnable-header-toggle-button vector-pinnable-header-unpin-button" data-event-name="pinnable-header.vector-appearance.unpin">hide</button>
</div>


</div>

							</div>
		</nav>
					</div>
				</div>
				<div id="bodyContent" class="vector-body" aria-labelledby="firstHeading" data-mw-ve-target-container>
					<div class="vector-body-before-content">
					
						<div id="siteSub" class="noprint">From Wikimedia Commons, the free media repository</div>
					</div>
					<div id="contentSub"><div id="mw-content-subtitle"></div></div>
					
					
					<div id="mw-content-text" class="mw-body-content"><ul id="filetoc" role="navigation"><li><a href="#file">File</a></li>
<li><a href="#filehistory">File history</a></li>
<li><a href="#filelinks">File usage on Commons</a></li>
<li><a href="#globalusage">File usage on other wikis</a></li>
<li><a href="#metadata">Metadata</a></li></ul><div class="fullImageLink" id="file"><a href="../../upload.wikimedia.org/wikipedia/commons/7/77/Emoji_u1f35c.svg"><img alt="File:Emoji u1f35c.svg" src="../../upload.wikimedia.org/wikipedia/commons/thumb/7/77/Emoji_u1f35c.svg/128px-Emoji_u1f35c.svg3a73.png?20170527173154" decoding="async" width="128" height="128" srcset="https://upload.wikimedia.org/wikipedia/commons/thumb/7/77/Emoji_u1f35c.svg/192px-Emoji_u1f35c.svg.png?20170527173154 1.5x, https://upload.wikimedia.org/wikipedia/commons/thumb/7/77/Emoji_u1f35c.svg/256px-Emoji_u1f35c.svg.png?20170527173154 2x" data-file-width="128" data-file-height="128"></a><div class="mw-filepage-resolutioninfo">Size of this PNG preview of this SVG file: <a href="../../upload.wikimedia.org/wikipedia/commons/thumb/7/77/Emoji_u1f35c.svg/128px-Emoji_u1f35c.svg.png" class="mw-thumbnail-link">128 × 128 pixels</a>. <span class="mw-filepage-other-resolutions">Other resolutions: <a href="../../upload.wikimedia.org/wikipedia/commons/thumb/7/77/Emoji_u1f35c.svg/240px-Emoji_u1f35c.svg.png" class="mw-thumbnail-link">240 × 240 pixels</a> | <a href="../../upload.wikimedia.org/wikipedia/commons/thumb/7/77/Emoji_u1f35c.svg/480px-Emoji_u1f35c.svg.png" class="mw-thumbnail-link">480 × 480 pixels</a> | <a href="../../upload.wikimedia.org/wikipedia/commons/thumb/7/77/Emoji_u1f35c.svg/768px-Emoji_u1f35c.svg.png" class="mw-thumbnail-link">768 × 768 pixels</a> | <a href="../../upload.wikimedia.org/wikipedia/commons/thumb/7/77/Emoji_u1f35c.svg/1024px-Emoji_u1f35c.svg.png" class="mw-thumbnail-link">1,024 × 1,024 pixels</a> | <a href="../../upload.wikimedia.org/wikipedia/commons/thumb/7/77/Emoji_u1f35c.svg/2048px-Emoji_u1f35c.svg.png" class="mw-thumbnail-link">2,048 × 2,048 pixels</a>.</span></div></div>
<div class="fullMedia"><bdi dir="ltr"><a href="../../upload.wikimedia.org/wikipedia/commons/7/77/Emoji_u1f35c.svg" class="internal" title="Emoji u1f35c.svg">Original file</a></bdi> <span class="fileInfo">(SVG file, nominally 128 × 128 pixels, file size: 14 KB)</span></div><div class='wbmi-tabs-container oo-ui-layout oo-ui-panelLayout'><div id='ooui-php-1' class='wbmi-tabs oo-ui-layout oo-ui-menuLayout oo-ui-menuLayout-static oo-ui-menuLayout-top oo-ui-menuLayout-showMenu oo-ui-indexLayout' data-ooui='{"_":"OO.ui.IndexLayout","classes":["wbmi-tabs"],"expanded":false,"menuPanel":{"tag":"ooui-php-2"},"contentPanel":{"tag":"ooui-php-3"},"autoFocus":false,"tabPanels":{"wikiTextPlusCaptions":{"tag":"ooui-php-4"},"statements":{"tag":"ooui-php-5"}},"tabSelectWidget":{"tag":"ooui-php-6"}}'><div aria-hidden='false' class='oo-ui-menuLayout-menu'><div id='ooui-php-2' class='oo-ui-layout oo-ui-panelLayout oo-ui-indexLayout-tabPanel' data-ooui='{"_":"OO.ui.PanelLayout","preserveContent":false,"expanded":false}'><div role='tablist' aria-multiselectable='false' tabindex='0' id='ooui-php-6' class='oo-ui-selectWidget oo-ui-selectWidget-unpressed oo-ui-widget oo-ui-widget-enabled oo-ui-tabSelectWidget oo-ui-tabSelectWidget-frameless' data-ooui='{"_":"OO.ui.TabSelectWidget","framed":false,"items":[{"tag":"ooui-php-7"},{"tag":"ooui-php-8"}]}'><div aria-selected='true' role='tab' id='ooui-php-7' class='oo-ui-widget oo-ui-widget-enabled oo-ui-labelElement oo-ui-optionWidget oo-ui-tabOptionWidget oo-ui-optionWidget-selected' data-ooui='{"_":"OO.ui.TabOptionWidget","selected":true,"label":"File information","data":"wikiTextPlusCaptions"}'><span class='oo-ui-labelElement-label'>File information</span></div><div aria-selected='false' role='tab' id='ooui-php-8' class='oo-ui-widget oo-ui-widget-enabled oo-ui-labelElement oo-ui-optionWidget oo-ui-tabOptionWidget' data-ooui='{"_":"OO.ui.TabOptionWidget","label":"Structured data","data":"statements"}'><span class='oo-ui-labelElement-label'>Structured data</span></div></div></div></div><div class='oo-ui-menuLayout-content'><div id='ooui-php-3' class='oo-ui-layout oo-ui-panelLayout oo-ui-stackLayout oo-ui-indexLayout-stackLayout' data-ooui='{"_":"OO.ui.StackLayout","preserveContent":false,"expanded":false,"items":[{"tag":"ooui-php-4"},{"tag":"ooui-php-5"}]}'><div role='tabpanel' id='ooui-php-4' class='wbmi-tab oo-ui-layout oo-ui-panelLayout oo-ui-panelLayout-scrollable oo-ui-tabPanelLayout oo-ui-tabPanelLayout-active' data-ooui='{"_":"OO.ui.TabPanelLayout","name":"wikiTextPlusCaptions","label":"File information","scrollable":true,"expanded":false,"classes":["wbmi-tab"]}'><h2 class="wbmi-captions-header">Captions</h2><div class="wbmi-entityview-captionsPanel oo-ui-layout oo-ui-panelLayout oo-ui-panelLayout-framed"><h3 class="wbmi-entityview-captions-header">Captions</h3><div class="wbmi-entityview-caption oo-ui-layout oo-ui-horizontalLayout"><label class="wbmi-language-label oo-ui-widget oo-ui-widget-enabled oo-ui-labelElement-label oo-ui-labelElement oo-ui-labelWidget">English</label><div lang="en" dir="ltr" class="wbmi-caption-value wbmi-entityview-emptyCaption">Add a one-line explanation of what this file represents</div></div></div><div id="mw-imagepage-content"><div class="mw-content-ltr mw-parser-output" lang="en" dir="ltr"><div class="mw-heading mw-heading2"><h2 id="Summary">Summary</h2><span class="mw-editsection"><span class="mw-editsection-bracket">[</span><a href="https://commons.wikimedia.org/w/index.php?title=File:Emoji_u1f35c.svg&amp;action=edit&amp;section=1" title="Edit section: Summary"><span>edit</span></a><span class="mw-editsection-bracket">]</span></span></div>
<div class="hproduct commons-file-information-table">
<table class="fileinfotpl-type-information vevent" dir="ltr">

<tbody><tr>
<td id="fileinfotpl_desc" class="fileinfo-paramfield" lang="en">Description<span class="summary fn" style="display:none">Emoji u1f35c.svg</span></td>
<td class="description">
<div class="description en" dir="ltr" lang="en"><span class="language en" title="English"><b>English: </b></span> A colored <a href="https://en.wikipedia.org/wiki/Emoji" class="extiw" title="en:Emoji">Emoji</a> from <a rel="nofollow" class="external text" href="https://github.com/googlei18n/noto-emoji/">Noto project</a>, released under <a rel="nofollow" class="external text" href="https://github.com/googlei18n/noto-emoji/blob/master/LICENSE">Apache license</a>
<p><b>Unicode name:</b> Steaming bowl
</p>
<b>Annotations:</b> Bowl, Food, Noodle, Ramen, Restaurant, Steaming</div></td>
</tr>

<tr>
<td id="fileinfotpl_date" class="fileinfo-paramfield" lang="en">Date</td>
<td lang="en">
<time class="dtstart" datetime="2017-05-23" lang="en" dir="ltr" style="white-space:nowrap">23 May 2017</time></td>
</tr>

<tr>
<td id="fileinfotpl_src" class="fileinfo-paramfield" lang="en">Source</td>
<td>
<a rel="nofollow" class="external free" href="../../github.com/googlefonts/noto-emoji/blob/f2a4f72/svg/emoji_u1f35c.html">https://github.com/googlei18n/noto-emoji/blob/f2a4f72/svg/emoji_u1f35c.svg</a></td>
</tr>

<tr>
<td id="fileinfotpl_aut" class="fileinfo-paramfield" lang="en">Author</td>
<td>
Google</td>
</tr>

<tr style="vertical-align: top"><td style="" class="fileinfo-paramfield">SVG&nbsp;development<div style="display: none;">InfoField</div></td><td style=""><div style="vertical-align:middle"><div style="display:table;width:auto;vertical-align:middle;direction:ltr;float:left;line-height:22px;height:24px;font-size:.96em;margin:0px;padding:2px 4px 2px 6px;color:var(--color-base,#000);background:var(--background-color-neutral-subtle,#F8F9FA);border:1px solid #BAB;border-collapse:separate;border-spacing:0px;padding:2px 4px 2px 0px;"><span typeof="mw:File"><a href="File_W3C_grn.html" class="mw-file-description"><img src="../../upload.wikimedia.org/wikipedia/commons/thumb/3/3e/W3C_grn.svg/40px-W3C_grn.svg.png" decoding="async" width="24" height="15" class="mw-file-element" srcset="https://upload.wikimedia.org/wikipedia/commons/thumb/3/3e/W3C_grn.svg/60px-W3C_grn.svg.png 2x" data-file-width="288" data-file-height="174"></a></span>&nbsp;<div lang="en" dir="ltr" class="description en" style="display:inline;">The <a href="Help_SVG.html" title="Help:SVG">SVG</a> code is <span class="plainlinks" style="background:var(--background-color-success-subtle, #dff2eb)"><a rel="nofollow" class="external text" href="https://validator.w3.org/check?uri=https%3A%2F%2Fcommons.wikimedia.org%2Fwiki%2FSpecial%3AFilepath%2FEmoji_u1f35c.svg&amp;doctype=Inline">valid</a></span>.</div></div><div style="display:table;width:;vertical-align:middle;direction:ltr;float:left;line-height:22px;height:24px;font-size:.96em;margin:0px;padding:2px 4px 2px 6px;color:var(--color-base,#000);background:var(--background-color-neutral-subtle,#F8F9FA);border:1px solid #BAB;border-collapse:separate;border-spacing:0px;;float:left;vertical-align:middle"><span typeof="mw:File"><a href="File_Adobe-yes.html" class="mw-file-description"><img src="../../upload.wikimedia.org/wikipedia/commons/thumb/0/04/Adobe-yes.svg/40px-Adobe-yes.svg.png" decoding="async" width="23" height="22" class="mw-file-element" srcset="https://upload.wikimedia.org/wikipedia/commons/thumb/0/04/Adobe-yes.svg/60px-Adobe-yes.svg.png 2x" data-file-width="342" data-file-height="322"></a></span>&nbsp;<div lang="en" dir="ltr" class="description en" style="display:inline;">This  emoji was created with <a href="https://en.wikipedia.org/wiki/Adobe_Illustrator" class="extiw" title="w:Adobe Illustrator">Adobe Illustrator</a>.</div></div></div></td>
</tr>
</tbody></table>
</div>
<p><br>
</p>
<div class="mw-heading mw-heading2"><h2 id="Licensing">Licensing</h2><span class="mw-editsection"><span class="mw-editsection-bracket">[</span><a href="https://commons.wikimedia.org/w/index.php?title=File:Emoji_u1f35c.svg&amp;action=edit&amp;section=2" title="Edit section: Licensing"><span>edit</span></a><span class="mw-editsection-bracket">]</span></span></div>
<table cellspacing="8" cellpadding="0" style="width:100%; clear:both; margin:0.5em auto; background-color: var(--background-color-neutral-subtle, #f9f9f9); color: var(--color-base, #202122); border: var(--border-subtle, 2px solid #e0e0e0);" lang="en" class="layouttemplate licensetpl" dir="ltr">

<tbody><tr>
<td><div class="wpImageAnnotatorControl wpImageAnnotatorOff"><span typeof="mw:File"><a href="File_Apache_Feather_Logo.html" class="mw-file-description"><img src="../../upload.wikimedia.org/wikipedia/commons/thumb/7/7e/Apache_Feather_Logo.svg/120px-Apache_Feather_Logo.svg.png" decoding="async" width="64" height="113" class="mw-file-element" srcset="https://upload.wikimedia.org/wikipedia/commons/thumb/7/7e/Apache_Feather_Logo.svg/250px-Apache_Feather_Logo.svg.png 2x" data-file-width="512" data-file-height="905"></a></span></div>
</td>
<td style="width:100%;">Copyright ©  Google
<p>Licensed under the <span lang="en" dir="ltr"><a href="https://en.wikipedia.org/wiki/Apache_License" class="extiw" title="en:Apache License"><span lang="en" dir="ltr">Apache License</span></a></span>, Version 2.0 (the "License"); you may not use this file except in compliance with the License. You may obtain a copy of the License at <a rel="nofollow" class="external free" href="https://www.apache.org/licenses/LICENSE-2.0">https://www.apache.org/licenses/LICENSE-2.0</a>. Unless required by applicable law or agreed to in writing, software distributed under the License is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the License for the specific language governing permissions and limitations under the License.
</p>
</td></tr>
<tr style="display: none;">
<td colspan="2"><span class="licensetpl_long">Apache License, Version 2.0</span><span class="licensetpl_short">Apache License 2.0</span><span class="licensetpl_link">http://www.apache.org/licenses/LICENSE-2.0</span><span class="licensetpl_attr_req">true</span><span class="licensetpl_link_req">true</span>
</td></tr></tbody></table><h1 class="mw-slot-header"><mediainfoslotheader></mediainfoslotheader></h1>
<!-- 
NewPP limit report
Parsed by mw‐web.eqiad.main‐694955c78b‐b52c6
Cached time: 20250731015003
Cache expiry: 2592000
Reduced expiry: false
Complications: []
CPU time usage: 0.147 seconds
Real time usage: 0.199 seconds
Preprocessor visited node count: 4700/1000000
Revision size: 717/2097152 bytes
Post‐expand include size: 36787/2097152 bytes
Template argument size: 5764/2097152 bytes
Highest expansion depth: 33/100
Expensive parser function count: 1/500
Unstrip recursion depth: 0/20
Unstrip post‐expand size: 42/5000000 bytes
Lua time usage: 0.057/10.000 seconds
Lua memory usage: 1539090/52428800 bytes
Number of Wikibase entities loaded: 1/400
-->
<!--
Transclusion expansion time report (%,ms,calls,template)
100.00%  184.553      1 -total
 83.11%  153.380      1 Template:Information
 58.95%  108.803      1 Template:Igen
 51.29%   94.655      1 Template:Information_field
 27.22%   50.232      1 Template:Created_with_Adobe_Illustrator
 16.78%   30.970      1 Template:Apache
 14.59%   26.920      1 Template:SVGvalid
 13.70%   25.286      1 Template:Ifsvg
 13.50%   24.921      2 Template:Created_with/layout
 13.44%   24.795      1 Template:Apache/layout
-->

<!-- Saved in parser cache with key commonswiki:pcache:34839638:|#|:idhash:wb=3!wbMobile=0 and timestamp 20250731015003 and revision id **********. Rendering was triggered because: page-view
 -->
</div></div><h2 id="filehistory">File history</h2>
<div id="mw-imagepage-section-filehistory">
<p>Click on a date/time to view the file as it appeared at that time.
</p>
<table class="wikitable filehistory">
<tr><th></th><th>Date/Time</th><th>Thumbnail</th><th>Dimensions</th><th>User</th><th>Comment</th></tr>
<tr><td>current</td><td class="filehistory-selected" style="white-space: nowrap;"><a href="../../upload.wikimedia.org/wikipedia/commons/7/77/Emoji_u1f35c.svg">17:31, 27 May 2017</a></td><td><a href="../../upload.wikimedia.org/wikipedia/commons/7/77/Emoji_u1f35c.svg"><img alt="Thumbnail for version as of 17:31, 27 May 2017" src="../../upload.wikimedia.org/wikipedia/commons/thumb/7/77/Emoji_u1f35c.svg/120px-Emoji_u1f35c.svg3a73.png?20170527173154" decoding="async" loading="lazy" width="120" height="120" data-file-width="128" data-file-height="128"></a></td><td>128 × 128 <span style="white-space: nowrap;">(14 KB)</span></td><td><a href="User_Ebrahim.html" class="mw-userlink" title="User:Ebrahim"><bdi>Ebrahim</bdi></a><span style="white-space: nowrap;"> <span class="mw-usertoollinks">(<a href="User_talk_Ebrahim.html" class="mw-usertoollinks-talk" title="User talk:Ebrahim">talk</a> | <a href="Special_Contributions/Ebrahim.html" class="mw-usertoollinks-contribs" title="Special:Contributions/Ebrahim">contribs</a>)</span></span></td><td dir="ltr">update</td></tr>
<tr><td></td><td style="white-space: nowrap;"><a href="../../upload.wikimedia.org/wikipedia/commons/archive/7/77/20170527173152%21Emoji_u1f35c.svg">19:40, 19 May 2017</a></td><td><a href="../../upload.wikimedia.org/wikipedia/commons/archive/7/77/20170527173152%21Emoji_u1f35c.svg"><img alt="Thumbnail for version as of 19:40, 19 May 2017" src="../../upload.wikimedia.org/wikipedia/commons/thumb/archive/7/77/20170527173152%21Emoji_u1f35c.svg/120px-Emoji_u1f35c.svg.png" decoding="async" loading="lazy" width="120" height="120" data-file-width="512" data-file-height="512"></a></td><td>512 × 512 <span style="white-space: nowrap;">(15 KB)</span></td><td><a href="User_Ebrahim.html" class="mw-userlink" title="User:Ebrahim"><bdi>Ebrahim</bdi></a><span style="white-space: nowrap;"> <span class="mw-usertoollinks">(<a href="User_talk_Ebrahim.html" class="mw-usertoollinks-talk" title="User talk:Ebrahim">talk</a> | <a href="Special_Contributions/Ebrahim.html" class="mw-usertoollinks-contribs" title="Special:Contributions/Ebrahim">contribs</a>)</span></span></td><td dir="ltr">update</td></tr>
<tr><td></td><td style="white-space: nowrap;"><a href="../../upload.wikimedia.org/wikipedia/commons/archive/7/77/20170519194002%21Emoji_u1f35c.svg">00:03, 20 August 2014</a></td><td><a href="../../upload.wikimedia.org/wikipedia/commons/archive/7/77/20170519194002%21Emoji_u1f35c.svg"><img alt="Thumbnail for version as of 00:03, 20 August 2014" src="../../upload.wikimedia.org/wikipedia/commons/thumb/archive/7/77/20170519194002%21Emoji_u1f35c.svg/120px-Emoji_u1f35c.svg.png" decoding="async" loading="lazy" width="120" height="120" data-file-width="128" data-file-height="128"></a></td><td>128 × 128 <span style="white-space: nowrap;">(15 KB)</span></td><td><a href="User_Ebrahim.html" class="mw-userlink" title="User:Ebrahim"><bdi>Ebrahim</bdi></a><span style="white-space: nowrap;"> <span class="mw-usertoollinks">(<a href="User_talk_Ebrahim.html" class="mw-usertoollinks-talk" title="User talk:Ebrahim">talk</a> | <a href="Special_Contributions/Ebrahim.html" class="mw-usertoollinks-contribs" title="Special:Contributions/Ebrahim">contribs</a>)</span></span></td><td dir="ltr">VicuñaUploader 1.20</td></tr>
</table>

</div>
<div class="mw-imagepage-upload-links"><p id="mw-imagepage-upload-disallowed">You cannot overwrite this file.</p></div><h2 id="filelinks">File usage on Commons</h2>
<div id='mw-imagepage-section-linkstoimage'>
<p>More than 100 pages use this file.
The following list shows the first 100 pages that use this file only.
A <a href="Special_WhatLinksHere/File_Emoji_u1f35c.html" title="Special:WhatLinksHere/File:Emoji u1f35c.svg">full list</a> is available.
</p><ul class="mw-imagepage-linkstoimage">
<li class="mw-imagepage-linkstoimage-ns6"><a href="File_A_Cheetah_in_Tanzania.html" title="File:A Cheetah in Tanzania.jpg">File:A Cheetah in Tanzania.jpg</a></li>
<li class="mw-imagepage-linkstoimage-ns6"><a href="File_A_lady_grinding_pepper.html" title="File:A lady grinding pepper.JPG">File:A lady grinding pepper.JPG</a></li>
<li class="mw-imagepage-linkstoimage-ns6"><a href="File_Africa_20060625_0262.html" title="File:Africa 20060625 0262.JPG">File:Africa 20060625 0262.JPG</a></li>
<li class="mw-imagepage-linkstoimage-ns6"><a href="File_Algerian_Couscous_from_Biskra.html" title="File:Algerian Couscous from Biskra.jpg">File:Algerian Couscous from Biskra.jpg</a></li>
<li class="mw-imagepage-linkstoimage-ns6"><a href="File_Algerian_Cousous_from_Biskra.html" title="File:Algerian Cousous from Biskra.jpg">File:Algerian Cousous from Biskra.jpg</a></li>
<li class="mw-imagepage-linkstoimage-ns6"><a href="File_Baby_elephants_playing.html" title="File:Baby elephants playing.JPG">File:Baby elephants playing.JPG</a></li>
<li class="mw-imagepage-linkstoimage-ns6"><a href="File_Boiled_Yam_and_Vegetable_Sauce.html" title="File:Boiled Yam and Vegetable Sauce.jpg">File:Boiled Yam and Vegetable Sauce.jpg</a></li>
<li class="mw-imagepage-linkstoimage-ns6"><a href="File_Boiled_eggs......html" title="File:Boiled eggs......JPG">File:Boiled eggs......JPG</a></li>
<li class="mw-imagepage-linkstoimage-ns6"><a href="File_Boneless_Grilled_Chicken_and_Chips.html" title="File:Boneless Grilled Chicken and Chips.jpg">File:Boneless Grilled Chicken and Chips.jpg</a></li>
<li class="mw-imagepage-linkstoimage-ns6"><a href="File_Breakfast_3.html" title="File:Breakfast 3.jpg">File:Breakfast 3.jpg</a></li>
<li class="mw-imagepage-linkstoimage-ns6"><a href="File_Chips_served_with_Deep_fried_chicken_and_grilled_Shrimps.html" title="File:Chips served with Deep fried chicken and grilled Shrimps.jpg">File:Chips served with Deep fried chicken and grilled Shrimps.jpg</a></li>
<li class="mw-imagepage-linkstoimage-ns6"><a href="File_Coffee_Cravings.html" title="File:Coffee Cravings.jpg">File:Coffee Cravings.jpg</a></li>
<li class="mw-imagepage-linkstoimage-ns6"><a href="File_Couscous_Fez.html" title="File:Couscous Fez.JPG">File:Couscous Fez.JPG</a></li>
<li class="mw-imagepage-linkstoimage-ns6"><a href="File_Cuisine_Ivoirienne_Foutou_sauce_graine.html" title="File:Cuisine Ivoirienne Foutou sauce graine.jpg">File:Cuisine Ivoirienne Foutou sauce graine.jpg</a></li>
<li class="mw-imagepage-linkstoimage-ns6"><a href="File_Delicious_Chapati.html" title="File:Delicious Chapati.jpg">File:Delicious Chapati.jpg</a></li>
<li class="mw-imagepage-linkstoimage-ns6"><a href="File_Delicious_chapati_served_with_peas.html" title="File:Delicious chapati served with peas.jpg">File:Delicious chapati served with peas.jpg</a></li>
<li class="mw-imagepage-linkstoimage-ns6"><a href="File_Dessert_1.html" title="File:Dessert 1.jpg">File:Dessert 1.jpg</a></li>
<li class="mw-imagepage-linkstoimage-ns6"><a href="File_Dinner_1_rice_pumpkin_beef.html" title="File:Dinner 1 rice pumpkin beef.jpg">File:Dinner 1 rice pumpkin beef.jpg</a></li>
<li class="mw-imagepage-linkstoimage-ns6"><a href="File_Dinner_2_ugali_with_fried_tripe.html" title="File:Dinner 2 ugali with fried tripe.jpg">File:Dinner 2 ugali with fried tripe.jpg</a></li>
<li class="mw-imagepage-linkstoimage-ns6"><a href="File_Eating_Banga_(Pam_Seed)_Rice..html" title="File:Eating Banga (Pam Seed) Rice..jpg">File:Eating Banga (Pam Seed) Rice..jpg</a></li>
<li class="mw-imagepage-linkstoimage-ns6"><a href="File_Eba_served_with_Fresh_fish_banga_soup_in_a_clay_pot.html" title="File:Eba served with Fresh fish banga soup in a clay pot.jpg">File:Eba served with Fresh fish banga soup in a clay pot.jpg</a></li>
<li class="mw-imagepage-linkstoimage-ns6"><a href="File_Eba_with_Ewedu_soup_%26_Stew_with_Croaker_Fish_and_Shaki.html" title="File:Eba with Ewedu soup &amp; Stew with Croaker Fish and Shaki.jpg">File:Eba with Ewedu soup &amp; Stew with Croaker Fish and Shaki.jpg</a></li>
<li class="mw-imagepage-linkstoimage-ns6"><a href="File_Fried_Rice%2c_Jollof_rice_and_salad%2c_served_with_Grilled_Chicken.html" title="File:Fried Rice, Jollof rice and salad, served with Grilled Chicken.jpg">File:Fried Rice, Jollof rice and salad, served with Grilled Chicken.jpg</a></li>
<li class="mw-imagepage-linkstoimage-ns6"><a href="File_Fried_Rice_and_Chicken.html" title="File:Fried Rice and Chicken.jpg">File:Fried Rice and Chicken.jpg</a></li>
<li class="mw-imagepage-linkstoimage-ns6"><a href="File_Fried_Ripe_Plantain.html" title="File:Fried Ripe Plantain.jpg">File:Fried Ripe Plantain.jpg</a></li>
<li class="mw-imagepage-linkstoimage-ns6"><a href="File_Fried_Talapia_Fish_with_Potato_Chips_in_Uganda.html" title="File:Fried Talapia Fish with Potato Chips in Uganda.jpg">File:Fried Talapia Fish with Potato Chips in Uganda.jpg</a></li>
<li class="mw-imagepage-linkstoimage-ns6"><a href="File_Fries_with_Dry-Fried_pork_%26_Homemade_Sauce.html" title="File:Fries with Dry-Fried pork &amp; Homemade Sauce.jpg">File:Fries with Dry-Fried pork &amp; Homemade Sauce.jpg</a></li>
<li class="mw-imagepage-linkstoimage-ns6"><a href="File_Fufuo..html" title="File:Fufuo..JPG">File:Fufuo..JPG</a></li>
<li class="mw-imagepage-linkstoimage-ns6"><a href="File_Fufuo.html" title="File:Fufuo.JPG">File:Fufuo.JPG</a></li>
<li class="mw-imagepage-linkstoimage-ns6"><a href="File_Funny_shack_at_a_small_airport_in_Tanzania.html" title="File:Funny shack at a small airport in Tanzania.JPG">File:Funny shack at a small airport in Tanzania.JPG</a></li>
<li class="mw-imagepage-linkstoimage-ns6"><a href="File_Githeri.html" title="File:Githeri.jpg">File:Githeri.jpg</a></li>
<li class="mw-imagepage-linkstoimage-ns6"><a href="File_Grinding_pepper.html" title="File:Grinding pepper.JPG">File:Grinding pepper.JPG</a></li>
<li class="mw-imagepage-linkstoimage-ns6"><a href="File_Groundnut_Soup.html" title="File:Groundnut Soup.JPG">File:Groundnut Soup.JPG</a></li>
<li class="mw-imagepage-linkstoimage-ns6"><a href="File_Home_Chef_-_African_food_and_cuisine.html" title="File:Home Chef - African food and cuisine.jpg">File:Home Chef - African food and cuisine.jpg</a></li>
<li class="mw-imagepage-linkstoimage-ns6"><a href="File_How_to_make_injera_in_d.markos%2cEthiopia.html" title="File:How to make injera in d.markos,Ethiopia.jpg">File:How to make injera in d.markos,Ethiopia.jpg</a></li>
<li class="mw-imagepage-linkstoimage-ns6"><a href="File_How_to_make_injera_in_d.markos%2cEthiopia2.html" title="File:How to make injera in d.markos,Ethiopia2.jpg">File:How to make injera in d.markos,Ethiopia2.jpg</a></li>
<li class="mw-imagepage-linkstoimage-ns6"><a href="File_JAMAL_YOUSSEF_2.html" title="File:JAMAL YOUSSEF 2.jpg">File:JAMAL YOUSSEF 2.jpg</a></li>
<li class="mw-imagepage-linkstoimage-ns6"><a href="File_Jamaican_Rice_served_with_grilled_Fish_and_Mixed_Salad_and_moi_moi_(Baked_beans).html" title="File:Jamaican Rice served with grilled Fish and Mixed Salad and moi moi (Baked beans).jpg">File:Jamaican Rice served with grilled Fish and Mixed Salad and moi moi (Baked beans).jpg</a></li>
<li class="mw-imagepage-linkstoimage-ns6"><a href="File_Jamaican_Sauce_served_with_Boiled_Yam%2c_fried_ripe_Plantain_and_fried_fish.html" title="File:Jamaican Sauce served with Boiled Yam, fried ripe Plantain and fried fish.jpg">File:Jamaican Sauce served with Boiled Yam, fried ripe Plantain and fried fish.jpg</a></li>
<li class="mw-imagepage-linkstoimage-ns6"><a href="File_Jay_nyama_Choma.html" title="File:Jay nyama Choma.jpg">File:Jay nyama Choma.jpg</a></li>
<li class="mw-imagepage-linkstoimage-ns6"><a href="File_Johannesburg-20130502-01362.html" title="File:Johannesburg-20130502-01362.jpg">File:Johannesburg-20130502-01362.jpg</a></li>
<li class="mw-imagepage-linkstoimage-ns6"><a href="File_Jollof_Rice_with_Beef_%26_Shrimps.html" title="File:Jollof Rice with Beef &amp; Shrimps.jpg">File:Jollof Rice with Beef &amp; Shrimps.jpg</a></li>
<li class="mw-imagepage-linkstoimage-ns6"><a href="File_Kaftaji.html" title="File:Kaftaji.jpg">File:Kaftaji.jpg</a></li>
<li class="mw-imagepage-linkstoimage-ns6"><a href="File_Kenkey_shop.html" title="File:Kenkey shop.JPG">File:Kenkey shop.JPG</a></li>
<li class="mw-imagepage-linkstoimage-ns6"><a href="File_Le_Gratin_de_Banane_Plantain.html" title="File:Le Gratin de Banane Plantain.JPG">File:Le Gratin de Banane Plantain.JPG</a></li>
<li class="mw-imagepage-linkstoimage-ns6"><a href="File_Lioness_after_feeding.html" title="File:Lioness after feeding.JPG">File:Lioness after feeding.JPG</a></li>
<li class="mw-imagepage-linkstoimage-ns6"><a href="File_Lioness_roaring_after_eating.html" title="File:Lioness roaring after eating.JPG">File:Lioness roaring after eating.JPG</a></li>
<li class="mw-imagepage-linkstoimage-ns6"><a href="File_Local_Beef_Stew.html" title="File:Local Beef Stew.jpg">File:Local Beef Stew.jpg</a></li>
<li class="mw-imagepage-linkstoimage-ns6"><a href="File_Lunch_3.html" title="File:Lunch 3.jpg">File:Lunch 3.jpg</a></li>
<li class="mw-imagepage-linkstoimage-ns6"><a href="File_Lunch_5.html" title="File:Lunch 5.jpg">File:Lunch 5.jpg</a></li>
<li class="mw-imagepage-linkstoimage-ns6"><a href="File_Lunch_6.html" title="File:Lunch 6.jpg">File:Lunch 6.jpg</a></li>
<li class="mw-imagepage-linkstoimage-ns6"><a href="File_Macaroni_with_Chopped_Beef.html" title="File:Macaroni with Chopped Beef.jpg">File:Macaroni with Chopped Beef.jpg</a></li>
<li class="mw-imagepage-linkstoimage-ns6"><a href="File_Mixed_Slice_of_Fruits.html" title="File:Mixed Slice of Fruits.jpg">File:Mixed Slice of Fruits.jpg</a></li>
<li class="mw-imagepage-linkstoimage-ns6"><a href="File_Noddles_served_with_Hot_dog%2c_chicken_and_Ketchup.html" title="File:Noddles served with Hot dog, chicken and Ketchup.jpg">File:Noddles served with Hot dog, chicken and Ketchup.jpg</a></li>
<li class="mw-imagepage-linkstoimage-ns6"><a href="File_Owo_(Palm_Oil_Soup).html" title="File:Owo (Palm Oil Soup).jpg">File:Owo (Palm Oil Soup).jpg</a></li>
<li class="mw-imagepage-linkstoimage-ns6"><a href="File_Owo_Soup_and_starch.html" title="File:Owo Soup and starch.jpg">File:Owo Soup and starch.jpg</a></li>
<li class="mw-imagepage-linkstoimage-ns6"><a href="File_Palm_nut_Soup.html" title="File:Palm nut Soup.JPG">File:Palm nut Soup.JPG</a></li>
<li class="mw-imagepage-linkstoimage-ns6"><a href="File_Pounded_Yam_and_Egusi_Soup.html" title="File:Pounded Yam and Egusi Soup.jpg">File:Pounded Yam and Egusi Soup.jpg</a></li>
<li class="mw-imagepage-linkstoimage-ns6"><a href="File_Pounded_Yam_and_Okro_Soup.html" title="File:Pounded Yam and Okro Soup.jpg">File:Pounded Yam and Okro Soup.jpg</a></li>
<li class="mw-imagepage-linkstoimage-ns6"><a href="File_Pumpking%2cSweet_potatoes%2cPork%2cRice_%26_peas.html" title="File:Pumpking,Sweet potatoes,Pork,Rice &amp; peas.jpg">File:Pumpking,Sweet potatoes,Pork,Rice &amp; peas.jpg</a></li>
<li class="mw-imagepage-linkstoimage-ns6"><a href="File_Rabbit_Grilled_from_Algeria.html" title="File:Rabbit Grilled from Algeria.JPG">File:Rabbit Grilled from Algeria.JPG</a></li>
<li class="mw-imagepage-linkstoimage-ns6"><a href="File_Red_Pepper_with_Onion.html" title="File:Red Pepper with Onion.JPG">File:Red Pepper with Onion.JPG</a></li>
<li class="mw-imagepage-linkstoimage-ns6"><a href="File_Rice_Served_with_Stew%2c_Cow_Leg_and_fish.html" title="File:Rice Served with Stew, Cow Leg and fish.jpg">File:Rice Served with Stew, Cow Leg and fish.jpg</a></li>
<li class="mw-imagepage-linkstoimage-ns6"><a href="File_Rice_served_with_banana%2c_pear_and_tomato_stew.html" title="File:Rice served with banana, pear and tomato stew.jpg">File:Rice served with banana, pear and tomato stew.jpg</a></li>
<li class="mw-imagepage-linkstoimage-ns6"><a href="File_Rice_served_with_fish_sauce_and_fried_Platain.html" title="File:Rice served with fish sauce and fried Platain.jpg">File:Rice served with fish sauce and fried Platain.jpg</a></li>
<li class="mw-imagepage-linkstoimage-ns6"><a href="File_Roast_chicken%2c_French_fries_%26_Vegetable_Salad.html" title="File:Roast chicken, French fries &amp; Vegetable Salad.jpg">File:Roast chicken, French fries &amp; Vegetable Salad.jpg</a></li>
<li class="mw-imagepage-linkstoimage-ns6"><a href="File_Semo.html" title="File:Semo.jpg">File:Semo.jpg</a></li>
<li class="mw-imagepage-linkstoimage-ns6"><a href="File_Serving_of_food.html" title="File:Serving of food.JPG">File:Serving of food.JPG</a></li>
<li class="mw-imagepage-linkstoimage-ns6"><a href="File_Snack_1.html" title="File:Snack 1.jpg">File:Snack 1.jpg</a></li>
<li class="mw-imagepage-linkstoimage-ns6"><a href="File_Soup_being_prepared.html" title="File:Soup being prepared.JPG">File:Soup being prepared.JPG</a></li>
<li class="mw-imagepage-linkstoimage-ns6"><a href="File_Special_jollof.html" title="File:Special jollof.jpg">File:Special jollof.jpg</a></li>
<li class="mw-imagepage-linkstoimage-ns6"><a href="File_Staftahi.html" title="File:Staftahi.jpg">File:Staftahi.jpg</a></li>
<li class="mw-imagepage-linkstoimage-ns6"><a href="File_Staftahi_2.html" title="File:Staftahi 2.jpg">File:Staftahi 2.jpg</a></li>
<li class="mw-imagepage-linkstoimage-ns6"><a href="File_Strawberries_and_vanilla_ice_cream_in_a_blue_bowl_with_a_spoon.html" title="File:Strawberries and vanilla ice cream in a blue bowl with a spoon.jpg">File:Strawberries and vanilla ice cream in a blue bowl with a spoon.jpg</a></li>
<li class="mw-imagepage-linkstoimage-ns6"><a href="File_Supper.html" title="File:Supper.jpg">File:Supper.jpg</a></li>
<li class="mw-imagepage-linkstoimage-ns6"><a href="File_Supper_2.html" title="File:Supper 2.jpg">File:Supper 2.jpg</a></li>
<li class="mw-imagepage-linkstoimage-ns6"><a href="File_Supper_3.html" title="File:Supper 3.jpg">File:Supper 3.jpg</a></li>
<li class="mw-imagepage-linkstoimage-ns6"><a href="File_Supper_5.html" title="File:Supper 5.jpg">File:Supper 5.jpg</a></li>
<li class="mw-imagepage-linkstoimage-ns6"><a href="File_Supper_7.html" title="File:Supper 7.jpg">File:Supper 7.jpg</a></li>
<li class="mw-imagepage-linkstoimage-ns6"><a href="File_Tilapia_Grilled_Fish.html" title="File:Tilapia Grilled Fish.jpg">File:Tilapia Grilled Fish.jpg</a></li>
<li class="mw-imagepage-linkstoimage-ns6"><a href="File_Twowo_chicafa.html" title="File:Twowo chicafa.jpg">File:Twowo chicafa.jpg</a></li>
<li class="mw-imagepage-linkstoimage-ns6"><a href="File_Ugandan_Dish.html" title="File:Ugandan Dish.jpg">File:Ugandan Dish.jpg</a></li>
<li class="mw-imagepage-linkstoimage-ns6"><a href="File_Vagitable_soup.html" title="File:Vagitable soup.JPG">File:Vagitable soup.JPG</a></li>
<li class="mw-imagepage-linkstoimage-ns6"><a href="File_Vegetable_Noddles.html" title="File:Vegetable Noddles.jpg">File:Vegetable Noddles.jpg</a></li>
<li class="mw-imagepage-linkstoimage-ns6"><a href="File_Veggie_Lunch.html" title="File:Veggie Lunch.jpg">File:Veggie Lunch.jpg</a></li>
<li class="mw-imagepage-linkstoimage-ns6"><a href="File_Zebra_Closeup.html" title="File:Zebra Closeup.JPG">File:Zebra Closeup.JPG</a></li>
<li class="mw-imagepage-linkstoimage-ns6"><a href="File_%d8%ae%d8%a8%d8%b2_%d9%83%d8%b3%d8%b1%d8%a9_%d8%a7%d9%84%d8%b4%d8%b9%d9%8a%d8%b1%d8%8c_%d8%aa%d9%88%d9%86%d8%b3_2014.html" title="File:خبز كسرة الشعير، تونس 2014.jpg">File:خبز كسرة الشعير، تونس 2014.jpg</a></li>
<li class="mw-imagepage-linkstoimage-ns6"><a href="File_%d8%b3%d9%88%d9%82_%d8%a8%d9%85%d8%af%d9%8a%d9%86%d8%a9_%d8%a7%d9%84%d9%85%d9%86%d8%b3%d8%aa%d9%8a%d8%b1_%d8%a7%d9%84%d8%aa%d9%88%d9%86%d8%b3%d9%8a%d8%a9_1.html" title="File:سوق بمدينة المنستير التونسية 1.JPG">File:سوق بمدينة المنستير التونسية 1.JPG</a></li>
<li class="mw-imagepage-linkstoimage-ns6"><a href="File_%d8%b3%d9%88%d9%82_%d8%a8%d9%85%d8%af%d9%8a%d9%86%d8%a9_%d8%a7%d9%84%d9%85%d9%86%d8%b3%d8%aa%d9%8a%d8%b1_%d8%a7%d9%84%d8%aa%d9%88%d9%86%d8%b3%d9%8a%d8%a9_2.html" title="File:سوق بمدينة المنستير التونسية 2.JPG">File:سوق بمدينة المنستير التونسية 2.JPG</a></li>
<li class="mw-imagepage-linkstoimage-ns6"><a href="File_%d8%b3%d9%88%d9%82_%d8%a8%d9%85%d8%af%d9%8a%d9%86%d8%a9_%d8%a7%d9%84%d9%85%d9%86%d8%b3%d8%aa%d9%8a%d8%b1_%d8%a7%d9%84%d8%aa%d9%88%d9%86%d8%b3%d9%8a%d8%a9_3.html" title="File:سوق بمدينة المنستير التونسية 3.JPG">File:سوق بمدينة المنستير التونسية 3.JPG</a></li>
<li class="mw-imagepage-linkstoimage-ns6"><a href="File_%d8%b3%d9%88%d9%82_%d8%a8%d9%85%d8%af%d9%8a%d9%86%d8%a9_%d8%a7%d9%84%d9%85%d9%86%d8%b3%d8%aa%d9%8a%d8%b1_%d8%a7%d9%84%d8%aa%d9%88%d9%86%d8%b3%d9%8a%d8%a9_4.html" title="File:سوق بمدينة المنستير التونسية 4.JPG">File:سوق بمدينة المنستير التونسية 4.JPG</a></li>
<li class="mw-imagepage-linkstoimage-ns6"><a href="File_%d8%b3%d9%88%d9%82_%d8%a8%d9%85%d8%af%d9%8a%d9%86%d8%a9_%d8%a7%d9%84%d9%85%d9%86%d8%b3%d8%aa%d9%8a%d8%b1_%d8%a7%d9%84%d8%aa%d9%88%d9%86%d8%b3%d9%8a%d8%a9_5.html" title="File:سوق بمدينة المنستير التونسية 5.JPG">File:سوق بمدينة المنستير التونسية 5.JPG</a></li>
<li class="mw-imagepage-linkstoimage-ns6"><a href="File_%d8%b3%d9%88%d9%82_%d8%a8%d9%85%d8%af%d9%8a%d9%86%d8%a9_%d8%a7%d9%84%d9%85%d9%86%d8%b3%d8%aa%d9%8a%d8%b1_%d8%a7%d9%84%d8%aa%d9%88%d9%86%d8%b3%d9%8a%d8%a9_6.html" title="File:سوق بمدينة المنستير التونسية 6.JPG">File:سوق بمدينة المنستير التونسية 6.JPG</a></li>
<li class="mw-imagepage-linkstoimage-ns6"><a href="File_%d8%b4%d8%ae%d8%b4%d9%88%d8%ae%d8%a9.html" title="File:شخشوخة.jpg">File:شخشوخة.jpg</a></li>
<li class="mw-imagepage-linkstoimage-ns6"><a href="File_%d8%b7%d8%a8%d9%82_%d8%a7%d9%84%d8%ae%d8%b6%d8%b1_%d8%a7%d9%84%d9%85%d8%ba%d8%b1%d8%a8%d9%8a.html" title="File:طبق الخضر المغربي.jpg">File:طبق الخضر المغربي.jpg</a></li>
<li class="mw-imagepage-linkstoimage-ns6"><a href="File_%d8%b7%d8%a8%d9%82_%d8%a7%d9%84%d8%b1%d9%81%d9%8a%d8%b3%d8%a9_%d8%a7%d9%84%d9%85%d8%ba%d8%b1%d8%a8%d9%8a.html" title="File:طبق الرفيسة المغربي.jpg">File:طبق الرفيسة المغربي.jpg</a></li>
<li class="mw-imagepage-linkstoimage-ns6"><a href="File_%d9%81%d9%84%d9%81%d9%84_%d8%a3%d8%ad%d9%85%d8%b1_%d9%82%d9%8a%d8%af_%d8%a7%d9%84%d8%aa%d8%ac%d9%81%d9%8a%d9%81_%d9%81%d9%8a_%d8%aa%d9%88%d9%86%d8%b3.html" title="File:فلفل أحمر قيد التجفيف في تونس.jpg">File:فلفل أحمر قيد التجفيف في تونس.jpg</a></li>
<li class="mw-imagepage-linkstoimage-ns10"><a href="Template_Wiki_Loves_Africa_2014_country.html" title="Template:Wiki Loves Africa 2014 country">Template:Wiki Loves Africa 2014 country</a></li>
<li class="mw-imagepage-linkstoimage-ns10"><a href="Template_Wiki_Loves_Africa_2014_country/ar.html" title="Template:Wiki Loves Africa 2014 country/ar">Template:Wiki Loves Africa 2014 country/ar</a></li>
<li class="mw-imagepage-linkstoimage-ns10"><a href="Template_Wiki_Loves_Africa_2014_country/en.html" title="Template:Wiki Loves Africa 2014 country/en">Template:Wiki Loves Africa 2014 country/en</a></li>
</ul>
<p>View <a href="Special_WhatLinksHere/File_Emoji_u1f35c.html" title="Special:WhatLinksHere/File:Emoji u1f35c.svg">more links</a> to this file.
</p></div>
<h2 id="globalusage">File usage on other wikis</h2>
<div id="mw-imagepage-section-globalusage"><p>The following other wikis use this file:
</p><ul class="plainlinks"><li class='mw-gu-onwiki-he_wikipedia_org'>Usage on he.wikipedia.org
<ul>	<li><a class="external" href="https://he.wikipedia.org/wiki/משתמש:Meni_yuzevich/אימוג%27י">משתמש:Meni yuzevich/אימוג&#039;י</a></li>
</ul></li>
<li class='mw-gu-onwiki-ja_wiktionary_org'>Usage on ja.wiktionary.org
<ul>	<li><a class="external" href="https://ja.wiktionary.org/wiki/🍜">🍜</a></li>
</ul></li>
<li class='mw-gu-onwiki-ko_wikipedia_org'>Usage on ko.wikipedia.org
<ul>	<li><a class="external" href="https://ko.wikipedia.org/wiki/위키프로젝트:대한민국">위키프로젝트:대한민국</a></li>
	<li><a class="external" href="https://ko.wikipedia.org/wiki/위키프로젝트:위키백과_토막글/기여/대한민국">위키프로젝트:위키백과 토막글/기여/대한민국</a></li>
</ul></li>
<li class='mw-gu-onwiki-uk_wikipedia_org'>Usage on uk.wikipedia.org
<ul>	<li><a class="external" href="https://uk.wikipedia.org/wiki/Борщ_український_з_пампушками">Борщ український з пампушками</a></li>
	<li><a class="external" href="https://uk.wikipedia.org/wiki/Зелений_борщ">Зелений борщ</a></li>
	<li><a class="external" href="https://uk.wikipedia.org/wiki/Капусняк">Капусняк</a></li>
	<li><a class="external" href="https://uk.wikipedia.org/wiki/Борщ_з_вушками">Борщ з вушками</a></li>
	<li><a class="external" href="https://uk.wikipedia.org/wiki/Щавлевий_суп">Щавлевий суп</a></li>
	<li><a class="external" href="https://uk.wikipedia.org/wiki/Гречаний_суп">Гречаний суп</a></li>
	<li><a class="external" href="https://uk.wikipedia.org/wiki/Касуела">Касуела</a></li>
	<li><a class="external" href="https://uk.wikipedia.org/wiki/Рибний_сукет">Рибний сукет</a></li>
	<li><a class="external" href="https://uk.wikipedia.org/wiki/Борщ_з_грибами">Борщ з грибами</a></li>
	<li><a class="external" href="https://uk.wikipedia.org/wiki/Гороховий_суп">Гороховий суп</a></li>
	<li><a class="external" href="https://uk.wikipedia.org/wiki/Суп_квасолевий">Суп квасолевий</a></li>
	<li><a class="external" href="https://uk.wikipedia.org/wiki/Магірица">Магірица</a></li>
	<li><a class="external" href="https://uk.wikipedia.org/wiki/Суп_з_плавців">Суп з плавців</a></li>
	<li><a class="external" href="https://uk.wikipedia.org/wiki/Поливка">Поливка</a></li>
	<li><a class="external" href="https://uk.wikipedia.org/wiki/Буябес">Буябес</a></li>
	<li><a class="external" href="https://uk.wikipedia.org/wiki/Фруктовий_суп">Фруктовий суп</a></li>
	<li><a class="external" href="https://uk.wikipedia.org/wiki/Мастава">Мастава</a></li>
	<li><a class="external" href="https://uk.wikipedia.org/wiki/Курячий_суп">Курячий суп</a></li>
	<li><a class="external" href="https://uk.wikipedia.org/wiki/Шаблон:Перші_страви-доробити">Шаблон:Перші страви-доробити</a></li>
	<li><a class="external" href="https://uk.wikipedia.org/wiki/Менудо">Менудо</a></li>
</ul></li>
<li class='mw-gu-onwiki-www_wikidata_org'>Usage on www.wikidata.org
<ul>	<li><a class="external" href="https://www.wikidata.org/wiki/Q87576965">Q87576965</a></li>
</ul></li>
</ul>
</div><h2 id="metadata">Metadata</h2>
<div class="mw-imagepage-section-metadata"><p>This file contains additional information such as Exif metadata which may have been added by the digital camera, scanner, or software program used to create or digitize it. If the file has been modified from its original state, some details such as the timestamp may not fully reflect those of the original file. The timestamp is only as accurate as the clock in the camera, and it may be completely wrong.</p><table id="mw_metadata" class="mw_metadata collapsed">
<tbody><tr class="exif-imagewidth mw-metadata-collapsible"><th>Width</th><td>128</td></tr><tr class="exif-imagelength mw-metadata-collapsible"><th>Height</th><td>128</td></tr></tbody></table>
</div><!--esi <esi:include src="/esitest-fa8a495983347898/content" /> --><noscript><img src="https://auth.wikimedia.org/loginwiki/wiki/Special:CentralAutoLogin/checkLoggedIn?useformat=desktop&amp;wikiid=commonswiki&amp;usesul3=1&amp;type=1x1" alt="" width="1" height="1" style="border: none; position: absolute;"></noscript></div><div role='tabpanel' aria-hidden='true' id='ooui-php-5' class='wbmi-tab oo-ui-layout oo-ui-panelLayout oo-ui-panelLayout-scrollable oo-ui-tabPanelLayout oo-ui-element-hidden' data-ooui='{"_":"OO.ui.TabPanelLayout","name":"statements","label":"Structured data","scrollable":true,"expanded":false,"classes":["wbmi-tab"]}'><h2 class="wbmi-structured-data-header">Structured data</h2><div id="P180" data-mw-property="P180" data-mw-statements="[]" data-mw-formatvalue="[]" class="wbmi-entityview-statementsGroup wbmi-entityview-statementsGroup-P180 oo-ui-layout oo-ui-panelLayout oo-ui-panelLayout-framed"><div class="wbmi-statements-widget"><div class="wbmi-statement-header"><div class="wbmi-entity-data"><div class="wbmi-entity-title"><h3 class="wbmi-statements-title">Items portrayed in this file</h3><h4 class="wbmi-entity-label"><bdi><a target="_blank" title="d:Special:EntityPage/P180" href="https://www.wikidata.org/wiki/Special:EntityPage/P180">depicts</a></bdi></h4></div></div></div><div class="wbmi-content-items-group"></div></div></div><div id="P571" data-mw-property="P571" data-mw-statements="[{&quot;mainsnak&quot;:{&quot;snaktype&quot;:&quot;value&quot;,&quot;property&quot;:&quot;P571&quot;,&quot;hash&quot;:&quot;13274f4f34cbbb11c76c440faf9c1c3b7a01b13c&quot;,&quot;datavalue&quot;:{&quot;value&quot;:{&quot;time&quot;:&quot;+2017-05-23T00:00:00Z&quot;,&quot;timezone&quot;:0,&quot;before&quot;:0,&quot;after&quot;:0,&quot;precision&quot;:11,&quot;calendarmodel&quot;:&quot;http:\/\/www.wikidata.org\/entity\/Q1985727&quot;},&quot;type&quot;:&quot;time&quot;}},&quot;type&quot;:&quot;statement&quot;,&quot;id&quot;:&quot;*********$87455C05-45BE-439D-B8D7-E93A41BDEABE&quot;,&quot;rank&quot;:&quot;normal&quot;}]" data-mw-formatvalue="{&quot;{\&quot;value\&quot;:{\&quot;entity-type\&quot;:\&quot;property\&quot;,\&quot;numeric-id\&quot;:571,\&quot;id\&quot;:\&quot;P571\&quot;},\&quot;type\&quot;:\&quot;wikibase-entityid\&quot;}&quot;:{&quot;text\/html&quot;:{&quot;en&quot;:{&quot;&quot;:&quot;<a target=\&quot;_blank\&quot; title=\&quot;d:Special:EntityPage\/P571\&quot; href=https://commons.wikimedia.org/"https:////www.wikidata.org//wiki//Special:EntityPage//P571/">inception<\/a>&quot;}},&quot;text\/plain&quot;:{&quot;en&quot;:{&quot;&quot;:&quot;inception&quot;}}},&quot;{\&quot;value\&quot;:{\&quot;time\&quot;:\&quot;+2017-05-23T00:00:00Z\&quot;,\&quot;timezone\&quot;:0,\&quot;before\&quot;:0,\&quot;after\&quot;:0,\&quot;precision\&quot;:11,\&quot;calendarmodel\&quot;:\&quot;http:\\\/\\\/www.wikidata.org\\\/entity\\\/Q1985727\&quot;},\&quot;type\&quot;:\&quot;time\&quot;}&quot;:{&quot;text\/html&quot;:{&quot;en&quot;:{&quot;P571&quot;:&quot;23 May 2017&quot;}},&quot;text\/plain&quot;:{&quot;en&quot;:{&quot;P571&quot;:&quot;23 May 2017&quot;}}}}" class="wbmi-entityview-statementsGroup wbmi-entityview-statementsGroup-P571 oo-ui-layout oo-ui-panelLayout oo-ui-panelLayout-framed"><div class="wbmi-statements-widget"><div class="wbmi-statement-header"><div class="wbmi-entity-data"><div class="wbmi-entity-title"><h4 class="wbmi-entity-label"><bdi><a target="_blank" title="d:Special:EntityPage/P571" href="https://www.wikidata.org/wiki/Special:EntityPage/P571">inception</a></bdi></h4></div></div></div><div class="wbmi-content-items-group"><div class="wbmi-item wbmi-item-read"><div class="wbmi-item-container"><div class="wbmi-entity-header"><div class="wbmi-entity-data"><div class="wbmi-entity-title"><h4 class="wbmi-entity-label"><bdi>23 May 2017</bdi></h4></div></div></div></div></div></div></div></div><div id="P3575" data-mw-property="P3575" data-mw-statements="[{&quot;mainsnak&quot;:{&quot;snaktype&quot;:&quot;value&quot;,&quot;property&quot;:&quot;P3575&quot;,&quot;hash&quot;:&quot;6e72f015055360bf9d2b2c4ebc2a28abb733f947&quot;,&quot;datavalue&quot;:{&quot;value&quot;:{&quot;amount&quot;:&quot;+14349&quot;,&quot;unit&quot;:&quot;http:\/\/www.wikidata.org\/entity\/Q8799&quot;},&quot;type&quot;:&quot;quantity&quot;}},&quot;type&quot;:&quot;statement&quot;,&quot;id&quot;:&quot;*********$E3DCBF3E-FB95-4C13-BE8C-D205F09482CA&quot;,&quot;rank&quot;:&quot;normal&quot;}]" data-mw-formatvalue="{&quot;{\&quot;value\&quot;:{\&quot;entity-type\&quot;:\&quot;property\&quot;,\&quot;numeric-id\&quot;:3575,\&quot;id\&quot;:\&quot;P3575\&quot;},\&quot;type\&quot;:\&quot;wikibase-entityid\&quot;}&quot;:{&quot;text\/html&quot;:{&quot;en&quot;:{&quot;&quot;:&quot;<a target=\&quot;_blank\&quot; title=\&quot;d:Special:EntityPage\/P3575\&quot; href=https://commons.wikimedia.org/"https:////www.wikidata.org//wiki//Special:EntityPage//P3575/">data size<\/a>&quot;}},&quot;text\/plain&quot;:{&quot;en&quot;:{&quot;&quot;:&quot;data size&quot;}}},&quot;{\&quot;value\&quot;:{\&quot;amount\&quot;:\&quot;+14349\&quot;,\&quot;unit\&quot;:\&quot;http:\\\/\\\/www.wikidata.org\\\/entity\\\/Q8799\&quot;},\&quot;type\&quot;:\&quot;quantity\&quot;}&quot;:{&quot;text\/html&quot;:{&quot;en&quot;:{&quot;P3575&quot;:&quot;14,349 <span class=\&quot;wb-unit\&quot;>byte<\/span>&quot;}},&quot;text\/plain&quot;:{&quot;en&quot;:{&quot;P3575&quot;:&quot;14,349 byte&quot;}}}}" class="wbmi-entityview-statementsGroup wbmi-entityview-statementsGroup-P3575 oo-ui-layout oo-ui-panelLayout oo-ui-panelLayout-framed"><div class="wbmi-statements-widget"><div class="wbmi-statement-header"><div class="wbmi-entity-data"><div class="wbmi-entity-title"><h4 class="wbmi-entity-label"><bdi><a target="_blank" title="d:Special:EntityPage/P3575" href="https://www.wikidata.org/wiki/Special:EntityPage/P3575">data size</a></bdi></h4></div></div></div><div class="wbmi-content-items-group"><div class="wbmi-item wbmi-item-read"><div class="wbmi-item-container"><div class="wbmi-entity-header"><div class="wbmi-entity-data"><div class="wbmi-entity-title"><h4 class="wbmi-entity-label"><bdi>14,349 <span class="wb-unit">byte</span></bdi></h4></div></div></div></div></div></div></div></div><div id="P1163" data-mw-property="P1163" data-mw-statements="[{&quot;mainsnak&quot;:{&quot;snaktype&quot;:&quot;value&quot;,&quot;property&quot;:&quot;P1163&quot;,&quot;hash&quot;:&quot;636199d1806c7cdbae57b4d4298f38fade12d052&quot;,&quot;datavalue&quot;:{&quot;value&quot;:&quot;image\/svg+xml&quot;,&quot;type&quot;:&quot;string&quot;}},&quot;type&quot;:&quot;statement&quot;,&quot;id&quot;:&quot;*********$FCD2AFDC-D208-4429-ADD6-427693287943&quot;,&quot;rank&quot;:&quot;normal&quot;}]" data-mw-formatvalue="{&quot;{\&quot;value\&quot;:{\&quot;entity-type\&quot;:\&quot;property\&quot;,\&quot;numeric-id\&quot;:1163,\&quot;id\&quot;:\&quot;P1163\&quot;},\&quot;type\&quot;:\&quot;wikibase-entityid\&quot;}&quot;:{&quot;text\/html&quot;:{&quot;en&quot;:{&quot;&quot;:&quot;<a target=\&quot;_blank\&quot; title=\&quot;d:Special:EntityPage\/P1163\&quot; href=https://commons.wikimedia.org/"https:////www.wikidata.org//wiki//Special:EntityPage//P1163/">media type<\/a>&quot;}},&quot;text\/plain&quot;:{&quot;en&quot;:{&quot;&quot;:&quot;media type&quot;}}},&quot;{\&quot;value\&quot;:\&quot;image\\\/svg+xml\&quot;,\&quot;type\&quot;:\&quot;string\&quot;}&quot;:{&quot;text\/html&quot;:{&quot;en&quot;:{&quot;P1163&quot;:&quot;image\/svg+xml&quot;}},&quot;text\/plain&quot;:{&quot;en&quot;:{&quot;P1163&quot;:&quot;image\/svg+xml&quot;}}}}" class="wbmi-entityview-statementsGroup wbmi-entityview-statementsGroup-P1163 oo-ui-layout oo-ui-panelLayout oo-ui-panelLayout-framed"><div class="wbmi-statements-widget"><div class="wbmi-statement-header"><div class="wbmi-entity-data"><div class="wbmi-entity-title"><h4 class="wbmi-entity-label"><bdi><a target="_blank" title="d:Special:EntityPage/P1163" href="https://www.wikidata.org/wiki/Special:EntityPage/P1163">media type</a></bdi></h4></div></div></div><div class="wbmi-content-items-group"><div class="wbmi-item wbmi-item-read"><div class="wbmi-item-container"><div class="wbmi-entity-header"><div class="wbmi-entity-data"><div class="wbmi-entity-title"><h4 class="wbmi-entity-label"><bdi>image/svg+xml</bdi></h4></div></div></div></div></div></div></div></div><div id="P4092" data-mw-property="P4092" data-mw-statements="[{&quot;mainsnak&quot;:{&quot;snaktype&quot;:&quot;value&quot;,&quot;property&quot;:&quot;P4092&quot;,&quot;hash&quot;:&quot;26ff034003268a46c32d582493d97e40f26214df&quot;,&quot;datavalue&quot;:{&quot;value&quot;:&quot;ceadc1634b02a17d67524b7c59e56acdc7278f27&quot;,&quot;type&quot;:&quot;string&quot;}},&quot;type&quot;:&quot;statement&quot;,&quot;qualifiers&quot;:{&quot;P459&quot;:[{&quot;snaktype&quot;:&quot;value&quot;,&quot;property&quot;:&quot;P459&quot;,&quot;hash&quot;:&quot;75dff03c151b13fbab93742164121c16a6aa0de1&quot;,&quot;datavalue&quot;:{&quot;value&quot;:{&quot;entity-type&quot;:&quot;item&quot;,&quot;numeric-id&quot;:13414952,&quot;id&quot;:&quot;Q13414952&quot;},&quot;type&quot;:&quot;wikibase-entityid&quot;}}]},&quot;qualifiers-order&quot;:[&quot;P459&quot;],&quot;id&quot;:&quot;*********$706B59BB-490A-455F-9061-957D23B0AC24&quot;,&quot;rank&quot;:&quot;normal&quot;}]" data-mw-formatvalue="{&quot;{\&quot;value\&quot;:{\&quot;entity-type\&quot;:\&quot;property\&quot;,\&quot;numeric-id\&quot;:4092,\&quot;id\&quot;:\&quot;P4092\&quot;},\&quot;type\&quot;:\&quot;wikibase-entityid\&quot;}&quot;:{&quot;text\/html&quot;:{&quot;en&quot;:{&quot;&quot;:&quot;<a target=\&quot;_blank\&quot; title=\&quot;d:Special:EntityPage\/P4092\&quot; href=https://commons.wikimedia.org/"https:////www.wikidata.org//wiki//Special:EntityPage//P4092/">checksum<\/a>&quot;}},&quot;text\/plain&quot;:{&quot;en&quot;:{&quot;&quot;:&quot;checksum&quot;}}},&quot;{\&quot;value\&quot;:\&quot;ceadc1634b02a17d67524b7c59e56acdc7278f27\&quot;,\&quot;type\&quot;:\&quot;string\&quot;}&quot;:{&quot;text\/html&quot;:{&quot;en&quot;:{&quot;P4092&quot;:&quot;ceadc1634b02a17d67524b7c59e56acdc7278f27&quot;}},&quot;text\/plain&quot;:{&quot;en&quot;:{&quot;P4092&quot;:&quot;ceadc1634b02a17d67524b7c59e56acdc7278f27&quot;}}},&quot;{\&quot;value\&quot;:{\&quot;entity-type\&quot;:\&quot;property\&quot;,\&quot;numeric-id\&quot;:459,\&quot;id\&quot;:\&quot;P459\&quot;},\&quot;type\&quot;:\&quot;wikibase-entityid\&quot;}&quot;:{&quot;text\/html&quot;:{&quot;en&quot;:{&quot;&quot;:&quot;<a target=\&quot;_blank\&quot; title=\&quot;d:Special:EntityPage\/P459\&quot; href=https://commons.wikimedia.org/"https:////www.wikidata.org//wiki//Special:EntityPage//P459/">determination method or standard<\/a>&quot;}},&quot;text\/plain&quot;:{&quot;en&quot;:{&quot;&quot;:&quot;determination method or standard&quot;}}},&quot;{\&quot;value\&quot;:{\&quot;entity-type\&quot;:\&quot;item\&quot;,\&quot;numeric-id\&quot;:13414952,\&quot;id\&quot;:\&quot;Q13414952\&quot;},\&quot;type\&quot;:\&quot;wikibase-entityid\&quot;}&quot;:{&quot;text\/html&quot;:{&quot;en&quot;:{&quot;P459&quot;:&quot;<a target=\&quot;_blank\&quot; title=\&quot;d:Special:EntityPage\/Q13414952\&quot; href=https://commons.wikimedia.org/"https:////www.wikidata.org//wiki//Special:EntityPage//Q13414952/">SHA-1<\/a>&quot;}},&quot;text\/plain&quot;:{&quot;en&quot;:{&quot;P459&quot;:&quot;SHA-1&quot;}}}}" class="wbmi-entityview-statementsGroup wbmi-entityview-statementsGroup-P4092 oo-ui-layout oo-ui-panelLayout oo-ui-panelLayout-framed"><div class="wbmi-statements-widget"><div class="wbmi-statement-header"><div class="wbmi-entity-data"><div class="wbmi-entity-title"><h4 class="wbmi-entity-label"><bdi><a target="_blank" title="d:Special:EntityPage/P4092" href="https://www.wikidata.org/wiki/Special:EntityPage/P4092">checksum</a></bdi></h4></div></div></div><div class="wbmi-content-items-group"><div class="wbmi-item wbmi-item-read"><div class="wbmi-item-container"><div class="wbmi-entity-header"><div class="wbmi-entity-data"><div class="wbmi-entity-title"><h4 class="wbmi-entity-label"><bdi>ceadc1634b02a17d67524b7c59e56acdc7278f27</bdi></h4></div></div></div><div class="wbmi-item-qualifiers"><div class="wbmi-snaklist-container"><div class="wbmi-snaklist-content"><div class="wbmi-snak"><div class="wbmi-snak-value"><a target="_blank" title="d:Special:EntityPage/P459" href="https://www.wikidata.org/wiki/Special:EntityPage/P459">determination method or standard</a><span class="wbmi-snak-value-separator">: </span><span class="wbmi-snak-value--value"><a target="_blank" title="d:Special:EntityPage/Q13414952" href="https://www.wikidata.org/wiki/Special:EntityPage/Q13414952">SHA-1</a></span></div></div></div></div></div></div></div></div></div></div></div></div></div></div></div>
<div class="printfooter" data-nosnippet="">Retrieved from "<a dir="ltr" href="https://commons.wikimedia.org/w/index.php?title=File:Emoji_u1f35c.svg&amp;oldid=**********">https://commons.wikimedia.org/w/index.php?title=File:Emoji_u1f35c.svg&amp;oldid=**********</a>"</div></div>
					<div id="catlinks" class="catlinks" data-mw="interface"><div id="mw-normal-catlinks" class="mw-normal-catlinks"><a href="Special_Categories.html" title="Special:Categories">Categories</a>: <ul><li><a href="Category_SVG_egg_icons.html" title="Category:SVG egg icons">SVG egg icons</a></li><li><a href="Category_Sliced_eggs.html" title="Category:Sliced eggs">Sliced eggs</a></li><li><a href="Category_Steam_in_art.html" title="Category:Steam in art">Steam in art</a></li><li><a href="Category_Noto_Color_Emoji_Nougat.html" title="Category:Noto Color Emoji Nougat">Noto Color Emoji Nougat</a></li><li><a href="Category_U%2b1F35C.html" title="Category:U+1F35C">U+1F35C</a></li></ul></div><div id="mw-hidden-catlinks" class="mw-hidden-catlinks mw-hidden-cats-user-shown">Hidden categories: <ul><li><a href="Category_Valid_SVG_created_with_Adobe_Illustrator__Emoji_Noto-Nougat.html" title="Category:Valid SVG created with Adobe Illustrator: Emoji Noto-Nougat">Valid SVG created with Adobe Illustrator: Emoji Noto-Nougat</a></li><li><a href="Category_Apache_License.html" title="Category:Apache License">Apache License</a></li></ul></div></div>
				</div>
			</main>
			
		</div>
		<div class="mw-footer-container">
			
<footer id="footer" class="mw-footer" >
	<ul id="footer-info">
	<li id="footer-info-lastmod"> This page was last edited on 4 March 2025, at 09:31.</li>
	<li id="footer-info-copyright">Files are available under licenses specified on their description page. All structured data from the file namespace is available under the <a rel="nofollow" class="external text" href="https://creativecommons.org/publicdomain/zero/1.0/">Creative Commons CC0 License</a>; all unstructured text is available under the <a rel="nofollow" class="external text" href="../../creativecommons.org/licenses/by-sa/4.0/index.html">Creative Commons Attribution-ShareAlike License</a>;
additional terms may apply.
By using this site, you agree to the <a class="external text" href="https://foundation.wikimedia.org/wiki/Special:MyLanguage/Policy:Terms_of_Use">Terms of Use</a> and the <a class="external text" href="https://foundation.wikimedia.org/wiki/Special:MyLanguage/Policy:Privacy_policy">Privacy Policy</a>.</li>
</ul>

	<ul id="footer-places">
	<li id="footer-places-privacy"><a href="https://foundation.wikimedia.org/wiki/Special:MyLanguage/Policy:Privacy_policy">Privacy policy</a></li>
	<li id="footer-places-about"><a href="Commons_Welcome.html">About Wikimedia Commons</a></li>
	<li id="footer-places-disclaimers"><a href="Commons_General_disclaimer.html">Disclaimers</a></li>
	<li id="footer-places-wm-codeofconduct"><a href="https://foundation.wikimedia.org/wiki/Special:MyLanguage/Policy:Universal_Code_of_Conduct">Code of Conduct</a></li>
	<li id="footer-places-developers"><a href="https://developer.wikimedia.org/">Developers</a></li>
	<li id="footer-places-statslink"><a href="https://stats.wikimedia.org/#/commons.wikimedia.org">Statistics</a></li>
	<li id="footer-places-cookiestatement"><a href="https://foundation.wikimedia.org/wiki/Special:MyLanguage/Policy:Cookie_statement">Cookie statement</a></li>
	<li id="footer-places-mobileview"><a href="http://commons.m.wikimedia.org/w/index.php?title=File:Emoji_u1f35c.svg&amp;mobileaction=toggle_view_mobile" class="noprint stopMobileRedirectToggle">Mobile view</a></li>
</ul>

	<ul id="footer-icons" class="noprint">
	<li id="footer-copyrightico"><a href="https://www.wikimedia.org/" class="cdx-button cdx-button--fake-button cdx-button--size-large cdx-button--fake-button--enabled"><picture><source media="(min-width: 500px)" srcset="/static/images/footer/wikimedia-button.svg" width="84" height="29"><img src="../static/images/footer/wikimedia.svg" width="25" height="25" alt="Wikimedia Foundation" lang="en" loading="lazy"></picture></a></li>
	<li id="footer-poweredbyico"><a href="https://www.mediawiki.org/" class="cdx-button cdx-button--fake-button cdx-button--size-large cdx-button--fake-button--enabled"><picture><source media="(min-width: 500px)" srcset="/w/resources/assets/poweredby_mediawiki.svg" width="88" height="31"><img src="../w/resources/assets/mediawiki_compact.svg" alt="Powered by MediaWiki" lang="en" width="25" height="25" loading="lazy"></picture></a></li>
</ul>

</footer>

		</div>
	</div> 
</div> 
<div class="vector-header-container vector-sticky-header-container no-font-mode-scale">
	<div id="vector-sticky-header" class="vector-sticky-header">
		<div class="vector-sticky-header-start">
			<div class="vector-sticky-header-icon-start vector-button-flush-left vector-button-flush-right" aria-hidden="true">
				<button class="cdx-button cdx-button--weight-quiet cdx-button--icon-only vector-sticky-header-search-toggle" tabindex="-1" data-event-name="ui.vector-sticky-search-form.icon"><span class="vector-icon mw-ui-icon-search mw-ui-icon-wikimedia-search"></span>

<span>Search</span>
			</button>
		</div>
			
		<div role="search" class="vector-search-box-vue vector-search-box">
			<div class="vector-typeahead-search-container">
				<div class="cdx-typeahead-search">
					<form action="https://commons.wikimedia.org/w/index.php" id="vector-sticky-search-form" class="cdx-search-input cdx-search-input--has-end-button">
						<div  class="cdx-search-input__input-wrapper"  data-search-loc="header-moved">
							<div class="cdx-text-input cdx-text-input--has-start-icon">
								<input
									class="cdx-text-input__input mw-searchInput" autocomplete="off"
									
									type="search" name="search" placeholder="Search Wikimedia Commons">
								<span class="cdx-text-input__icon cdx-text-input__start-icon"></span>
							</div>
							<input type="hidden" name="title" value="Special:MediaSearch">
						</div>
						<button class="cdx-button cdx-search-input__end-button">Search</button>
					</form>
				</div>
			</div>
		</div>
		<div class="vector-sticky-header-context-bar">
				<nav aria-label="Contents" class="vector-toc-landmark">
						
					<div id="vector-sticky-header-toc" class="vector-dropdown mw-portlet mw-portlet-sticky-header-toc vector-sticky-header-toc vector-button-flush-left"  >
						<input type="checkbox" id="vector-sticky-header-toc-checkbox" role="button" aria-haspopup="true" data-event-name="ui.dropdown-vector-sticky-header-toc" class="vector-dropdown-checkbox "  aria-label="Toggle the table of contents"  >
						<label id="vector-sticky-header-toc-label" for="vector-sticky-header-toc-checkbox" class="vector-dropdown-label cdx-button cdx-button--fake-button cdx-button--fake-button--enabled cdx-button--weight-quiet cdx-button--icon-only " aria-hidden="true"  ><span class="vector-icon mw-ui-icon-listBullet mw-ui-icon-wikimedia-listBullet"></span>

<span class="vector-dropdown-label-text">Toggle the table of contents</span>
						</label>
						<div class="vector-dropdown-content">
					
						<div id="vector-sticky-header-toc-unpinned-container" class="vector-unpinned-container">
						</div>
					
						</div>
					</div>
			</nav>
				<div class="vector-sticky-header-context-bar-primary" aria-hidden="true" ><span class="mw-page-title-namespace">File</span><span class="mw-page-title-separator">:</span><span class="mw-page-title-main">Emoji u1f35c.svg</span></div>
			</div>
		</div>
		<div class="vector-sticky-header-end" aria-hidden="true">
			<div class="vector-sticky-header-icons">
				<a href="#" class="cdx-button cdx-button--fake-button cdx-button--fake-button--enabled cdx-button--weight-quiet cdx-button--icon-only" id="ca-talk-sticky-header" tabindex="-1" data-event-name="talk-sticky-header"><span class="vector-icon mw-ui-icon-speechBubbles mw-ui-icon-wikimedia-speechBubbles"></span>

<span></span>
			</a>
			<a href="#" class="cdx-button cdx-button--fake-button cdx-button--fake-button--enabled cdx-button--weight-quiet cdx-button--icon-only" id="ca-subject-sticky-header" tabindex="-1" data-event-name="subject-sticky-header"><span class="vector-icon mw-ui-icon-article mw-ui-icon-wikimedia-article"></span>

<span></span>
			</a>
			<a href="#" class="cdx-button cdx-button--fake-button cdx-button--fake-button--enabled cdx-button--weight-quiet cdx-button--icon-only" id="ca-history-sticky-header" tabindex="-1" data-event-name="history-sticky-header"><span class="vector-icon mw-ui-icon-wikimedia-history mw-ui-icon-wikimedia-wikimedia-history"></span>

<span></span>
			</a>
			<a href="#" class="cdx-button cdx-button--fake-button cdx-button--fake-button--enabled cdx-button--weight-quiet cdx-button--icon-only mw-watchlink" id="ca-watchstar-sticky-header" tabindex="-1" data-event-name="watch-sticky-header"><span class="vector-icon mw-ui-icon-wikimedia-star mw-ui-icon-wikimedia-wikimedia-star"></span>

<span></span>
			</a>
			<a href="#" class="cdx-button cdx-button--fake-button cdx-button--fake-button--enabled cdx-button--weight-quiet cdx-button--icon-only" id="ca-edit-sticky-header" tabindex="-1" data-event-name="wikitext-edit-sticky-header"><span class="vector-icon mw-ui-icon-wikimedia-wikiText mw-ui-icon-wikimedia-wikimedia-wikiText"></span>

<span></span>
			</a>
			<a href="#" class="cdx-button cdx-button--fake-button cdx-button--fake-button--enabled cdx-button--weight-quiet cdx-button--icon-only" id="ca-ve-edit-sticky-header" tabindex="-1" data-event-name="ve-edit-sticky-header"><span class="vector-icon mw-ui-icon-wikimedia-edit mw-ui-icon-wikimedia-wikimedia-edit"></span>

<span></span>
			</a>
			<a href="#" class="cdx-button cdx-button--fake-button cdx-button--fake-button--enabled cdx-button--weight-quiet cdx-button--icon-only" id="ca-viewsource-sticky-header" tabindex="-1" data-event-name="ve-edit-protected-sticky-header"><span class="vector-icon mw-ui-icon-wikimedia-editLock mw-ui-icon-wikimedia-wikimedia-editLock"></span>

<span></span>
			</a>
		</div>
			<div class="vector-sticky-header-buttons">
				<a href="#" class="cdx-button cdx-button--fake-button cdx-button--fake-button--enabled cdx-button--weight-quiet cdx-button--action-progressive" id="ca-addsection-sticky-header" tabindex="-1" data-event-name="addsection-sticky-header"><span class="vector-icon mw-ui-icon-speechBubbleAdd-progressive mw-ui-icon-wikimedia-speechBubbleAdd-progressive"></span>

<span>Add topic</span>
			</a>
		</div>
			<div class="vector-sticky-header-icon-end">
				<div class="vector-user-links">
				</div>
			</div>
		</div>
	</div>
</div>
<div class="mw-portlet mw-portlet-dock-bottom emptyPortlet" id="p-dock-bottom">
	<ul>
		
	</ul>
</div>
<script>(RLQ=window.RLQ||[]).push(function(){mw.config.set({"wgHostname":"mw-web.eqiad.main-7b48b5fb74-5gq7d","wgBackendResponseTime":277,"wgPageParseReport":{"limitreport":{"cputime":"0.147","walltime":"0.199","ppvisitednodes":{"value":4700,"limit":1000000},"revisionsize":{"value":717,"limit":2097152},"postexpandincludesize":{"value":36787,"limit":2097152},"templateargumentsize":{"value":5764,"limit":2097152},"expansiondepth":{"value":33,"limit":100},"expensivefunctioncount":{"value":1,"limit":500},"unstrip-depth":{"value":0,"limit":20},"unstrip-size":{"value":42,"limit":5000000},"entityaccesscount":{"value":1,"limit":400},"timingprofile":["100.00%  184.553      1 -total"," 83.11%  153.380      1 Template:Information"," 58.95%  108.803      1 Template:Igen"," 51.29%   94.655      1 Template:Information_field"," 27.22%   50.232      1 Template:Created_with_Adobe_Illustrator"," 16.78%   30.970      1 Template:Apache"," 14.59%   26.920      1 Template:SVGvalid"," 13.70%   25.286      1 Template:Ifsvg"," 13.50%   24.921      2 Template:Created_with/layout"," 13.44%   24.795      1 Template:Apache/layout"]},"scribunto":{"limitreport-timeusage":{"value":"0.057","limit":"10.000"},"limitreport-memusage":{"value":1539090,"limit":52428800}},"cachereport":{"origin":"mw-web.eqiad.main-694955c78b-b52c6","timestamp":"20250731015003","ttl":2592000,"transientcontent":false}}});});</script>
<script type="application/ld+json">{"@context":"https:\/\/schema.org","@type":"ImageObject","contentUrl":"https:\/\/upload.wikimedia.org\/wikipedia\/commons\/7\/77\/Emoji_u1f35c.svg","license":"http:\/\/www.apache.org\/licenses\/LICENSE-2.0","acquireLicensePage":"\/\/commons.wikimedia.org\/wiki\/File:Emoji_u1f35c.svg","uploadDate":"2017-05-27 17:31:54"}</script>
</body>

<!-- Mirrored from commons.wikimedia.org/wiki/File:Emoji_u1f35c.svg by HTTrack Website Copier/3.x [XR&CO'2014], Sun, 03 Aug 2025 08:49:52 GMT -->
</html>