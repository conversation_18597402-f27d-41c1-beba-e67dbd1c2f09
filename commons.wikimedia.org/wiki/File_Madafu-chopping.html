<!DOCTYPE html>
<html class="client-nojs vector-feature-language-in-header-disabled vector-feature-language-in-main-page-header-disabled vector-feature-page-tools-pinned-disabled vector-feature-toc-pinned-clientpref-1 vector-feature-main-menu-pinned-disabled vector-feature-limited-width-clientpref-1 vector-feature-limited-width-content-enabled vector-feature-custom-font-size-clientpref--excluded vector-feature-appearance-pinned-clientpref-1 vector-feature-night-mode-enabled skin-theme-clientpref-day vector-sticky-header-enabled vector-toc-available" lang="en" dir="ltr">

<!-- Mirrored from commons.wikimedia.org/wiki/File:Madafu-chopping.jpg by HTTrack Website Copier/3.x [XR&CO'2014], Sun, 03 Aug 2025 07:42:47 GMT -->
<!-- Added by HTTrack --><meta http-equiv="content-type" content="text/html;charset=UTF-8" /><!-- /Added by HTTrack -->
<head>
<meta charset="UTF-8">
<title>File:Madafu-chopping.jpg - Wikimedia Commons</title>
<script>(function(){var className="client-js vector-feature-language-in-header-disabled vector-feature-language-in-main-page-header-disabled vector-feature-page-tools-pinned-disabled vector-feature-toc-pinned-clientpref-1 vector-feature-main-menu-pinned-disabled vector-feature-limited-width-clientpref-1 vector-feature-limited-width-content-enabled vector-feature-custom-font-size-clientpref--excluded vector-feature-appearance-pinned-clientpref-1 vector-feature-night-mode-enabled skin-theme-clientpref-day vector-sticky-header-enabled vector-toc-available";var cookie=document.cookie.match(/(?:^|; )commonswikimwclientpreferences=([^;]+)/);if(cookie){cookie[1].split('%2C').forEach(function(pref){className=className.replace(new RegExp('(^| )'+pref.replace(/-clientpref-\w+$|[^\w-]+/g,'')+'-clientpref-\\w+( |$)'),'$1'+pref+'$2');});}document.documentElement.className=className;}());RLCONF={"wgBreakFrames":true,"wgSeparatorTransformTable":["",""],"wgDigitTransformTable":["",""],"wgDefaultDateFormat":"dmy","wgMonthNames":["","January","February","March","April","May","June","July","August","September","October","November","December"],"wgRequestId":"f4488463-e146-4e3f-80fe-a58b4e48d566","wgCanonicalNamespace":"File","wgCanonicalSpecialPageName":false,"wgNamespaceNumber":6,"wgPageName":"File:Madafu-chopping.jpg","wgTitle":"Madafu-chopping.jpg","wgCurRevisionId":1033630111,"wgRevisionId":1033630111,"wgArticleId":36601495,"wgIsArticle":true,"wgIsRedirect":false,"wgAction":"view","wgUserName":null,"wgUserGroups":["*"],"wgCategories":["Images from Wiki Loves Africa 2014 in Kenya","CC-BY-SA-4.0","Self-published work","Images from Wiki Loves Africa 2014","Uploaded via Campaign:wlafrica","Food preparation in Kenya"],"wgPageViewLanguage":"en","wgPageContentLanguage":"en","wgPageContentModel":"wikitext","wgRelevantPageName":"File:Madafu-chopping.jpg","wgRelevantArticleId":36601495,"wgIsProbablyEditable":true,"wgRelevantPageIsProbablyEditable":true,"wgRestrictionEdit":[],"wgRestrictionMove":[],"wgRestrictionUpload":[],"wgNoticeProject":"commons","wgCiteReferencePreviewsActive":true,"wgMediaViewerOnClick":true,"wgMediaViewerEnabledByDefault":false,"wgVisualEditor":{"pageLanguageCode":"en","pageLanguageDir":"ltr","pageVariantFallbacks":"en"},"wgMFDisplayWikibaseDescriptions":{"search":true,"watchlist":true,"tagline":true,"nearby":true},"wgWMESchemaEditAttemptStepOversample":false,"wgWMEPageLength":400,"wbUserPreferredContentLanguages":["en"],"wbUserSpecifiedLanguages":["en"],"wbCopyright":{"version":"wikibase-1","messageHtml":"By clicking \"publish\", you agree to the \u003Ca href=\"/wiki/Commons:Copyrights\" class=\"mw-redirect\" title=\"Commons:Copyrights\"\u003Eterms of use\u003C/a\u003E, and you irrevocably agree to release your contribution under the \u003Ca rel=\"nofollow\" class=\"external text\" href=\"https://creativecommons.org/publicdomain/zero/1.0/\"\u003ECreative Commons CC0 License\u003C/a\u003E."},"wbBadgeItems":[],"wbMultiLingualStringLimit":250,"wbTaintedReferencesEnabled":false,"wgMetricsPlatformUserExperiments":{"active_experiments":[],"overrides":[],"enrolled":[],"assigned":[],"subject_ids":[],"sampling_units":[]},"wbEntityId":"*********","wgEditSubmitButtonLabelPublish":true,"upwizPropertyTitles":{"P180":"Main subjects visible in this work"},"upwizPropertyPlaceholders":{"P180":"e.g., Angolan giraffe"},"upwizPropertyCopyLabels":{"P180":"Main subjects"},"wgULSPosition":"personal","wgULSisCompactLinksEnabled":true,"wgVector2022LanguageInHeader":false,"wgULSisLanguageSelectorEmpty":false,"wbmiDefaultProperties":["P180"],"wbmiPropertyTitles":{"P180":"Items portrayed in this file"},"wbmiPropertyTypes":{"P180":"wikibase-item","P571":"time","P4082":"wikibase-item","P6216":"wikibase-item","P275":"wikibase-item","P7482":"wikibase-item","P170":"wikibase-item","P3831":"wikibase-item","P2093":"string","P4174":"external-id"},"wbmiRepoApiUrl":"/w/api.php","wbmiHelpUrls":{"P180":"https://commons.wikimedia.org/wiki/Special:MyLanguage/Commons:Depicts"},"wbmiExternalEntitySearchBaseUri":"https://www.wikidata.org/w/api.php","wbmiSupportedDataTypes":["wikibase-item","string","quantity","time","monolingualtext","external-id","globe-coordinate","url"],"wbCurrentRevision":1033630111,"wbEntity":{"type":"mediainfo","id":"*********","labels":[],"descriptions":[],"statements":{"P571":[{"mainsnak":{"snaktype":"value","property":"P571","hash":"a39d8edf4086785bc3984f2053a12e1b3977319e","datavalue":{"value":{"time":"+2014-11-04T00:00:00Z","timezone":0,"before":0,"after":0,"precision":11,"calendarmodel":"http://www.wikidata.org/entity/Q1985727"},"type":"time"}},"type":"statement","id":"*********$4EC8A274-DA19-42C2-87C9-30413067E002","rank":"normal"}],"P4082":[{"mainsnak":{"snaktype":"value","property":"P4082","hash":"934c25321d67b3a2ce32005abc06ebfa13d529e8","datavalue":{"value":{"entity-type":"item","numeric-id":66215,"id":"Q66215"},"type":"wikibase-entityid"}},"type":"statement","id":"*********$5C69C079-394A-47FA-B061-1D597710B93B","rank":"normal"}],"P6216":[{"mainsnak":{"snaktype":"value","property":"P6216","hash":"5570347fdc76d2a80732f51ea10ee4b144a084e0","datavalue":{"value":{"entity-type":"item","numeric-id":50423863,"id":"Q50423863"},"type":"wikibase-entityid"}},"type":"statement","id":"*********$C9F5AAF1-1BB0-430E-8E76-01F3A57E8D33","rank":"normal"}],"P275":[{"mainsnak":{"snaktype":"value","property":"P275","hash":"ec6e754c5042e13b53376139e505ebd6708745a4","datavalue":{"value":{"entity-type":"item","numeric-id":18199165,"id":"*********"},"type":"wikibase-entityid"}},"type":"statement","id":"*********$A63F835D-DEEC-42B7-9457-443A2DCA6589","rank":"normal"}],"P7482":[{"mainsnak":{"snaktype":"value","property":"P7482","hash":"83568a288a8b8b4714a68e7239d8406833762864","datavalue":{"value":{"entity-type":"item","numeric-id":66458942,"id":"Q66458942"},"type":"wikibase-entityid"}},"type":"statement","id":"*********$B1801C92-410F-440E-921F-C231C0AFF8CB","rank":"normal"}],"P170":[{"mainsnak":{"snaktype":"somevalue","property":"P170","hash":"d3550e860f988c6675fff913440993f58f5c40c5"},"type":"statement","qualifiers":{"P3831":[{"snaktype":"value","property":"P3831","hash":"c5e04952fd00011abf931be1b701f93d9e6fa5d7","datavalue":{"value":{"entity-type":"item","numeric-id":33231,"id":"Q33231"},"type":"wikibase-entityid"}}],"P2093":[{"snaktype":"value","property":"P2093","hash":"720ed39de54d0facfda214c97b2abe999feac28f","datavalue":{"value":"kungu irungu","type":"string"}}],"P4174":[{"snaktype":"value","property":"P4174","hash":"b7c456c9fc6eeba79f1c389d3251b66412fcae52","datavalue":{"value":"Kungu01","type":"string"}}]},"qualifiers-order":["P3831","P2093","P4174"],"id":"*********$CF584A4E-8C5C-4969-B85C-93B446DAE7BB","rank":"normal"}]}},"wbmiMinCaptionLength":5,"wbmiMaxCaptionLength":250,"wbmiParsedMessageAnonEditWarning":"\u003Cp\u003EYou are not logged in and your \u003Ca href=\"https://en.wikipedia.org/wiki/IP_address\" class=\"extiw\" title=\"w:IP address\"\u003EIP address\u003C/a\u003E will be publicly visible if you make any edits. \u003Ca href=\"/wiki/Special:UserLogin\" title=\"Special:UserLogin\"\u003ELogging in\u003C/a\u003E or \u003Ca href=\"/wiki/Special:CreateAccount\" title=\"Special:CreateAccount\"\u003Ecreating an account\u003C/a\u003E will conceal your IP address and provide you with many other \u003Ca href=\"https://en.wikipedia.org/wiki/Wikipedia:Why_create_an_account%3F\" class=\"extiw\" title=\"w:Wikipedia:Why create an account?\"\u003Ebenefits\u003C/a\u003E. Please do not save test edits. If you want to experiment, please use the \u003Ca href=\"/wiki/Commons:Sandbox\" title=\"Commons:Sandbox\"\u003ESandbox\u003C/a\u003E.\n\u003C/p\u003E","wbmiProtectionMsg":null,"wbmiShowIPEditingWarning":true,"wgCheckUserClientHintsHeadersJsApi":["brands","architecture","bitness","fullVersionList","mobile","model","platform","platformVersion"]};
RLSTATE={"ext.gadget.Long-Image-Names-in-Categories":"ready","ext.globalCssJs.user.styles":"ready","site.styles":"ready","user.styles":"ready","ext.globalCssJs.user":"ready","user":"ready","user.options":"loading","wikibase.alltargets":"ready","wikibase.desktop":"ready","jquery.wikibase.toolbar.styles":"ready","mediawiki.interface.helpers.styles":"ready","mediawiki.interface.helpers.linker.styles":"ready","mediawiki.action.view.filepage":"ready","skins.vector.search.codex.styles":"ready","skins.vector.styles":"ready","skins.vector.icons":"ready","filepage":"ready","ext.wikimediamessages.styles":"ready","ext.visualEditor.desktopArticleTarget.noscript":"ready","ext.uls.pt":"ready","wikibase.mediainfo.filepage.styles":"ready","wikibase.mediainfo.statements.styles":"ready"};RLPAGEMODULES=["ext.xLab","wikibase.entityPage.entityLoaded","wikibase.ui.entityViewInit","mediawiki.action.view.metadata","site","mediawiki.page.ready","Skins.vector.html","ext.centralNotice.geoIP","ext.centralNotice.startUp","ext.gadget.Slideshow","ext.gadget.ZoomViewer","ext.gadget.CollapsibleTemplates","ext.gadget.Stockphoto","ext.gadget.WatchlistNotice","ext.gadget.AjaxQuickDelete","ext.gadget.WikiMiniAtlas","ext.gadget.LanguageSelect","ext.gadget.PictureOfTheYearEnhancements","ext.urlShortener.toolbar","ext.centralauth.centralautologin","mmv.bootstrap","ext.visualEditor.desktopArticleTarget.init","ext.visualEditor.targetLoader","ext.echo.centralauth","ext.eventLogging","ext.wikimediaEvents","ext.wikimediaEvents.wikibase","ext.navigationTiming","ext.uls.compactlinks","ext.uls.interface","wikibase.mediainfo.filePageDisplay","ext.checkUser.clientHints"];</script>
<script>(RLQ=window.RLQ||[]).push(function(){mw.loader.impl(function(){return["user.options@12s5i",function($,jQuery,require,module){mw.user.tokens.set({"patrolToken":"+\\","watchToken":"+\\","csrfToken":"+\\"});
}];});});</script>
<link rel="stylesheet" href="../w/load02c7.css?lang=en&amp;modules=ext.uls.pt%7Cext.visualEditor.desktopArticleTarget.noscript%7Cext.wikimediamessages.styles%7Cfilepage%7Cjquery.wikibase.toolbar.styles%7Cmediawiki.action.view.filepage%7Cmediawiki.interface.helpers.linker.styles%7Cmediawiki.interface.helpers.styles%7Cskins.vector.icons%2Cstyles%7Cskins.vector.search.codex.styles%7Cwikibase.alltargets%2Cdesktop%7Cwikibase.mediainfo.filepage.styles%7Cwikibase.mediainfo.statements.styles&amp;only=styles&amp;skin=vector-2022">
<script async="" src="https://commons.wikimedia.org/w/load.php?lang=en&amp;modules=startup&amp;only=scripts&amp;raw=1&amp;skin=vector-2022"></script>
<meta name="ResourceLoaderDynamicStyles" content="">
<link rel="stylesheet" href="../w/load3e3b.css?lang=en&amp;modules=ext.gadget.Long-Image-Names-in-Categories&amp;only=styles&amp;skin=vector-2022">
<link rel="stylesheet" href="../w/loada24d.css?lang=en&amp;modules=site.styles&amp;only=styles&amp;skin=vector-2022">
<meta name="generator" content="MediaWiki 1.45.0-wmf.12">
<meta name="referrer" content="origin">
<meta name="referrer" content="origin-when-cross-origin">
<meta name="robots" content="max-image-preview:standard">
<meta name="format-detection" content="telephone=no">
<meta property="og:image" content="https://upload.wikimedia.org/wikipedia/commons/thumb/5/5f/Madafu-chopping.jpg/1200px-Madafu-chopping.jpg">
<meta property="og:image:width" content="1200">
<meta property="og:image:height" content="904">
<meta property="og:image" content="https://upload.wikimedia.org/wikipedia/commons/thumb/5/5f/Madafu-chopping.jpg/960px-Madafu-chopping.jpg">
<meta property="og:image:width" content="800">
<meta property="og:image:height" content="603">
<meta property="og:image:width" content="640">
<meta property="og:image:height" content="482">
<meta name="viewport" content="width=1120">
<meta property="og:title" content="File:Madafu-chopping.jpg - Wikimedia Commons">
<meta property="og:type" content="website">
<link rel="preconnect" href="../../upload.wikimedia.org/index.html">
<link rel="alternate" href="Special_EntityData/*********.json" type="application/json">
<link rel="alternate" href="Special_EntityData/*********.php" type="application/vnd.php.serialized">
<link rel="alternate" href="Special_EntityData/*********.n3" type="text/n3">
<link rel="alternate" href="Special_EntityData/*********.ttl" type="text/turtle">
<link rel="alternate" href="Special_EntityData/*********.nt" type="application/n-triples">
<link rel="alternate" href="Special_EntityData/*********.rdf" type="application/rdf+xml">
<link rel="alternate" href="Special_EntityData/*********.jsonld" type="application/ld+json">
<link rel="alternate" media="only screen and (max-width: 640px)" href="../../commons.m.wikimedia.org/wiki/File_Madafu-chopping.html">
<link rel="alternate" type="application/x-wiki" title="Edit" href="../w/indexcf0c.html?title=File:Madafu-chopping.jpg&amp;action=edit">
<link rel="apple-touch-icon" href="../static/apple-touch/commons.png">
<link rel="icon" href="../static/favicon/commons.ico">
<link rel="search" type="application/opensearchdescription+xml" href="../w/rest.php/v1/search" title="Wikimedia Commons">
<link rel="EditURI" type="application/rsd+xml" href="https://commons.wikimedia.org/w/api.php?action=rsd">
<link rel="canonical" href="File_Madafu-chopping.html">
<link rel="license" href="../../creativecommons.org/licenses/by-sa/4.0/index.html">
<link rel="alternate" type="application/atom+xml" title="Wikimedia Commons Atom feed" href="https://commons.wikimedia.org/w/api.php?hidebots=1&amp;hidecategorization=1&amp;hideWikibase=1&amp;translations=filter&amp;urlversion=1&amp;days=7&amp;limit=50&amp;action=feedrecentchanges&amp;feedformat=atom">
<link rel="dns-prefetch" href="../../meta.wikimedia.org/index.html" />
<link rel="dns-prefetch" href="Auth.wikimedia.html">
</head>
<body class="skin--responsive skin-vector skin-vector-search-vue mediawiki ltr sitedir-ltr mw-hide-empty-elt ns-6 ns-subject mw-editable page-File_Madafu-chopping_jpg rootpage-File_Madafu-chopping_jpg skin-vector-2022 action-view wb-entitypage wb-mediainfopage wb-mediainfopage-*********"><a class="mw-jump-link" href="#bodyContent">Jump to content</a>
<div class="vector-header-container">
	<header class="vector-header mw-header no-font-mode-scale">
		<div class="vector-header-start">
			<nav class="vector-main-menu-landmark" aria-label="Site">
				
<div id="vector-main-menu-dropdown" class="vector-dropdown vector-main-menu-dropdown vector-button-flush-left vector-button-flush-right"  title="Main menu" >
	<input type="checkbox" id="vector-main-menu-dropdown-checkbox" role="button" aria-haspopup="true" data-event-name="ui.dropdown-vector-main-menu-dropdown" class="vector-dropdown-checkbox "  aria-label="Main menu"  >
	<label id="vector-main-menu-dropdown-label" for="vector-main-menu-dropdown-checkbox" class="vector-dropdown-label cdx-button cdx-button--fake-button cdx-button--fake-button--enabled cdx-button--weight-quiet cdx-button--icon-only " aria-hidden="true"  ><span class="vector-icon mw-ui-icon-menu mw-ui-icon-wikimedia-menu"></span>

<span class="vector-dropdown-label-text">Main menu</span>
	</label>
	<div class="vector-dropdown-content">


				<div id="vector-main-menu-unpinned-container" class="vector-unpinned-container">
		
<div id="vector-main-menu" class="vector-main-menu vector-pinnable-element">
	<div
	class="vector-pinnable-header vector-main-menu-pinnable-header vector-pinnable-header-unpinned"
	data-feature-name="main-menu-pinned"
	data-pinnable-element-id="vector-main-menu"
	data-pinned-container-id="vector-main-menu-pinned-container"
	data-unpinned-container-id="vector-main-menu-unpinned-container"
>
	<div class="vector-pinnable-header-label">Main menu</div>
	<button class="vector-pinnable-header-toggle-button vector-pinnable-header-pin-button" data-event-name="pinnable-header.vector-main-menu.pin">move to sidebar</button>
	<button class="vector-pinnable-header-toggle-button vector-pinnable-header-unpin-button" data-event-name="pinnable-header.vector-main-menu.unpin">hide</button>
</div>

	
<div id="p-navigation" class="vector-menu mw-portlet mw-portlet-navigation"  >
	<div class="vector-menu-heading">
		Navigate
	</div>
	<div class="vector-menu-content">
		
		<ul class="vector-menu-content-list">
			
			<li id="n-mainpage-description" class="mw-list-item"><a href="Main_Page.html" title="Visit the main page [z]" accesskey="z"><span>Main page</span></a></li><li id="n-welcome" class="mw-list-item"><a href="Commons_Welcome.html"><span>Welcome</span></a></li><li id="n-portal" class="mw-list-item"><a href="Commons_Community_portal.html" title="About the project, what you can do, where to find things"><span>Community portal</span></a></li><li id="n-village-pump" class="mw-list-item"><a href="Commons_Village_pump.html"><span>Village pump</span></a></li><li id="n-help" class="mw-list-item"><a href="Special_MyLanguage/Help_Contents.html" title="The place to find out"><span>Help center</span></a></li>
		</ul>
		
	</div>
</div>

	
	
<div id="p-participate" class="vector-menu mw-portlet mw-portlet-participate"  >
	<div class="vector-menu-heading">
		Participate
	</div>
	<div class="vector-menu-content">
		
		<ul class="vector-menu-content-list">
			
			<li id="n-uploadbtn" class="mw-list-item"><a href="Special_UploadWizard.html"><span>Upload file</span></a></li><li id="n-recentchanges" class="mw-list-item"><a href="Special_RecentChanges.html" title="A list of recent changes in the wiki [r]" accesskey="r"><span>Recent changes</span></a></li><li id="n-latestfiles" class="mw-list-item"><a href="Special_NewFiles.html"><span>Latest files</span></a></li><li id="n-randomimage" class="mw-list-item"><a href="File_Corliss_valvegear%2c_Gordon%27s_improved_(New_Catechism_of_the_Steam_Engine%2c_1904).html" title="Load a random file [x]" accesskey="x"><span>Random file</span></a></li><li id="n-contact" class="mw-list-item"><a href="Commons_Contact_us.html"><span>Contact us</span></a></li><li id="n-specialpages" class="mw-list-item"><a href="Special_SpecialPages.html"><span>Special pages</span></a></li>
		</ul>
		
	</div>
</div>

	
<div id="vector-main-menu" class="vector-menu "  >
	<div class="vector-menu-heading">
		
	</div>
	<div class="vector-menu-content">
		
		<ul class="vector-menu-content-list">
			
			
		</ul>
		
	</div>
</div>

</div>

				</div>

	</div>
</div>

		</nav>
			
<a href="Main_Page.html" class="mw-logo">
	<img class="mw-logo-icon" src="../static/images/icons/commonswiki.svg" alt="" aria-hidden="true" height="50" width="50">
	<span class="mw-logo-container skin-invert">
		<img class="mw-logo-wordmark" alt="Wikimedia Commons" src="../static/images/mobile/copyright/commonswiki-wordmark.svg" style="width: 7.1875em; height: 2em;">
	</span>
</a>

		</div>
		<div class="vector-header-end">
			
<div id="p-search" role="search" class="vector-search-box-vue  vector-search-box-collapses vector-search-box">
	<a href="Special_MediaSearch.html" class="cdx-button cdx-button--fake-button cdx-button--fake-button--enabled cdx-button--weight-quiet cdx-button--icon-only search-toggle" title="Search Wikimedia Commons [f]" accesskey="f"><span class="vector-icon mw-ui-icon-search mw-ui-icon-wikimedia-search"></span>

<span>Search</span>
	</a>
	<div class="vector-typeahead-search-container">
		<div class="cdx-typeahead-search">
			<form action="https://commons.wikimedia.org/w/index.php" id="searchform" class="cdx-search-input cdx-search-input--has-end-button">
				<div id="simpleSearch" class="cdx-search-input__input-wrapper"  data-search-loc="header-moved">
					<div class="cdx-text-input cdx-text-input--has-start-icon">
						<input
							class="cdx-text-input__input mw-searchInput" autocomplete="off"
							 type="search" name="search" placeholder="Search Wikimedia Commons" aria-label="Search Wikimedia Commons" autocapitalize="sentences" spellcheck="false" title="Search Wikimedia Commons [f]" accesskey="f" id="searchInput"
							>
						<span class="cdx-text-input__icon cdx-text-input__start-icon"></span>
					</div>
					<input type="hidden" name="title" value="Special:MediaSearch">
				</div>
				<button class="cdx-button cdx-search-input__end-button">Search</button>
			</form>
		</div>
	</div>
</div>

			<nav class="vector-user-links vector-user-links-wide" aria-label="Personal tools">
	<div class="vector-user-links-main">
	
<div id="p-vector-user-menu-preferences" class="vector-menu mw-portlet"  >
	<div class="vector-menu-content">
		
		<ul class="vector-menu-content-list">
			<li id="ca-uls" class="mw-list-item active user-links-collapsible-item"><a data-mw="interface" href="#" class="uls-trigger cdx-button cdx-button--fake-button cdx-button--fake-button--enabled cdx-button--weight-quiet"><span class="vector-icon mw-ui-icon-wikimedia-language mw-ui-icon-wikimedia-wikimedia-language"></span>

<span>English</span></a>
</li>

			
		</ul>
		
	</div>
</div>

	
<div id="p-vector-user-menu-userpage" class="vector-menu mw-portlet emptyPortlet"  >
	<div class="vector-menu-content">
		
		<ul class="vector-menu-content-list">
			
			
		</ul>
		
	</div>
</div>

	<nav class="vector-appearance-landmark" aria-label="Appearance">
		
<div id="vector-appearance-dropdown" class="vector-dropdown "  title="Change the appearance of the page&#039;s font size, width, and color" >
	<input type="checkbox" id="vector-appearance-dropdown-checkbox" role="button" aria-haspopup="true" data-event-name="ui.dropdown-vector-appearance-dropdown" class="vector-dropdown-checkbox "  aria-label="Appearance"  >
	<label id="vector-appearance-dropdown-label" for="vector-appearance-dropdown-checkbox" class="vector-dropdown-label cdx-button cdx-button--fake-button cdx-button--fake-button--enabled cdx-button--weight-quiet cdx-button--icon-only " aria-hidden="true"  ><span class="vector-icon mw-ui-icon-appearance mw-ui-icon-wikimedia-appearance"></span>

<span class="vector-dropdown-label-text">Appearance</span>
	</label>
	<div class="vector-dropdown-content">


			<div id="vector-appearance-unpinned-container" class="vector-unpinned-container">
				
			</div>
		
	</div>
</div>

	</nav>
	
<div id="p-vector-user-menu-notifications" class="vector-menu mw-portlet emptyPortlet"  >
	<div class="vector-menu-content">
		
		<ul class="vector-menu-content-list">
			
			
		</ul>
		
	</div>
</div>

	
<div id="p-vector-user-menu-overflow" class="vector-menu mw-portlet"  >
	<div class="vector-menu-content">
		
		<ul class="vector-menu-content-list">
			<li id="pt-sitesupport-2" class="user-links-collapsible-item mw-list-item user-links-collapsible-item"><a data-mw="interface" href="https://donate.wikimedia.org/?wmf_source=donate&amp;wmf_medium=sidebar&amp;wmf_campaign=commons.wikimedia.org&amp;uselang=en" class=""><span>Donate</span></a>
</li>
<li id="pt-createaccount-2" class="user-links-collapsible-item mw-list-item user-links-collapsible-item"><a data-mw="interface" href="https://auth.wikimedia.org/loginwiki/wiki/Special:CentralAutoLogin/checkLoggedIn?useformat=desktop&amp;type=redirect&amp;returnUrlToken=d75cc1216a9df64be9fc087dbd5bb6ac&amp;wikiid=commonswiki&amp;usesul3=1&amp;useformat=desktop" title="You are encouraged to create an account and log in; however, it is not mandatory" class=""><span>Create account</span></a>
</li>
<li id="pt-login-2" class="user-links-collapsible-item mw-list-item user-links-collapsible-item"><a data-mw="interface" href="https://auth.wikimedia.org/commonswiki/wiki/Special:UserLogin?useformat=desktop&amp;usesul3=1&amp;returnto=File%3AMadafu-chopping.jpg&amp;centralauthLoginToken=9924b691560f40c4019dbb105e98845a" title="You are encouraged to log in; however, it is not mandatory [o]" accesskey="o" class=""><span>Log in</span></a>
</li>

			
		</ul>
		
	</div>
</div>

	</div>
	
<div id="vector-user-links-dropdown" class="vector-dropdown vector-user-menu vector-button-flush-right vector-user-menu-logged-out"  title="More options" >
	<input type="checkbox" id="vector-user-links-dropdown-checkbox" role="button" aria-haspopup="true" data-event-name="ui.dropdown-vector-user-links-dropdown" class="vector-dropdown-checkbox "  aria-label="Personal tools"  >
	<label id="vector-user-links-dropdown-label" for="vector-user-links-dropdown-checkbox" class="vector-dropdown-label cdx-button cdx-button--fake-button cdx-button--fake-button--enabled cdx-button--weight-quiet cdx-button--icon-only " aria-hidden="true"  ><span class="vector-icon mw-ui-icon-ellipsis mw-ui-icon-wikimedia-ellipsis"></span>

<span class="vector-dropdown-label-text">Personal tools</span>
	</label>
	<div class="vector-dropdown-content">


		
<div id="p-personal" class="vector-menu mw-portlet mw-portlet-personal user-links-collapsible-item"  title="User menu" >
	<div class="vector-menu-content">
		
		<ul class="vector-menu-content-list">
			
			<li id="pt-sitesupport" class="user-links-collapsible-item mw-list-item"><a href="https://donate.wikimedia.org/?wmf_source=donate&amp;wmf_medium=sidebar&amp;wmf_campaign=commons.wikimedia.org&amp;uselang=en"><span>Donate</span></a></li><li id="pt-createaccount" class="user-links-collapsible-item mw-list-item"><a href="https://auth.wikimedia.org/loginwiki/wiki/Special:CentralAutoLogin/checkLoggedIn?useformat=desktop&amp;type=redirect&amp;returnUrlToken=d75cc1216a9df64be9fc087dbd5bb6ac&amp;wikiid=commonswiki&amp;usesul3=1&amp;useformat=desktop" title="You are encouraged to create an account and log in; however, it is not mandatory"><span class="vector-icon mw-ui-icon-userAdd mw-ui-icon-wikimedia-userAdd"></span> <span>Create account</span></a></li><li id="pt-login" class="user-links-collapsible-item mw-list-item"><a href="https://auth.wikimedia.org/commonswiki/wiki/Special:UserLogin?useformat=desktop&amp;usesul3=1&amp;returnto=File%3AMadafu-chopping.jpg&amp;centralauthLoginToken=9924b691560f40c4019dbb105e98845a" title="You are encouraged to log in; however, it is not mandatory [o]" accesskey="o"><span class="vector-icon mw-ui-icon-logIn mw-ui-icon-wikimedia-logIn"></span> <span>Log in</span></a></li>
		</ul>
		
	</div>
</div>

<div id="p-user-menu-anon-editor" class="vector-menu mw-portlet mw-portlet-user-menu-anon-editor"  >
	<div class="vector-menu-heading">
		Pages for logged out editors <a href="Help_Introduction.html" aria-label="Learn more about editing"><span>learn more</span></a>
	</div>
	<div class="vector-menu-content">
		
		<ul class="vector-menu-content-list">
			
			<li id="pt-anoncontribs" class="mw-list-item"><a href="Special_MyContributions.html" title="A list of edits made from this IP address [y]" accesskey="y"><span>Contributions</span></a></li><li id="pt-anontalk" class="mw-list-item"><a href="Special_MyTalk.html" title="Discussion about edits from this IP address [n]" accesskey="n"><span>Talk</span></a></li>
		</ul>
		
	</div>
</div>

	
	</div>
</div>

</nav>

		</div>
	</header>
</div>
<div class="mw-page-container">
	<div class="mw-page-container-inner">
		<div class="vector-sitenotice-container">
			<div id="siteNotice"><!-- CentralNotice --></div>
		</div>
		<div class="vector-column-start">
			<div class="vector-main-menu-container">
		<div id="mw-navigation">
			<nav id="mw-panel" class="vector-main-menu-landmark" aria-label="Site">
				<div id="vector-main-menu-pinned-container" class="vector-pinned-container">
				
				</div>
		</nav>
		</div>
	</div>
	<div class="vector-sticky-pinned-container">
				<nav id="mw-panel-toc" aria-label="Contents" data-event-name="ui.sidebar-toc" class="mw-table-of-contents-container vector-toc-landmark">
					<div id="vector-toc-pinned-container" class="vector-pinned-container">
					<div id="vector-toc" class="vector-toc vector-pinnable-element">
	<div
	class="vector-pinnable-header vector-toc-pinnable-header vector-pinnable-header-pinned"
	data-feature-name="toc-pinned"
	data-pinnable-element-id="vector-toc"
	
	
>
	<h2 class="vector-pinnable-header-label">Contents</h2>
	<button class="vector-pinnable-header-toggle-button vector-pinnable-header-pin-button" data-event-name="pinnable-header.vector-toc.pin">move to sidebar</button>
	<button class="vector-pinnable-header-toggle-button vector-pinnable-header-unpin-button" data-event-name="pinnable-header.vector-toc.unpin">hide</button>
</div>


	<ul class="vector-toc-contents" id="mw-panel-toc-list">
		<li id="toc-mw-content-text"
			class="vector-toc-list-item vector-toc-level-1">
			<a href="#" class="vector-toc-link">
				<div class="vector-toc-text">Beginning</div>
			</a>
		</li>
		<li id="toc-Summary"
		class="vector-toc-list-item vector-toc-level-1 vector-toc-list-item-expanded">
		<a class="vector-toc-link" href="#Summary">
			<div class="vector-toc-text">
				<span class="vector-toc-numb">1</span>
				<span>Summary</span>
			</div>
		</a>
		
		<ul id="toc-Summary-sublist" class="vector-toc-list">
		</ul>
	</li>
	<li id="toc-Licensing"
		class="vector-toc-list-item vector-toc-level-1 vector-toc-list-item-expanded">
		<a class="vector-toc-link" href="#Licensing">
			<div class="vector-toc-text">
				<span class="vector-toc-numb">2</span>
				<span>Licensing</span>
			</div>
		</a>
		
		<ul id="toc-Licensing-sublist" class="vector-toc-list">
		</ul>
	</li>
</ul>
</div>

					</div>
		</nav>
			</div>
		</div>
		<div class="mw-content-container">
			<main id="content" class="mw-body">
				<header class="mw-body-header vector-page-titlebar no-font-mode-scale">
					<nav aria-label="Contents" class="vector-toc-landmark">
						
<div id="vector-page-titlebar-toc" class="vector-dropdown vector-page-titlebar-toc vector-button-flush-left"  title="Table of Contents" >
	<input type="checkbox" id="vector-page-titlebar-toc-checkbox" role="button" aria-haspopup="true" data-event-name="ui.dropdown-vector-page-titlebar-toc" class="vector-dropdown-checkbox "  aria-label="Toggle the table of contents"  >
	<label id="vector-page-titlebar-toc-label" for="vector-page-titlebar-toc-checkbox" class="vector-dropdown-label cdx-button cdx-button--fake-button cdx-button--fake-button--enabled cdx-button--weight-quiet cdx-button--icon-only " aria-hidden="true"  ><span class="vector-icon mw-ui-icon-listBullet mw-ui-icon-wikimedia-listBullet"></span>

<span class="vector-dropdown-label-text">Toggle the table of contents</span>
	</label>
	<div class="vector-dropdown-content">


							<div id="vector-page-titlebar-toc-unpinned-container" class="vector-unpinned-container">
			</div>
		
	</div>
</div>

					</nav>
					<h1 id="firstHeading" class="firstHeading mw-first-heading"><span class="mw-page-title-namespace">File</span><span class="mw-page-title-separator">:</span><span class="mw-page-title-main">Madafu-chopping.jpg</span></h1>
						<div class="mw-indicators">
		</div>
</header>
				<div class="vector-page-toolbar vector-feature-custom-font-size-clientpref--excluded">
					<div class="vector-page-toolbar-container">
						<div id="left-navigation">
							<nav aria-label="Namespaces">
								
<div id="p-associated-pages" class="vector-menu vector-menu-tabs mw-portlet mw-portlet-associated-pages"  >
	<div class="vector-menu-content">
		
		<ul class="vector-menu-content-list">
			
			<li id="ca-nstab-image" class="selected vector-tab-noicon mw-list-item"><a href="File_Madafu-chopping.html" title="View the file page [c]" accesskey="c"><span>File</span></a></li><li id="ca-talk" class="new vector-tab-noicon mw-list-item"><a href="https://commons.wikimedia.org/w/index.php?title=File_talk:Madafu-chopping.jpg&amp;action=edit&amp;redlink=1" rel="discussion" class="new" title="Discussion about the content page (page does not exist) [t]" accesskey="t"><span>Discussion</span></a></li>
		</ul>
		
	</div>
</div>

								
<div id="vector-variants-dropdown" class="vector-dropdown emptyPortlet"  >
	<input type="checkbox" id="vector-variants-dropdown-checkbox" role="button" aria-haspopup="true" data-event-name="ui.dropdown-vector-variants-dropdown" class="vector-dropdown-checkbox " aria-label="Change language variant"   >
	<label id="vector-variants-dropdown-label" for="vector-variants-dropdown-checkbox" class="vector-dropdown-label cdx-button cdx-button--fake-button cdx-button--fake-button--enabled cdx-button--weight-quiet" aria-hidden="true"  ><span class="vector-dropdown-label-text">English</span>
	</label>
	<div class="vector-dropdown-content">


					
<div id="p-variants" class="vector-menu mw-portlet mw-portlet-variants emptyPortlet"  >
	<div class="vector-menu-content">
		
		<ul class="vector-menu-content-list">
			
			
		</ul>
		
	</div>
</div>

				
	</div>
</div>

							</nav>
						</div>
						<div id="right-navigation" class="vector-collapsible">
							<nav aria-label="Views">
								
<div id="p-views" class="vector-menu vector-menu-tabs mw-portlet mw-portlet-views"  >
	<div class="vector-menu-content">
		
		<ul class="vector-menu-content-list">
			
			<li id="ca-view" class="selected vector-tab-noicon mw-list-item"><a href="File_Madafu-chopping.html"><span>Read</span></a></li><li id="ca-edit" class="vector-tab-noicon mw-list-item"><a href="../w/indexcf0c.html?title=File:Madafu-chopping.jpg&amp;action=edit" title="Edit this page [e]" accesskey="e"><span>Edit</span></a></li><li id="ca-history" class="vector-tab-noicon mw-list-item"><a href="https://commons.wikimedia.org/w/index.php?title=File:Madafu-chopping.jpg&amp;action=history" title="Past revisions of this page [h]" accesskey="h"><span>View history</span></a></li>
		</ul>
		
	</div>
</div>

							</nav>
				
							<nav class="vector-page-tools-landmark" aria-label="Page tools">
								
<div id="vector-page-tools-dropdown" class="vector-dropdown vector-page-tools-dropdown"  >
	<input type="checkbox" id="vector-page-tools-dropdown-checkbox" role="button" aria-haspopup="true" data-event-name="ui.dropdown-vector-page-tools-dropdown" class="vector-dropdown-checkbox "  aria-label="Tools"  >
	<label id="vector-page-tools-dropdown-label" for="vector-page-tools-dropdown-checkbox" class="vector-dropdown-label cdx-button cdx-button--fake-button cdx-button--fake-button--enabled cdx-button--weight-quiet" aria-hidden="true"  ><span class="vector-dropdown-label-text">Tools</span>
	</label>
	<div class="vector-dropdown-content">


									<div id="vector-page-tools-unpinned-container" class="vector-unpinned-container">
						
<div id="vector-page-tools" class="vector-page-tools vector-pinnable-element">
	<div
	class="vector-pinnable-header vector-page-tools-pinnable-header vector-pinnable-header-unpinned"
	data-feature-name="page-tools-pinned"
	data-pinnable-element-id="vector-page-tools"
	data-pinned-container-id="vector-page-tools-pinned-container"
	data-unpinned-container-id="vector-page-tools-unpinned-container"
>
	<div class="vector-pinnable-header-label">Tools</div>
	<button class="vector-pinnable-header-toggle-button vector-pinnable-header-pin-button" data-event-name="pinnable-header.vector-page-tools.pin">move to sidebar</button>
	<button class="vector-pinnable-header-toggle-button vector-pinnable-header-unpin-button" data-event-name="pinnable-header.vector-page-tools.unpin">hide</button>
</div>

	
<div id="p-cactions" class="vector-menu mw-portlet mw-portlet-cactions emptyPortlet vector-has-collapsible-items"  title="More options" >
	<div class="vector-menu-heading">
		Actions
	</div>
	<div class="vector-menu-content">
		
		<ul class="vector-menu-content-list">
			
			<li id="ca-more-view" class="selected vector-more-collapsible-item mw-list-item"><a href="File_Madafu-chopping.html"><span>Read</span></a></li><li id="ca-more-edit" class="vector-more-collapsible-item mw-list-item"><a href="../w/indexcf0c.html?title=File:Madafu-chopping.jpg&amp;action=edit" title="Edit this page [e]" accesskey="e"><span>Edit</span></a></li><li id="ca-more-history" class="vector-more-collapsible-item mw-list-item"><a href="https://commons.wikimedia.org/w/index.php?title=File:Madafu-chopping.jpg&amp;action=history"><span>View history</span></a></li>
		</ul>
		
	</div>
</div>

<div id="p-tb" class="vector-menu mw-portlet mw-portlet-tb"  >
	<div class="vector-menu-heading">
		General
	</div>
	<div class="vector-menu-content">
		
		<ul class="vector-menu-content-list">
			
			<li id="t-whatlinkshere" class="mw-list-item"><a href="Special_WhatLinksHere/File_Madafu-chopping.html" title="A list of all wiki pages that link here [j]" accesskey="j"><span>What links here</span></a></li><li id="t-recentchangeslinked" class="mw-list-item"><a href="Special_RecentChangesLinked/File_Madafu-chopping.html" rel="nofollow" title="Recent changes in pages linked from this page [k]" accesskey="k"><span>Related changes</span></a></li><li id="t-permalink" class="mw-list-item"><a href="https://commons.wikimedia.org/w/index.php?title=File:Madafu-chopping.jpg&amp;oldid=1033630111" title="Permanent link to this revision of this page"><span>Permanent link</span></a></li><li id="t-info" class="mw-list-item"><a href="https://commons.wikimedia.org/w/index.php?title=File:Madafu-chopping.jpg&amp;action=info" title="More information about this page"><span>Page information</span></a></li><li id="t-cite" class="mw-list-item"><a href="https://commons.wikimedia.org/w/index.php?title=Special:CiteThisPage&amp;page=File%3AMadafu-chopping.jpg&amp;id=1033630111&amp;wpFormIdentifier=titleform" title="Information on how to cite this page"><span>Cite this page</span></a></li><li id="t-urlshortener" class="mw-list-item"><a href="../w/index1bf2.html?title=Special:UrlShortener&amp;url=https%3A%2F%2Fcommons.wikimedia.org%2Fwiki%2FFile%3AMadafu-chopping.jpg"><span>Get shortened URL</span></a></li><li id="t-urlshortener-qrcode" class="mw-list-item"><a href="../w/index16fc.html?title=Special:QrCode&amp;url=https%3A%2F%2Fcommons.wikimedia.org%2Fwiki%2FFile%3AMadafu-chopping.jpg"><span>Download QR code</span></a></li><li id="t-wb-concept-uri" class="mw-list-item"><a href="https://commons.wikimedia.org/entity/*********" title="URI that identifies the concept described by this Item"><span>Concept URI</span></a></li>
		</ul>
		
	</div>
</div>

<div id="p-electronpdfservice-sidebar-portlet-heading" class="vector-menu mw-portlet mw-portlet-electronpdfservice-sidebar-portlet-heading"  >
	<div class="vector-menu-heading">
		Print/export
	</div>
	<div class="vector-menu-content">
		
		<ul class="vector-menu-content-list">
			
			<li id="electron-print_pdf" class="mw-list-item"><a href="https://commons.wikimedia.org/w/index.php?title=Special:DownloadAsPdf&amp;page=File%3AMadafu-chopping.jpg&amp;action=show-download-screen"><span>Download as PDF</span></a></li><li id="t-print" class="mw-list-item"><a href="javascript:print();" rel="alternate" title="Printable version of this page [p]" accesskey="p"><span>Printable version</span></a></li>
		</ul>
		
	</div>
</div>

<div id="p-wikibase-otherprojects" class="vector-menu mw-portlet mw-portlet-wikibase-otherprojects emptyPortlet"  >
	<div class="vector-menu-heading">
		In other projects
	</div>
	<div class="vector-menu-content">
		
		<ul class="vector-menu-content-list">
			
			
		</ul>
		
	</div>
</div>

</div>

									</div>
				
	</div>
</div>

							</nav>
						</div>
					</div>
				</div>
				<div class="vector-column-end no-font-mode-scale">
					<div class="vector-sticky-pinned-container">
						<nav class="vector-page-tools-landmark" aria-label="Page tools">
							<div id="vector-page-tools-pinned-container" class="vector-pinned-container">
				
							</div>
		</nav>
						<nav class="vector-appearance-landmark" aria-label="Appearance">
							<div id="vector-appearance-pinned-container" class="vector-pinned-container">
				<div id="vector-appearance" class="vector-appearance vector-pinnable-element">
	<div
	class="vector-pinnable-header vector-appearance-pinnable-header vector-pinnable-header-pinned"
	data-feature-name="appearance-pinned"
	data-pinnable-element-id="vector-appearance"
	data-pinned-container-id="vector-appearance-pinned-container"
	data-unpinned-container-id="vector-appearance-unpinned-container"
>
	<div class="vector-pinnable-header-label">Appearance</div>
	<button class="vector-pinnable-header-toggle-button vector-pinnable-header-pin-button" data-event-name="pinnable-header.vector-appearance.pin">move to sidebar</button>
	<button class="vector-pinnable-header-toggle-button vector-pinnable-header-unpin-button" data-event-name="pinnable-header.vector-appearance.unpin">hide</button>
</div>


</div>

							</div>
		</nav>
					</div>
				</div>
				<div id="bodyContent" class="vector-body" aria-labelledby="firstHeading" data-mw-ve-target-container>
					<div class="vector-body-before-content">
					
						<div id="siteSub" class="noprint">From Wikimedia Commons, the free media repository</div>
					</div>
					<div id="contentSub"><div id="mw-content-subtitle"></div></div>
					
					
					<div id="mw-content-text" class="mw-body-content"><ul id="filetoc" role="navigation"><li><a href="#file">File</a></li>
<li><a href="#filehistory">File history</a></li>
<li><a href="#filelinks">File usage on Commons</a></li>
<li><a href="#metadata">Metadata</a></li></ul><div class="fullImageLink" id="file"><a href="../../upload.wikimedia.org/wikipedia/commons/5/5f/Madafu-chopping.jpg"><img alt="File:Madafu-chopping.jpg" src="../../upload.wikimedia.org/wikipedia/commons/thumb/5/5f/Madafu-chopping.jpg/960px-Madafu-chopping2de9.jpg?20141104110941" decoding="async" width="796" height="600" srcset="https://upload.wikimedia.org/wikipedia/commons/thumb/5/5f/Madafu-chopping.jpg/1194px-Madafu-chopping.jpg?20141104110941 1.5x, https://upload.wikimedia.org/wikipedia/commons/thumb/5/5f/Madafu-chopping.jpg/1592px-Madafu-chopping.jpg?20141104110941 2x" data-file-width="4544" data-file-height="3424"></a><div class="mw-filepage-resolutioninfo">Size of this preview: <a href="../../upload.wikimedia.org/wikipedia/commons/thumb/5/5f/Madafu-chopping.jpg/960px-Madafu-chopping.jpg" class="mw-thumbnail-link">796 × 600 pixels</a>. <span class="mw-filepage-other-resolutions">Other resolutions: <a href="../../upload.wikimedia.org/wikipedia/commons/thumb/5/5f/Madafu-chopping.jpg/330px-Madafu-chopping.jpg" class="mw-thumbnail-link">319 × 240 pixels</a> | <a href="../../upload.wikimedia.org/wikipedia/commons/thumb/5/5f/Madafu-chopping.jpg/960px-Madafu-chopping.jpg" class="mw-thumbnail-link">637 × 480 pixels</a> | <a href="../../upload.wikimedia.org/wikipedia/commons/thumb/5/5f/Madafu-chopping.jpg/1019px-Madafu-chopping.jpg" class="mw-thumbnail-link">1,019 × 768 pixels</a> | <a href="../../upload.wikimedia.org/wikipedia/commons/thumb/5/5f/Madafu-chopping.jpg/1280px-Madafu-chopping.jpg" class="mw-thumbnail-link">1,280 × 965 pixels</a> | <a href="../../upload.wikimedia.org/wikipedia/commons/thumb/5/5f/Madafu-chopping.jpg/2560px-Madafu-chopping.jpg" class="mw-thumbnail-link">2,560 × 1,929 pixels</a> | <a href="../../upload.wikimedia.org/wikipedia/commons/5/5f/Madafu-chopping.jpg" class="mw-thumbnail-link">4,544 × 3,424 pixels</a>.</span></div></div>
<div class="fullMedia"><bdi dir="ltr"><a href="../../upload.wikimedia.org/wikipedia/commons/5/5f/Madafu-chopping.jpg" class="internal" title="Madafu-chopping.jpg">Original file</a></bdi> <span class="fileInfo">(4,544 × 3,424 pixels, file size: 7.56 MB, MIME type: <span class="mime-type">image/jpeg</span>)</span></div><div class='wbmi-tabs-container oo-ui-layout oo-ui-panelLayout'><div id='ooui-php-1' class='wbmi-tabs oo-ui-layout oo-ui-menuLayout oo-ui-menuLayout-static oo-ui-menuLayout-top oo-ui-menuLayout-showMenu oo-ui-indexLayout' data-ooui='{"_":"OO.ui.IndexLayout","classes":["wbmi-tabs"],"expanded":false,"menuPanel":{"tag":"ooui-php-2"},"contentPanel":{"tag":"ooui-php-3"},"autoFocus":false,"tabPanels":{"wikiTextPlusCaptions":{"tag":"ooui-php-4"},"statements":{"tag":"ooui-php-5"}},"tabSelectWidget":{"tag":"ooui-php-6"}}'><div aria-hidden='false' class='oo-ui-menuLayout-menu'><div id='ooui-php-2' class='oo-ui-layout oo-ui-panelLayout oo-ui-indexLayout-tabPanel' data-ooui='{"_":"OO.ui.PanelLayout","preserveContent":false,"expanded":false}'><div role='tablist' aria-multiselectable='false' tabindex='0' id='ooui-php-6' class='oo-ui-selectWidget oo-ui-selectWidget-unpressed oo-ui-widget oo-ui-widget-enabled oo-ui-tabSelectWidget oo-ui-tabSelectWidget-frameless' data-ooui='{"_":"OO.ui.TabSelectWidget","framed":false,"items":[{"tag":"ooui-php-7"},{"tag":"ooui-php-8"}]}'><div aria-selected='true' role='tab' id='ooui-php-7' class='oo-ui-widget oo-ui-widget-enabled oo-ui-labelElement oo-ui-optionWidget oo-ui-tabOptionWidget oo-ui-optionWidget-selected' data-ooui='{"_":"OO.ui.TabOptionWidget","selected":true,"label":"File information","data":"wikiTextPlusCaptions"}'><span class='oo-ui-labelElement-label'>File information</span></div><div aria-selected='false' role='tab' id='ooui-php-8' class='oo-ui-widget oo-ui-widget-enabled oo-ui-labelElement oo-ui-optionWidget oo-ui-tabOptionWidget' data-ooui='{"_":"OO.ui.TabOptionWidget","label":"Structured data","data":"statements"}'><span class='oo-ui-labelElement-label'>Structured data</span></div></div></div></div><div class='oo-ui-menuLayout-content'><div id='ooui-php-3' class='oo-ui-layout oo-ui-panelLayout oo-ui-stackLayout oo-ui-indexLayout-stackLayout' data-ooui='{"_":"OO.ui.StackLayout","preserveContent":false,"expanded":false,"items":[{"tag":"ooui-php-4"},{"tag":"ooui-php-5"}]}'><div role='tabpanel' id='ooui-php-4' class='wbmi-tab oo-ui-layout oo-ui-panelLayout oo-ui-panelLayout-scrollable oo-ui-tabPanelLayout oo-ui-tabPanelLayout-active' data-ooui='{"_":"OO.ui.TabPanelLayout","name":"wikiTextPlusCaptions","label":"File information","scrollable":true,"expanded":false,"classes":["wbmi-tab"]}'><h2 class="wbmi-captions-header">Captions</h2><div class="wbmi-entityview-captionsPanel oo-ui-layout oo-ui-panelLayout oo-ui-panelLayout-framed"><h3 class="wbmi-entityview-captions-header">Captions</h3><div class="wbmi-entityview-caption oo-ui-layout oo-ui-horizontalLayout"><label class="wbmi-language-label oo-ui-widget oo-ui-widget-enabled oo-ui-labelElement-label oo-ui-labelElement oo-ui-labelWidget">English</label><div lang="en" dir="ltr" class="wbmi-caption-value wbmi-entityview-emptyCaption">Add a one-line explanation of what this file represents</div></div></div><div id="mw-imagepage-content"><div class="mw-content-ltr mw-parser-output" lang="en" dir="ltr"><div class="mw-heading mw-heading2"><h2 id="Summary">Summary</h2><span class="mw-editsection"><span class="mw-editsection-bracket">[</span><a href="https://commons.wikimedia.org/w/index.php?title=File:Madafu-chopping.jpg&amp;action=edit&amp;section=1" title="Edit section: Summary"><span>edit</span></a><span class="mw-editsection-bracket">]</span></span></div>
<div class="hproduct commons-file-information-table">
<table class="fileinfotpl-type-information vevent" dir="ltr">

<tbody><tr>
<td id="fileinfotpl_desc" class="fileinfo-paramfield" lang="en">Description<span class="summary fn" style="display:none">Madafu-chopping.jpg</span></td>
<td class="description">
<div class="description en" dir="ltr" lang="en"><span class="language en" title="English"><b>English: </b></span> Madafu</div><div class="description ig" dir="ltr" lang="ig"><span class="language ig" title="Igbo"><b>Igbo: </b></span> Akụ oyibo</div>
<table cellspacing="0" style="width:425px; text-align:left; color:#000; background:#ddd; border:1px solid #bbb; margin:1px;" dir="ltr" class="layouttemplate">
<tbody><tr>
<td style="width:22px; height:22px;"><span typeof="mw:File"><a href="File_Emoji_u1f35c.html" class="mw-file-description" title="Food icon"><img alt="Food icon" src="../../upload.wikimedia.org/wikipedia/commons/thumb/7/77/Emoji_u1f35c.svg/40px-Emoji_u1f35c.svg.png" decoding="async" width="40" height="40" class="mw-file-element" srcset="https://upload.wikimedia.org/wikipedia/commons/thumb/7/77/Emoji_u1f35c.svg/60px-Emoji_u1f35c.svg.png 1.5x, https://upload.wikimedia.org/wikipedia/commons/thumb/7/77/Emoji_u1f35c.svg/120px-Emoji_u1f35c.svg.png 2x" data-file-width="128" data-file-height="128"></a></span>
</td>
<td style="font-size:8pt; padding:1pt; line-height:1.1em;">
<div lang="en" dir="ltr" class="description en" style="display:inline;">This is an image of food from</div> <a href="https://en.wikipedia.org/wiki/en:Kenya" class="extiw" title="w:en:Kenya"><span title="country in Eastern Africa">Kenya</span></a> 
</td></tr></tbody></table></td>
</tr>

<tr>
<td id="fileinfotpl_date" class="fileinfo-paramfield" lang="en">Date</td>
<td lang="en">
<time class="dtstart" datetime="2014-11-04" lang="en" dir="ltr" style="white-space:nowrap">4 November 2014</time></td>
</tr>

<tr>
<td id="fileinfotpl_src" class="fileinfo-paramfield" lang="en">Source</td>
<td>
<span class="int-own-work" lang="en">Own work</span></td>
</tr>

<tr>
<td id="fileinfotpl_aut" class="fileinfo-paramfield" lang="en">Author</td>
<td>
<a href="https://commons.wikimedia.org/w/index.php?title=User:Kungu01&amp;action=edit&amp;redlink=1" class="new" title="User:Kungu01 (page does not exist)">kungu irungu</a></td>
</tr>


</tbody></table>
</div>
<div class="mw-heading mw-heading2"><h2 id="Licensing">Licensing</h2><span class="mw-editsection"><span class="mw-editsection-bracket">[</span><a href="https://commons.wikimedia.org/w/index.php?title=File:Madafu-chopping.jpg&amp;action=edit&amp;section=2" title="Edit section: Licensing"><span>edit</span></a><span class="mw-editsection-bracket">]</span></span></div>
<div style="clear:both; margin:0.5em auto; background-color:var(--background-color-interactive,#eaecf0); color:inherit; border:2px solid var(--border-color-subtle,#c8ccd1); padding:8px; direction:ltr;" class="licensetpl_wrapper"><div class="center" style="font-weight:bold;"><div lang="en" dir="ltr" class="description en" style="display:inline;">I, the copyright holder of this work, hereby publish it under the following license:</div></div>
<div class="responsive-license-cc layouttemplate licensetpl" dir="ltr" lang="en"><div class="rlicense-icons"><span class="skin-invert" typeof="mw:File"><span title="w:en:Creative Commons"><img alt="w:en:Creative Commons" src="../../upload.wikimedia.org/wikipedia/commons/thumb/7/79/CC_some_rights_reserved.svg/120px-CC_some_rights_reserved.svg.png" decoding="async" width="90" height="36" class="mw-file-element" srcset="https://upload.wikimedia.org/wikipedia/commons/thumb/7/79/CC_some_rights_reserved.svg/250px-CC_some_rights_reserved.svg.png 1.5x" data-file-width="744" data-file-height="300"></span></span><br>
<span class="skin-invert" typeof="mw:File"><span title="attribution"><img alt="attribution" src="../../upload.wikimedia.org/wikipedia/commons/thumb/1/11/Cc-by_new_white.svg/40px-Cc-by_new_white.svg.png" decoding="async" width="24" height="24" class="mw-file-element" srcset="https://upload.wikimedia.org/wikipedia/commons/thumb/1/11/Cc-by_new_white.svg/60px-Cc-by_new_white.svg.png 2x" data-file-width="64" data-file-height="64"></span></span> <span class="skin-invert" typeof="mw:File"><span title="share alike"><img alt="share alike" src="../../upload.wikimedia.org/wikipedia/commons/thumb/d/df/Cc-sa_white.svg/40px-Cc-sa_white.svg.png" decoding="async" width="24" height="24" class="mw-file-element" srcset="https://upload.wikimedia.org/wikipedia/commons/thumb/d/df/Cc-sa_white.svg/60px-Cc-sa_white.svg.png 2x" data-file-width="64" data-file-height="64"></span></span></div><div class="rlicense-text"><div class="rlicense-declaration">This file is licensed under the <a href="https://en.wikipedia.org/wiki/en:Creative_Commons" class="extiw" title="w:en:Creative Commons">Creative Commons</a> <a href="http://creativecommons.org/licenses/by-sa/4.0/deed.en" class="extiw" title="creativecommons:by-sa/4.0/deed.en">Attribution-Share Alike 4.0 International</a> license.</div><div class="rlicense-desc" style="text-align:start;" lang="en">
<dl><dd>You are free:
<ul><li><b>to share</b> – to copy, distribute and transmit the work</li>
<li><b>to remix</b> – to adapt the work</li></ul></dd>
<dd>Under the following conditions:
<ul><li><b>attribution</b> – You must give appropriate credit, provide a link to the license, and indicate if changes were made. You may do so in any reasonable manner, but not in any way that suggests the licensor endorses you or your use.</li>
<li><b>share alike</b> – If you remix, transform, or build upon the material, you must distribute your contributions under the <a href="https://creativecommons.org/share-your-work/licensing-considerations/compatible-licenses" class="extiw" title="ccorg:share-your-work/licensing-considerations/compatible-licenses">same or compatible license</a> as the original.</li></ul></dd></dl></div><span class="licensetpl_link" style="display:none;">https://creativecommons.org/licenses/by-sa/4.0</span><span class="licensetpl_short" style="display:none;">CC BY-SA 4.0 </span><span class="licensetpl_long" style="display:none;">Creative Commons Attribution-Share Alike 4.0 </span><span class="licensetpl_link_req" style="display:none;">true</span><span class="licensetpl_attr_req" style="display:none;">true</span></div></div></div>
<p><br>
</p>
<table cellspacing="8" cellpadding="0" style="width:100%;clear:both;text-align:center;margin:0.5em auto;background-color:var(--background-color-interactive-subtle,#f9f9f9);color: inherit;border:2px solid var(--border-color-muted,#e0e0e0);" dir="ltr" lang="en" class="layouttemplate partnershiptemplate" role="presentation">
<tbody><tr>
<td style="width:60px;"><span typeof="mw:File"><a href="Commons_Wiki_Loves_Africa_2014/en.html" title="Wiki Loves Africa 2014"><img alt="Wiki Loves Africa 2014" src="../../upload.wikimedia.org/wikipedia/commons/thumb/7/71/WIKI_LOVES_AFRICA.png/120px-WIKI_LOVES_AFRICA.png" decoding="async" width="90" height="50" class="mw-file-element" srcset="https://upload.wikimedia.org/wikipedia/commons/thumb/7/71/WIKI_LOVES_AFRICA.png/250px-WIKI_LOVES_AFRICA.png 1.5x" data-file-width="708" data-file-height="392"></a></span>
</td>
<td><div lang="en" dir="ltr" class="description en" style="display:inline;">This image was uploaded as part of <a href="Commons_Wiki_Loves_Africa_2014.html" title="Commons:Wiki Loves Africa 2014">Wiki Loves Africa 2014</a>. </div>
<hr>
<p><span style="font-size:x-small;line-height:140%" class="plainlinks noprint"><a class="external text" href="Template_Wiki_Loves_Africa_2014/af.html">Afrikaans</a>&nbsp;| <a class="external text" href="Template_Wiki_Loves_Africa_2014/als.html">Alemannisch</a>&nbsp;| <a class="external text" href="Template_Wiki_Loves_Africa_2014/ar.html">العربية</a>&nbsp;| <a class="external text" href="Template_Wiki_Loves_Africa_2014/bn.html">বাংলা</a>&nbsp;| <a class="external text" href="Template_Wiki_Loves_Africa_2014/br.html">brezhoneg</a>&nbsp;| <a class="external text" href="Template_Wiki_Loves_Africa_2014/ca.html">català</a>&nbsp;| <a class="external text" href="Template_Wiki_Loves_Africa_2014/cy.html">Cymraeg</a>&nbsp;| <a class="external text" href="Template_Wiki_Loves_Africa_2014/da.html">dansk</a>&nbsp;| <a class="external text" href="Template_Wiki_Loves_Africa_2014/de.html">Deutsch</a>&nbsp;| <a class="external text" href="Template_Wiki_Loves_Africa_2014/en.html">English</a>&nbsp;| <a class="external text" href="Template_Wiki_Loves_Africa_2014/es.html">español</a>&nbsp;| <a class="external text" href="Template_Wiki_Loves_Africa_2014/et.html">eesti</a>&nbsp;| <a class="external text" href="Template_Wiki_Loves_Africa_2014/fi.html">suomi</a>&nbsp;| <a class="external text" href="Template_Wiki_Loves_Africa_2014/fr.html">français</a>&nbsp;| <a class="external text" href="Template_Wiki_Loves_Africa_2014/fy.html">Frysk</a>&nbsp;| <a class="external text" href="Template_Wiki_Loves_Africa_2014/ga.html">Gaeilge</a>&nbsp;| <a class="external text" href="Template_Wiki_Loves_Africa_2014/gl.html">galego</a>&nbsp;| <a class="external text" href="Template_Wiki_Loves_Africa_2014/hu.html">magyar</a>&nbsp;| <a class="external text" href="Template_Wiki_Loves_Africa_2014/io.html">Ido</a>&nbsp;| <a class="external text" href="Template_Wiki_Loves_Africa_2014/it.html">italiano</a>&nbsp;| <a class="external text" href="Template_Wiki_Loves_Africa_2014/ja.html">日本語</a>&nbsp;| <a class="external text" href="Template_Wiki_Loves_Africa_2014/ko.html">한국어</a>&nbsp;| <a class="external text" href="Template_Wiki_Loves_Africa_2014/lb.html">Lëtzebuergesch</a>&nbsp;| <a class="external text" href="Template_Wiki_Loves_Africa_2014/nb.html">norsk bokmål</a>&nbsp;| <a class="external text" href="Template_Wiki_Loves_Africa_2014/ne.html">नेपाली</a>&nbsp;| <a class="external text" href="Template_Wiki_Loves_Africa_2014/nl.html">Nederlands</a>&nbsp;| <a class="external text" href="Template_Wiki_Loves_Africa_2014/nn.html">norsk nynorsk</a>&nbsp;| <a class="external text" href="Template_Wiki_Loves_Africa_2014/no.html">norsk</a>&nbsp;| <a class="external text" href="Template_Wiki_Loves_Africa_2014/pt.html">português</a>&nbsp;| <a class="external text" href="Template_Wiki_Loves_Africa_2014/ro.html">română</a>&nbsp;| <a class="external text" href="Template_Wiki_Loves_Africa_2014/ru.html">русский</a>&nbsp;| <a class="external text" href="Template_Wiki_Loves_Africa_2014/scn.html">sicilianu</a>&nbsp;| <a class="external text" href="Template_Wiki_Loves_Africa_2014/sl.html">slovenščina</a>&nbsp;| <a class="external text" href="Template_Wiki_Loves_Africa_2014/sq.html">shqip</a>&nbsp;| <a class="external text" href="Template_Wiki_Loves_Africa_2014/sr.html">српски / srpski</a>&nbsp;| <a class="external text" href="Template_Wiki_Loves_Africa_2014/sv.html">svenska</a>&nbsp;| <a class="external text" href="Template_Wiki_Loves_Africa_2014/th.html">ไทย</a>&nbsp;| <a class="external text" href="Template_Wiki_Loves_Africa_2014/tl.html">Tagalog</a>&nbsp;| <small class="plainlinks"><a class="external text" href="https://commons.wikimedia.org/w/index.php?title=Template:Wiki_Loves_Africa_2014/lang&amp;action=edit">+/−</a></small></span>
</p>
</td>
<td style="width:60px;"><figure class="mw-halign-right" typeof="mw:File"><span><img src="../../upload.wikimedia.org/wikipedia/commons/thumb/b/b0/%d9%83%d8%b3%d9%83%d8%b3%d9%8a_%d8%a8%d8%a7%d9%84%d8%ae%d8%b6%d8%b1%d8%a9_%d9%88%d8%a7%d9%84%d9%82%d8%af%d9%8a%d8%af%d8%8c_%d8%aa%d9%88%d9%86%d8%b3_2013.jpg/60px-%d9%83%d8%b3%d9%83%d8%b3%d9%8a_%d8%a8%d8%a7%d9%84%d8%ae%d8%b6%d8%b1%d8%a9_%d9%88%d8%a7%d9%84%d9%82%d8%af%d9%8a%d8%af%d8%8c_%d8%aa%d9%88%d9%86%d8%b3_2013.jpg" decoding="async" width="60" height="60" class="mw-file-element" srcset="https://upload.wikimedia.org/wikipedia/commons/thumb/b/b0/%D9%83%D8%B3%D9%83%D8%B3%D9%8A_%D8%A8%D8%A7%D9%84%D8%AE%D8%B6%D8%B1%D8%A9_%D9%88%D8%A7%D9%84%D9%82%D8%AF%D9%8A%D8%AF%D8%8C_%D8%AA%D9%88%D9%86%D8%B3_2013.jpg/120px-%D9%83%D8%B3%D9%83%D8%B3%D9%8A_%D8%A8%D8%A7%D9%84%D8%AE%D8%B6%D8%B1%D8%A9_%D9%88%D8%A7%D9%84%D9%82%D8%AF%D9%8A%D8%AF%D8%8C_%D8%AA%D9%88%D9%86%D8%B3_2013.jpg 1.5x" data-file-width="3084" data-file-height="3084"></span><figcaption></figcaption></figure>
</td></tr></tbody></table><h1 class="mw-slot-header"><mediainfoslotheader></mediainfoslotheader></h1>
<!-- 
NewPP limit report
Parsed by mw‐web.eqiad.main‐7b48b5fb74‐ftlg7
Cached time: 20250803020113
Cache expiry: 2592000
Reduced expiry: false
Complications: []
CPU time usage: 0.100 seconds
Real time usage: 0.157 seconds
Preprocessor visited node count: 725/1000000
Revision size: 385/2097152 bytes
Post‐expand include size: 32610/2097152 bytes
Template argument size: 438/2097152 bytes
Highest expansion depth: 17/100
Expensive parser function count: 3/500
Unstrip recursion depth: 0/20
Unstrip post‐expand size: 8/5000000 bytes
Lua time usage: 0.061/10.000 seconds
Lua memory usage: 1421951/52428800 bytes
Number of Wikibase entities loaded: 1/400
-->
<!--
Transclusion expansion time report (%,ms,calls,template)
100.00%  130.282      1 -total
 69.25%   90.217      1 Template:Information
 34.07%   44.393      1 Template:Wiki_Loves_Africa_2014_country
 27.20%   35.431      1 Template:Wiki_Loves_Africa_2014_country/layout
 24.53%   31.964      1 Template:Country
 18.68%   24.339      1 Template:Wiki_Loves_Africa_2014
 16.54%   21.547      1 Template:Kenya
 15.33%   19.975      1 Template:Label
 14.97%   19.505      1 Template:Wiki_Loves_Africa_2014/layout
 11.82%   15.400      1 Template:Self
-->

<!-- Saved in parser cache with key commonswiki:pcache:36601495:|#|:idhash:wb=3!wbMobile=0 and timestamp 20250803020113 and revision id 1033630111. Rendering was triggered because: page-view
 -->
</div></div><h2 id="filehistory">File history</h2>
<div id="mw-imagepage-section-filehistory">
<p>Click on a date/time to view the file as it appeared at that time.
</p>
<table class="wikitable filehistory">
<tr><th></th><th>Date/Time</th><th>Thumbnail</th><th>Dimensions</th><th>User</th><th>Comment</th></tr>
<tr><td>current</td><td class="filehistory-selected" style="white-space: nowrap;"><a href="../../upload.wikimedia.org/wikipedia/commons/5/5f/Madafu-chopping.jpg">11:09, 4 November 2014</a></td><td><a href="../../upload.wikimedia.org/wikipedia/commons/5/5f/Madafu-chopping.jpg"><img alt="Thumbnail for version as of 11:09, 4 November 2014" src="../../upload.wikimedia.org/wikipedia/commons/thumb/5/5f/Madafu-chopping.jpg/120px-Madafu-chopping2de9.jpg?20141104110941" decoding="async" loading="lazy" width="120" height="90" data-file-width="4544" data-file-height="3424"></a></td><td>4,544 × 3,424 <span style="white-space: nowrap;">(7.56 MB)</span></td><td><a href="https://commons.wikimedia.org/w/index.php?title=User:Kungu01&amp;action=edit&amp;redlink=1" class="mw-userlink new" title="User:Kungu01 (page does not exist)"><bdi>Kungu01</bdi></a><span style="white-space: nowrap;"> <span class="mw-usertoollinks">(<a href="User_talk_Kungu01.html" class="mw-usertoollinks-talk" title="User talk:Kungu01">talk</a> | <a href="Special_Contributions/Kungu01.html" class="mw-usertoollinks-contribs" title="Special:Contributions/Kungu01">contribs</a>)</span></span></td><td dir="ltr">User created page with UploadWizard</td></tr>
</table>

</div>
<div class="mw-imagepage-upload-links"><p id="mw-imagepage-upload-disallowed">You cannot overwrite this file.</p></div><h2 id="filelinks">File usage on Commons</h2>
<div id='mw-imagepage-section-linkstoimage'>
<p>The following 2 pages use this file:
</p><ul class="mw-imagepage-linkstoimage">
<li class="mw-imagepage-linkstoimage-ns2"><a href="User_PierreSelim/WLA_with_categories.html" title="User:PierreSelim/WLA with categories">User:PierreSelim/WLA with categories</a></li>
<li class="mw-imagepage-linkstoimage-ns2"><a href="User_PierreSelim/Wiki_Loves_Africa.html" title="User:PierreSelim/Wiki Loves Africa">User:PierreSelim/Wiki Loves Africa</a></li>
</ul>
</div>
<h2 id="metadata">Metadata</h2>
<div class="mw-imagepage-section-metadata"><p>This file contains additional information such as Exif metadata which may have been added by the digital camera, scanner, or software program used to create or digitize it. If the file has been modified from its original state, some details such as the timestamp may not fully reflect those of the original file. The timestamp is only as accurate as the clock in the camera, and it may be completely wrong.</p><table id="mw_metadata" class="mw_metadata collapsed">
<tbody><tr class="exif-make"><th>Camera manufacturer</th><td><a href="https://en.wikipedia.org/wiki/Canon_(company)" class="extiw" title="w:Canon (company)">Canon</a></td></tr><tr class="exif-model"><th>Camera model</th><td><a href="https://en.wikipedia.org/wiki/en:Special:Search/Canon_EOS_6D" class="extiw" title="w:en:Special:Search/Canon EOS 6D">Canon EOS 6D</a></td></tr><tr class="exif-artist"><th>Author</th><td>Moses Odanga</td></tr><tr class="exif-copyright"><th>Copyright holder</th><td><ul class="metadata-langlist"><li class="mw-metadata-lang-default"><span class="mw-metadata-lang-value">Modan Photography</span></li>
</ul></td></tr><tr class="exif-exposuretime"><th>Exposure time</th><td>1/80 sec (0.0125)</td></tr><tr class="exif-fnumber"><th><a href="https://en.wikipedia.org/wiki/F-number" class="extiw" title="wikipedia:F-number">F-number</a></th><td>f/1.8</td></tr><tr class="exif-isospeedratings"><th>ISO speed rating</th><td>8,000</td></tr><tr class="exif-datetimeoriginal"><th>Date and time of data generation</th><td>18:49, 27 July 2014</td></tr><tr class="exif-focallength"><th>Lens focal length</th><td>50 mm</td></tr><tr class="exif-imagewidth mw-metadata-collapsible"><th>Width</th><td>5,472 px</td></tr><tr class="exif-imagelength mw-metadata-collapsible"><th>Height</th><td>3,648 px</td></tr><tr class="exif-bitspersample mw-metadata-collapsible"><th>Bits per component</th><td><ul><li>8</li>
<li>8</li>
<li>8</li></ul></td></tr><tr class="exif-photometricinterpretation mw-metadata-collapsible"><th>Pixel composition</th><td>RGB</td></tr><tr class="exif-orientation mw-metadata-collapsible"><th>Orientation</th><td>Normal</td></tr><tr class="exif-samplesperpixel mw-metadata-collapsible"><th>Number of components</th><td>3</td></tr><tr class="exif-xresolution mw-metadata-collapsible"><th>Horizontal resolution</th><td>72 dpi</td></tr><tr class="exif-yresolution mw-metadata-collapsible"><th>Vertical resolution</th><td>72 dpi</td></tr><tr class="exif-software mw-metadata-collapsible"><th>Software used</th><td><a href="https://en.wikipedia.org/wiki/Adobe_Photoshop_CS6_(Windows)" class="extiw" title="w:Adobe Photoshop CS6 (Windows)">Adobe Photoshop CS6 (Windows)</a></td></tr><tr class="exif-datetime mw-metadata-collapsible"><th>File change date and time</th><td>13:06, 4 November 2014</td></tr><tr class="exif-whitepoint mw-metadata-collapsible"><th>White point chromaticity</th><td><ul><li>0.313</li>
<li>0.329</li></ul></td></tr><tr class="exif-primarychromaticities mw-metadata-collapsible"><th>Chromaticities of primarities</th><td><ul><li>0.64</li>
<li>0.33</li>
<li>0.21</li>
<li>0.71</li>
<li>0.15</li>
<li>0.06</li></ul></td></tr><tr class="exif-ycbcrcoefficients mw-metadata-collapsible"><th>Color space transformation matrix coefficients</th><td><ul><li>0.299</li>
<li>0.587</li>
<li>0.114</li></ul></td></tr><tr class="exif-ycbcrpositioning mw-metadata-collapsible"><th>Y and C positioning</th><td>Co-sited</td></tr><tr class="exif-exposureprogram mw-metadata-collapsible"><th>Exposure Program</th><td>Manual</td></tr><tr class="exif-exifversion mw-metadata-collapsible"><th>Exif version</th><td>2.3</td></tr><tr class="exif-datetimedigitized mw-metadata-collapsible"><th>Date and time of digitizing</th><td>18:49, 27 July 2014</td></tr><tr class="exif-componentsconfiguration mw-metadata-collapsible"><th>Meaning of each component</th><td><ol><li>Y</li>
<li>Cb</li>
<li>Cr</li>
<li>does not exist</li></ol></td></tr><tr class="exif-shutterspeedvalue mw-metadata-collapsible"><th>APEX shutter speed</th><td>6.375</td></tr><tr class="exif-aperturevalue mw-metadata-collapsible"><th>APEX aperture</th><td>1.625</td></tr><tr class="exif-exposurebiasvalue mw-metadata-collapsible"><th>APEX exposure bias</th><td>0</td></tr><tr class="exif-maxaperturevalue mw-metadata-collapsible"><th>Maximum land aperture</th><td>1.75 APEX (f/1.83)</td></tr><tr class="exif-meteringmode mw-metadata-collapsible"><th>Metering mode</th><td>Pattern</td></tr><tr class="exif-flash mw-metadata-collapsible"><th>Flash</th><td>Flash did not fire, compulsory flash suppression</td></tr><tr class="exif-subsectime mw-metadata-collapsible"><th>DateTime subseconds</th><td>20</td></tr><tr class="exif-subsectimeoriginal mw-metadata-collapsible"><th>DateTimeOriginal subseconds</th><td>20</td></tr><tr class="exif-subsectimedigitized mw-metadata-collapsible"><th>DateTimeDigitized subseconds</th><td>20</td></tr><tr class="exif-flashpixversion mw-metadata-collapsible"><th>Supported Flashpix version</th><td>1</td></tr><tr class="exif-colorspace mw-metadata-collapsible"><th>Color space</th><td>Uncalibrated</td></tr><tr class="exif-focalplanexresolution mw-metadata-collapsible"><th>Focal plane X resolution</th><td>3,810.5849582173</td></tr><tr class="exif-focalplaneyresolution mw-metadata-collapsible"><th>Focal plane Y resolution</th><td>3,815.89958159</td></tr><tr class="exif-focalplaneresolutionunit mw-metadata-collapsible"><th>Focal plane resolution unit</th><td>inches</td></tr><tr class="exif-customrendered mw-metadata-collapsible"><th>Custom image processing</th><td>Normal process</td></tr><tr class="exif-exposuremode mw-metadata-collapsible"><th>Exposure mode</th><td>Manual exposure</td></tr><tr class="exif-whitebalance mw-metadata-collapsible"><th>White balance</th><td>Auto white balance</td></tr><tr class="exif-scenecapturetype mw-metadata-collapsible"><th>Scene capture type</th><td>Standard</td></tr><tr class="exif-gpsversionid mw-metadata-collapsible"><th>GPS tag version</th><td>0.0.3.2</td></tr><tr class="exif-serialnumber mw-metadata-collapsible"><th>Serial number of camera</th><td>148028001032</td></tr><tr class="exif-lens mw-metadata-collapsible"><th>Lens used</th><td>EF50mm f/1.8 II</td></tr><tr class="exif-rating mw-metadata-collapsible"><th>Rating (out of 5)</th><td>0</td></tr><tr class="exif-datetimemetadata mw-metadata-collapsible"><th>Date metadata was last modified</th><td>16:06, 4 November 2014</td></tr><tr class="exif-originaldocumentid mw-metadata-collapsible"><th>Unique ID of original document</th><td>C30A717A71CE81D72B246D48550A6155</td></tr></tbody></table>
</div><!--esi <esi:include src="/esitest-fa8a495983347898/content" /> --><noscript><img src="https://auth.wikimedia.org/loginwiki/wiki/Special:CentralAutoLogin/checkLoggedIn?useformat=desktop&amp;wikiid=commonswiki&amp;usesul3=1&amp;type=1x1" alt="" width="1" height="1" style="border: none; position: absolute;"></noscript></div><div role='tabpanel' aria-hidden='true' id='ooui-php-5' class='wbmi-tab oo-ui-layout oo-ui-panelLayout oo-ui-panelLayout-scrollable oo-ui-tabPanelLayout oo-ui-element-hidden' data-ooui='{"_":"OO.ui.TabPanelLayout","name":"statements","label":"Structured data","scrollable":true,"expanded":false,"classes":["wbmi-tab"]}'><h2 class="wbmi-structured-data-header">Structured data</h2><div id="P180" data-mw-property="P180" data-mw-statements="[]" data-mw-formatvalue="[]" class="wbmi-entityview-statementsGroup wbmi-entityview-statementsGroup-P180 oo-ui-layout oo-ui-panelLayout oo-ui-panelLayout-framed"><div class="wbmi-statements-widget"><div class="wbmi-statement-header"><div class="wbmi-entity-data"><div class="wbmi-entity-title"><h3 class="wbmi-statements-title">Items portrayed in this file</h3><h4 class="wbmi-entity-label"><bdi><a target="_blank" title="d:Special:EntityPage/P180" href="https://www.wikidata.org/wiki/Special:EntityPage/P180">depicts</a></bdi></h4></div></div></div><div class="wbmi-content-items-group"></div></div></div><div id="P170" data-mw-property="P170" data-mw-statements="[{&quot;mainsnak&quot;:{&quot;snaktype&quot;:&quot;somevalue&quot;,&quot;property&quot;:&quot;P170&quot;,&quot;hash&quot;:&quot;d3550e860f988c6675fff913440993f58f5c40c5&quot;},&quot;type&quot;:&quot;statement&quot;,&quot;qualifiers&quot;:{&quot;P3831&quot;:[{&quot;snaktype&quot;:&quot;value&quot;,&quot;property&quot;:&quot;P3831&quot;,&quot;hash&quot;:&quot;c5e04952fd00011abf931be1b701f93d9e6fa5d7&quot;,&quot;datavalue&quot;:{&quot;value&quot;:{&quot;entity-type&quot;:&quot;item&quot;,&quot;numeric-id&quot;:33231,&quot;id&quot;:&quot;Q33231&quot;},&quot;type&quot;:&quot;wikibase-entityid&quot;}}],&quot;P2093&quot;:[{&quot;snaktype&quot;:&quot;value&quot;,&quot;property&quot;:&quot;P2093&quot;,&quot;hash&quot;:&quot;720ed39de54d0facfda214c97b2abe999feac28f&quot;,&quot;datavalue&quot;:{&quot;value&quot;:&quot;kungu irungu&quot;,&quot;type&quot;:&quot;string&quot;}}],&quot;P4174&quot;:[{&quot;snaktype&quot;:&quot;value&quot;,&quot;property&quot;:&quot;P4174&quot;,&quot;hash&quot;:&quot;b7c456c9fc6eeba79f1c389d3251b66412fcae52&quot;,&quot;datavalue&quot;:{&quot;value&quot;:&quot;Kungu01&quot;,&quot;type&quot;:&quot;string&quot;}}]},&quot;qualifiers-order&quot;:[&quot;P3831&quot;,&quot;P2093&quot;,&quot;P4174&quot;],&quot;id&quot;:&quot;*********$CF584A4E-8C5C-4969-B85C-93B446DAE7BB&quot;,&quot;rank&quot;:&quot;normal&quot;}]" data-mw-formatvalue="{&quot;{\&quot;value\&quot;:{\&quot;entity-type\&quot;:\&quot;property\&quot;,\&quot;numeric-id\&quot;:170,\&quot;id\&quot;:\&quot;P170\&quot;},\&quot;type\&quot;:\&quot;wikibase-entityid\&quot;}&quot;:{&quot;text\/html&quot;:{&quot;en&quot;:{&quot;&quot;:&quot;<a target=\&quot;_blank\&quot; title=\&quot;d:Special:EntityPage\/P170\&quot; href=https://commons.wikimedia.org/"https:////www.wikidata.org//wiki//Special:EntityPage//P170/">creator<\/a>&quot;}},&quot;text\/plain&quot;:{&quot;en&quot;:{&quot;&quot;:&quot;creator&quot;}}},&quot;{\&quot;value\&quot;:{\&quot;entity-type\&quot;:\&quot;property\&quot;,\&quot;numeric-id\&quot;:3831,\&quot;id\&quot;:\&quot;P3831\&quot;},\&quot;type\&quot;:\&quot;wikibase-entityid\&quot;}&quot;:{&quot;text\/html&quot;:{&quot;en&quot;:{&quot;&quot;:&quot;<a target=\&quot;_blank\&quot; title=\&quot;d:Special:EntityPage\/P3831\&quot; href=https://commons.wikimedia.org/"https:////www.wikidata.org//wiki//Special:EntityPage//P3831/">object of statement has role<\/a>&quot;}},&quot;text\/plain&quot;:{&quot;en&quot;:{&quot;&quot;:&quot;object of statement has role&quot;}}},&quot;{\&quot;value\&quot;:{\&quot;entity-type\&quot;:\&quot;item\&quot;,\&quot;numeric-id\&quot;:33231,\&quot;id\&quot;:\&quot;Q33231\&quot;},\&quot;type\&quot;:\&quot;wikibase-entityid\&quot;}&quot;:{&quot;text\/html&quot;:{&quot;en&quot;:{&quot;P3831&quot;:&quot;<a target=\&quot;_blank\&quot; title=\&quot;d:Special:EntityPage\/Q33231\&quot; href=https://commons.wikimedia.org/"https:////www.wikidata.org//wiki//Special:EntityPage//Q33231/">photographer<\/a>&quot;}},&quot;text\/plain&quot;:{&quot;en&quot;:{&quot;P3831&quot;:&quot;photographer&quot;}}},&quot;{\&quot;value\&quot;:{\&quot;entity-type\&quot;:\&quot;property\&quot;,\&quot;numeric-id\&quot;:2093,\&quot;id\&quot;:\&quot;P2093\&quot;},\&quot;type\&quot;:\&quot;wikibase-entityid\&quot;}&quot;:{&quot;text\/html&quot;:{&quot;en&quot;:{&quot;&quot;:&quot;<a target=\&quot;_blank\&quot; title=\&quot;d:Special:EntityPage\/P2093\&quot; href=https://commons.wikimedia.org/"https:////www.wikidata.org//wiki//Special:EntityPage//P2093/">author name string<\/a>&quot;}},&quot;text\/plain&quot;:{&quot;en&quot;:{&quot;&quot;:&quot;author name string&quot;}}},&quot;{\&quot;value\&quot;:\&quot;kungu irungu\&quot;,\&quot;type\&quot;:\&quot;string\&quot;}&quot;:{&quot;text\/html&quot;:{&quot;en&quot;:{&quot;P2093&quot;:&quot;kungu irungu&quot;}},&quot;text\/plain&quot;:{&quot;en&quot;:{&quot;P2093&quot;:&quot;kungu irungu&quot;}}},&quot;{\&quot;value\&quot;:{\&quot;entity-type\&quot;:\&quot;property\&quot;,\&quot;numeric-id\&quot;:4174,\&quot;id\&quot;:\&quot;P4174\&quot;},\&quot;type\&quot;:\&quot;wikibase-entityid\&quot;}&quot;:{&quot;text\/html&quot;:{&quot;en&quot;:{&quot;&quot;:&quot;<a target=\&quot;_blank\&quot; title=\&quot;d:Special:EntityPage\/P4174\&quot; href=https://commons.wikimedia.org/"https:////www.wikidata.org//wiki//Special:EntityPage//P4174/">Wikimedia username<\/a>&quot;}},&quot;text\/plain&quot;:{&quot;en&quot;:{&quot;&quot;:&quot;Wikimedia username&quot;}}},&quot;{\&quot;value\&quot;:\&quot;Kungu01\&quot;,\&quot;type\&quot;:\&quot;string\&quot;}&quot;:{&quot;text\/html&quot;:{&quot;en&quot;:{&quot;P4174&quot;:&quot;<a target=\&quot;_blank\&quot; class=\&quot;wb-external-id external\&quot; href=https://commons.wikimedia.org/"https:////www.wikidata.org//wiki//Special:CentralAuth?target=Kungu01\%22 rel=\&quot;nofollow\&quot;>Kungu01<\/a>&quot;}},&quot;text\/plain&quot;:{&quot;en&quot;:{&quot;P4174&quot;:&quot;Kungu01&quot;}}}}" class="wbmi-entityview-statementsGroup wbmi-entityview-statementsGroup-P170 oo-ui-layout oo-ui-panelLayout oo-ui-panelLayout-framed"><div class="wbmi-statements-widget"><div class="wbmi-statement-header"><div class="wbmi-entity-data"><div class="wbmi-entity-title"><h4 class="wbmi-entity-label"><bdi><a target="_blank" title="d:Special:EntityPage/P170" href="https://www.wikidata.org/wiki/Special:EntityPage/P170">creator</a></bdi></h4></div></div></div><div class="wbmi-content-items-group"><div class="wbmi-item wbmi-item-read"><div class="wbmi-item-container"><div class="wbmi-entity-header"><div class="wbmi-entity-data"><div class="wbmi-entity-title"><h4 class="wbmi-entity-label"><bdi>some value</bdi></h4></div></div></div><div class="wbmi-item-qualifiers"><div class="wbmi-snaklist-container"><div class="wbmi-snaklist-content"><div class="wbmi-snak"><div class="wbmi-snak-value"><a target="_blank" title="d:Special:EntityPage/P3831" href="https://www.wikidata.org/wiki/Special:EntityPage/P3831">object of statement has role</a><span class="wbmi-snak-value-separator">: </span><span class="wbmi-snak-value--value"><a target="_blank" title="d:Special:EntityPage/Q33231" href="https://www.wikidata.org/wiki/Special:EntityPage/Q33231">photographer</a></span></div></div><div class="wbmi-snak"><div class="wbmi-snak-value"><a target="_blank" title="d:Special:EntityPage/P2093" href="https://www.wikidata.org/wiki/Special:EntityPage/P2093">author name string</a><span class="wbmi-snak-value-separator">: </span><span class="wbmi-snak-value--value">kungu irungu</span></div></div><div class="wbmi-snak"><div class="wbmi-snak-value"><a target="_blank" title="d:Special:EntityPage/P4174" href="https://www.wikidata.org/wiki/Special:EntityPage/P4174">Wikimedia username</a><span class="wbmi-snak-value-separator">: </span><span class="wbmi-snak-value--value"><a target="_blank" class="wb-external-id external" href="https://www.wikidata.org/wiki/Special:CentralAuth?target=Kungu01" rel="nofollow">Kungu01</a></span></div></div></div></div></div></div></div></div></div></div><div id="P6216" data-mw-property="P6216" data-mw-statements="[{&quot;mainsnak&quot;:{&quot;snaktype&quot;:&quot;value&quot;,&quot;property&quot;:&quot;P6216&quot;,&quot;hash&quot;:&quot;5570347fdc76d2a80732f51ea10ee4b144a084e0&quot;,&quot;datavalue&quot;:{&quot;value&quot;:{&quot;entity-type&quot;:&quot;item&quot;,&quot;numeric-id&quot;:50423863,&quot;id&quot;:&quot;Q50423863&quot;},&quot;type&quot;:&quot;wikibase-entityid&quot;}},&quot;type&quot;:&quot;statement&quot;,&quot;id&quot;:&quot;*********$C9F5AAF1-1BB0-430E-8E76-01F3A57E8D33&quot;,&quot;rank&quot;:&quot;normal&quot;}]" data-mw-formatvalue="{&quot;{\&quot;value\&quot;:{\&quot;entity-type\&quot;:\&quot;property\&quot;,\&quot;numeric-id\&quot;:6216,\&quot;id\&quot;:\&quot;P6216\&quot;},\&quot;type\&quot;:\&quot;wikibase-entityid\&quot;}&quot;:{&quot;text\/html&quot;:{&quot;en&quot;:{&quot;&quot;:&quot;<a target=\&quot;_blank\&quot; title=\&quot;d:Special:EntityPage\/P6216\&quot; href=https://commons.wikimedia.org/"https:////www.wikidata.org//wiki//Special:EntityPage//P6216/">copyright status<\/a>&quot;}},&quot;text\/plain&quot;:{&quot;en&quot;:{&quot;&quot;:&quot;copyright status&quot;}}},&quot;{\&quot;value\&quot;:{\&quot;entity-type\&quot;:\&quot;item\&quot;,\&quot;numeric-id\&quot;:50423863,\&quot;id\&quot;:\&quot;Q50423863\&quot;},\&quot;type\&quot;:\&quot;wikibase-entityid\&quot;}&quot;:{&quot;text\/html&quot;:{&quot;en&quot;:{&quot;P6216&quot;:&quot;<a target=\&quot;_blank\&quot; title=\&quot;d:Special:EntityPage\/Q50423863\&quot; href=https://commons.wikimedia.org/"https:////www.wikidata.org//wiki//Special:EntityPage//Q50423863/">copyrighted<\/a>&quot;}},&quot;text\/plain&quot;:{&quot;en&quot;:{&quot;P6216&quot;:&quot;copyrighted&quot;}}}}" class="wbmi-entityview-statementsGroup wbmi-entityview-statementsGroup-P6216 oo-ui-layout oo-ui-panelLayout oo-ui-panelLayout-framed"><div class="wbmi-statements-widget"><div class="wbmi-statement-header"><div class="wbmi-entity-data"><div class="wbmi-entity-title"><h4 class="wbmi-entity-label"><bdi><a target="_blank" title="d:Special:EntityPage/P6216" href="https://www.wikidata.org/wiki/Special:EntityPage/P6216">copyright status</a></bdi></h4></div></div></div><div class="wbmi-content-items-group"><div class="wbmi-item wbmi-item-read"><div class="wbmi-item-container"><div class="wbmi-entity-header"><div class="wbmi-entity-data"><div class="wbmi-entity-title"><h4 class="wbmi-entity-label"><bdi><a target="_blank" title="d:Special:EntityPage/Q50423863" href="https://www.wikidata.org/wiki/Special:EntityPage/Q50423863">copyrighted</a></bdi></h4></div></div></div></div></div></div></div></div><div id="P275" data-mw-property="P275" data-mw-statements="[{&quot;mainsnak&quot;:{&quot;snaktype&quot;:&quot;value&quot;,&quot;property&quot;:&quot;P275&quot;,&quot;hash&quot;:&quot;ec6e754c5042e13b53376139e505ebd6708745a4&quot;,&quot;datavalue&quot;:{&quot;value&quot;:{&quot;entity-type&quot;:&quot;item&quot;,&quot;numeric-id&quot;:18199165,&quot;id&quot;:&quot;*********&quot;},&quot;type&quot;:&quot;wikibase-entityid&quot;}},&quot;type&quot;:&quot;statement&quot;,&quot;id&quot;:&quot;*********$A63F835D-DEEC-42B7-9457-443A2DCA6589&quot;,&quot;rank&quot;:&quot;normal&quot;}]" data-mw-formatvalue="{&quot;{\&quot;value\&quot;:{\&quot;entity-type\&quot;:\&quot;property\&quot;,\&quot;numeric-id\&quot;:275,\&quot;id\&quot;:\&quot;P275\&quot;},\&quot;type\&quot;:\&quot;wikibase-entityid\&quot;}&quot;:{&quot;text\/html&quot;:{&quot;en&quot;:{&quot;&quot;:&quot;<a target=\&quot;_blank\&quot; title=\&quot;d:Special:EntityPage\/P275\&quot; href=https://commons.wikimedia.org/"https:////www.wikidata.org//wiki//Special:EntityPage//P275/">copyright license<\/a>&quot;}},&quot;text\/plain&quot;:{&quot;en&quot;:{&quot;&quot;:&quot;copyright license&quot;}}},&quot;{\&quot;value\&quot;:{\&quot;entity-type\&quot;:\&quot;item\&quot;,\&quot;numeric-id\&quot;:18199165,\&quot;id\&quot;:\&quot;*********\&quot;},\&quot;type\&quot;:\&quot;wikibase-entityid\&quot;}&quot;:{&quot;text\/html&quot;:{&quot;en&quot;:{&quot;P275&quot;:&quot;<a target=\&quot;_blank\&quot; title=\&quot;d:Special:EntityPage\/*********\&quot; href=https://commons.wikimedia.org/"https:////www.wikidata.org//wiki//Special:EntityPage//*********/">Creative Commons Attribution-ShareAlike 4.0 International<\/a>&quot;}},&quot;text\/plain&quot;:{&quot;en&quot;:{&quot;P275&quot;:&quot;Creative Commons Attribution-ShareAlike 4.0 International&quot;}}}}" class="wbmi-entityview-statementsGroup wbmi-entityview-statementsGroup-P275 oo-ui-layout oo-ui-panelLayout oo-ui-panelLayout-framed"><div class="wbmi-statements-widget"><div class="wbmi-statement-header"><div class="wbmi-entity-data"><div class="wbmi-entity-title"><h4 class="wbmi-entity-label"><bdi><a target="_blank" title="d:Special:EntityPage/P275" href="https://www.wikidata.org/wiki/Special:EntityPage/P275">copyright license</a></bdi></h4></div></div></div><div class="wbmi-content-items-group"><div class="wbmi-item wbmi-item-read"><div class="wbmi-item-container"><div class="wbmi-entity-header"><div class="wbmi-entity-data"><div class="wbmi-entity-title"><h4 class="wbmi-entity-label"><bdi><a target="_blank" title="d:Special:EntityPage/*********" href="https://www.wikidata.org/wiki/Special:EntityPage/*********">Creative Commons Attribution-ShareAlike 4.0 International</a></bdi></h4></div></div></div></div></div></div></div></div><div id="P571" data-mw-property="P571" data-mw-statements="[{&quot;mainsnak&quot;:{&quot;snaktype&quot;:&quot;value&quot;,&quot;property&quot;:&quot;P571&quot;,&quot;hash&quot;:&quot;a39d8edf4086785bc3984f2053a12e1b3977319e&quot;,&quot;datavalue&quot;:{&quot;value&quot;:{&quot;time&quot;:&quot;+2014-11-04T00:00:00Z&quot;,&quot;timezone&quot;:0,&quot;before&quot;:0,&quot;after&quot;:0,&quot;precision&quot;:11,&quot;calendarmodel&quot;:&quot;http:\/\/www.wikidata.org\/entity\/Q1985727&quot;},&quot;type&quot;:&quot;time&quot;}},&quot;type&quot;:&quot;statement&quot;,&quot;id&quot;:&quot;*********$4EC8A274-DA19-42C2-87C9-30413067E002&quot;,&quot;rank&quot;:&quot;normal&quot;}]" data-mw-formatvalue="{&quot;{\&quot;value\&quot;:{\&quot;entity-type\&quot;:\&quot;property\&quot;,\&quot;numeric-id\&quot;:571,\&quot;id\&quot;:\&quot;P571\&quot;},\&quot;type\&quot;:\&quot;wikibase-entityid\&quot;}&quot;:{&quot;text\/html&quot;:{&quot;en&quot;:{&quot;&quot;:&quot;<a target=\&quot;_blank\&quot; title=\&quot;d:Special:EntityPage\/P571\&quot; href=https://commons.wikimedia.org/"https:////www.wikidata.org//wiki//Special:EntityPage//P571/">inception<\/a>&quot;}},&quot;text\/plain&quot;:{&quot;en&quot;:{&quot;&quot;:&quot;inception&quot;}}},&quot;{\&quot;value\&quot;:{\&quot;time\&quot;:\&quot;+2014-11-04T00:00:00Z\&quot;,\&quot;timezone\&quot;:0,\&quot;before\&quot;:0,\&quot;after\&quot;:0,\&quot;precision\&quot;:11,\&quot;calendarmodel\&quot;:\&quot;http:\\\/\\\/www.wikidata.org\\\/entity\\\/Q1985727\&quot;},\&quot;type\&quot;:\&quot;time\&quot;}&quot;:{&quot;text\/html&quot;:{&quot;en&quot;:{&quot;P571&quot;:&quot;4 November 2014&quot;}},&quot;text\/plain&quot;:{&quot;en&quot;:{&quot;P571&quot;:&quot;4 November 2014&quot;}}}}" class="wbmi-entityview-statementsGroup wbmi-entityview-statementsGroup-P571 oo-ui-layout oo-ui-panelLayout oo-ui-panelLayout-framed"><div class="wbmi-statements-widget"><div class="wbmi-statement-header"><div class="wbmi-entity-data"><div class="wbmi-entity-title"><h4 class="wbmi-entity-label"><bdi><a target="_blank" title="d:Special:EntityPage/P571" href="https://www.wikidata.org/wiki/Special:EntityPage/P571">inception</a></bdi></h4></div></div></div><div class="wbmi-content-items-group"><div class="wbmi-item wbmi-item-read"><div class="wbmi-item-container"><div class="wbmi-entity-header"><div class="wbmi-entity-data"><div class="wbmi-entity-title"><h4 class="wbmi-entity-label"><bdi>4 November 2014</bdi></h4></div></div></div></div></div></div></div></div><div id="P4082" data-mw-property="P4082" data-mw-statements="[{&quot;mainsnak&quot;:{&quot;snaktype&quot;:&quot;value&quot;,&quot;property&quot;:&quot;P4082&quot;,&quot;hash&quot;:&quot;934c25321d67b3a2ce32005abc06ebfa13d529e8&quot;,&quot;datavalue&quot;:{&quot;value&quot;:{&quot;entity-type&quot;:&quot;item&quot;,&quot;numeric-id&quot;:66215,&quot;id&quot;:&quot;Q66215&quot;},&quot;type&quot;:&quot;wikibase-entityid&quot;}},&quot;type&quot;:&quot;statement&quot;,&quot;id&quot;:&quot;*********$5C69C079-394A-47FA-B061-1D597710B93B&quot;,&quot;rank&quot;:&quot;normal&quot;}]" data-mw-formatvalue="{&quot;{\&quot;value\&quot;:{\&quot;entity-type\&quot;:\&quot;property\&quot;,\&quot;numeric-id\&quot;:4082,\&quot;id\&quot;:\&quot;P4082\&quot;},\&quot;type\&quot;:\&quot;wikibase-entityid\&quot;}&quot;:{&quot;text\/html&quot;:{&quot;en&quot;:{&quot;&quot;:&quot;<a target=\&quot;_blank\&quot; title=\&quot;d:Special:EntityPage\/P4082\&quot; href=https://commons.wikimedia.org/"https:////www.wikidata.org//wiki//Special:EntityPage//P4082/">captured with<\/a>&quot;}},&quot;text\/plain&quot;:{&quot;en&quot;:{&quot;&quot;:&quot;captured with&quot;}}},&quot;{\&quot;value\&quot;:{\&quot;entity-type\&quot;:\&quot;item\&quot;,\&quot;numeric-id\&quot;:66215,\&quot;id\&quot;:\&quot;Q66215\&quot;},\&quot;type\&quot;:\&quot;wikibase-entityid\&quot;}&quot;:{&quot;text\/html&quot;:{&quot;en&quot;:{&quot;P4082&quot;:&quot;<a target=\&quot;_blank\&quot; title=\&quot;d:Special:EntityPage\/Q66215\&quot; href=https://commons.wikimedia.org/"https:////www.wikidata.org//wiki//Special:EntityPage//Q66215/">Canon EOS 6D<\/a>&quot;}},&quot;text\/plain&quot;:{&quot;en&quot;:{&quot;P4082&quot;:&quot;Canon EOS 6D&quot;}}}}" class="wbmi-entityview-statementsGroup wbmi-entityview-statementsGroup-P4082 oo-ui-layout oo-ui-panelLayout oo-ui-panelLayout-framed"><div class="wbmi-statements-widget"><div class="wbmi-statement-header"><div class="wbmi-entity-data"><div class="wbmi-entity-title"><h4 class="wbmi-entity-label"><bdi><a target="_blank" title="d:Special:EntityPage/P4082" href="https://www.wikidata.org/wiki/Special:EntityPage/P4082">captured with</a></bdi></h4></div></div></div><div class="wbmi-content-items-group"><div class="wbmi-item wbmi-item-read"><div class="wbmi-item-container"><div class="wbmi-entity-header"><div class="wbmi-entity-data"><div class="wbmi-entity-title"><h4 class="wbmi-entity-label"><bdi><a target="_blank" title="d:Special:EntityPage/Q66215" href="https://www.wikidata.org/wiki/Special:EntityPage/Q66215">Canon EOS 6D</a></bdi></h4></div></div></div></div></div></div></div></div><div id="P7482" data-mw-property="P7482" data-mw-statements="[{&quot;mainsnak&quot;:{&quot;snaktype&quot;:&quot;value&quot;,&quot;property&quot;:&quot;P7482&quot;,&quot;hash&quot;:&quot;83568a288a8b8b4714a68e7239d8406833762864&quot;,&quot;datavalue&quot;:{&quot;value&quot;:{&quot;entity-type&quot;:&quot;item&quot;,&quot;numeric-id&quot;:66458942,&quot;id&quot;:&quot;Q66458942&quot;},&quot;type&quot;:&quot;wikibase-entityid&quot;}},&quot;type&quot;:&quot;statement&quot;,&quot;id&quot;:&quot;*********$B1801C92-410F-440E-921F-C231C0AFF8CB&quot;,&quot;rank&quot;:&quot;normal&quot;}]" data-mw-formatvalue="{&quot;{\&quot;value\&quot;:{\&quot;entity-type\&quot;:\&quot;property\&quot;,\&quot;numeric-id\&quot;:7482,\&quot;id\&quot;:\&quot;P7482\&quot;},\&quot;type\&quot;:\&quot;wikibase-entityid\&quot;}&quot;:{&quot;text\/html&quot;:{&quot;en&quot;:{&quot;&quot;:&quot;<a target=\&quot;_blank\&quot; title=\&quot;d:Special:EntityPage\/P7482\&quot; href=https://commons.wikimedia.org/"https:////www.wikidata.org//wiki//Special:EntityPage//P7482/">source of file<\/a>&quot;}},&quot;text\/plain&quot;:{&quot;en&quot;:{&quot;&quot;:&quot;source of file&quot;}}},&quot;{\&quot;value\&quot;:{\&quot;entity-type\&quot;:\&quot;item\&quot;,\&quot;numeric-id\&quot;:66458942,\&quot;id\&quot;:\&quot;Q66458942\&quot;},\&quot;type\&quot;:\&quot;wikibase-entityid\&quot;}&quot;:{&quot;text\/html&quot;:{&quot;en&quot;:{&quot;P7482&quot;:&quot;<a target=\&quot;_blank\&quot; title=\&quot;d:Special:EntityPage\/Q66458942\&quot; href=https://commons.wikimedia.org/"https:////www.wikidata.org//wiki//Special:EntityPage//Q66458942/">original creation by uploader<\/a>&quot;}},&quot;text\/plain&quot;:{&quot;en&quot;:{&quot;P7482&quot;:&quot;original creation by uploader&quot;}}}}" class="wbmi-entityview-statementsGroup wbmi-entityview-statementsGroup-P7482 oo-ui-layout oo-ui-panelLayout oo-ui-panelLayout-framed"><div class="wbmi-statements-widget"><div class="wbmi-statement-header"><div class="wbmi-entity-data"><div class="wbmi-entity-title"><h4 class="wbmi-entity-label"><bdi><a target="_blank" title="d:Special:EntityPage/P7482" href="https://www.wikidata.org/wiki/Special:EntityPage/P7482">source of file</a></bdi></h4></div></div></div><div class="wbmi-content-items-group"><div class="wbmi-item wbmi-item-read"><div class="wbmi-item-container"><div class="wbmi-entity-header"><div class="wbmi-entity-data"><div class="wbmi-entity-title"><h4 class="wbmi-entity-label"><bdi><a target="_blank" title="d:Special:EntityPage/Q66458942" href="https://www.wikidata.org/wiki/Special:EntityPage/Q66458942">original creation by uploader</a></bdi></h4></div></div></div></div></div></div></div></div></div></div></div></div></div>
<div class="printfooter" data-nosnippet="">Retrieved from "<a dir="ltr" href="https://commons.wikimedia.org/w/index.php?title=File:Madafu-chopping.jpg&amp;oldid=1033630111">https://commons.wikimedia.org/w/index.php?title=File:Madafu-chopping.jpg&amp;oldid=1033630111</a>"</div></div>
					<div id="catlinks" class="catlinks" data-mw="interface"><div id="mw-normal-catlinks" class="mw-normal-catlinks"><a href="Special_Categories.html" title="Special:Categories">Category</a>: <ul><li><a href="Category_Food_preparation_in_Kenya.html" title="Category:Food preparation in Kenya">Food preparation in Kenya</a></li></ul></div><div id="mw-hidden-catlinks" class="mw-hidden-catlinks mw-hidden-cats-user-shown">Hidden categories: <ul><li><a href="Category_Images_from_Wiki_Loves_Africa_2014_in_Kenya.html" title="Category:Images from Wiki Loves Africa 2014 in Kenya">Images from Wiki Loves Africa 2014 in Kenya</a></li><li><a href="Category_CC-BY-SA-4.html" title="Category:CC-BY-SA-4.0">CC-BY-SA-4.0</a></li><li><a href="Category_Self-published_work.html" title="Category:Self-published work">Self-published work</a></li><li><a href="Category_Images_from_Wiki_Loves_Africa_2014.html" title="Category:Images from Wiki Loves Africa 2014">Images from Wiki Loves Africa 2014</a></li><li><a href="Category_Uploaded_via_Campaign_wlafrica.html" title="Category:Uploaded via Campaign:wlafrica">Uploaded via Campaign:wlafrica</a></li></ul></div></div>
				</div>
			</main>
			
		</div>
		<div class="mw-footer-container">
			
<footer id="footer" class="mw-footer" >
	<ul id="footer-info">
	<li id="footer-info-lastmod"> This page was last edited on 20 May 2025, at 22:41.</li>
	<li id="footer-info-copyright">Files are available under licenses specified on their description page. All structured data from the file namespace is available under the <a rel="nofollow" class="external text" href="https://creativecommons.org/publicdomain/zero/1.0/">Creative Commons CC0 License</a>; all unstructured text is available under the <a rel="nofollow" class="external text" href="../../creativecommons.org/licenses/by-sa/4.0/index.html">Creative Commons Attribution-ShareAlike License</a>;
additional terms may apply.
By using this site, you agree to the <a class="external text" href="https://foundation.wikimedia.org/wiki/Special:MyLanguage/Policy:Terms_of_Use">Terms of Use</a> and the <a class="external text" href="https://foundation.wikimedia.org/wiki/Special:MyLanguage/Policy:Privacy_policy">Privacy Policy</a>.</li>
</ul>

	<ul id="footer-places">
	<li id="footer-places-privacy"><a href="https://foundation.wikimedia.org/wiki/Special:MyLanguage/Policy:Privacy_policy">Privacy policy</a></li>
	<li id="footer-places-about"><a href="Commons_Welcome.html">About Wikimedia Commons</a></li>
	<li id="footer-places-disclaimers"><a href="Commons_General_disclaimer.html">Disclaimers</a></li>
	<li id="footer-places-wm-codeofconduct"><a href="https://foundation.wikimedia.org/wiki/Special:MyLanguage/Policy:Universal_Code_of_Conduct">Code of Conduct</a></li>
	<li id="footer-places-developers"><a href="https://developer.wikimedia.org/">Developers</a></li>
	<li id="footer-places-statslink"><a href="https://stats.wikimedia.org/#/commons.wikimedia.org">Statistics</a></li>
	<li id="footer-places-cookiestatement"><a href="https://foundation.wikimedia.org/wiki/Special:MyLanguage/Policy:Cookie_statement">Cookie statement</a></li>
	<li id="footer-places-mobileview"><a href="http://commons.m.wikimedia.org/w/index.php?title=File:Madafu-chopping.jpg&amp;mobileaction=toggle_view_mobile" class="noprint stopMobileRedirectToggle">Mobile view</a></li>
</ul>

	<ul id="footer-icons" class="noprint">
	<li id="footer-copyrightico"><a href="https://www.wikimedia.org/" class="cdx-button cdx-button--fake-button cdx-button--size-large cdx-button--fake-button--enabled"><picture><source media="(min-width: 500px)" srcset="/static/images/footer/wikimedia-button.svg" width="84" height="29"><img src="../static/images/footer/wikimedia.svg" width="25" height="25" alt="Wikimedia Foundation" lang="en" loading="lazy"></picture></a></li>
	<li id="footer-poweredbyico"><a href="https://www.mediawiki.org/" class="cdx-button cdx-button--fake-button cdx-button--size-large cdx-button--fake-button--enabled"><picture><source media="(min-width: 500px)" srcset="/w/resources/assets/poweredby_mediawiki.svg" width="88" height="31"><img src="../w/resources/assets/mediawiki_compact.svg" alt="Powered by MediaWiki" lang="en" width="25" height="25" loading="lazy"></picture></a></li>
</ul>

</footer>

		</div>
	</div> 
</div> 
<div class="vector-header-container vector-sticky-header-container no-font-mode-scale">
	<div id="vector-sticky-header" class="vector-sticky-header">
		<div class="vector-sticky-header-start">
			<div class="vector-sticky-header-icon-start vector-button-flush-left vector-button-flush-right" aria-hidden="true">
				<button class="cdx-button cdx-button--weight-quiet cdx-button--icon-only vector-sticky-header-search-toggle" tabindex="-1" data-event-name="ui.vector-sticky-search-form.icon"><span class="vector-icon mw-ui-icon-search mw-ui-icon-wikimedia-search"></span>

<span>Search</span>
			</button>
		</div>
			
		<div role="search" class="vector-search-box-vue vector-search-box">
			<div class="vector-typeahead-search-container">
				<div class="cdx-typeahead-search">
					<form action="https://commons.wikimedia.org/w/index.php" id="vector-sticky-search-form" class="cdx-search-input cdx-search-input--has-end-button">
						<div  class="cdx-search-input__input-wrapper"  data-search-loc="header-moved">
							<div class="cdx-text-input cdx-text-input--has-start-icon">
								<input
									class="cdx-text-input__input mw-searchInput" autocomplete="off"
									
									type="search" name="search" placeholder="Search Wikimedia Commons">
								<span class="cdx-text-input__icon cdx-text-input__start-icon"></span>
							</div>
							<input type="hidden" name="title" value="Special:MediaSearch">
						</div>
						<button class="cdx-button cdx-search-input__end-button">Search</button>
					</form>
				</div>
			</div>
		</div>
		<div class="vector-sticky-header-context-bar">
				<nav aria-label="Contents" class="vector-toc-landmark">
						
					<div id="vector-sticky-header-toc" class="vector-dropdown mw-portlet mw-portlet-sticky-header-toc vector-sticky-header-toc vector-button-flush-left"  >
						<input type="checkbox" id="vector-sticky-header-toc-checkbox" role="button" aria-haspopup="true" data-event-name="ui.dropdown-vector-sticky-header-toc" class="vector-dropdown-checkbox "  aria-label="Toggle the table of contents"  >
						<label id="vector-sticky-header-toc-label" for="vector-sticky-header-toc-checkbox" class="vector-dropdown-label cdx-button cdx-button--fake-button cdx-button--fake-button--enabled cdx-button--weight-quiet cdx-button--icon-only " aria-hidden="true"  ><span class="vector-icon mw-ui-icon-listBullet mw-ui-icon-wikimedia-listBullet"></span>

<span class="vector-dropdown-label-text">Toggle the table of contents</span>
						</label>
						<div class="vector-dropdown-content">
					
						<div id="vector-sticky-header-toc-unpinned-container" class="vector-unpinned-container">
						</div>
					
						</div>
					</div>
			</nav>
				<div class="vector-sticky-header-context-bar-primary" aria-hidden="true" ><span class="mw-page-title-namespace">File</span><span class="mw-page-title-separator">:</span><span class="mw-page-title-main">Madafu-chopping.jpg</span></div>
			</div>
		</div>
		<div class="vector-sticky-header-end" aria-hidden="true">
			<div class="vector-sticky-header-icons">
				<a href="#" class="cdx-button cdx-button--fake-button cdx-button--fake-button--enabled cdx-button--weight-quiet cdx-button--icon-only" id="ca-talk-sticky-header" tabindex="-1" data-event-name="talk-sticky-header"><span class="vector-icon mw-ui-icon-speechBubbles mw-ui-icon-wikimedia-speechBubbles"></span>

<span></span>
			</a>
			<a href="#" class="cdx-button cdx-button--fake-button cdx-button--fake-button--enabled cdx-button--weight-quiet cdx-button--icon-only" id="ca-subject-sticky-header" tabindex="-1" data-event-name="subject-sticky-header"><span class="vector-icon mw-ui-icon-article mw-ui-icon-wikimedia-article"></span>

<span></span>
			</a>
			<a href="#" class="cdx-button cdx-button--fake-button cdx-button--fake-button--enabled cdx-button--weight-quiet cdx-button--icon-only" id="ca-history-sticky-header" tabindex="-1" data-event-name="history-sticky-header"><span class="vector-icon mw-ui-icon-wikimedia-history mw-ui-icon-wikimedia-wikimedia-history"></span>

<span></span>
			</a>
			<a href="#" class="cdx-button cdx-button--fake-button cdx-button--fake-button--enabled cdx-button--weight-quiet cdx-button--icon-only mw-watchlink" id="ca-watchstar-sticky-header" tabindex="-1" data-event-name="watch-sticky-header"><span class="vector-icon mw-ui-icon-wikimedia-star mw-ui-icon-wikimedia-wikimedia-star"></span>

<span></span>
			</a>
			<a href="#" class="cdx-button cdx-button--fake-button cdx-button--fake-button--enabled cdx-button--weight-quiet cdx-button--icon-only" id="ca-edit-sticky-header" tabindex="-1" data-event-name="wikitext-edit-sticky-header"><span class="vector-icon mw-ui-icon-wikimedia-wikiText mw-ui-icon-wikimedia-wikimedia-wikiText"></span>

<span></span>
			</a>
			<a href="#" class="cdx-button cdx-button--fake-button cdx-button--fake-button--enabled cdx-button--weight-quiet cdx-button--icon-only" id="ca-ve-edit-sticky-header" tabindex="-1" data-event-name="ve-edit-sticky-header"><span class="vector-icon mw-ui-icon-wikimedia-edit mw-ui-icon-wikimedia-wikimedia-edit"></span>

<span></span>
			</a>
			<a href="#" class="cdx-button cdx-button--fake-button cdx-button--fake-button--enabled cdx-button--weight-quiet cdx-button--icon-only" id="ca-viewsource-sticky-header" tabindex="-1" data-event-name="ve-edit-protected-sticky-header"><span class="vector-icon mw-ui-icon-wikimedia-editLock mw-ui-icon-wikimedia-wikimedia-editLock"></span>

<span></span>
			</a>
		</div>
			<div class="vector-sticky-header-buttons">
				<a href="#" class="cdx-button cdx-button--fake-button cdx-button--fake-button--enabled cdx-button--weight-quiet cdx-button--action-progressive" id="ca-addsection-sticky-header" tabindex="-1" data-event-name="addsection-sticky-header"><span class="vector-icon mw-ui-icon-speechBubbleAdd-progressive mw-ui-icon-wikimedia-speechBubbleAdd-progressive"></span>

<span>Add topic</span>
			</a>
		</div>
			<div class="vector-sticky-header-icon-end">
				<div class="vector-user-links">
				</div>
			</div>
		</div>
	</div>
</div>
<div class="mw-portlet mw-portlet-dock-bottom emptyPortlet" id="p-dock-bottom">
	<ul>
		
	</ul>
</div>
<script>(RLQ=window.RLQ||[]).push(function(){mw.config.set({"wgHostname":"mw-web.eqiad.main-7b48b5fb74-ftlg7","wgBackendResponseTime":408,"wgPageParseReport":{"limitreport":{"cputime":"0.100","walltime":"0.157","ppvisitednodes":{"value":725,"limit":1000000},"revisionsize":{"value":385,"limit":2097152},"postexpandincludesize":{"value":32610,"limit":2097152},"templateargumentsize":{"value":438,"limit":2097152},"expansiondepth":{"value":17,"limit":100},"expensivefunctioncount":{"value":3,"limit":500},"unstrip-depth":{"value":0,"limit":20},"unstrip-size":{"value":8,"limit":5000000},"entityaccesscount":{"value":1,"limit":400},"timingprofile":["100.00%  130.282      1 -total"," 69.25%   90.217      1 Template:Information"," 34.07%   44.393      1 Template:Wiki_Loves_Africa_2014_country"," 27.20%   35.431      1 Template:Wiki_Loves_Africa_2014_country/layout"," 24.53%   31.964      1 Template:Country"," 18.68%   24.339      1 Template:Wiki_Loves_Africa_2014"," 16.54%   21.547      1 Template:Kenya"," 15.33%   19.975      1 Template:Label"," 14.97%   19.505      1 Template:Wiki_Loves_Africa_2014/layout"," 11.82%   15.400      1 Template:Self"]},"scribunto":{"limitreport-timeusage":{"value":"0.061","limit":"10.000"},"limitreport-memusage":{"value":1421951,"limit":52428800}},"cachereport":{"origin":"mw-web.eqiad.main-7b48b5fb74-ftlg7","timestamp":"20250803020113","ttl":2592000,"transientcontent":false}}});});</script>
<script type="application/ld+json">{"@context":"https:\/\/schema.org","@type":"ImageObject","contentUrl":"https:\/\/upload.wikimedia.org\/wikipedia\/commons\/5\/5f\/Madafu-chopping.jpg","license":"https:\/\/creativecommons.org\/licenses\/by-sa\/4.0","acquireLicensePage":"\/\/commons.wikimedia.org\/wiki\/File:Madafu-chopping.jpg","uploadDate":"2014-11-04 11:09:41"}</script>
</body>

<!-- Mirrored from commons.wikimedia.org/wiki/File:Madafu-chopping.jpg by HTTrack Website Copier/3.x [XR&CO'2014], Sun, 03 Aug 2025 07:49:16 GMT -->
</html>