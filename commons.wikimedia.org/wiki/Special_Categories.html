<!DOCTYPE html>
<html class="client-nojs vector-feature-language-in-header-disabled vector-feature-language-in-main-page-header-disabled vector-feature-page-tools-pinned-disabled vector-feature-toc-pinned-clientpref-1 vector-feature-main-menu-pinned-disabled vector-feature-limited-width-clientpref-1 vector-feature-limited-width-content-disabled vector-feature-custom-font-size-clientpref--excluded vector-feature-appearance-pinned-clientpref-1 vector-feature-night-mode-enabled skin-theme-clientpref-day vector-sticky-header-enabled vector-toc-not-available" lang="en" dir="ltr">

<!-- Mirrored from commons.wikimedia.org/wiki/Special:Categories by HTTrack Website Copier/3.x [XR&CO'2014], Sun, 03 Aug 2025 15:20:47 GMT -->
<!-- Added by HTTrack --><meta http-equiv="content-type" content="text/html;charset=UTF-8" /><!-- /Added by HTTrack -->
<head>
<meta charset="UTF-8">
<title>Categories - Wikimedia Commons</title>
<script>(function(){var className="client-js vector-feature-language-in-header-disabled vector-feature-language-in-main-page-header-disabled vector-feature-page-tools-pinned-disabled vector-feature-toc-pinned-clientpref-1 vector-feature-main-menu-pinned-disabled vector-feature-limited-width-clientpref-1 vector-feature-limited-width-content-disabled vector-feature-custom-font-size-clientpref--excluded vector-feature-appearance-pinned-clientpref-1 vector-feature-night-mode-enabled skin-theme-clientpref-day vector-sticky-header-enabled vector-toc-not-available";var cookie=document.cookie.match(/(?:^|; )commonswikimwclientpreferences=([^;]+)/);if(cookie){cookie[1].split('%2C').forEach(function(pref){className=className.replace(new RegExp('(^| )'+pref.replace(/-clientpref-\w+$|[^\w-]+/g,'')+'-clientpref-\\w+( |$)'),'$1'+pref+'$2');});}document.documentElement.className=className;}());RLCONF={"wgBreakFrames":true,"wgSeparatorTransformTable":["",""],"wgDigitTransformTable":["",""],"wgDefaultDateFormat":"dmy","wgMonthNames":["","January","February","March","April","May","June","July","August","September","October","November","December"],"wgRequestId":"edcb0b4e-58c6-41f4-83da-b4f013c44f03","wgCanonicalNamespace":"Special","wgCanonicalSpecialPageName":"Categories","wgNamespaceNumber":-1,"wgPageName":"Special:Categories","wgTitle":"Categories","wgCurRevisionId":0,"wgRevisionId":0,"wgArticleId":0,"wgIsArticle":false,"wgIsRedirect":false,"wgAction":"view","wgUserName":null,"wgUserGroups":["*"],"wgCategories":[],"wgPageViewLanguage":"en","wgPageContentLanguage":"en","wgPageContentModel":"wikitext","wgRelevantPageName":"Special:Categories","wgRelevantArticleId":0,"wgIsProbablyEditable":false,"wgRelevantPageIsProbablyEditable":false,"wgNoticeProject":"commons","wgCiteReferencePreviewsActive":true,"wgMediaViewerOnClick":true,"wgMediaViewerEnabledByDefault":false,"wgVisualEditor":{"pageLanguageCode":"en","pageLanguageDir":"ltr","pageVariantFallbacks":"en"},"wgMFDisplayWikibaseDescriptions":{"search":true,"watchlist":true,"tagline":true,"nearby":true},"wgWMESchemaEditAttemptStepOversample":false,"wgWMEPageLength":0,"wgMetricsPlatformUserExperiments":{"active_experiments":[],"overrides":[],"enrolled":[],"assigned":[],"subject_ids":[],"sampling_units":[]},"upwizPropertyTitles":{"P180":"Main subjects visible in this work"},"upwizPropertyPlaceholders":{"P180":"e.g., Angolan giraffe"},"upwizPropertyCopyLabels":{"P180":"Main subjects"},"wgEditSubmitButtonLabelPublish":true,"wgULSPosition":"personal","wgULSisCompactLinksEnabled":true,"wgVector2022LanguageInHeader":false,"wgULSisLanguageSelectorEmpty":true,"wbmiDefaultProperties":["P180"],"wbmiPropertyTitles":{"P180":"Items portrayed in this file"},"wbmiPropertyTypes":{"P180":"wikibase-item"},"wbmiRepoApiUrl":"/w/api.php","wbmiHelpUrls":{"P180":"https://commons.wikimedia.org/wiki/Special:MyLanguage/Commons:Depicts"},"wbmiExternalEntitySearchBaseUri":"https://www.wikidata.org/w/api.php","wbmiSupportedDataTypes":["wikibase-item","string","quantity","time","monolingualtext","external-id","globe-coordinate","url"],"wgCheckUserClientHintsHeadersJsApi":["brands","architecture","bitness","fullVersionList","mobile","model","platform","platformVersion"]};
RLSTATE={"ext.gadget.Long-Image-Names-in-Categories":"ready","ext.globalCssJs.user.styles":"ready","site.styles":"ready","user.styles":"ready","ext.globalCssJs.user":"ready","user":"ready","user.options":"loading","mediawiki.helplink":"ready","oojs-ui-core.styles":"ready","oojs-ui.styles.indicators":"ready","mediawiki.widgets.styles":"ready","oojs-ui-core.icons":"ready","mediawiki.htmlform.ooui.styles":"ready","mediawiki.htmlform.styles":"ready","mediawiki.codex.messagebox.styles":"ready","mediawiki.pager.styles":"ready","skins.vector.search.codex.styles":"ready","skins.vector.styles":"ready","skins.vector.icons":"ready","ext.wikimediamessages.styles":"ready","ext.visualEditor.desktopArticleTarget.noscript":"ready","ext.uls.pt":"ready"};RLPAGEMODULES=["ext.xLab","mediawiki.htmlform","mediawiki.htmlform.ooui","mediawiki.widgets","site","mediawiki.page.ready","https://commons.wikimedia.org/wiki/skins.vector.js","ext.centralNotice.geoIP","ext.gadget.Slideshow","ext.gadget.ZoomViewer","ext.gadget.CollapsibleTemplates","ext.gadget.Stockphoto","ext.gadget.WatchlistNotice","ext.gadget.AjaxQuickDelete","ext.gadget.WikiMiniAtlas","ext.gadget.LanguageSelect","ext.gadget.PictureOfTheYearEnhancements","ext.urlShortener.toolbar","ext.centralauth.centralautologin","ext.visualEditor.desktopArticleTarget.init","ext.visualEditor.targetLoader","ext.echo.centralauth","ext.eventLogging","ext.wikimediaEvents","ext.wikimediaEvents.wikibase","ext.navigationTiming","ext.uls.compactlinks","ext.uls.interface","ext.checkUser.clientHints"];</script>
<script>(RLQ=window.RLQ||[]).push(function(){mw.loader.impl(function(){return["user.options@12s5i",function($,jQuery,require,module){mw.user.tokens.set({"patrolToken":"+\\","watchToken":"+\\","csrfToken":"+\\"});
}];});});</script>
<link rel="stylesheet" href="https://commons.wikimedia.org/w/load.php?lang=en&amp;modules=ext.uls.pt%7Cext.visualEditor.desktopArticleTarget.noscript%7Cext.wikimediamessages.styles%7Cmediawiki.codex.messagebox.styles%7Cmediawiki.helplink%7Cmediawiki.htmlform.ooui.styles%7Cmediawiki.htmlform.styles%7Cmediawiki.pager.styles%7Cmediawiki.widgets.styles%7Coojs-ui-core.icons%2Cstyles%7Coojs-ui.styles.indicators%7Cskins.vector.icons%2Cstyles%7Cskins.vector.search.codex.styles&amp;only=styles&amp;skin=vector-2022">
<script async="" src="https://commons.wikimedia.org/w/load.php?lang=en&amp;modules=startup&amp;only=scripts&amp;raw=1&amp;skin=vector-2022"></script>
<meta name="ResourceLoaderDynamicStyles" content="">
<link rel="stylesheet" href="https://commons.wikimedia.org/w/load.php?lang=en&amp;modules=ext.gadget.Long-Image-Names-in-Categories&amp;only=styles&amp;skin=vector-2022">
<link rel="stylesheet" href="https://commons.wikimedia.org/w/load.php?lang=en&amp;modules=site.styles&amp;only=styles&amp;skin=vector-2022">
<meta name="generator" content="MediaWiki 1.45.0-wmf.12">
<meta name="referrer" content="origin">
<meta name="referrer" content="origin-when-cross-origin">
<meta name="robots" content="noindex,nofollow,max-image-preview:standard">
<meta name="format-detection" content="telephone=no">
<meta name="viewport" content="width=1120">
<meta property="og:title" content="Categories - Wikimedia Commons">
<meta property="og:type" content="website">
<link rel="alternate" media="only screen and (max-width: 640px)" href="http://commons.m.wikimedia.org/wiki/Special:Categories">
<link rel="apple-touch-icon" href="https://commons.wikimedia.org/static/apple-touch/commons.png">
<link rel="icon" href="https://commons.wikimedia.org/static/favicon/commons.ico">
<link rel="search" type="application/opensearchdescription+xml" href="https://commons.wikimedia.org/w/rest.php/v1/search" title="Wikimedia Commons">
<link rel="EditURI" type="application/rsd+xml" href="http://commons.wikimedia.org/w/api.php?action=rsd">
<link rel="canonical" href="https://commons.wikimedia.org/wiki/Special:Categories">
<link rel="license" href="https://creativecommons.org/licenses/by-sa/4.0/">
<link rel="alternate" type="application/atom+xml" title="Wikimedia Commons Atom feed" href="https://commons.wikimedia.org/w/index.php?title=Special:RecentChanges&amp;feed=atom">
<link rel="dns-prefetch" href="https://commons.wikimedia.org/wiki/auth.wikimedia.org">
</head>
<body class="skin--responsive skin-vector skin-vector-search-vue mediawiki ltr sitedir-ltr mw-hide-empty-elt ns--1 ns-special mw-special-Categories page-Special_Categories rootpage-Special_Categories skin-vector-2022 action-view"><a class="mw-jump-link" href="#bodyContent">Jump to content</a>
<div class="vector-header-container">
	<header class="vector-header mw-header no-font-mode-scale">
		<div class="vector-header-start">
			<nav class="vector-main-menu-landmark" aria-label="Site">
				
<div id="vector-main-menu-dropdown" class="vector-dropdown vector-main-menu-dropdown vector-button-flush-left vector-button-flush-right"  title="Main menu" >
	<input type="checkbox" id="vector-main-menu-dropdown-checkbox" role="button" aria-haspopup="true" data-event-name="ui.dropdown-vector-main-menu-dropdown" class="vector-dropdown-checkbox "  aria-label="Main menu"  >
	<label id="vector-main-menu-dropdown-label" for="vector-main-menu-dropdown-checkbox" class="vector-dropdown-label cdx-button cdx-button--fake-button cdx-button--fake-button--enabled cdx-button--weight-quiet cdx-button--icon-only " aria-hidden="true"  ><span class="vector-icon mw-ui-icon-menu mw-ui-icon-wikimedia-menu"></span>

<span class="vector-dropdown-label-text">Main menu</span>
	</label>
	<div class="vector-dropdown-content">


				<div id="vector-main-menu-unpinned-container" class="vector-unpinned-container">
		
<div id="vector-main-menu" class="vector-main-menu vector-pinnable-element">
	<div
	class="vector-pinnable-header vector-main-menu-pinnable-header vector-pinnable-header-unpinned"
	data-feature-name="main-menu-pinned"
	data-pinnable-element-id="vector-main-menu"
	data-pinned-container-id="vector-main-menu-pinned-container"
	data-unpinned-container-id="vector-main-menu-unpinned-container"
>
	<div class="vector-pinnable-header-label">Main menu</div>
	<button class="vector-pinnable-header-toggle-button vector-pinnable-header-pin-button" data-event-name="pinnable-header.vector-main-menu.pin">move to sidebar</button>
	<button class="vector-pinnable-header-toggle-button vector-pinnable-header-unpin-button" data-event-name="pinnable-header.vector-main-menu.unpin">hide</button>
</div>

	
<div id="p-navigation" class="vector-menu mw-portlet mw-portlet-navigation"  >
	<div class="vector-menu-heading">
		Navigate
	</div>
	<div class="vector-menu-content">
		
		<ul class="vector-menu-content-list">
			
			<li id="n-mainpage-description" class="mw-list-item"><a href="https://commons.wikimedia.org/wiki/Main_Page" title="Visit the main page [z]" accesskey="z"><span>Main page</span></a></li><li id="n-welcome" class="mw-list-item"><a href="https://commons.wikimedia.org/wiki/Commons:Welcome"><span>Welcome</span></a></li><li id="n-portal" class="mw-list-item"><a href="https://commons.wikimedia.org/wiki/Commons:Community_portal" title="About the project, what you can do, where to find things"><span>Community portal</span></a></li><li id="n-village-pump" class="mw-list-item"><a href="https://commons.wikimedia.org/wiki/Commons:Village_pump"><span>Village pump</span></a></li><li id="n-help" class="mw-list-item"><a href="https://commons.wikimedia.org/wiki/Special:MyLanguage/Help:Contents" title="The place to find out"><span>Help center</span></a></li>
		</ul>
		
	</div>
</div>

	
	
<div id="p-participate" class="vector-menu mw-portlet mw-portlet-participate"  >
	<div class="vector-menu-heading">
		Participate
	</div>
	<div class="vector-menu-content">
		
		<ul class="vector-menu-content-list">
			
			<li id="n-uploadbtn" class="mw-list-item"><a href="https://commons.wikimedia.org/wiki/Special:UploadWizard"><span>Upload file</span></a></li><li id="n-recentchanges" class="mw-list-item"><a href="https://commons.wikimedia.org/wiki/Special:RecentChanges" title="A list of recent changes in the wiki [r]" accesskey="r"><span>Recent changes</span></a></li><li id="n-latestfiles" class="mw-list-item"><a href="https://commons.wikimedia.org/wiki/Special:NewFiles"><span>Latest files</span></a></li><li id="n-randomimage" class="mw-list-item"><a href="https://commons.wikimedia.org/wiki/Special:Random/File" title="Load a random file [x]" accesskey="x"><span>Random file</span></a></li><li id="n-contact" class="mw-list-item"><a href="https://commons.wikimedia.org/wiki/Commons:Contact_us"><span>Contact us</span></a></li><li id="n-specialpages" class="mw-list-item"><a href="https://commons.wikimedia.org/wiki/Special:SpecialPages"><span>Special pages</span></a></li>
		</ul>
		
	</div>
</div>

	
<div id="vector-main-menu" class="vector-menu "  >
	<div class="vector-menu-heading">
		
	</div>
	<div class="vector-menu-content">
		
		<ul class="vector-menu-content-list">
			
			
		</ul>
		
	</div>
</div>

</div>

				</div>

	</div>
</div>

		</nav>
			
<a href="https://commons.wikimedia.org/wiki/Main_Page" class="mw-logo">
	<img class="mw-logo-icon" src="https://commons.wikimedia.org/static/images/icons/commonswiki.svg" alt="" aria-hidden="true" height="50" width="50">
	<span class="mw-logo-container skin-invert">
		<img class="mw-logo-wordmark" alt="Wikimedia Commons" src="https://commons.wikimedia.org/static/images/mobile/copyright/commonswiki-wordmark.svg" style="width: 7.1875em; height: 2em;">
	</span>
</a>

		</div>
		<div class="vector-header-end">
			
<div id="p-search" role="search" class="vector-search-box-vue  vector-search-box-collapses vector-search-box">
	<a href="https://commons.wikimedia.org/wiki/Special:MediaSearch" class="cdx-button cdx-button--fake-button cdx-button--fake-button--enabled cdx-button--weight-quiet cdx-button--icon-only search-toggle" title="Search Wikimedia Commons [f]" accesskey="f"><span class="vector-icon mw-ui-icon-search mw-ui-icon-wikimedia-search"></span>

<span>Search</span>
	</a>
	<div class="vector-typeahead-search-container">
		<div class="cdx-typeahead-search">
			<form action="https://commons.wikimedia.org/w/index.php" id="searchform" class="cdx-search-input cdx-search-input--has-end-button">
				<div id="simpleSearch" class="cdx-search-input__input-wrapper"  data-search-loc="header-moved">
					<div class="cdx-text-input cdx-text-input--has-start-icon">
						<input
							class="cdx-text-input__input mw-searchInput" autocomplete="off"
							 type="search" name="search" placeholder="Search Wikimedia Commons" aria-label="Search Wikimedia Commons" autocapitalize="sentences" spellcheck="false" title="Search Wikimedia Commons [f]" accesskey="f" id="searchInput"
							>
						<span class="cdx-text-input__icon cdx-text-input__start-icon"></span>
					</div>
					<input type="hidden" name="title" value="Special:MediaSearch">
				</div>
				<button class="cdx-button cdx-search-input__end-button">Search</button>
			</form>
		</div>
	</div>
</div>

			<nav class="vector-user-links vector-user-links-wide" aria-label="Personal tools">
	<div class="vector-user-links-main">
	
<div id="p-vector-user-menu-preferences" class="vector-menu mw-portlet"  >
	<div class="vector-menu-content">
		
		<ul class="vector-menu-content-list">
			<li id="ca-uls" class="mw-list-item active user-links-collapsible-item"><a data-mw="interface" href="#" class="uls-trigger cdx-button cdx-button--fake-button cdx-button--fake-button--enabled cdx-button--weight-quiet"><span class="vector-icon mw-ui-icon-wikimedia-language mw-ui-icon-wikimedia-wikimedia-language"></span>

<span>English</span></a>
</li>

			
		</ul>
		
	</div>
</div>

	
<div id="p-vector-user-menu-userpage" class="vector-menu mw-portlet emptyPortlet"  >
	<div class="vector-menu-content">
		
		<ul class="vector-menu-content-list">
			
			
		</ul>
		
	</div>
</div>

	<nav class="vector-appearance-landmark" aria-label="Appearance">
		
<div id="vector-appearance-dropdown" class="vector-dropdown "  title="Change the appearance of the page&#039;s font size, width, and color" >
	<input type="checkbox" id="vector-appearance-dropdown-checkbox" role="button" aria-haspopup="true" data-event-name="ui.dropdown-vector-appearance-dropdown" class="vector-dropdown-checkbox "  aria-label="Appearance"  >
	<label id="vector-appearance-dropdown-label" for="vector-appearance-dropdown-checkbox" class="vector-dropdown-label cdx-button cdx-button--fake-button cdx-button--fake-button--enabled cdx-button--weight-quiet cdx-button--icon-only " aria-hidden="true"  ><span class="vector-icon mw-ui-icon-appearance mw-ui-icon-wikimedia-appearance"></span>

<span class="vector-dropdown-label-text">Appearance</span>
	</label>
	<div class="vector-dropdown-content">


			<div id="vector-appearance-unpinned-container" class="vector-unpinned-container">
				
			</div>
		
	</div>
</div>

	</nav>
	
<div id="p-vector-user-menu-notifications" class="vector-menu mw-portlet emptyPortlet"  >
	<div class="vector-menu-content">
		
		<ul class="vector-menu-content-list">
			
			
		</ul>
		
	</div>
</div>

	
<div id="p-vector-user-menu-overflow" class="vector-menu mw-portlet"  >
	<div class="vector-menu-content">
		
		<ul class="vector-menu-content-list">
			<li id="pt-sitesupport-2" class="user-links-collapsible-item mw-list-item user-links-collapsible-item"><a data-mw="interface" href="https://donate.wikimedia.org/?wmf_source=donate&amp;wmf_medium=sidebar&amp;wmf_campaign=commons.wikimedia.org&amp;uselang=en" class=""><span>Donate</span></a>
</li>
<li id="pt-createaccount-2" class="user-links-collapsible-item mw-list-item user-links-collapsible-item"><a data-mw="interface" href="https://commons.wikimedia.org/w/index.php?title=Special:CreateAccount&amp;returnto=Special%3ACategories" title="You are encouraged to create an account and log in; however, it is not mandatory" class=""><span>Create account</span></a>
</li>
<li id="pt-login-2" class="user-links-collapsible-item mw-list-item user-links-collapsible-item"><a data-mw="interface" href="https://commons.wikimedia.org/w/index.php?title=Special:UserLogin&amp;returnto=Special%3ACategories" title="You are encouraged to log in; however, it is not mandatory [o]" accesskey="o" class=""><span>Log in</span></a>
</li>

			
		</ul>
		
	</div>
</div>

	</div>
	
<div id="vector-user-links-dropdown" class="vector-dropdown vector-user-menu vector-button-flush-right vector-user-menu-logged-out"  title="More options" >
	<input type="checkbox" id="vector-user-links-dropdown-checkbox" role="button" aria-haspopup="true" data-event-name="ui.dropdown-vector-user-links-dropdown" class="vector-dropdown-checkbox "  aria-label="Personal tools"  >
	<label id="vector-user-links-dropdown-label" for="vector-user-links-dropdown-checkbox" class="vector-dropdown-label cdx-button cdx-button--fake-button cdx-button--fake-button--enabled cdx-button--weight-quiet cdx-button--icon-only " aria-hidden="true"  ><span class="vector-icon mw-ui-icon-ellipsis mw-ui-icon-wikimedia-ellipsis"></span>

<span class="vector-dropdown-label-text">Personal tools</span>
	</label>
	<div class="vector-dropdown-content">


		
<div id="p-personal" class="vector-menu mw-portlet mw-portlet-personal user-links-collapsible-item"  title="User menu" >
	<div class="vector-menu-content">
		
		<ul class="vector-menu-content-list">
			
			<li id="pt-sitesupport" class="user-links-collapsible-item mw-list-item"><a href="https://donate.wikimedia.org/?wmf_source=donate&amp;wmf_medium=sidebar&amp;wmf_campaign=commons.wikimedia.org&amp;uselang=en"><span>Donate</span></a></li><li id="pt-createaccount" class="user-links-collapsible-item mw-list-item"><a href="https://commons.wikimedia.org/w/index.php?title=Special:CreateAccount&amp;returnto=Special%3ACategories" title="You are encouraged to create an account and log in; however, it is not mandatory"><span class="vector-icon mw-ui-icon-userAdd mw-ui-icon-wikimedia-userAdd"></span> <span>Create account</span></a></li><li id="pt-login" class="user-links-collapsible-item mw-list-item"><a href="https://commons.wikimedia.org/w/index.php?title=Special:UserLogin&amp;returnto=Special%3ACategories" title="You are encouraged to log in; however, it is not mandatory [o]" accesskey="o"><span class="vector-icon mw-ui-icon-logIn mw-ui-icon-wikimedia-logIn"></span> <span>Log in</span></a></li>
		</ul>
		
	</div>
</div>

<div id="p-user-menu-anon-editor" class="vector-menu mw-portlet mw-portlet-user-menu-anon-editor"  >
	<div class="vector-menu-heading">
		Pages for logged out editors <a href="https://commons.wikimedia.org/wiki/Help:Introduction" aria-label="Learn more about editing"><span>learn more</span></a>
	</div>
	<div class="vector-menu-content">
		
		<ul class="vector-menu-content-list">
			
			<li id="pt-anoncontribs" class="mw-list-item"><a href="https://commons.wikimedia.org/wiki/Special:MyContributions" title="A list of edits made from this IP address [y]" accesskey="y"><span>Contributions</span></a></li><li id="pt-anontalk" class="mw-list-item"><a href="https://commons.wikimedia.org/wiki/Special:MyTalk" title="Discussion about edits from this IP address [n]" accesskey="n"><span>Talk</span></a></li>
		</ul>
		
	</div>
</div>

	
	</div>
</div>

</nav>

		</div>
	</header>
</div>
<div class="mw-page-container">
	<div class="mw-page-container-inner">
		<div class="vector-sitenotice-container">
			<div id="siteNotice"><!-- CentralNotice --></div>
		</div>
		<div class="vector-column-start">
			<div class="vector-main-menu-container">
		<div id="mw-navigation">
			<nav id="mw-panel" class="vector-main-menu-landmark" aria-label="Site">
				<div id="vector-main-menu-pinned-container" class="vector-pinned-container">
				
				</div>
		</nav>
		</div>
	</div>
</div>
		<div class="mw-content-container">
			<main id="content" class="mw-body">
				<header class="mw-body-header vector-page-titlebar no-font-mode-scale">
					<h1 id="firstHeading" class="firstHeading mw-first-heading">Categories</h1>
						<div class="mw-indicators">
		<div id="mw-indicator-mw-helplink" class="mw-indicator"><a href="https://www.mediawiki.org/wiki/Special:MyLanguage/Help:Categories" target="_blank" class="mw-helplink"><span class="mw-helplink-icon"></span>Help</a></div>
		</div>
</header>
				<div class="vector-page-toolbar vector-feature-custom-font-size-clientpref--excluded">
					<div class="vector-page-toolbar-container">
						<div id="left-navigation">
							<nav aria-label="Namespaces">
								
<div id="p-associated-pages" class="vector-menu vector-menu-tabs mw-portlet mw-portlet-associated-pages emptyPortlet"  >
	<div class="vector-menu-content">
		
		<ul class="vector-menu-content-list">
			
			
		</ul>
		
	</div>
</div>

								
<div id="vector-variants-dropdown" class="vector-dropdown emptyPortlet"  >
	<input type="checkbox" id="vector-variants-dropdown-checkbox" role="button" aria-haspopup="true" data-event-name="ui.dropdown-vector-variants-dropdown" class="vector-dropdown-checkbox " aria-label="Change language variant"   >
	<label id="vector-variants-dropdown-label" for="vector-variants-dropdown-checkbox" class="vector-dropdown-label cdx-button cdx-button--fake-button cdx-button--fake-button--enabled cdx-button--weight-quiet" aria-hidden="true"  ><span class="vector-dropdown-label-text">English</span>
	</label>
	<div class="vector-dropdown-content">


					
<div id="p-variants" class="vector-menu mw-portlet mw-portlet-variants emptyPortlet"  >
	<div class="vector-menu-content">
		
		<ul class="vector-menu-content-list">
			
			
		</ul>
		
	</div>
</div>

				
	</div>
</div>

							</nav>
						</div>
						<div id="right-navigation" class="vector-collapsible">
							<nav aria-label="Views">
								
<div id="p-views" class="vector-menu vector-menu-tabs mw-portlet mw-portlet-views emptyPortlet"  >
	<div class="vector-menu-content">
		
		<ul class="vector-menu-content-list">
			
			
		</ul>
		
	</div>
</div>

							</nav>
				
							<nav class="vector-page-tools-landmark" aria-label="Page tools">
								
<div id="vector-page-tools-dropdown" class="vector-dropdown vector-page-tools-dropdown"  >
	<input type="checkbox" id="vector-page-tools-dropdown-checkbox" role="button" aria-haspopup="true" data-event-name="ui.dropdown-vector-page-tools-dropdown" class="vector-dropdown-checkbox "  aria-label="Tools"  >
	<label id="vector-page-tools-dropdown-label" for="vector-page-tools-dropdown-checkbox" class="vector-dropdown-label cdx-button cdx-button--fake-button cdx-button--fake-button--enabled cdx-button--weight-quiet" aria-hidden="true"  ><span class="vector-dropdown-label-text">Tools</span>
	</label>
	<div class="vector-dropdown-content">


									<div id="vector-page-tools-unpinned-container" class="vector-unpinned-container">
						
<div id="vector-page-tools" class="vector-page-tools vector-pinnable-element">
	<div
	class="vector-pinnable-header vector-page-tools-pinnable-header vector-pinnable-header-unpinned"
	data-feature-name="page-tools-pinned"
	data-pinnable-element-id="vector-page-tools"
	data-pinned-container-id="vector-page-tools-pinned-container"
	data-unpinned-container-id="vector-page-tools-unpinned-container"
>
	<div class="vector-pinnable-header-label">Tools</div>
	<button class="vector-pinnable-header-toggle-button vector-pinnable-header-pin-button" data-event-name="pinnable-header.vector-page-tools.pin">move to sidebar</button>
	<button class="vector-pinnable-header-toggle-button vector-pinnable-header-unpin-button" data-event-name="pinnable-header.vector-page-tools.unpin">hide</button>
</div>

	
<div id="p-cactions" class="vector-menu mw-portlet mw-portlet-cactions emptyPortlet"  title="More options" >
	<div class="vector-menu-heading">
		Actions
	</div>
	<div class="vector-menu-content">
		
		<ul class="vector-menu-content-list">
			
			
		</ul>
		
	</div>
</div>

<div id="p-tb" class="vector-menu mw-portlet mw-portlet-tb"  >
	<div class="vector-menu-heading">
		General
	</div>
	<div class="vector-menu-content">
		
		<ul class="vector-menu-content-list">
			
			<li id="t-print" class="mw-list-item"><a href="javascript:print();" rel="alternate" title="Printable version of this page [p]" accesskey="p"><span>Printable version</span></a></li><li id="t-urlshortener" class="mw-list-item"><a href="https://commons.wikimedia.org/w/index.php?title=Special:UrlShortener&amp;url=https%3A%2F%2Fcommons.wikimedia.org%2Fwiki%2FSpecial%3ACategories"><span>Get shortened URL</span></a></li><li id="t-urlshortener-qrcode" class="mw-list-item"><a href="https://commons.wikimedia.org/w/index.php?title=Special:QrCode&amp;url=https%3A%2F%2Fcommons.wikimedia.org%2Fwiki%2FSpecial%3ACategories"><span>Download QR code</span></a></li>
		</ul>
		
	</div>
</div>

<div id="p-wikibase-otherprojects" class="vector-menu mw-portlet mw-portlet-wikibase-otherprojects emptyPortlet"  >
	<div class="vector-menu-heading">
		In other projects
	</div>
	<div class="vector-menu-content">
		
		<ul class="vector-menu-content-list">
			
			
		</ul>
		
	</div>
</div>

</div>

									</div>
				
	</div>
</div>

							</nav>
						</div>
					</div>
				</div>
				<div class="vector-column-end no-font-mode-scale">
					<div class="vector-sticky-pinned-container">
						<nav class="vector-page-tools-landmark" aria-label="Page tools">
							<div id="vector-page-tools-pinned-container" class="vector-pinned-container">
				
							</div>
		</nav>
						<nav class="vector-appearance-landmark" aria-label="Appearance">
							<div id="vector-appearance-pinned-container" class="vector-pinned-container">
				<div id="vector-appearance" class="vector-appearance vector-pinnable-element">
	<div
	class="vector-pinnable-header vector-appearance-pinnable-header vector-pinnable-header-pinned"
	data-feature-name="appearance-pinned"
	data-pinnable-element-id="vector-appearance"
	data-pinned-container-id="vector-appearance-pinned-container"
	data-unpinned-container-id="vector-appearance-unpinned-container"
>
	<div class="vector-pinnable-header-label">Appearance</div>
	<button class="vector-pinnable-header-toggle-button vector-pinnable-header-pin-button" data-event-name="pinnable-header.vector-appearance.pin">move to sidebar</button>
	<button class="vector-pinnable-header-toggle-button vector-pinnable-header-unpin-button" data-event-name="pinnable-header.vector-appearance.unpin">hide</button>
</div>


</div>

							</div>
		</nav>
					</div>
				</div>
				<div id="bodyContent" class="vector-body" aria-labelledby="firstHeading" data-mw-ve-target-container>
					<div class="vector-body-before-content">
					
						
					</div>
					<div id="contentSub"><div id="mw-content-subtitle"></div></div>
					
					
					<div id="mw-content-text" class="mw-body-content"><div class="mw-spcontent"><p>The following categories exist on Commons. 
</p>
<ul><li>For an introduction, see <a href="https://commons.wikimedia.org/wiki/Commons:Categories" title="Commons:Categories">Commons:Categories</a></li>
<li>These categories contain media or pages. In red, it includes <a href="https://commons.wikimedia.org/wiki/Special:WantedCategories" title="Special:WantedCategories">Special:WantedCategories</a> that need a category description page. <a href="https://commons.wikimedia.org/wiki/Special:UnusedCategories" title="Special:UnusedCategories">Special:UnusedCategories</a> are not shown here. <a href="https://commons.wikimedia.org/wiki/Special:PrefixIndex/Category:" title="Special:PrefixIndex/Category:">Special:PrefixIndex/Category</a> lists all categories with existing category description pages.</li></ul><div class='mw-htmlform-ooui-wrapper oo-ui-layout oo-ui-panelLayout oo-ui-panelLayout-padded oo-ui-panelLayout-framed'><form action='https://commons.wikimedia.org/wiki/Special:Categories' method='get' enctype='application/x-www-form-urlencoded' class='mw-htmlform mw-htmlform-ooui oo-ui-layout oo-ui-formLayout'><fieldset class='oo-ui-layout oo-ui-labelElement oo-ui-fieldsetLayout'><legend class='oo-ui-fieldsetLayout-header'><span class='oo-ui-iconElement-icon oo-ui-iconElement-noIcon'></span><span class='oo-ui-labelElement-label'>Categories</span></legend><div class='oo-ui-fieldsetLayout-group'><div class='oo-ui-widget oo-ui-widget-enabled'><div data-mw-modules='mediawiki.widgets' id='ooui-php-2' class='mw-htmlform-field-HTMLTitleTextField mw-htmlform-autoinfuse oo-ui-layout oo-ui-labelElement oo-ui-fieldLayout oo-ui-fieldLayout-align-top' data-ooui='{"_":"mw.htmlform.FieldLayout","fieldWidget":{"tag":"from"},"align":"top","helpInline":true,"$overlay":true,"label":{"html":"Display categories starting at:"},"classes":["mw-htmlform-field-HTMLTitleTextField","mw-htmlform-autoinfuse"]}'><div class='oo-ui-fieldLayout-body'><span class='oo-ui-fieldLayout-header'><label for='ooui-php-1' class='oo-ui-labelElement-label'>Display categories starting at:</label></span><div class='oo-ui-fieldLayout-field'><div id='from' class='oo-ui-widget oo-ui-widget-enabled oo-ui-inputWidget oo-ui-indicatorElement oo-ui-textInputWidget oo-ui-textInputWidget-type-text oo-ui-textInputWidget-php mw-widget-titleInputWidget' data-ooui='{"_":"mw.widgets.TitleInputWidget","namespace":14,"relative":true,"$overlay":true,"maxLength":255,"name":"from","inputId":"ooui-php-1","indicator":"required","required":true}'><input type='text' tabindex='0' name='from' value='' required='' maxlength='255' id='ooui-php-1' class='oo-ui-inputWidget-input' /><span class='oo-ui-iconElement-icon oo-ui-iconElement-noIcon'></span><span class='oo-ui-indicatorElement-indicator oo-ui-indicator-required'></span></div></div></div></div><div class="mw-htmlform-submit-buttons">
<span id='ooui-php-3' class='mw-htmlform-submit oo-ui-widget oo-ui-widget-enabled oo-ui-inputWidget oo-ui-buttonElement oo-ui-buttonElement-framed oo-ui-labelElement oo-ui-flaggedElement-primary oo-ui-flaggedElement-progressive oo-ui-buttonInputWidget' data-ooui='{"_":"OO.ui.ButtonInputWidget","type":"submit","value":"Show","label":"Show","flags":["primary","progressive"],"classes":["mw-htmlform-submit"]}'><button type='submit' tabindex='0' value='Show' class='oo-ui-inputWidget-input oo-ui-buttonElement-button'><span class='oo-ui-iconElement-icon oo-ui-iconElement-noIcon oo-ui-image-invert'></span><span class='oo-ui-labelElement-label'>Show</span><span class='oo-ui-indicatorElement-indicator oo-ui-indicatorElement-noIndicator oo-ui-image-invert'></span></button></span></div>
</div></div></fieldset></form></div><div class="mw-pager-navigation-bar">(<span class="mw-firstlink">first</span> | <a href="https://commons.wikimedia.org/w/index.php?title=Special:Categories&amp;dir=prev" class="mw-lastlink">last</a>) View (<span class="mw-prevlink">previous 50</span> | <a href="https://commons.wikimedia.org/w/index.php?title=Special:Categories&amp;offset=%221749_Del_Sr_Adolfo_Hasse._Sassone%22_Air_%C3%A0_1_v%2C_3_instr._et_basse%2C_en_partition_-_btv1b10887029m" rel="next" class="mw-nextlink">next 50</a>) (<a href="https://commons.wikimedia.org/w/index.php?title=Special:Categories&amp;offset=&amp;limit=20" class="mw-numlink">20</a> | <span class="mw-numlink">50</span> | <a href="https://commons.wikimedia.org/w/index.php?title=Special:Categories&amp;offset=&amp;limit=100" class="mw-numlink">100</a> | <a href="https://commons.wikimedia.org/w/index.php?title=Special:Categories&amp;offset=&amp;limit=250" class="mw-numlink">250</a> | <a href="https://commons.wikimedia.org/w/index.php?title=Special:Categories&amp;offset=&amp;limit=500" class="mw-numlink">500</a>)</div><ul><li><bdi dir="ltr"><a href="https://commons.wikimedia.org/wiki/Category:!!!" title="Category:!!!">!!!</a></bdi> (137 members)</li>
<li><bdi dir="ltr"><a href="https://commons.wikimedia.org/wiki/Category:!---demo" title="Category:!---demo">!---demo</a></bdi> (1 member)</li>
<li><bdi dir="ltr"><a href="https://commons.wikimedia.org/wiki/Category:!Caminhos_pelotenses" title="Category:!Caminhos pelotenses">!Caminhos pelotenses</a></bdi> (2 members)</li>
<li><bdi dir="ltr"><a href="https://commons.wikimedia.org/wiki/Category:!Ilustre_Patrimônio" title="Category:!Ilustre Patrimônio">!Ilustre Patrimônio</a></bdi> (2 members)</li>
<li><bdi dir="ltr"><a href="https://commons.wikimedia.org/wiki/Category:!Khamab_Kalahari_Reserve" title="Category:!Khamab Kalahari Reserve">!Khamab Kalahari Reserve</a></bdi> (2 members)</li>
<li><bdi dir="ltr"><a href="https://commons.wikimedia.org/wiki/Category:!Mais_Teoria_da_História_na_Wiki" title="Category:!Mais Teoria da História na Wiki">!Mais Teoria da História na Wiki</a></bdi> (3 members)</li>
<li><bdi dir="ltr"><a href="https://commons.wikimedia.org/wiki/Category:!Mais_Teoria_da_História_na_Wiki_(Mais_Diversidade)" title="Category:!Mais Teoria da História na Wiki (Mais Diversidade)">!Mais Teoria da História na Wiki (Mais Diversidade)</a></bdi> (36 members)</li>
<li><bdi dir="ltr"><a href="https://commons.wikimedia.org/wiki/Category:!Mais_Teoria_da_História_na_Wiki_(Mais_Mulheres)" title="Category:!Mais Teoria da História na Wiki (Mais Mulheres)">!Mais Teoria da História na Wiki (Mais Mulheres)</a></bdi> (209 members)</li>
<li><bdi dir="ltr"><a href="https://commons.wikimedia.org/wiki/Category:!Mais_Teoria_da_História_na_Wiki_(Povos_originários_-_Oficina_LII)" title="Category:!Mais Teoria da História na Wiki (Povos originários - Oficina LII)">!Mais Teoria da História na Wiki (Povos originários - Oficina LII)</a></bdi> (1 member)</li>
<li><bdi dir="ltr"><a href="https://commons.wikimedia.org/wiki/Category:!Mediengruppe_Bitnik" title="Category:!Mediengruppe Bitnik">!Mediengruppe Bitnik</a></bdi> (3 members)</li>
<li><bdi dir="ltr"><a href="https://commons.wikimedia.org/wiki/Category:!No_Pasaran!-Konferenz_2022" title="Category:!No Pasaran!-Konferenz 2022">!No Pasaran!-Konferenz 2022</a></bdi> (76 members)</li>
<li><bdi dir="ltr"><a href="https://commons.wikimedia.org/wiki/Category:!Projeto_Documenta_Campo_Bom" title="Category:!Projeto Documenta Campo Bom">!Projeto Documenta Campo Bom</a></bdi> (19 members)</li>
<li><bdi dir="ltr"><a href="https://commons.wikimedia.org/wiki/Category:!Projeto_PatrimoniAIs" title="Category:!Projeto PatrimoniAIs">!Projeto PatrimoniAIs</a></bdi> (48 members)</li>
<li><bdi dir="ltr"><a href="https://commons.wikimedia.org/wiki/Category:!Wiki_Loves_Cultura_Popular_2024" title="Category:!Wiki Loves Cultura Popular 2024">!Wiki Loves Cultura Popular 2024</a></bdi> (1 member)</li>
<li><bdi dir="ltr"><a href="https://commons.wikimedia.org/wiki/Category:!Wiki_Loves_Cultura_Popular_2024/Brasil" title="Category:!Wiki Loves Cultura Popular 2024/Brasil">!Wiki Loves Cultura Popular 2024/Brasil</a></bdi> (5 members)</li>
<li><bdi dir="ltr"><a href="https://commons.wikimedia.org/wiki/Category:!Wiki_Ocupa_Alcântara" title="Category:!Wiki Ocupa Alcântara">!Wiki Ocupa Alcântara</a></bdi> (1 member)</li>
<li><bdi dir="ltr"><a href="https://commons.wikimedia.org/wiki/Category:!Xaus_Lodge" title="Category:!Xaus Lodge">!Xaus Lodge</a></bdi> (12 members)</li>
<li><bdi dir="ltr"><a href="https://commons.wikimedia.org/wiki/Category:!_(text)" title="Category:! (text)">! (text)</a></bdi> (12 members)</li>
<li><bdi dir="ltr"><a href="https://commons.wikimedia.org/wiki/Category:!_Sarasa_!_chanson_espagnole...._Paroles_de_J.-J._Cadenus._Musique_de_Juan_Valverde_-_bpt6k1166242z" title="Category:! Sarasa ! chanson espagnole.... Paroles de J.-J. Cadenus. Musique de Juan Valverde - bpt6k1166242z">! Sarasa ! chanson espagnole.... Paroles de J.-J. Cadenus. Musique de Juan Valverde - bpt6k1166242z</a></bdi> (5 members)</li>
<li><bdi dir="ltr"><a href="https://commons.wikimedia.org/wiki/Category:!_for_i" title="Category:! for i">! for i</a></bdi> (34 members)</li>
<li><bdi dir="ltr"><a href="https://commons.wikimedia.org/wiki/Category:!_hidden_temp_category_for_archiving_!" title="Category:! hidden temp category for archiving !">! hidden temp category for archiving !</a></bdi> (0 members)</li>
<li><bdi dir="ltr"><a href="https://commons.wikimedia.org/wiki/Category:!_in_logos" title="Category:! in logos">! in logos</a></bdi> (173 members)</li>
<li><bdi dir="ltr"><a href="https://commons.wikimedia.org/wiki/Category:%22%22Fuite_en_Egypte_;_à_l%27arrière-plan_-_chute_des_idoles_-_btv1b10575412r" title="Category:&quot;&quot;Fuite en Egypte ; à l&#039;arrière-plan - chute des idoles - btv1b10575412r">&quot;&quot;Fuite en Egypte ; à l&#039;arrière-plan - chute des idoles - btv1b10575412r</a></bdi> (3 members)</li>
<li><bdi dir="ltr"><a href="https://commons.wikimedia.org/wiki/Category:%22(...)_1er_manuscrit%22_Duo_pour_hautbois_et_basson_et_piano_sur_des_thèmes_de_Sémiramide_de_Rossini._Par_G._Vogt_(manuscrit_autographe)_-_btv1b108643437" title="Category:&quot;(...) 1er manuscrit&quot; Duo pour hautbois et basson et piano sur des thèmes de Sémiramide de Rossini. Par G. Vogt (manuscrit autographe) - btv1b108643437">&quot;(...) 1er manuscrit&quot; Duo pour hautbois et basson et piano sur des thèmes de Sémiramide de Rossini. Par G. Vogt (manuscrit autographe) - btv1b108643437</a></bdi> (16 members)</li>
<li><bdi dir="ltr"><a href="https://commons.wikimedia.org/wiki/Category:%22(...)_Anthem._Come_unto_Me_for_Chorus_and_Organ_by_Alexandre_Guilmant_op._21._English_text_arranged_by_William_C._Carl%22_(manuscrit_autographe)_-_btv1b10076151s" title="Category:&quot;(...) Anthem. Come unto Me for Chorus and Organ by Alexandre Guilmant op. 21. English text arranged by William C. Carl&quot; (manuscrit autographe) - btv1b10076151s">&quot;(...) Anthem. Come unto Me for Chorus and Organ by Alexandre Guilmant op. 21. English text arranged by William C. Carl&quot; (manuscrit autographe) - btv1b10076151s</a></bdi> (11 members)</li>
<li><bdi dir="ltr"><a href="https://commons.wikimedia.org/wiki/Category:%22(...)_Chœur._Guiraud%22._Avec_orchestre._Pour_le_concours_préparatoire_de_Rome_1859_(manuscrit_autographe)_-_btv1b10864414t" title="Category:&quot;(...) Chœur. Guiraud&quot;. Avec orchestre. Pour le concours préparatoire de Rome 1859 (manuscrit autographe) - btv1b10864414t">&quot;(...) Chœur. Guiraud&quot;. Avec orchestre. Pour le concours préparatoire de Rome 1859 (manuscrit autographe) - btv1b10864414t</a></bdi> (29 members)</li>
<li><bdi dir="ltr"><a href="https://commons.wikimedia.org/wiki/Category:%22(...)_Duetto_Concertato_Per_Piano_Forte_e_Chitarra_Del_Sig.r_F.do_Carulli._Piano_Forte.%22_En_la_-_btv1b10877233s" title="Category:&quot;(...) Duetto Concertato Per Piano Forte e Chitarra Del Sig.r F.do Carulli. Piano Forte.&quot; En la - btv1b10877233s">&quot;(...) Duetto Concertato Per Piano Forte e Chitarra Del Sig.r F.do Carulli. Piano Forte.&quot; En la - btv1b10877233s</a></bdi> (16 members)</li>
<li><bdi dir="ltr"><a href="https://commons.wikimedia.org/wiki/Category:%22(...)_die_dreyfache_Hochzitt_im_Bäsethal%22._Opéra._Musique_de_J._B._Weckerlin_(manuscrit_autographe)_-_btv1b52514292d" title="Category:&quot;(...) die dreyfache Hochzitt im Bäsethal&quot;. Opéra. Musique de J. B. Weckerlin (manuscrit autographe) - btv1b52514292d">&quot;(...) die dreyfache Hochzitt im Bäsethal&quot;. Opéra. Musique de J. B. Weckerlin (manuscrit autographe) - btv1b52514292d</a></bdi> (166 members)</li>
<li><bdi dir="ltr"><a href="https://commons.wikimedia.org/wiki/Category:%22(...)_fugue%22_du_concours_préparatoire_de_Rome_1859._Par_E._Guiraud_(manuscrit_autographe)_-_btv1b10864411g" title="Category:&quot;(...) fugue&quot; du concours préparatoire de Rome 1859. Par E. Guiraud (manuscrit autographe) - btv1b10864411g">&quot;(...) fugue&quot; du concours préparatoire de Rome 1859. Par E. Guiraud (manuscrit autographe) - btv1b10864411g</a></bdi> (14 members)</li>
<li><bdi dir="ltr"><a href="https://commons.wikimedia.org/wiki/Category:%22(...)_la_Esmeralda._(...)_Pas_de_deux%22._Avec_accompagnement._Ballet._Musique_de_C._Pugni_-_btv1b108788213" title="Category:&quot;(...) la Esmeralda. (...) Pas de deux&quot;. Avec accompagnement. Ballet. Musique de C. Pugni - btv1b108788213">&quot;(...) la Esmeralda. (...) Pas de deux&quot;. Avec accompagnement. Ballet. Musique de C. Pugni - btv1b108788213</a></bdi> (19 members)</li>
<li><bdi dir="ltr"><a href="https://commons.wikimedia.org/wiki/Category:%22*%22_for_%22×%22" title="Category:&quot;*&quot; for &quot;×&quot;">&quot;*&quot; for &quot;×&quot;</a></bdi> (2 members)</li>
<li><bdi dir="ltr"><a href="https://commons.wikimedia.org/wiki/Category:%22---demo" title="Category:&quot;---demo">&quot;---demo</a></bdi> (1 member)</li>
<li><bdi dir="ltr"><a href="https://commons.wikimedia.org/wiki/Category:%22..._Epithalame._(Poème_de)_Maurice_Bouchor._(Musique_d%27)_Ernest_Chausson,_op._14.%22_Mélodie_pour_1_voix_et_piano_(manuscrit_autographe)_-_btv1b10861225w" title="Category:&quot;... Epithalame. (Poème de) Maurice Bouchor. (Musique d&#039;) Ernest Chausson, op. 14.&quot; Mélodie pour 1 voix et piano (manuscrit autographe) - btv1b10861225w">&quot;... Epithalame. (Poème de) Maurice Bouchor. (Musique d&#039;) Ernest Chausson, op. 14.&quot; Mélodie pour 1 voix et piano (manuscrit autographe) - btv1b10861225w</a></bdi> (8 members)</li>
<li><bdi dir="ltr"><a href="https://commons.wikimedia.org/wiki/Category:%22...is_watching_you!%22" title="Category:&quot;...is watching you!&quot;">&quot;...is watching you!&quot;</a></bdi> (2 members)</li>
<li><bdi dir="ltr"><a href="https://commons.wikimedia.org/wiki/Category:%221._Suite_de_Noels._arrangés_par_Mr_Simon%22_pour_orgue_ou_clavecin_-_btv1b10075490s" title="Category:&quot;1. Suite de Noels. arrangés par Mr Simon&quot; pour orgue ou clavecin - btv1b10075490s">&quot;1. Suite de Noels. arrangés par Mr Simon&quot; pour orgue ou clavecin - btv1b10075490s</a></bdi> (11 members)</li>
<li><bdi dir="ltr"><a href="https://commons.wikimedia.org/wiki/Category:%221000_Gestalten%22_–_art_action_at_2017_G20_Hamburg_summit" title="Category:&quot;1000 Gestalten&quot; – art action at 2017 G20 Hamburg summit">&quot;1000 Gestalten&quot; – art action at 2017 G20 Hamburg summit</a></bdi> (67 members)</li>
<li><bdi dir="ltr"><a href="https://commons.wikimedia.org/wiki/Category:%2212_Duetti_per_due_Soprani,_e_Cembalo_Del_Sig.r_Giovanni_Amadeo_Nauman%22_-_btv1b10070958w" title="Category:&quot;12 Duetti per due Soprani, e Cembalo Del Sig.r Giovanni Amadeo Nauman&quot; - btv1b10070958w">&quot;12 Duetti per due Soprani, e Cembalo Del Sig.r Giovanni Amadeo Nauman&quot; - btv1b10070958w</a></bdi> (63 members)</li>
<li><bdi dir="ltr"><a href="https://commons.wikimedia.org/wiki/Category:%2212_Morceaux_pour_l%27Orgue_expressif%22._(SN_792)_(manuscrit_autographe)_-_btv1b108835188" title="Category:&quot;12 Morceaux pour l&#039;Orgue expressif&quot;. (SN 792) (manuscrit autographe) - btv1b108835188">&quot;12 Morceaux pour l&#039;Orgue expressif&quot;. (SN 792) (manuscrit autographe) - btv1b108835188</a></bdi> (15 members)</li>
<li><bdi dir="ltr"><a href="https://commons.wikimedia.org/wiki/Category:%2212_Variations_pour_le_Piano-forte%22._(SN_160)_(manuscrit_autographe)_-_btv1b10883594s" title="Category:&quot;12 Variations pour le Piano-forte&quot;. (SN 160) (manuscrit autographe) - btv1b10883594s">&quot;12 Variations pour le Piano-forte&quot;. (SN 160) (manuscrit autographe) - btv1b10883594s</a></bdi> (13 members)</li>
<li><bdi dir="ltr"><a href="https://commons.wikimedia.org/wiki/Category:%2212e_Barcarolle._Gabriel_Fauré%22._Pour_piano_(manuscrit_autographe)_-_btv1b550094812" title="Category:&quot;12e Barcarolle. Gabriel Fauré&quot;. Pour piano (manuscrit autographe) - btv1b550094812">&quot;12e Barcarolle. Gabriel Fauré&quot;. Pour piano (manuscrit autographe) - btv1b550094812</a></bdi> (27 members)</li>
<li><bdi dir="ltr"><a href="https://commons.wikimedia.org/wiki/Category:%2215me%22_Recueil_de_nouvelles_contre-danses_et_walzers_pour_deux_violons_faisant_suite_à_Auguste_par_Marin_Pichon..._-_btv1b108834734" title="Category:&quot;15me&quot; Recueil de nouvelles contre-danses et walzers pour deux violons faisant suite à Auguste par Marin Pichon... - btv1b108834734">&quot;15me&quot; Recueil de nouvelles contre-danses et walzers pour deux violons faisant suite à Auguste par Marin Pichon... - btv1b108834734</a></bdi> (27 members)</li>
<li><bdi dir="ltr"><a href="https://commons.wikimedia.org/wiki/Category:%2215ème%22_Concerto_de_Viotti._Violon_principal_-_btv1b108816899" title="Category:&quot;15ème&quot; Concerto de Viotti. Violon principal - btv1b108816899">&quot;15ème&quot; Concerto de Viotti. Violon principal - btv1b108816899</a></bdi> (15 members)</li>
<li><bdi dir="ltr"><a href="https://commons.wikimedia.org/wiki/Category:%2216e%22_Recueil_d%27airs_d%27opéra_et_opéra_comiques_arrangés_pour_deux_flûtes..._-_btv1b9081812s" title="Category:&quot;16e&quot; Recueil d&#039;airs d&#039;opéra et opéra comiques arrangés pour deux flûtes... - btv1b9081812s">&quot;16e&quot; Recueil d&#039;airs d&#039;opéra et opéra comiques arrangés pour deux flûtes... - btv1b9081812s</a></bdi> (54 members)</li>
<li><bdi dir="ltr"><a href="https://commons.wikimedia.org/wiki/Category:%2216me%22_Recueil_de_nouvelles_contre-danses_et_walzers_pour_deux_violons_faisant_suite_à_Auguste_par_Marin_Pichon..._-_btv1b108834751" title="Category:&quot;16me&quot; Recueil de nouvelles contre-danses et walzers pour deux violons faisant suite à Auguste par Marin Pichon... - btv1b108834751">&quot;16me&quot; Recueil de nouvelles contre-danses et walzers pour deux violons faisant suite à Auguste par Marin Pichon... - btv1b108834751</a></bdi> (26 members)</li>
<li><bdi dir="ltr"><a href="https://commons.wikimedia.org/wiki/Category:%221740._Sinfonia_con_Trombe,_Corni_da_Caccia_Oboe_Violini,_Viola_e_Basso._Del_sig._Gaetano_Latilla%22_-_btv1b10071382h" title="Category:&quot;1740. Sinfonia con Trombe, Corni da Caccia Oboe Violini, Viola e Basso. Del sig. Gaetano Latilla&quot; - btv1b10071382h">&quot;1740. Sinfonia con Trombe, Corni da Caccia Oboe Violini, Viola e Basso. Del sig. Gaetano Latilla&quot; - btv1b10071382h</a></bdi> (22 members)</li>
<li><bdi dir="ltr"><a href="https://commons.wikimedia.org/wiki/Category:%221747%22_In_S._Gio_-_Grisostomo_Il_Carnovale._Del_Sig.r_Gio_-_Adolfo_Hasse_Sasso%22._Air_à_1_v.,_3_instr._et_basse_en_partition_-_btv1b108831429" title="Category:&quot;1747&quot; In S. Gio - Grisostomo Il Carnovale. Del Sig.r Gio - Adolfo Hasse Sasso&quot;. Air à 1 v., 3 instr. et basse en partition - btv1b108831429">&quot;1747&quot; In S. Gio - Grisostomo Il Carnovale. Del Sig.r Gio - Adolfo Hasse Sasso&quot;. Air à 1 v., 3 instr. et basse en partition - btv1b108831429</a></bdi> (10 members)</li>
<li><bdi dir="ltr"><a href="https://commons.wikimedia.org/wiki/Category:%221747_S._Gio_Crisostomo_In_Venezia_Il_Carnavale_Del_Sig.r_Gio_-_Adolfo_Hasse_Sassone%22._Air_à_1_v.,_3_instr._et_basse_en_partition_-_btv1b10883140d" title="Category:&quot;1747 S. Gio Crisostomo In Venezia Il Carnavale Del Sig.r Gio - Adolfo Hasse Sassone&quot;. Air à 1 v., 3 instr. et basse en partition - btv1b10883140d">&quot;1747 S. Gio Crisostomo In Venezia Il Carnavale Del Sig.r Gio - Adolfo Hasse Sassone&quot;. Air à 1 v., 3 instr. et basse en partition - btv1b10883140d</a></bdi> (8 members)</li>
<li><bdi dir="ltr"><a href="https://commons.wikimedia.org/wiki/Category:%221749._Del_Sig.r_Adolfo_Hasse_Sossone%22._Air_à_1_v.,_2_fl.,_2_v.ons,_alt._et_basse_en_partition_-_btv1b10886961r" title="Category:&quot;1749. Del Sig.r Adolfo Hasse Sossone&quot;. Air à 1 v., 2 fl., 2 v.ons, alt. et basse en partition - btv1b10886961r">&quot;1749. Del Sig.r Adolfo Hasse Sossone&quot;. Air à 1 v., 2 fl., 2 v.ons, alt. et basse en partition - btv1b10886961r</a></bdi> (8 members)</li>
<li><bdi dir="ltr"><a href="https://commons.wikimedia.org/wiki/Category:%221749._Del_Sr_Adolfo_Hasse_Sassone%22_Air_à_1_voix,_3_instruments_et_basse_en_partition_-_btv1b10886958g" title="Category:&quot;1749. Del Sr Adolfo Hasse Sassone&quot; Air à 1 voix, 3 instruments et basse en partition - btv1b10886958g">&quot;1749. Del Sr Adolfo Hasse Sassone&quot; Air à 1 voix, 3 instruments et basse en partition - btv1b10886958g</a></bdi> (8 members)</li>
<li><bdi dir="ltr"><a href="https://commons.wikimedia.org/wiki/Category:%221749_Del_Sr_Adolfo_Hasse._Sassone%22_Air_à_1_v,_3_instr._et_basse,_en_partition_-_btv1b10887029m" title="Category:&quot;1749 Del Sr Adolfo Hasse. Sassone&quot; Air à 1 v, 3 instr. et basse, en partition - btv1b10887029m">&quot;1749 Del Sr Adolfo Hasse. Sassone&quot; Air à 1 v, 3 instr. et basse, en partition - btv1b10887029m</a></bdi> (8 members)</li>
</ul><div class="mw-pager-navigation-bar">(<span class="mw-firstlink">first</span> | <a href="https://commons.wikimedia.org/w/index.php?title=Special:Categories&amp;dir=prev" class="mw-lastlink">last</a>) View (<span class="mw-prevlink">previous 50</span> | <a href="https://commons.wikimedia.org/w/index.php?title=Special:Categories&amp;offset=%221749_Del_Sr_Adolfo_Hasse._Sassone%22_Air_%C3%A0_1_v%2C_3_instr._et_basse%2C_en_partition_-_btv1b10887029m" rel="next" class="mw-nextlink">next 50</a>) (<a href="https://commons.wikimedia.org/w/index.php?title=Special:Categories&amp;offset=&amp;limit=20" class="mw-numlink">20</a> | <span class="mw-numlink">50</span> | <a href="https://commons.wikimedia.org/w/index.php?title=Special:Categories&amp;offset=&amp;limit=100" class="mw-numlink">100</a> | <a href="https://commons.wikimedia.org/w/index.php?title=Special:Categories&amp;offset=&amp;limit=250" class="mw-numlink">250</a> | <a href="https://commons.wikimedia.org/w/index.php?title=Special:Categories&amp;offset=&amp;limit=500" class="mw-numlink">500</a>)</div></div><!--esi <esi:include src="/esitest-fa8a495983347898/content" /> --><noscript><img src="https://commons.wikimedia.org/wiki/Special:CentralAutoLogin/start?type=1x1&amp;usesul3=1" alt="" width="1" height="1" style="border: none; position: absolute;"></noscript>
<div class="printfooter" data-nosnippet="">Retrieved from "<a dir="ltr" href="https://commons.wikimedia.org/wiki/Special:Categories">https://commons.wikimedia.org/wiki/Special:Categories</a>"</div></div>
					<div id="catlinks" class="catlinks catlinks-allhidden" data-mw="interface"></div>
				</div>
			</main>
			
		</div>
		<div class="mw-footer-container">
			
<footer id="footer" class="mw-footer" >
	<ul id="footer-info">
</ul>

	<ul id="footer-places">
	<li id="footer-places-privacy"><a href="https://foundation.wikimedia.org/wiki/Special:MyLanguage/Policy:Privacy_policy">Privacy policy</a></li>
	<li id="footer-places-about"><a href="https://commons.wikimedia.org/wiki/Commons:Welcome">About Wikimedia Commons</a></li>
	<li id="footer-places-disclaimers"><a href="https://commons.wikimedia.org/wiki/Commons:General_disclaimer">Disclaimers</a></li>
	<li id="footer-places-wm-codeofconduct"><a href="https://foundation.wikimedia.org/wiki/Special:MyLanguage/Policy:Universal_Code_of_Conduct">Code of Conduct</a></li>
	<li id="footer-places-developers"><a href="https://developer.wikimedia.org/">Developers</a></li>
	<li id="footer-places-statslink"><a href="https://stats.wikimedia.org/#/commons.wikimedia.org">Statistics</a></li>
	<li id="footer-places-cookiestatement"><a href="https://foundation.wikimedia.org/wiki/Special:MyLanguage/Policy:Cookie_statement">Cookie statement</a></li>
	<li id="footer-places-mobileview"><a href="http://commons.m.wikimedia.org/w/index.php?title=Special:Categories&amp;mobileaction=toggle_view_mobile" class="noprint stopMobileRedirectToggle">Mobile view</a></li>
</ul>

	<ul id="footer-icons" class="noprint">
	<li id="footer-copyrightico"><a href="https://www.wikimedia.org/" class="cdx-button cdx-button--fake-button cdx-button--size-large cdx-button--fake-button--enabled"><picture><source media="(min-width: 500px)" srcset="/static/images/footer/wikimedia-button.svg" width="84" height="29"><img src="https://commons.wikimedia.org/static/images/footer/wikimedia.svg" width="25" height="25" alt="Wikimedia Foundation" lang="en" loading="lazy"></picture></a></li>
	<li id="footer-poweredbyico"><a href="https://www.mediawiki.org/" class="cdx-button cdx-button--fake-button cdx-button--size-large cdx-button--fake-button--enabled"><picture><source media="(min-width: 500px)" srcset="/w/resources/assets/poweredby_mediawiki.svg" width="88" height="31"><img src="https://commons.wikimedia.org/w/resources/assets/mediawiki_compact.svg" alt="Powered by MediaWiki" lang="en" width="25" height="25" loading="lazy"></picture></a></li>
</ul>

</footer>

		</div>
	</div> 
</div> 
<div class="vector-header-container vector-sticky-header-container no-font-mode-scale">
	<div id="vector-sticky-header" class="vector-sticky-header">
		<div class="vector-sticky-header-start">
			<div class="vector-sticky-header-icon-start vector-button-flush-left vector-button-flush-right" aria-hidden="true">
				<button class="cdx-button cdx-button--weight-quiet cdx-button--icon-only vector-sticky-header-search-toggle" tabindex="-1" data-event-name="ui.vector-sticky-search-form.icon"><span class="vector-icon mw-ui-icon-search mw-ui-icon-wikimedia-search"></span>

<span>Search</span>
			</button>
		</div>
			
		<div role="search" class="vector-search-box-vue vector-search-box">
			<div class="vector-typeahead-search-container">
				<div class="cdx-typeahead-search">
					<form action="https://commons.wikimedia.org/w/index.php" id="vector-sticky-search-form" class="cdx-search-input cdx-search-input--has-end-button">
						<div  class="cdx-search-input__input-wrapper"  data-search-loc="header-moved">
							<div class="cdx-text-input cdx-text-input--has-start-icon">
								<input
									class="cdx-text-input__input mw-searchInput" autocomplete="off"
									
									type="search" name="search" placeholder="Search Wikimedia Commons">
								<span class="cdx-text-input__icon cdx-text-input__start-icon"></span>
							</div>
							<input type="hidden" name="title" value="Special:MediaSearch">
						</div>
						<button class="cdx-button cdx-search-input__end-button">Search</button>
					</form>
				</div>
			</div>
		</div>
		<div class="vector-sticky-header-context-bar">
				<div class="vector-sticky-header-context-bar-primary" aria-hidden="true" >Categories</div>
			</div>
		</div>
		<div class="vector-sticky-header-end" aria-hidden="true">
			<div class="vector-sticky-header-icons">
				<a href="#" class="cdx-button cdx-button--fake-button cdx-button--fake-button--enabled cdx-button--weight-quiet cdx-button--icon-only" id="ca-talk-sticky-header" tabindex="-1" data-event-name="talk-sticky-header"><span class="vector-icon mw-ui-icon-speechBubbles mw-ui-icon-wikimedia-speechBubbles"></span>

<span></span>
			</a>
			<a href="#" class="cdx-button cdx-button--fake-button cdx-button--fake-button--enabled cdx-button--weight-quiet cdx-button--icon-only" id="ca-subject-sticky-header" tabindex="-1" data-event-name="subject-sticky-header"><span class="vector-icon mw-ui-icon-article mw-ui-icon-wikimedia-article"></span>

<span></span>
			</a>
			<a href="#" class="cdx-button cdx-button--fake-button cdx-button--fake-button--enabled cdx-button--weight-quiet cdx-button--icon-only" id="ca-history-sticky-header" tabindex="-1" data-event-name="history-sticky-header"><span class="vector-icon mw-ui-icon-wikimedia-history mw-ui-icon-wikimedia-wikimedia-history"></span>

<span></span>
			</a>
			<a href="#" class="cdx-button cdx-button--fake-button cdx-button--fake-button--enabled cdx-button--weight-quiet cdx-button--icon-only mw-watchlink" id="ca-watchstar-sticky-header" tabindex="-1" data-event-name="watch-sticky-header"><span class="vector-icon mw-ui-icon-wikimedia-star mw-ui-icon-wikimedia-wikimedia-star"></span>

<span></span>
			</a>
			<a href="#" class="cdx-button cdx-button--fake-button cdx-button--fake-button--enabled cdx-button--weight-quiet cdx-button--icon-only" id="ca-edit-sticky-header" tabindex="-1" data-event-name="wikitext-edit-sticky-header"><span class="vector-icon mw-ui-icon-wikimedia-wikiText mw-ui-icon-wikimedia-wikimedia-wikiText"></span>

<span></span>
			</a>
			<a href="#" class="cdx-button cdx-button--fake-button cdx-button--fake-button--enabled cdx-button--weight-quiet cdx-button--icon-only" id="ca-ve-edit-sticky-header" tabindex="-1" data-event-name="ve-edit-sticky-header"><span class="vector-icon mw-ui-icon-wikimedia-edit mw-ui-icon-wikimedia-wikimedia-edit"></span>

<span></span>
			</a>
			<a href="#" class="cdx-button cdx-button--fake-button cdx-button--fake-button--enabled cdx-button--weight-quiet cdx-button--icon-only" id="ca-viewsource-sticky-header" tabindex="-1" data-event-name="ve-edit-protected-sticky-header"><span class="vector-icon mw-ui-icon-wikimedia-editLock mw-ui-icon-wikimedia-wikimedia-editLock"></span>

<span></span>
			</a>
		</div>
			<div class="vector-sticky-header-buttons">
				<a href="#" class="cdx-button cdx-button--fake-button cdx-button--fake-button--enabled cdx-button--weight-quiet cdx-button--action-progressive" id="ca-addsection-sticky-header" tabindex="-1" data-event-name="addsection-sticky-header"><span class="vector-icon mw-ui-icon-speechBubbleAdd-progressive mw-ui-icon-wikimedia-speechBubbleAdd-progressive"></span>

<span>Add topic</span>
			</a>
		</div>
			<div class="vector-sticky-header-icon-end">
				<div class="vector-user-links">
				</div>
			</div>
		</div>
	</div>
</div>
<div class="mw-portlet mw-portlet-dock-bottom emptyPortlet" id="p-dock-bottom">
	<ul>
		
	</ul>
</div>
<script>(RLQ=window.RLQ||[]).push(function(){mw.config.set({"wgHostname":"mw-web.eqiad.main-7b48b5fb74-s6zfw","wgBackendResponseTime":158});});</script>
</body>

<!-- Mirrored from commons.wikimedia.org/wiki/Special:Categories by HTTrack Website Copier/3.x [XR&CO'2014], Sun, 03 Aug 2025 15:20:47 GMT -->
</html>