<!DOCTYPE html>
<html class="client-nojs vector-feature-language-in-header-disabled vector-feature-language-in-main-page-header-disabled vector-feature-page-tools-pinned-disabled vector-feature-toc-pinned-clientpref-1 vector-feature-main-menu-pinned-disabled vector-feature-limited-width-clientpref-1 vector-feature-limited-width-content-disabled vector-feature-custom-font-size-clientpref--excluded vector-feature-appearance-pinned-clientpref-1 vector-feature-night-mode-enabled skin-theme-clientpref-day vector-sticky-header-enabled vector-toc-not-available" lang="en" dir="ltr">

<!-- Mirrored from commons.wikimedia.org/wiki/Special:Contributions/Kungu01 by HTTrack Website Copier/3.x [XR&CO'2014], Sun, 03 Aug 2025 09:00:40 GMT -->
<!-- Added by HTTrack --><meta http-equiv="content-type" content="text/html;charset=UTF-8" /><!-- /Added by HTTrack -->
<head>
<meta charset="UTF-8">
<title>User contributions for Kungu01 - Wikimedia Commons</title>
<script>(function(){var className="client-js vector-feature-language-in-header-disabled vector-feature-language-in-main-page-header-disabled vector-feature-page-tools-pinned-disabled vector-feature-toc-pinned-clientpref-1 vector-feature-main-menu-pinned-disabled vector-feature-limited-width-clientpref-1 vector-feature-limited-width-content-disabled vector-feature-custom-font-size-clientpref--excluded vector-feature-appearance-pinned-clientpref-1 vector-feature-night-mode-enabled skin-theme-clientpref-day vector-sticky-header-enabled vector-toc-not-available";var cookie=document.cookie.match(/(?:^|; )commonswikimwclientpreferences=([^;]+)/);if(cookie){cookie[1].split('%2C').forEach(function(pref){className=className.replace(new RegExp('(^| )'+pref.replace(/-clientpref-\w+$|[^\w-]+/g,'')+'-clientpref-\\w+( |$)'),'$1'+pref+'$2');});}document.documentElement.className=className;}());RLCONF={"wgBreakFrames":false,"wgSeparatorTransformTable":["",""],"wgDigitTransformTable":["",""],"wgDefaultDateFormat":"dmy","wgMonthNames":["","January","February","March","April","May","June","July","August","September","October","November","December"],"wgRequestId":"70d78be3-0bb7-4c3c-b52d-a6310e2c09a1","wgCanonicalNamespace":"Special","wgCanonicalSpecialPageName":"Contributions","wgNamespaceNumber":-1,"wgPageName":"Special:Contributions/Kungu01","wgTitle":"Contributions/Kungu01","wgCurRevisionId":0,"wgRevisionId":0,"wgArticleId":0,"wgIsArticle":false,"wgIsRedirect":false,"wgAction":"view","wgUserName":null,"wgUserGroups":["*"],"wgCategories":[],"wgPageViewLanguage":"en","wgPageContentLanguage":"en","wgPageContentModel":"wikitext","wgRelevantPageName":"Special:Contributions/Kungu01","wgRelevantArticleId":0,"wgIsProbablyEditable":false,"wgRelevantPageIsProbablyEditable":false,"wgRelevantUserName":"Kungu01","wgNoticeProject":"commons","wgCiteReferencePreviewsActive":true,"wgMediaViewerOnClick":true,"wgMediaViewerEnabledByDefault":false,"wgVisualEditor":{"pageLanguageCode":"en","pageLanguageDir":"ltr","pageVariantFallbacks":"en"},"wgMFDisplayWikibaseDescriptions":{"search":true,"watchlist":true,"tagline":true,"nearby":true},"wgWMESchemaEditAttemptStepOversample":false,"wgWMEPageLength":0,"wgMetricsPlatformUserExperiments":{"active_experiments":[],"overrides":[],"enrolled":[],"assigned":[],"subject_ids":[],"sampling_units":[]},"upwizPropertyTitles":{"P180":"Main subjects visible in this work"},"upwizPropertyPlaceholders":{"P180":"e.g., Angolan giraffe"},"upwizPropertyCopyLabels":{"P180":"Main subjects"},"wgEditSubmitButtonLabelPublish":true,"thanks-confirmation-required":true,"wgULSPosition":"personal","wgULSisCompactLinksEnabled":true,"wgVector2022LanguageInHeader":false,"wgULSisLanguageSelectorEmpty":true,"wbmiDefaultProperties":["P180"],"wbmiPropertyTitles":{"P180":"Items portrayed in this file"},"wbmiPropertyTypes":{"P180":"wikibase-item"},"wbmiRepoApiUrl":"/w/api.php","wbmiHelpUrls":{"P180":"https://commons.wikimedia.org/wiki/Special:MyLanguage/Commons:Depicts"},"wbmiExternalEntitySearchBaseUri":"https://www.wikidata.org/w/api.php","wbmiSupportedDataTypes":["wikibase-item","string","quantity","time","monolingualtext","external-id","globe-coordinate","url"],"wgCheckUserClientHintsHeadersJsApi":["brands","architecture","bitness","fullVersionList","mobile","model","platform","platformVersion"]};
RLSTATE={"ext.gadget.Long-Image-Names-in-Categories":"ready","ext.globalCssJs.user.styles":"ready","site.styles":"ready","user.styles":"ready","ext.globalCssJs.user":"ready","user":"ready","user.options":"loading","jquery.makeCollapsible.styles":"ready","mediawiki.interface.helpers.styles":"ready","mediawiki.special":"ready","mediawiki.special.changeslist":"ready","mediawiki.helplink":"ready","oojs-ui-core.styles":"ready","oojs-ui.styles.indicators":"ready","mediawiki.widgets.styles":"ready","oojs-ui-core.icons":"ready","mediawiki.htmlform.ooui.styles":"ready","mediawiki.htmlform.styles":"ready","mediawiki.codex.messagebox.styles":"ready","mediawiki.widgets.DateInputWidget.styles":"ready","mediawiki.pager.styles":"ready","skins.vector.search.codex.styles":"ready","skins.vector.styles":"ready","skins.vector.icons":"ready","mediawiki.feedlink":"ready","ext.wikimediamessages.styles":"ready","ext.visualEditor.desktopArticleTarget.noscript":"ready","ext.uls.pt":"ready"};RLPAGEMODULES=["ext.xLab","mediawiki.page.ready","mediawiki.special.contributions","mediawiki.htmlform","jquery.makeCollapsible","mediawiki.htmlform.ooui","mediawiki.widgets.UserInputWidget","mediawiki.widgets","oojs-ui-widgets","mediawiki.widgets.DateInputWidget","site","skins.vector.html","ext.centralNotice.geoIP","ext.gadget.Slideshow","ext.gadget.ZoomViewer","ext.gadget.CollapsibleTemplates","ext.gadget.Stockphoto","ext.gadget.WatchlistNotice","ext.gadget.AjaxQuickDelete","ext.gadget.WikiMiniAtlas","ext.gadget.LanguageSelect","ext.gadget.PictureOfTheYearEnhancements","ext.urlShortener.toolbar","ext.centralauth.centralautologin","mmv.bootstrap","ext.visualEditor.desktopArticleTarget.init","ext.visualEditor.targetLoader","ext.echo.centralauth","ext.thanks.corethank","ext.eventLogging","ext.wikimediaEvents","ext.wikimediaEvents.wikibase","ext.navigationTiming","ext.uls.compactlinks","ext.uls.interface","ext.checkUser.clientHints"];</script>
<script>(RLQ=window.RLQ||[]).push(function(){mw.loader.impl(function(){return["user.options@12s5i",function($,jQuery,require,module){mw.user.tokens.set({"patrolToken":"+\\","watchToken":"+\\","csrfToken":"+\\"});
}];});});</script>
<link rel="stylesheet" href="../../w/load4f70.css?lang=en&amp;modules=ext.uls.pt%7Cext.visualEditor.desktopArticleTarget.noscript%7Cext.wikimediamessages.styles%7Cjquery.makeCollapsible.styles%7Cmediawiki.codex.messagebox.styles%7Cmediawiki.feedlink%2Chelplink%2Cspecial%7Cmediawiki.htmlform.ooui.styles%7Cmediawiki.htmlform.styles%7Cmediawiki.interface.helpers.styles%7Cmediawiki.pager.styles%7Cmediawiki.special.changeslist%7Cmediawiki.widgets.DateInputWidget.styles%7Cmediawiki.widgets.styles%7Coojs-ui-core.icons%2Cstyles%7Coojs-ui.styles.indicators%7Cskins.vector.icons%2Cstyles%7Cskins.vector.search.codex.styles&amp;only=styles&amp;skin=vector-2022">
<script async="" src="../../w/load9565.php?lang=en&amp;modules=startup&amp;only=scripts&amp;raw=1&amp;skin=vector-2022"></script>
<meta name="ResourceLoaderDynamicStyles" content="">
<link rel="stylesheet" href="../../w/load3e3b.css?lang=en&amp;modules=ext.gadget.Long-Image-Names-in-Categories&amp;only=styles&amp;skin=vector-2022">
<link rel="stylesheet" href="../../w/loada24d.css?lang=en&amp;modules=site.styles&amp;only=styles&amp;skin=vector-2022">
<meta name="generator" content="MediaWiki 1.45.0-wmf.12">
<meta name="referrer" content="origin">
<meta name="referrer" content="origin-when-cross-origin">
<meta name="robots" content="noindex,nofollow,max-image-preview:standard">
<meta name="format-detection" content="telephone=no">
<meta name="viewport" content="width=1120">
<meta property="og:title" content="User contributions for Kungu01 - Wikimedia Commons">
<meta property="og:type" content="website">
<link rel="preconnect" href="http://upload.wikimedia.org/">
<link rel="alternate" media="only screen and (max-width: 640px)" href="http://commons.m.wikimedia.org/wiki/Special:Contributions/Kungu01">
<link rel="apple-touch-icon" href="https://commons.wikimedia.org/static/apple-touch/commons.png">
<link rel="icon" href="https://commons.wikimedia.org/static/favicon/commons.ico">
<link rel="search" type="application/opensearchdescription+xml" href="https://commons.wikimedia.org/w/rest.php/v1/search" title="Wikimedia Commons">
<link rel="EditURI" type="application/rsd+xml" href="http://commons.wikimedia.org/w/api.php?action=rsd">
<link rel="canonical" href="https://commons.wikimedia.org/wiki/Special:Contributions/Kungu01">
<link rel="license" href="https://creativecommons.org/licenses/by-sa/4.0/">
<link rel="alternate" type="application/atom+xml" title="&quot;Special:Contributions/Kungu01&quot; Atom feed" href="https://commons.wikimedia.org/w/api.php?action=feedcontributions&amp;user=Kungu01&amp;feedformat=atom">
<link rel="alternate" type="application/atom+xml" title="Wikimedia Commons Atom feed" href="https://commons.wikimedia.org/w/index.php?title=Special:RecentChanges&amp;feed=atom">
<link rel="dns-prefetch" href="https://commons.wikimedia.org/wiki/Special:Contributions/auth.wikimedia.org">
</head>
<body class="mw-special-ContributionsSpecialPage skin--responsive skin-vector skin-vector-search-vue mediawiki ltr sitedir-ltr mw-hide-empty-elt ns--1 ns-special mw-special-Contributions page-Special_Contributions_Kungu01 rootpage-Special_Contributions_Kungu01 skin-vector-2022 action-view"><a class="mw-jump-link" href="#bodyContent">Jump to content</a>
<div class="vector-header-container">
	<header class="vector-header mw-header no-font-mode-scale">
		<div class="vector-header-start">
			<nav class="vector-main-menu-landmark" aria-label="Site">
				
<div id="vector-main-menu-dropdown" class="vector-dropdown vector-main-menu-dropdown vector-button-flush-left vector-button-flush-right"  title="Main menu" >
	<input type="checkbox" id="vector-main-menu-dropdown-checkbox" role="button" aria-haspopup="true" data-event-name="ui.dropdown-vector-main-menu-dropdown" class="vector-dropdown-checkbox "  aria-label="Main menu"  >
	<label id="vector-main-menu-dropdown-label" for="vector-main-menu-dropdown-checkbox" class="vector-dropdown-label cdx-button cdx-button--fake-button cdx-button--fake-button--enabled cdx-button--weight-quiet cdx-button--icon-only " aria-hidden="true"  ><span class="vector-icon mw-ui-icon-menu mw-ui-icon-wikimedia-menu"></span>

<span class="vector-dropdown-label-text">Main menu</span>
	</label>
	<div class="vector-dropdown-content">


				<div id="vector-main-menu-unpinned-container" class="vector-unpinned-container">
		
<div id="vector-main-menu" class="vector-main-menu vector-pinnable-element">
	<div
	class="vector-pinnable-header vector-main-menu-pinnable-header vector-pinnable-header-unpinned"
	data-feature-name="main-menu-pinned"
	data-pinnable-element-id="vector-main-menu"
	data-pinned-container-id="vector-main-menu-pinned-container"
	data-unpinned-container-id="vector-main-menu-unpinned-container"
>
	<div class="vector-pinnable-header-label">Main menu</div>
	<button class="vector-pinnable-header-toggle-button vector-pinnable-header-pin-button" data-event-name="pinnable-header.vector-main-menu.pin">move to sidebar</button>
	<button class="vector-pinnable-header-toggle-button vector-pinnable-header-unpin-button" data-event-name="pinnable-header.vector-main-menu.unpin">hide</button>
</div>

	
<div id="p-navigation" class="vector-menu mw-portlet mw-portlet-navigation"  >
	<div class="vector-menu-heading">
		Navigate
	</div>
	<div class="vector-menu-content">
		
		<ul class="vector-menu-content-list">
			
			<li id="n-mainpage-description" class="mw-list-item"><a href="https://commons.wikimedia.org/wiki/Main_Page" title="Visit the main page [z]" accesskey="z"><span>Main page</span></a></li><li id="n-welcome" class="mw-list-item"><a href="https://commons.wikimedia.org/wiki/Commons:Welcome"><span>Welcome</span></a></li><li id="n-portal" class="mw-list-item"><a href="https://commons.wikimedia.org/wiki/Commons:Community_portal" title="About the project, what you can do, where to find things"><span>Community portal</span></a></li><li id="n-village-pump" class="mw-list-item"><a href="https://commons.wikimedia.org/wiki/Commons:Village_pump"><span>Village pump</span></a></li><li id="n-help" class="mw-list-item"><a href="https://commons.wikimedia.org/wiki/Special:MyLanguage/Help:Contents" title="The place to find out"><span>Help center</span></a></li>
		</ul>
		
	</div>
</div>

	
	
<div id="p-participate" class="vector-menu mw-portlet mw-portlet-participate"  >
	<div class="vector-menu-heading">
		Participate
	</div>
	<div class="vector-menu-content">
		
		<ul class="vector-menu-content-list">
			
			<li id="n-uploadbtn" class="mw-list-item"><a href="https://commons.wikimedia.org/wiki/Special:UploadWizard"><span>Upload file</span></a></li><li id="n-recentchanges" class="mw-list-item"><a href="https://commons.wikimedia.org/wiki/Special:RecentChanges" title="A list of recent changes in the wiki [r]" accesskey="r"><span>Recent changes</span></a></li><li id="n-latestfiles" class="mw-list-item"><a href="https://commons.wikimedia.org/wiki/Special:NewFiles"><span>Latest files</span></a></li><li id="n-randomimage" class="mw-list-item"><a href="https://commons.wikimedia.org/wiki/Special:Random/File" title="Load a random file [x]" accesskey="x"><span>Random file</span></a></li><li id="n-contact" class="mw-list-item"><a href="https://commons.wikimedia.org/wiki/Commons:Contact_us"><span>Contact us</span></a></li><li id="n-specialpages" class="mw-list-item"><a href="https://commons.wikimedia.org/wiki/Special:SpecialPages"><span>Special pages</span></a></li>
		</ul>
		
	</div>
</div>

	
<div id="vector-main-menu" class="vector-menu "  >
	<div class="vector-menu-heading">
		
	</div>
	<div class="vector-menu-content">
		
		<ul class="vector-menu-content-list">
			
			
		</ul>
		
	</div>
</div>

</div>

				</div>

	</div>
</div>

		</nav>
			
<a href="https://commons.wikimedia.org/wiki/Main_Page" class="mw-logo">
	<img class="mw-logo-icon" src="https://commons.wikimedia.org/static/images/icons/commonswiki.svg" alt="" aria-hidden="true" height="50" width="50">
	<span class="mw-logo-container skin-invert">
		<img class="mw-logo-wordmark" alt="Wikimedia Commons" src="https://commons.wikimedia.org/static/images/mobile/copyright/commonswiki-wordmark.svg" style="width: 7.1875em; height: 2em;">
	</span>
</a>

		</div>
		<div class="vector-header-end">
			
<div id="p-search" role="search" class="vector-search-box-vue  vector-search-box-collapses vector-search-box">
	<a href="https://commons.wikimedia.org/wiki/Special:MediaSearch" class="cdx-button cdx-button--fake-button cdx-button--fake-button--enabled cdx-button--weight-quiet cdx-button--icon-only search-toggle" title="Search Wikimedia Commons [f]" accesskey="f"><span class="vector-icon mw-ui-icon-search mw-ui-icon-wikimedia-search"></span>

<span>Search</span>
	</a>
	<div class="vector-typeahead-search-container">
		<div class="cdx-typeahead-search">
			<form action="https://commons.wikimedia.org/w/index.php" id="searchform" class="cdx-search-input cdx-search-input--has-end-button">
				<div id="simpleSearch" class="cdx-search-input__input-wrapper"  data-search-loc="header-moved">
					<div class="cdx-text-input cdx-text-input--has-start-icon">
						<input
							class="cdx-text-input__input mw-searchInput" autocomplete="off"
							 type="search" name="search" placeholder="Search Wikimedia Commons" aria-label="Search Wikimedia Commons" autocapitalize="sentences" spellcheck="false" title="Search Wikimedia Commons [f]" accesskey="f" id="searchInput"
							>
						<span class="cdx-text-input__icon cdx-text-input__start-icon"></span>
					</div>
					<input type="hidden" name="title" value="Special:MediaSearch">
				</div>
				<button class="cdx-button cdx-search-input__end-button">Search</button>
			</form>
		</div>
	</div>
</div>

			<nav class="vector-user-links vector-user-links-wide" aria-label="Personal tools">
	<div class="vector-user-links-main">
	
<div id="p-vector-user-menu-preferences" class="vector-menu mw-portlet"  >
	<div class="vector-menu-content">
		
		<ul class="vector-menu-content-list">
			<li id="ca-uls" class="mw-list-item active user-links-collapsible-item"><a data-mw="interface" href="#" class="uls-trigger cdx-button cdx-button--fake-button cdx-button--fake-button--enabled cdx-button--weight-quiet"><span class="vector-icon mw-ui-icon-wikimedia-language mw-ui-icon-wikimedia-wikimedia-language"></span>

<span>English</span></a>
</li>

			
		</ul>
		
	</div>
</div>

	
<div id="p-vector-user-menu-userpage" class="vector-menu mw-portlet emptyPortlet"  >
	<div class="vector-menu-content">
		
		<ul class="vector-menu-content-list">
			
			
		</ul>
		
	</div>
</div>

	<nav class="vector-appearance-landmark" aria-label="Appearance">
		
<div id="vector-appearance-dropdown" class="vector-dropdown "  title="Change the appearance of the page&#039;s font size, width, and color" >
	<input type="checkbox" id="vector-appearance-dropdown-checkbox" role="button" aria-haspopup="true" data-event-name="ui.dropdown-vector-appearance-dropdown" class="vector-dropdown-checkbox "  aria-label="Appearance"  >
	<label id="vector-appearance-dropdown-label" for="vector-appearance-dropdown-checkbox" class="vector-dropdown-label cdx-button cdx-button--fake-button cdx-button--fake-button--enabled cdx-button--weight-quiet cdx-button--icon-only " aria-hidden="true"  ><span class="vector-icon mw-ui-icon-appearance mw-ui-icon-wikimedia-appearance"></span>

<span class="vector-dropdown-label-text">Appearance</span>
	</label>
	<div class="vector-dropdown-content">


			<div id="vector-appearance-unpinned-container" class="vector-unpinned-container">
				
			</div>
		
	</div>
</div>

	</nav>
	
<div id="p-vector-user-menu-notifications" class="vector-menu mw-portlet emptyPortlet"  >
	<div class="vector-menu-content">
		
		<ul class="vector-menu-content-list">
			
			
		</ul>
		
	</div>
</div>

	
<div id="p-vector-user-menu-overflow" class="vector-menu mw-portlet"  >
	<div class="vector-menu-content">
		
		<ul class="vector-menu-content-list">
			<li id="pt-sitesupport-2" class="user-links-collapsible-item mw-list-item user-links-collapsible-item"><a data-mw="interface" href="https://donate.wikimedia.org/?wmf_source=donate&amp;wmf_medium=sidebar&amp;wmf_campaign=commons.wikimedia.org&amp;uselang=en" class=""><span>Donate</span></a>
</li>
<li id="pt-createaccount-2" class="user-links-collapsible-item mw-list-item user-links-collapsible-item"><a data-mw="interface" href="https://commons.wikimedia.org/w/index.php?title=Special:CreateAccount&amp;returnto=Special%3AContributions%2FKungu01" title="You are encouraged to create an account and log in; however, it is not mandatory" class=""><span>Create account</span></a>
</li>
<li id="pt-login-2" class="user-links-collapsible-item mw-list-item user-links-collapsible-item"><a data-mw="interface" href="https://commons.wikimedia.org/w/index.php?title=Special:UserLogin&amp;returnto=Special%3AContributions%2FKungu01" title="You are encouraged to log in; however, it is not mandatory [o]" accesskey="o" class=""><span>Log in</span></a>
</li>

			
		</ul>
		
	</div>
</div>

	</div>
	
<div id="vector-user-links-dropdown" class="vector-dropdown vector-user-menu vector-button-flush-right vector-user-menu-logged-out"  title="More options" >
	<input type="checkbox" id="vector-user-links-dropdown-checkbox" role="button" aria-haspopup="true" data-event-name="ui.dropdown-vector-user-links-dropdown" class="vector-dropdown-checkbox "  aria-label="Personal tools"  >
	<label id="vector-user-links-dropdown-label" for="vector-user-links-dropdown-checkbox" class="vector-dropdown-label cdx-button cdx-button--fake-button cdx-button--fake-button--enabled cdx-button--weight-quiet cdx-button--icon-only " aria-hidden="true"  ><span class="vector-icon mw-ui-icon-ellipsis mw-ui-icon-wikimedia-ellipsis"></span>

<span class="vector-dropdown-label-text">Personal tools</span>
	</label>
	<div class="vector-dropdown-content">


		
<div id="p-personal" class="vector-menu mw-portlet mw-portlet-personal user-links-collapsible-item"  title="User menu" >
	<div class="vector-menu-content">
		
		<ul class="vector-menu-content-list">
			
			<li id="pt-sitesupport" class="user-links-collapsible-item mw-list-item"><a href="https://donate.wikimedia.org/?wmf_source=donate&amp;wmf_medium=sidebar&amp;wmf_campaign=commons.wikimedia.org&amp;uselang=en"><span>Donate</span></a></li><li id="pt-createaccount" class="user-links-collapsible-item mw-list-item"><a href="https://commons.wikimedia.org/w/index.php?title=Special:CreateAccount&amp;returnto=Special%3AContributions%2FKungu01" title="You are encouraged to create an account and log in; however, it is not mandatory"><span class="vector-icon mw-ui-icon-userAdd mw-ui-icon-wikimedia-userAdd"></span> <span>Create account</span></a></li><li id="pt-login" class="user-links-collapsible-item mw-list-item"><a href="https://commons.wikimedia.org/w/index.php?title=Special:UserLogin&amp;returnto=Special%3AContributions%2FKungu01" title="You are encouraged to log in; however, it is not mandatory [o]" accesskey="o"><span class="vector-icon mw-ui-icon-logIn mw-ui-icon-wikimedia-logIn"></span> <span>Log in</span></a></li>
		</ul>
		
	</div>
</div>

<div id="p-user-menu-anon-editor" class="vector-menu mw-portlet mw-portlet-user-menu-anon-editor"  >
	<div class="vector-menu-heading">
		Pages for logged out editors <a href="https://commons.wikimedia.org/wiki/Help:Introduction" aria-label="Learn more about editing"><span>learn more</span></a>
	</div>
	<div class="vector-menu-content">
		
		<ul class="vector-menu-content-list">
			
			<li id="pt-anoncontribs" class="mw-list-item"><a href="https://commons.wikimedia.org/wiki/Special:MyContributions" title="A list of edits made from this IP address [y]" accesskey="y"><span>Contributions</span></a></li><li id="pt-anontalk" class="mw-list-item"><a href="https://commons.wikimedia.org/wiki/Special:MyTalk" title="Discussion about edits from this IP address [n]" accesskey="n"><span>Talk</span></a></li>
		</ul>
		
	</div>
</div>

	
	</div>
</div>

</nav>

		</div>
	</header>
</div>
<div class="mw-page-container">
	<div class="mw-page-container-inner">
		<div class="vector-sitenotice-container">
			<div id="siteNotice"><!-- CentralNotice --></div>
		</div>
		<div class="vector-column-start">
			<div class="vector-main-menu-container">
		<div id="mw-navigation">
			<nav id="mw-panel" class="vector-main-menu-landmark" aria-label="Site">
				<div id="vector-main-menu-pinned-container" class="vector-pinned-container">
				
				</div>
		</nav>
		</div>
	</div>
</div>
		<div class="mw-content-container">
			<main id="content" class="mw-body">
				<header class="mw-body-header vector-page-titlebar no-font-mode-scale">
					<h1 id="firstHeading" class="firstHeading mw-first-heading">User contributions for <bdi>Kungu01</bdi></h1>
						<div class="mw-indicators">
		<div id="mw-indicator-mw-helplink" class="mw-indicator"><a href="https://www.mediawiki.org/wiki/Special:MyLanguage/Help:User_contributions" target="_blank" class="mw-helplink"><span class="mw-helplink-icon"></span>Help</a></div>
		</div>
</header>
				<div class="vector-page-toolbar vector-feature-custom-font-size-clientpref--excluded">
					<div class="vector-page-toolbar-container">
						<div id="left-navigation">
							<nav aria-label="Namespaces">
								
<div id="p-associated-pages" class="vector-menu vector-menu-tabs mw-portlet mw-portlet-associated-pages emptyPortlet"  >
	<div class="vector-menu-content">
		
		<ul class="vector-menu-content-list">
			
			
		</ul>
		
	</div>
</div>

								
<div id="vector-variants-dropdown" class="vector-dropdown emptyPortlet"  >
	<input type="checkbox" id="vector-variants-dropdown-checkbox" role="button" aria-haspopup="true" data-event-name="ui.dropdown-vector-variants-dropdown" class="vector-dropdown-checkbox " aria-label="Change language variant"   >
	<label id="vector-variants-dropdown-label" for="vector-variants-dropdown-checkbox" class="vector-dropdown-label cdx-button cdx-button--fake-button cdx-button--fake-button--enabled cdx-button--weight-quiet" aria-hidden="true"  ><span class="vector-dropdown-label-text">English</span>
	</label>
	<div class="vector-dropdown-content">


					
<div id="p-variants" class="vector-menu mw-portlet mw-portlet-variants emptyPortlet"  >
	<div class="vector-menu-content">
		
		<ul class="vector-menu-content-list">
			
			
		</ul>
		
	</div>
</div>

				
	</div>
</div>

							</nav>
						</div>
						<div id="right-navigation" class="vector-collapsible">
							<nav aria-label="Views">
								
<div id="p-views" class="vector-menu vector-menu-tabs mw-portlet mw-portlet-views emptyPortlet"  >
	<div class="vector-menu-content">
		
		<ul class="vector-menu-content-list">
			
			
		</ul>
		
	</div>
</div>

							</nav>
				
							<nav class="vector-page-tools-landmark" aria-label="Page tools">
								
<div id="vector-page-tools-dropdown" class="vector-dropdown vector-page-tools-dropdown"  >
	<input type="checkbox" id="vector-page-tools-dropdown-checkbox" role="button" aria-haspopup="true" data-event-name="ui.dropdown-vector-page-tools-dropdown" class="vector-dropdown-checkbox "  aria-label="Tools"  >
	<label id="vector-page-tools-dropdown-label" for="vector-page-tools-dropdown-checkbox" class="vector-dropdown-label cdx-button cdx-button--fake-button cdx-button--fake-button--enabled cdx-button--weight-quiet" aria-hidden="true"  ><span class="vector-dropdown-label-text">Tools</span>
	</label>
	<div class="vector-dropdown-content">


									<div id="vector-page-tools-unpinned-container" class="vector-unpinned-container">
						
<div id="vector-page-tools" class="vector-page-tools vector-pinnable-element">
	<div
	class="vector-pinnable-header vector-page-tools-pinnable-header vector-pinnable-header-unpinned"
	data-feature-name="page-tools-pinned"
	data-pinnable-element-id="vector-page-tools"
	data-pinned-container-id="vector-page-tools-pinned-container"
	data-unpinned-container-id="vector-page-tools-unpinned-container"
>
	<div class="vector-pinnable-header-label">Tools</div>
	<button class="vector-pinnable-header-toggle-button vector-pinnable-header-pin-button" data-event-name="pinnable-header.vector-page-tools.pin">move to sidebar</button>
	<button class="vector-pinnable-header-toggle-button vector-pinnable-header-unpin-button" data-event-name="pinnable-header.vector-page-tools.unpin">hide</button>
</div>

	
<div id="p-cactions" class="vector-menu mw-portlet mw-portlet-cactions emptyPortlet"  title="More options" >
	<div class="vector-menu-heading">
		Actions
	</div>
	<div class="vector-menu-content">
		
		<ul class="vector-menu-content-list">
			
			
		</ul>
		
	</div>
</div>

<div id="p-tb" class="vector-menu mw-portlet mw-portlet-tb"  >
	<div class="vector-menu-heading">
		General
	</div>
	<div class="vector-menu-content">
		
		<ul class="vector-menu-content-list">
			
			<li id="feedlinks" class="mw-list-item"><a href="https://commons.wikimedia.org/w/api.php?action=feedcontributions&amp;user=Kungu01&amp;feedformat=atom" id="feed-atom" rel="alternate" type="application/atom+xml" class="feedlink" title="Atom feed for this page"><span>Atom</span></a></li><li id="tb-uploads" class="mw-list-item"><a href="https://commons.wikimedia.org/w/index.php?title=Special:ListFiles/Kungu01&amp;ilshowall=1" title="A list of uploads by this user"><span>User uploads</span></a></li><li id="t-contributions" class="mw-list-item"><a href="https://commons.wikimedia.org/wiki/Special:Contributions/Kungu01" title="A list of contributions by this user"><span>User contributions</span></a></li><li id="t-log" class="mw-list-item"><a href="https://commons.wikimedia.org/wiki/Special:Log/Kungu01"><span>Logs</span></a></li><li id="t-userrights" class="mw-list-item"><a href="https://commons.wikimedia.org/wiki/Special:UserRights/Kungu01"><span>View user groups</span></a></li><li id="t-print" class="mw-list-item"><a href="javascript:print();" rel="alternate" title="Printable version of this page [p]" accesskey="p"><span>Printable version</span></a></li><li id="t-urlshortener" class="mw-list-item"><a href="https://commons.wikimedia.org/w/index.php?title=Special:UrlShortener&amp;url=https%3A%2F%2Fcommons.wikimedia.org%2Fwiki%2FSpecial%3AContributions%2FKungu01"><span>Get shortened URL</span></a></li><li id="t-urlshortener-qrcode" class="mw-list-item"><a href="https://commons.wikimedia.org/w/index.php?title=Special:QrCode&amp;url=https%3A%2F%2Fcommons.wikimedia.org%2Fwiki%2FSpecial%3AContributions%2FKungu01"><span>Download QR code</span></a></li>
		</ul>
		
	</div>
</div>

<div id="p-wikibase-otherprojects" class="vector-menu mw-portlet mw-portlet-wikibase-otherprojects emptyPortlet"  >
	<div class="vector-menu-heading">
		In other projects
	</div>
	<div class="vector-menu-content">
		
		<ul class="vector-menu-content-list">
			
			
		</ul>
		
	</div>
</div>

</div>

									</div>
				
	</div>
</div>

							</nav>
						</div>
					</div>
				</div>
				<div class="vector-column-end no-font-mode-scale">
					<div class="vector-sticky-pinned-container">
						<nav class="vector-page-tools-landmark" aria-label="Page tools">
							<div id="vector-page-tools-pinned-container" class="vector-pinned-container">
				
							</div>
		</nav>
						<nav class="vector-appearance-landmark" aria-label="Appearance">
							<div id="vector-appearance-pinned-container" class="vector-pinned-container">
				<div id="vector-appearance" class="vector-appearance vector-pinnable-element">
	<div
	class="vector-pinnable-header vector-appearance-pinnable-header vector-pinnable-header-pinned"
	data-feature-name="appearance-pinned"
	data-pinnable-element-id="vector-appearance"
	data-pinned-container-id="vector-appearance-pinned-container"
	data-unpinned-container-id="vector-appearance-unpinned-container"
>
	<div class="vector-pinnable-header-label">Appearance</div>
	<button class="vector-pinnable-header-toggle-button vector-pinnable-header-pin-button" data-event-name="pinnable-header.vector-appearance.pin">move to sidebar</button>
	<button class="vector-pinnable-header-toggle-button vector-pinnable-header-unpin-button" data-event-name="pinnable-header.vector-appearance.unpin">hide</button>
</div>


</div>

							</div>
		</nav>
					</div>
				</div>
				<div id="bodyContent" class="vector-body" aria-labelledby="firstHeading" data-mw-ve-target-container>
					<div class="vector-body-before-content">
					
						
					</div>
					<div id="contentSub"><div id="mw-content-subtitle"><div class="mw-contributions-user-tools">Results for <bdi><a href="https://commons.wikimedia.org/w/index.php?title=User:Kungu01&amp;action=edit&amp;redlink=1" class="new" title="User:Kungu01 (page does not exist)">Kungu01</a></bdi> <span class="mw-changeslist-links"><span><a href="https://commons.wikimedia.org/wiki/User_talk:Kungu01" class="mw-contributions-link-talk" title="User talk:Kungu01">talk</a></span> <span><a href="https://commons.wikimedia.org/w/index.php?title=Special:Log/block&amp;page=User%3AKungu01" class="mw-contributions-link-block-log" title="Special:Log/block">block log</a></span> <span><a href="https://commons.wikimedia.org/wiki/Special:ListFiles/Kungu01" class="mw-contributions-link-uploads" title="Special:ListFiles/Kungu01">uploads</a></span> <span><a href="https://commons.wikimedia.org/wiki/Special:Log/Kungu01" class="mw-contributions-link-logs" title="Special:Log/Kungu01">logs</a></span> <span><span class="plainlinks"><a class="external" href="https://meta.wikimedia.org/wiki/Special:Log?type=gblblock&amp;page=Kungu01">global block log</a></span></span> <span><a href="https://commons.wikimedia.org/wiki/Special:CentralAuth/Kungu01" class="mw-contributions-link-centralauth" title="Special:CentralAuth/Kungu01">global account</a></span> <span><a href="https://commons.wikimedia.org/w/index.php?title=Special:AbuseLog&amp;wpSearchUser=Kungu01" class="mw-contributions-link-abuse-log" title="Abuse log for this user">filter log</a></span></span></div><div class="mw-contributions-editor-info">A user with 3 edits. Account created on 4 November 2014.</div></div></div>
					
					
					<div id="mw-content-text" class="mw-body-content"><div class='mw-htmlform-ooui-wrapper oo-ui-layout oo-ui-panelLayout oo-ui-panelLayout-padded oo-ui-panelLayout-framed'><form action='https://commons.wikimedia.org/w/index.php' method='get' enctype='application/x-www-form-urlencoded' class='mw-htmlform mw-htmlform-ooui oo-ui-layout oo-ui-formLayout'><fieldset class='oo-ui-layout oo-ui-labelElement oo-ui-fieldsetLayout mw-collapsibleFieldsetLayout mw-collapsible mw-collapsed'><legend role='button' class='oo-ui-fieldsetLayout-header mw-collapsible-toggle'><span class='oo-ui-iconElement-icon oo-ui-iconElement-noIcon'></span><span class='oo-ui-labelElement-label'>Search for contributions</span><span class='oo-ui-widget oo-ui-widget-enabled oo-ui-iconElement-icon oo-ui-icon-expand oo-ui-iconElement oo-ui-labelElement-invisible oo-ui-iconWidget'>Expand</span><span class='oo-ui-widget oo-ui-widget-enabled oo-ui-iconElement-icon oo-ui-icon-collapse oo-ui-iconElement oo-ui-labelElement-invisible oo-ui-iconWidget'>Collapse</span></legend><div class='oo-ui-fieldsetLayout-group mw-collapsible-content'><div class='oo-ui-widget oo-ui-widget-enabled'><input type="hidden" value="Special:Contributions" name="title">
<div class='oo-ui-layout oo-ui-panelLayout oo-ui-panelLayout-padded oo-ui-panelLayout-framed'><fieldset class='oo-ui-layout oo-ui-labelElement oo-ui-fieldsetLayout'><legend class='oo-ui-fieldsetLayout-header'><span class='oo-ui-iconElement-icon oo-ui-iconElement-noIcon'></span><span class='oo-ui-labelElement-label'>⧼contribs-top⧽</span></legend><div class='oo-ui-fieldsetLayout-group'><div class='oo-ui-widget oo-ui-widget-enabled'><div id="mw-htmlform-contribs-top"><div data-mw-modules='mediawiki.widgets.UserInputWidget' id='ooui-php-13' class='mw-htmlform-field-HTMLUserTextField mw-htmlform-autoinfuse oo-ui-layout oo-ui-labelElement oo-ui-fieldLayout oo-ui-fieldLayout-align-top' data-ooui='{"_":"mw.htmlform.FieldLayout","fieldWidget":{"tag":"mw-target-user-or-ip"},"align":"top","helpInline":true,"$overlay":true,"label":{"html":"IP address or username:"},"classes":["mw-htmlform-field-HTMLUserTextField","mw-htmlform-autoinfuse"]}'><div class='oo-ui-fieldLayout-body'><span class='oo-ui-fieldLayout-header'><label for='ooui-php-2' class='oo-ui-labelElement-label'>IP address or username:</label></span><div class='oo-ui-fieldLayout-field'><div id='mw-target-user-or-ip' class='oo-ui-widget oo-ui-widget-enabled oo-ui-inputWidget oo-ui-indicatorElement oo-ui-textInputWidget oo-ui-textInputWidget-type-text oo-ui-textInputWidget-php mw-widget-userInputWidget' data-ooui='{"_":"mw.widgets.UserInputWidget","$overlay":true,"excludenamed":false,"excludetemp":false,"name":"target","value":"Kungu01","inputId":"ooui-php-2","indicator":"required","required":true}'><input type='text' tabindex='0' name='target' value='Kungu01' required='' id='ooui-php-2' class='oo-ui-inputWidget-input' /><span class='oo-ui-iconElement-icon oo-ui-iconElement-noIcon'></span><span class='oo-ui-indicatorElement-indicator oo-ui-indicator-required'></span></div></div></div></div><div data-mw-modules='mediawiki.widgets' id='ooui-php-14' class='mw-htmlform-field-HTMLSelectNamespace namespaceselector mw-htmlform-autoinfuse oo-ui-layout oo-ui-labelElement oo-ui-fieldLayout oo-ui-fieldLayout-align-top' data-ooui='{"_":"mw.htmlform.FieldLayout","fieldWidget":{"tag":"namespace"},"align":"top","helpInline":true,"$overlay":true,"label":{"html":"Namespace:"},"classes":["mw-htmlform-field-HTMLSelectNamespace","namespaceselector","mw-htmlform-autoinfuse"]}'><div class='oo-ui-fieldLayout-body'><span class='oo-ui-fieldLayout-header'><label for='ooui-php-3' class='oo-ui-labelElement-label'>Namespace:</label></span><div class='oo-ui-fieldLayout-field'><div id='namespace' class='oo-ui-widget oo-ui-widget-enabled oo-ui-inputWidget oo-ui-dropdownInputWidget oo-ui-dropdownInputWidget-php mw-widget-namespaceInputWidget' data-ooui='{"_":"mw.widgets.NamespaceInputWidget","includeAllValue":"all","userLang":false,"exclude":[],"include":null,"dropdown":{"$overlay":true},"name":"namespace","value":"all","inputId":"ooui-php-3","required":false}'><select tabindex='0' name='namespace' id='ooui-php-3' class='oo-ui-inputWidget-input'><option value='all' selected='selected'>all</option><option value='0'>(Gallery)</option><option value='1'>Talk</option><option value='2'>User</option><option value='3'>User talk</option><option value='4'>Commons</option><option value='5'>Commons talk</option><option value='6'>File</option><option value='7'>File talk</option><option value='8'>MediaWiki</option><option value='9'>MediaWiki talk</option><option value='10'>Template</option><option value='11'>Template talk</option><option value='12'>Help</option><option value='13'>Help talk</option><option value='14'>Category</option><option value='15'>Category talk</option><option value='100'>Creator</option><option value='101'>Creator talk</option><option value='102'>TimedText</option><option value='103'>TimedText talk</option><option value='104'>Sequence</option><option value='105'>Sequence talk</option><option value='106'>Institution</option><option value='107'>Institution talk</option><option value='460'>Campaign</option><option value='461'>Campaign talk</option><option value='486'>Data</option><option value='487'>Data talk</option><option value='828'>Module</option><option value='829'>Module talk</option><option value='1198'>Translations</option><option value='1199'>Translations talk</option><option value='2600'>Topic</option></select><span class='oo-ui-widget oo-ui-widget-enabled oo-ui-indicatorElement-indicator oo-ui-indicator-down oo-ui-indicatorElement oo-ui-labelElement-invisible oo-ui-indicatorWidget'></span></div></div></div></div><div id='ooui-php-15' class='mw-htmlform-field-HTMLMultiSelectField  mw-htmlform-flatlist mw-htmlform-hide-if oo-ui-layout oo-ui-fieldLayout oo-ui-fieldLayout-align-top' data-ooui='{"_":"mw.htmlform.FieldLayout","fieldWidget":{"tag":"ooui-php-16"},"align":"top","helpInline":true,"$overlay":true,"condState":{"hide":["===","namespace","all"]},"classes":["mw-htmlform-field-HTMLMultiSelectField"," mw-htmlform-flatlist","mw-htmlform-hide-if"]}'><div class='oo-ui-fieldLayout-body'><span class='oo-ui-fieldLayout-header'><label id='ooui-php-6' class='oo-ui-labelElement-label'></label></span><div class='oo-ui-fieldLayout-field'><div aria-labelledby='ooui-php-6' id='ooui-php-16' class=' mw-htmlform-flatlist oo-ui-widget oo-ui-widget-enabled oo-ui-inputWidget oo-ui-checkboxMultiselectInputWidget' data-ooui='{"_":"OO.ui.CheckboxMultiselectInputWidget","options":[{"data":"nsInvert","label":"Invert selection","disabled":false},{"data":"associated","label":"Associated namespace","disabled":false}],"name":"wpfilters[]","value":[],"data":[],"classes":[" mw-htmlform-flatlist"]}'><div class='oo-ui-layout oo-ui-labelElement oo-ui-fieldLayout oo-ui-fieldLayout-align-inline'><div class='oo-ui-fieldLayout-body'><span class='oo-ui-fieldLayout-field'><span class='oo-ui-widget oo-ui-widget-enabled oo-ui-inputWidget oo-ui-checkboxInputWidget'><input type='checkbox' tabindex='0' name='wpfilters[]' value='nsInvert' id='ooui-php-4' class='oo-ui-inputWidget-input' /><span class='oo-ui-checkboxInputWidget-checkIcon oo-ui-widget oo-ui-widget-enabled oo-ui-iconElement-icon oo-ui-icon-check oo-ui-iconElement oo-ui-labelElement-invisible oo-ui-iconWidget oo-ui-image-invert'></span></span></span><span class='oo-ui-fieldLayout-header'><label for='ooui-php-4' class='oo-ui-labelElement-label'>Invert selection</label></span></div></div><div class='oo-ui-layout oo-ui-labelElement oo-ui-fieldLayout oo-ui-fieldLayout-align-inline'><div class='oo-ui-fieldLayout-body'><span class='oo-ui-fieldLayout-field'><span class='oo-ui-widget oo-ui-widget-enabled oo-ui-inputWidget oo-ui-checkboxInputWidget'><input type='checkbox' tabindex='0' name='wpfilters[]' value='associated' id='ooui-php-5' class='oo-ui-inputWidget-input' /><span class='oo-ui-checkboxInputWidget-checkIcon oo-ui-widget oo-ui-widget-enabled oo-ui-iconElement-icon oo-ui-icon-check oo-ui-iconElement oo-ui-labelElement-invisible oo-ui-iconWidget oo-ui-image-invert'></span></span></span><span class='oo-ui-fieldLayout-header'><label for='ooui-php-5' class='oo-ui-labelElement-label'>Associated namespace</label></span></div></div></div></div></div></div><div id='ooui-php-17' class='mw-htmlform-field-HTMLTagFilter mw-tagfilter-input mw-htmlform-autoinfuse oo-ui-layout oo-ui-labelElement oo-ui-fieldLayout oo-ui-fieldLayout-align-top' data-ooui='{"_":"mw.htmlform.FieldLayout","fieldWidget":{"tag":"tagfilter"},"align":"top","helpInline":true,"$overlay":true,"label":{"html":"&lt;a href=https://commons.wikimedia.org/"//wiki//Special:Tags/" title=\"Special:Tags\"&gt;Tag&lt;\/a&gt; filter:"},"classes":["mw-htmlform-field-HTMLTagFilter","mw-tagfilter-input","mw-htmlform-autoinfuse"]}'><div class='oo-ui-fieldLayout-body'><span class='oo-ui-fieldLayout-header'><label for='ooui-php-8' class='oo-ui-labelElement-label'><a href="https://commons.wikimedia.org/wiki/Special:Tags" title="Special:Tags">Tag</a> filter:</label></span><div class='oo-ui-fieldLayout-field'><div id='tagfilter' class='oo-ui-widget oo-ui-widget-enabled oo-ui-inputWidget oo-ui-textInputWidget oo-ui-textInputWidget-type-text oo-ui-textInputWidget-php oo-ui-comboBoxInputWidget oo-ui-comboBoxInputWidget-php' data-ooui='{"_":"OO.ui.ComboBoxInputWidget","options":[{"data":"visualeditor-wikitext","label":"2017 wikitext editor"},{"data":"Abusefilter: Smiley","label":"Abusefilter: Smiley"},{"data":"ACDC","label":"AC\/DC"},{"data":"OAuth CID: 6093","label":"addshore-v1-nonowner-test [1.0]"},{"data":"Adiutor","label":"Adiutor"},{"data":"advanced mobile edit","label":"Advanced mobile edit"},{"data":"OAuth CID: 7626","label":"African German Phrasebook server v2 [1.0]"},{"data":"OAuth CID: 13699","label":"agpbv3 [1.0]"},{"data":"android app edit","label":"Android app edit"},{"data":"removed source information","label":"Anonymous or new user removed source information"},{"data":"app-full-source","label":"App full source"},{"data":"app-image-caption-add","label":"App image caption add"},{"data":"app-image-caption-translate","label":"App image caption translate"},{"data":"app-image-tag-add","label":"App image tag add"},{"data":"app-suggestededit","label":"App suggested edit"},{"data":"app-talk-reply","label":"App talk reply"},{"data":"app-talk-source","label":"App talk source"},{"data":"app-talk-topic","label":"App talk topic"},{"data":"app-undo","label":"App undo"},{"data":"OAuth CID: 3695","label":"app5 [1.0]"},{"data":"ASCII text added","label":"ASCII text added"},{"data":"AWB","label":"AWB"},{"data":"OAuth CID: 4986","label":"beta-vct [1.0]"},{"data":"mw-blank","label":"Blanking"},{"data":"Blocked user editing own talk page","label":"Blocked user editing own talk page"},{"data":"BotSDC","label":"Bot adding SDC"},{"data":"OAuth CID: 8209","label":"BUB2 - wmcloud [1.0]"},{"data":"OAuth CID: 5210","label":"BUB2-new [2.0]"},{"data":"OAuth CID: 6442","label":"bub2-uploader [1.0]"},{"data":"OAuth CID: 13141","label":"CampWiz-Categorizer-Dev [1.0]"},{"data":"OAuth CID: 13118","label":"CampWiz-Categorizer-Localhost [1.0]"},{"data":"Cat-a-lot","label":"Cat-a-lot"},{"data":"convenient-discussions","label":"CD"},{"data":"OAuth CID: 1313","label":"commons localhost testing [1.0]"},{"data":"OAuth CID: 1181","label":"Commons Mass Description (test local) [1.0]"},{"data":"OAuth CID: 821","label":"Commons mass description filler [1.2]"},{"data":"OAuth CID: 1835","label":"commonshelper [1.0]"},{"data":"community configuration","label":"Community Configuration"},{"data":"OAuth CID: 7093","label":"Como [2.1]"},{"data":"OAuth CID: 6609","label":"Como allauth test [1.0]"},{"data":"abusefilter-condition-limit","label":"condition limit reached"},{"data":"mw-contentmodelchange","label":"content model change"},{"data":"OAuth CID: 18","label":"CropCrop [1.0]"},{"data":"OAuth CID: 64","label":"CropTool"},{"data":"OAuth CID: 19","label":"CropTool [1.0]"},{"data":"OAuth CID: 27","label":"CropTool [1.1]"},{"data":"OAuth CID: 593","label":"CropTool [1.4]"},{"data":"OAuth CID: 1784","label":"CropTool [1.5]"},{"data":"OAuth CID: 6969","label":"CropTool testing [1.0]"},{"data":"cross-wiki-upload","label":"Cross-wiki upload"},{"data":"OAuth CID: 128","label":"Debug Flickipedia [1.0]"},{"data":"DepictAssist","label":"DepictAssist"},{"data":"OAuth CID: 2459","label":"Depictor [0.1]"},{"data":"OAuth CID: 1904","label":"DiBabel [1.2]"},{"data":"disambiguator-link-added","label":"Disambiguation links"},{"data":"discussiontools","label":"discussiontools (hidden tag)"},{"data":"discussiontools-added-comment","label":"discussiontools-added-comment (hidden tag)"},{"data":"discussiontools-source","label":"discussiontools-source (hidden tag)"},{"data":"discussiontools-source-enhanced","label":"discussiontools-source-enhanced (hidden tag)"},{"data":"discussiontools-visual","label":"discussiontools-visual (hidden tag)"},{"data":"OAuth CID: 99","label":"Dispenser [1.0]"},{"data":"OAuth CID: 410","label":"Dispenser [2.4]"},{"data":"OAuth CID: 1842","label":"drawshield [1.0]"},{"data":"OAuth CID: 1510","label":"dtz [1.1]"},{"data":"OAuth CID: 1855","label":"dtz [1.2]"},{"data":"editcheck-references-activated","label":"Edit Check (references) activated"},{"data":"editcheck-reference-decline-irrelevant","label":"Edit Check (references) declined (irrelevant)"},{"data":"editcheck-newreference","label":"editcheck-newreference (hidden tag)"},{"data":"OAuth CID: 2499","label":"EditGroups for Commons [1.0]"},{"data":"editing policy page","label":"Editing policy page"},{"data":"editing POTD or MOTD description","label":"editing POTD or MOTD description"},{"data":"emoji","label":"Emoji"},{"data":"EnhancedStash","label":"EnhancedStash"},{"data":"FileImporter","label":"FileImporter"},{"data":"OAuth CID: 1770","label":"FIST [1.1]"},{"data":"OAuth CID: 5738","label":"Flcikypedia 2023-10-10 [1.0]"},{"data":"OAuth CID: 1285","label":"Flickommons [1.0]"},{"data":"uploadwizard-flickr","label":"Flickr"},{"data":"OAuth CID: 1327","label":"Flickr Dashboard [1.0]"},{"data":"flickr2commons","label":"flickr2commons"},{"data":"OAuth CID: 1771","label":"flickr2commons [1.0]"},{"data":"OAuth CID: 9374","label":"flickr2commons-ng [1.0]"},{"data":"OAuth CID: 6125","label":"Flickypedia (demo for Montevideo) [1.0]"},{"data":"OAuth CID: 6361","label":"Flickypedia [1.0]"},{"data":"OAuth CID: 1910","label":"GDrive to Commons Uploader [1.6]"},{"data":"OAuth CID: 1838","label":"geograph2commons [1.0]"},{"data":"OAuth CID: 927","label":"glam2commons [1.0]"},{"data":"OAuth CID: 1337","label":"Google drive to Wikimedia Commons [1.4]"},{"data":"OAuth CID: 1738","label":"Google drive to Wikimedia Commons [1.5]"},{"data":"OAuth CID: 3060","label":"Google Photos Commons Upload [2.0]"},{"data":"HotCat","label":"HotCat"},{"data":"OAuth CID: 2343","label":"IA Upload [2.0]"},{"data":"OAuth CID: 40","label":"ia-upload [0.2]"},{"data":"OAuth CID: 772","label":"ia-upload [1.1]"},{"data":"OAuth CID: 1762","label":"ia-upload [2.0]"},{"data":"OAuth CID: 1804","label":"IABotManagementConsole [1.2]"},{"data":"OAuth CID: 8989","label":"Image Annotation Tool [1.0]"},{"data":"OAuth CID: 2039","label":"Image Annotator [1.0]"},{"data":"image notes by new users","label":"Image notes by new users"},{"data":"OAuth CID: 1140","label":"import-500px [0.1]"},{"data":"fileimporter-imported","label":"Imported with FileImporter"},{"data":"ios app edit","label":"iOS app edit"},{"data":"OAuth CID: 2450","label":"Isa [2.2]"},{"data":"OAuth CID: 1561","label":"ISA Local Dev 2 (TEST) [1.0]"},{"data":"OAuth CID: 3006","label":"Isa-dev [1.0]"},{"data":"OAuth CID: 1558","label":"Isa-tool (dev-personal) [1.0]"},{"data":"OAuth CID: 1393","label":"Isa-tool [1.0]"},{"data":"OAuth CID: 8356","label":"KenBurnsEffect tool [1.0]"},{"data":"OAuth CID: 8393","label":"KenBurnsEffect tool [1.1]"},{"data":"Large upload by a new user","label":"Large upload by a new user"},{"data":"OAuth CID: 42","label":"LCA Tools [0.1]"},{"data":"OAuth CID: 60","label":"LCA Tools [1.0]"},{"data":"OAuth CID: 76","label":"LCA Tools [1.5]"},{"data":"License review by non-Image-reviewers","label":"License review by non-image-reviewers"},{"data":"OAuth CID: 1077","label":"Lingua Libre [2.0]"},{"data":"OAuth CID: 1735","label":"Lingua Libre [2.2]"},{"data":"OAuth CID: 2231","label":"Lingua Libre [2.30]"},{"data":"OAuth CID: 13275","label":"Lingua Libre [3.2]"},{"data":"OAuth CID: 5971","label":"Lingua Libre Development - localhost 2 [1.0]"},{"data":"OAuth CID: 13063","label":"Lingualibre [4.0]"},{"data":"OAuth CID: 496","label":"locator-tool [2.1]"},{"data":"OAuth CID: 1857","label":"locator-tool [3.0]"},{"data":"Logo with questionable license","label":"Logo with questionable license"},{"data":"manual deletion request by new user","label":"Manual deletion request"},{"data":"mw-manual-revert","label":"Manual revert"},{"data":"massmessage-delivery","label":"MassMessage delivery"},{"data":"OAuth CID: 1614","label":"Media Data Verification Tool (Dev 2) [1.0]"},{"data":"OAuth CID: 1993","label":"Media Data Verification Tool [1.0]"},{"data":"OAuth CID: 968","label":"merge2pdf [0.3]"},{"data":"meta spam id","label":"meta spam id"},{"data":"mobile app edit","label":"Mobile app edit"},{"data":"mobile edit","label":"Mobile edit"},{"data":"mobile web edit","label":"Mobile web edit"},{"data":"fileimporter","label":"Modified by FileImporter"},{"data":"OAuth CID: 926","label":"monumental-upload-https [1.5]"},{"data":"OAuth CID: 931","label":"monumental-wlm [1.5]"},{"data":"OAuth CID: 359","label":"mpaatools [1.3]"},{"data":"OAuth CID: 1314","label":"Musikverket Description Translations [1.0]"},{"data":"rillke-mw-js-bot","label":"MwJSBot.js (Software by Rillke)"},{"data":"OAuth CID: 8283","label":"NCC2Commons1 [1.0]"},{"data":"mw-new-redirect","label":"New redirect"},{"data":"discussiontools-newtopic","label":"New topic"},{"data":"New user rapidly uploading files","label":"New user rapidly uploading files"},{"data":"OAuth CID: 1190","label":"NOA Upload Tool [1.0]"},{"data":"OAuth CID: 1349","label":"Nordic Museum Depicts [1.0]"},{"data":"OAuth CID: 1700","label":"Noun Project Uploader (localhost) [1.0]"},{"data":"OAuth CID: 1975","label":"Noun Project Uploader (localhost) [3.0]"},{"data":"nuke","label":"Nuke"},{"data":"OAuth CID: 67","label":"OAuth Uploader"},{"data":"openrefine","label":"openrefine"},{"data":"openrefine-3.6","label":"openrefine-3.6"},{"data":"openrefine-3.7","label":"openrefine-3.7"},{"data":"openrefine-3.8","label":"openrefine-3.8"},{"data":"OAuth CID: 8754","label":"OSM-Zoom Tool [1.0]"},{"data":"OUploadForm","label":"OUploadForm (hidden tag)"},{"data":"Overwriting artwork","label":"Overwriting artwork"},{"data":"OAuth CID: 9443","label":"OWIDImporter [1.0]"},{"data":"OAuth CID: 8698","label":"ParliamentDiagram [1.0]"},{"data":"OAuth CID: 538","label":"parliamentdiagram [1.1]"},{"data":"OAuth CID: 8472","label":"ParliamentDiagram-Beta [1.2]"},{"data":"OAuth CID: 1696","label":"PatrAll [1.0]"},{"data":"OAuth CID: 429","label":"PAWS [1.2]"},{"data":"OAuth CID: 3711","label":"PAWS [2.1]"},{"data":"OAuth CID: 4664","label":"paws [2.2]"},{"data":"PermissionOTRS","label":"PermissionVRTS"},{"data":"OAuth CID: 10974","label":"PicStalker Local [2.0]"},{"data":"possible vandalism","label":"Possible vandalism"},{"data":"possibly out of scope","label":"possibly out of scope"},{"data":"OAuth CID: 4182","label":"POTY admin [1.0]"},{"data":"poty-script","label":"POTY helper script"},{"data":"OAuth CID: 1366","label":"QR Code Generator [1.0]"},{"data":"OAuth CID: 1744","label":"QR Code Generator [2.0]"},{"data":"OAuth CID: 2394","label":"QR Code Generator [3.0]"},{"data":"OAuth CID: 1277","label":"QuickCategories [1.0]"},{"data":"OAuth CID: 1703","label":"QuickCategories [1.1]"},{"data":"OAuth CID: 1351","label":"QuickStatements [1.5]"},{"data":"OAuth CID: 1776","label":"quickstatements [2.0]"},{"data":"mw-recreated","label":"Recreated"},{"data":"mw-changed-redirect-target","label":"Redirect target changed"},{"data":"removal of duplicates","label":"Removal of duplicates"},{"data":"Removal of ticket permission","label":"Removal of ticket permission"},{"data":"mw-removed-redirect","label":"Removed redirect"},{"data":"removing information template","label":"Removing information template"},{"data":"RenameLink","label":"RenameLink"},{"data":"mw-replace","label":"Replaced"},{"data":"discussiontools-reply","label":"Reply"},{"data":"mw-reverted","label":"Reverted"},{"data":"mw-rollback","label":"Rollback"},{"data":"RotateLink","label":"RotateLink"},{"data":"OAuth CID: 737","label":"samwilson-ia-upload [1.0]"},{"data":"OAuth CID: 5710","label":"Savica 2, now with file uploads [1.0]"},{"data":"OAuth CID: 5731","label":"Savica 2023-10-09a [1.0]"},{"data":"mw-server-side-upload","label":"Server-side upload"},{"data":"Short caption","label":"Short file caption"},{"data":"OAuth CID: 864","label":"sibutest [1.2]"},{"data":"OAuth CID: 913","label":"sibutest [2.4]"},{"data":"OAuth CID: 933","label":"sibutest [3.0]"},{"data":"small image note","label":"Small image note"},{"data":"OAuth CID: 6972","label":"soda-test-croptool-toolforge-test [0.0]"},{"data":"OAuth CID: 1932","label":"Staging.ajapaik.ee [1.0]"},{"data":"apps-suggested-edits","label":"Suggested Edits edit"},{"data":"sunflower","label":"Sunflower"},{"data":"OAuth CID: 1268","label":"SVG Translate [1.2]"},{"data":"OAuth CID: 1404","label":"SVG Translate [1.3]"},{"data":"OAuth CID: 1732","label":"SVG Translate [1.4]"},{"data":"OAuth CID: 116","label":"SVGTranslate [1.0]"},{"data":"OAuth CID: 1188","label":"SWViewer [1.0]"},{"data":"OAuth CID: 1261","label":"SWViewer [1.2]"},{"data":"OAuth CID: 1352","label":"SWViewer [1.3]"},{"data":"OAuth CID: 1805","label":"SWViewer [1.4]"},{"data":"OAuth CID: 6365","label":"SWViewer [1.6]"},{"data":"OAuth CID: 951","label":"Takedown Tools [1.0]"},{"data":"template removed","label":"template removed"},{"data":"OAuth CID: 7699","label":"test-v2-agpb [1.0]"},{"data":"OAuth CID: 1281","label":"TestApp [1.0]"},{"data":"OAuth CID: 2951","label":"TestOauthWikiMI [7.0]"},{"data":"OAuth CID: 3154","label":"TestOauthWikiMI [8.0]"},{"data":"OAuth CID: 1901","label":"Thenoun Uploader [1.0]"},{"data":"Ticket permission added by non-VRT member","label":"Ticket permission added by non-VRT member"},{"data":"OAuth CID: 1229","label":"Tracker (localhost) [1.3]"},{"data":"OAuth CID: 1317","label":"Tracker (localhost) [1.4]"},{"data":"OAuth CID: 1322","label":"Tracker (test) [1.3]"},{"data":"OAuth CID: 1231","label":"Tracker [1.1]"},{"data":"OAuth CID: 1321","label":"Tracker [1.3]"},{"data":"OAuth CID: 1872","label":"Tracker [1.4]"},{"data":"translate-translation-pages","label":"translate-translation-pages (hidden tag)"},{"data":"translation with only definition","label":"translation with only definition"},{"data":"twinkle","label":"Twinkle"},{"data":"OAuth CID: 1372","label":"Twitter to Commons [1.0]"},{"data":"OAuth CID: 1378","label":"Twitter to Commons [1.1]"},{"data":"mw-undo","label":"Undo"},{"data":"uploadwizard","label":"uploadwizard (hidden tag)"},{"data":"OAuth CID: 1839","label":"url2commons [1.0]"},{"data":"OAuth CID: 157","label":"Video Editing Server - Cloud9 [1.0]"},{"data":"OAuth CID: 163","label":"Video Editing Server - Production [1.0]"},{"data":"OAuth CID: 1424","label":"video-cut-tool-back-end [3.1]"},{"data":"OAuth CID: 1425","label":"video-cut-tool-back-end [3.2]"},{"data":"OAuth CID: 394","label":"video2commons"},{"data":"OAuth CID: 392","label":"video2commons [0.2]"},{"data":"OAuth CID: 8887","label":"video2commons-test [1.0]"},{"data":"OAuth CID: 3599","label":"video_cutting [1.0]"},{"data":"OAuth CID: 72","label":"Videoconvert [0.2]"},{"data":"OAuth CID: 81","label":"Videoconvert [0.3]"},{"data":"OAuth CID: 3795","label":"VideocutTool [1.0]"},{"data":"OAuth CID: 13623","label":"VideoCutTool [1.0]"},{"data":"OAuth CID: 1626","label":"VideoCutTool [2.6]"},{"data":"OAuth CID: 2937","label":"videocuttool [5.0]"},{"data":"OAuth CID: 2938","label":"videocuttool [7]"},{"data":"OAuth CID: 4651","label":"VideoCutTool development [0.1]"},{"data":"OAuth CID: 4185","label":"videocuttool-test-2 [1.0]"},{"data":"OAuth CID: 2551","label":"VideoCutTools - Beta [1.0]"},{"data":"OAuth CID: 2573","label":"VideoCutTools - oauth2 [1.0]"},{"data":"OAuth CID: 2195","label":"VideoCutTools [1.0]"},{"data":"OAuth CID: 1164","label":"Videowiki [1.0]"},{"data":"OAuth CID: 1165","label":"Videowiki [1.1]"},{"data":"OAuth CID: 1192","label":"Videowiki [1.2]"},{"data":"OAuth CID: 1241","label":"Videowiki [1.6]"},{"data":"OAuth CID: 2718","label":"Videowiki [2.2]"},{"data":"OAuth CID: 1303","label":"VideowikiLocal [1.1]"},{"data":"OAuth CID: 1329","label":"VideowikiLocal [1.2]"},{"data":"OAuth CID: 1692","label":"VideowikiLocal [1.3]"},{"data":"visualeditor","label":"Visual edit"},{"data":"visualeditor-switched","label":"Visual edit: Switched"},{"data":"VisualFileChange","label":"VisualFileChange"},{"data":"OAuth CID: 378","label":"Widar [1.4]"},{"data":"OAuth CID: 2576","label":"Wiki Commons uploader [1.0]"},{"data":"OAuth CID: 2928","label":"Wiki Loves Brasil [1.0]"},{"data":"OAuth CID: 2386","label":"Wiki Loves Monuments Italia photo upload app [2.0.1]"},{"data":"OAuth CID: 8214","label":"Wiki Loves Monuments tool for improving data [1.0]"},{"data":"OAuth CID: 2322","label":"Wiki Museu do Ipiranga - Onde o objeto \u00e9 usado? [1.0]"},{"data":"OAuth CID: 2404","label":"Wiki Museu do Ipiranga - Para que serve? [3.0]"},{"data":"OAuth CID: 2642","label":"wikicrowd [1.0]"},{"data":"OAuth CID: 2641","label":"wikicrowd-localhost [1.0]"},{"data":"wikidata-for-firefox","label":"Wikidata for Firefox"},{"data":"OAuth CID: 1453","label":"Wikidata Image Positions [1.1]"},{"data":"OAuth CID: 1702","label":"Wikidata Image Positions [1.2]"},{"data":"OAuth CID: 5385","label":"Wikidocumentaries demo [1.0]"},{"data":"OAuth CID: 5125","label":"wikidocumentaries_upload_test5 [2.0]"},{"data":"wikieditor","label":"wikieditor (hidden tag)"},{"data":"OAuth CID: 1745","label":"Wikifile Transfer [2.0]"},{"data":"OAuth CID: 2395","label":"Wikifile Transfer [4.0]"},{"data":"OAuth CID: 10649","label":"Wikifile Transfer [5.0]"},{"data":"wikilove","label":"wikilove"},{"data":"OAuth CID: 5309","label":"wikilovesmonuments-italy-app [3.0]"},{"data":"OAuth CID: 346","label":"Wikimedia Commons Project Wikimaps Warper [2.0]"},{"data":"OAuth CID: 350","label":"Wikimedia Commons Project Wikimaps Warper [2.1]"},{"data":"OAuth CID: 544","label":"Wikimedia Commons Project Wikimaps Warper [2.2]"},{"data":"OAuth CID: 619","label":"wikishootme [1.1]"},{"data":"OAuth CID: 1828","label":"wikishootme [1.9]"},{"data":"OAuth CID: 2348","label":"Wikisource Image Uploader [1.0]"},{"data":"OAuth CID: 579","label":"WLM-IT wikigite [1.0]"},{"data":"OAuth CID: 5228","label":"work2 [1.0]"}],"$overlay":true,"name":"tagfilter","inputId":"ooui-php-8","required":false}'><input type='text' tabindex='0' name='tagfilter' value='' list='ooui-php-7' id='ooui-php-8' class='oo-ui-inputWidget-input' /><span class='oo-ui-iconElement-icon oo-ui-iconElement-noIcon'></span><span class='oo-ui-indicatorElement-indicator oo-ui-indicatorElement-noIndicator'></span><span class='oo-ui-widget oo-ui-widget-enabled oo-ui-indicatorElement-indicator oo-ui-indicator-down oo-ui-indicatorElement oo-ui-labelElement-invisible oo-ui-indicatorWidget'></span><datalist id='ooui-php-7'><option value='visualeditor-wikitext'>2017 wikitext editor</option><option value='Abusefilter: Smiley'>Abusefilter: Smiley</option><option value='ACDC'>AC/DC</option><option value='OAuth CID: 6093'>addshore-v1-nonowner-test [1.0]</option><option value='Adiutor'>Adiutor</option><option value='advanced mobile edit'>Advanced mobile edit</option><option value='OAuth CID: 7626'>African German Phrasebook server v2 [1.0]</option><option value='OAuth CID: 13699'>agpbv3 [1.0]</option><option value='android app edit'>Android app edit</option><option value='removed source information'>Anonymous or new user removed source information</option><option value='app-full-source'>App full source</option><option value='app-image-caption-add'>App image caption add</option><option value='app-image-caption-translate'>App image caption translate</option><option value='app-image-tag-add'>App image tag add</option><option value='app-suggestededit'>App suggested edit</option><option value='app-talk-reply'>App talk reply</option><option value='app-talk-source'>App talk source</option><option value='app-talk-topic'>App talk topic</option><option value='app-undo'>App undo</option><option value='OAuth CID: 3695'>app5 [1.0]</option><option value='ASCII text added'>ASCII text added</option><option value='AWB'>AWB</option><option value='OAuth CID: 4986'>beta-vct [1.0]</option><option value='mw-blank'>Blanking</option><option value='Blocked user editing own talk page'>Blocked user editing own talk page</option><option value='BotSDC'>Bot adding SDC</option><option value='OAuth CID: 8209'>BUB2 - wmcloud [1.0]</option><option value='OAuth CID: 5210'>BUB2-new [2.0]</option><option value='OAuth CID: 6442'>bub2-uploader [1.0]</option><option value='OAuth CID: 13141'>CampWiz-Categorizer-Dev [1.0]</option><option value='OAuth CID: 13118'>CampWiz-Categorizer-Localhost [1.0]</option><option value='Cat-a-lot'>Cat-a-lot</option><option value='convenient-discussions'>CD</option><option value='OAuth CID: 1313'>commons localhost testing [1.0]</option><option value='OAuth CID: 1181'>Commons Mass Description (test local) [1.0]</option><option value='OAuth CID: 821'>Commons mass description filler [1.2]</option><option value='OAuth CID: 1835'>commonshelper [1.0]</option><option value='community configuration'>Community Configuration</option><option value='OAuth CID: 7093'>Como [2.1]</option><option value='OAuth CID: 6609'>Como allauth test [1.0]</option><option value='abusefilter-condition-limit'>condition limit reached</option><option value='mw-contentmodelchange'>content model change</option><option value='OAuth CID: 18'>CropCrop [1.0]</option><option value='OAuth CID: 64'>CropTool</option><option value='OAuth CID: 19'>CropTool [1.0]</option><option value='OAuth CID: 27'>CropTool [1.1]</option><option value='OAuth CID: 593'>CropTool [1.4]</option><option value='OAuth CID: 1784'>CropTool [1.5]</option><option value='OAuth CID: 6969'>CropTool testing [1.0]</option><option value='cross-wiki-upload'>Cross-wiki upload</option><option value='OAuth CID: 128'>Debug Flickipedia [1.0]</option><option value='DepictAssist'>DepictAssist</option><option value='OAuth CID: 2459'>Depictor [0.1]</option><option value='OAuth CID: 1904'>DiBabel [1.2]</option><option value='disambiguator-link-added'>Disambiguation links</option><option value='discussiontools'>discussiontools (hidden tag)</option><option value='discussiontools-added-comment'>discussiontools-added-comment (hidden tag)</option><option value='discussiontools-source'>discussiontools-source (hidden tag)</option><option value='discussiontools-source-enhanced'>discussiontools-source-enhanced (hidden tag)</option><option value='discussiontools-visual'>discussiontools-visual (hidden tag)</option><option value='OAuth CID: 99'>Dispenser [1.0]</option><option value='OAuth CID: 410'>Dispenser [2.4]</option><option value='OAuth CID: 1842'>drawshield [1.0]</option><option value='OAuth CID: 1510'>dtz [1.1]</option><option value='OAuth CID: 1855'>dtz [1.2]</option><option value='editcheck-references-activated'>Edit Check (references) activated</option><option value='editcheck-reference-decline-irrelevant'>Edit Check (references) declined (irrelevant)</option><option value='editcheck-newreference'>editcheck-newreference (hidden tag)</option><option value='OAuth CID: 2499'>EditGroups for Commons [1.0]</option><option value='editing policy page'>Editing policy page</option><option value='editing POTD or MOTD description'>editing POTD or MOTD description</option><option value='emoji'>Emoji</option><option value='EnhancedStash'>EnhancedStash</option><option value='FileImporter'>FileImporter</option><option value='OAuth CID: 1770'>FIST [1.1]</option><option value='OAuth CID: 5738'>Flcikypedia 2023-10-10 [1.0]</option><option value='OAuth CID: 1285'>Flickommons [1.0]</option><option value='uploadwizard-flickr'>Flickr</option><option value='OAuth CID: 1327'>Flickr Dashboard [1.0]</option><option value='flickr2commons'>flickr2commons</option><option value='OAuth CID: 1771'>flickr2commons [1.0]</option><option value='OAuth CID: 9374'>flickr2commons-ng [1.0]</option><option value='OAuth CID: 6125'>Flickypedia (demo for Montevideo) [1.0]</option><option value='OAuth CID: 6361'>Flickypedia [1.0]</option><option value='OAuth CID: 1910'>GDrive to Commons Uploader [1.6]</option><option value='OAuth CID: 1838'>geograph2commons [1.0]</option><option value='OAuth CID: 927'>glam2commons [1.0]</option><option value='OAuth CID: 1337'>Google drive to Wikimedia Commons [1.4]</option><option value='OAuth CID: 1738'>Google drive to Wikimedia Commons [1.5]</option><option value='OAuth CID: 3060'>Google Photos Commons Upload [2.0]</option><option value='HotCat'>HotCat</option><option value='OAuth CID: 2343'>IA Upload [2.0]</option><option value='OAuth CID: 40'>ia-upload [0.2]</option><option value='OAuth CID: 772'>ia-upload [1.1]</option><option value='OAuth CID: 1762'>ia-upload [2.0]</option><option value='OAuth CID: 1804'>IABotManagementConsole [1.2]</option><option value='OAuth CID: 8989'>Image Annotation Tool [1.0]</option><option value='OAuth CID: 2039'>Image Annotator [1.0]</option><option value='image notes by new users'>Image notes by new users</option><option value='OAuth CID: 1140'>import-500px [0.1]</option><option value='fileimporter-imported'>Imported with FileImporter</option><option value='ios app edit'>iOS app edit</option><option value='OAuth CID: 2450'>Isa [2.2]</option><option value='OAuth CID: 1561'>ISA Local Dev 2 (TEST) [1.0]</option><option value='OAuth CID: 3006'>Isa-dev [1.0]</option><option value='OAuth CID: 1558'>Isa-tool (dev-personal) [1.0]</option><option value='OAuth CID: 1393'>Isa-tool [1.0]</option><option value='OAuth CID: 8356'>KenBurnsEffect tool [1.0]</option><option value='OAuth CID: 8393'>KenBurnsEffect tool [1.1]</option><option value='Large upload by a new user'>Large upload by a new user</option><option value='OAuth CID: 42'>LCA Tools [0.1]</option><option value='OAuth CID: 60'>LCA Tools [1.0]</option><option value='OAuth CID: 76'>LCA Tools [1.5]</option><option value='License review by non-Image-reviewers'>License review by non-image-reviewers</option><option value='OAuth CID: 1077'>Lingua Libre [2.0]</option><option value='OAuth CID: 1735'>Lingua Libre [2.2]</option><option value='OAuth CID: 2231'>Lingua Libre [2.30]</option><option value='OAuth CID: 13275'>Lingua Libre [3.2]</option><option value='OAuth CID: 5971'>Lingua Libre Development - localhost 2 [1.0]</option><option value='OAuth CID: 13063'>Lingualibre [4.0]</option><option value='OAuth CID: 496'>locator-tool [2.1]</option><option value='OAuth CID: 1857'>locator-tool [3.0]</option><option value='Logo with questionable license'>Logo with questionable license</option><option value='manual deletion request by new user'>Manual deletion request</option><option value='mw-manual-revert'>Manual revert</option><option value='massmessage-delivery'>MassMessage delivery</option><option value='OAuth CID: 1614'>Media Data Verification Tool (Dev 2) [1.0]</option><option value='OAuth CID: 1993'>Media Data Verification Tool [1.0]</option><option value='OAuth CID: 968'>merge2pdf [0.3]</option><option value='meta spam id'>meta spam id</option><option value='mobile app edit'>Mobile app edit</option><option value='mobile edit'>Mobile edit</option><option value='mobile web edit'>Mobile web edit</option><option value='fileimporter'>Modified by FileImporter</option><option value='OAuth CID: 926'>monumental-upload-https [1.5]</option><option value='OAuth CID: 931'>monumental-wlm [1.5]</option><option value='OAuth CID: 359'>mpaatools [1.3]</option><option value='OAuth CID: 1314'>Musikverket Description Translations [1.0]</option><option value='rillke-mw-js-bot'>MwJSBot.js (Software by Rillke)</option><option value='OAuth CID: 8283'>NCC2Commons1 [1.0]</option><option value='mw-new-redirect'>New redirect</option><option value='discussiontools-newtopic'>New topic</option><option value='New user rapidly uploading files'>New user rapidly uploading files</option><option value='OAuth CID: 1190'>NOA Upload Tool [1.0]</option><option value='OAuth CID: 1349'>Nordic Museum Depicts [1.0]</option><option value='OAuth CID: 1700'>Noun Project Uploader (localhost) [1.0]</option><option value='OAuth CID: 1975'>Noun Project Uploader (localhost) [3.0]</option><option value='nuke'>Nuke</option><option value='OAuth CID: 67'>OAuth Uploader</option><option value='openrefine'>openrefine</option><option value='openrefine-3.6'>openrefine-3.6</option><option value='openrefine-3.7'>openrefine-3.7</option><option value='openrefine-3.8'>openrefine-3.8</option><option value='OAuth CID: 8754'>OSM-Zoom Tool [1.0]</option><option value='OUploadForm'>OUploadForm (hidden tag)</option><option value='Overwriting artwork'>Overwriting artwork</option><option value='OAuth CID: 9443'>OWIDImporter [1.0]</option><option value='OAuth CID: 8698'>ParliamentDiagram [1.0]</option><option value='OAuth CID: 538'>parliamentdiagram [1.1]</option><option value='OAuth CID: 8472'>ParliamentDiagram-Beta [1.2]</option><option value='OAuth CID: 1696'>PatrAll [1.0]</option><option value='OAuth CID: 429'>PAWS [1.2]</option><option value='OAuth CID: 3711'>PAWS [2.1]</option><option value='OAuth CID: 4664'>paws [2.2]</option><option value='PermissionOTRS'>PermissionVRTS</option><option value='OAuth CID: 10974'>PicStalker Local [2.0]</option><option value='possible vandalism'>Possible vandalism</option><option value='possibly out of scope'>possibly out of scope</option><option value='OAuth CID: 4182'>POTY admin [1.0]</option><option value='poty-script'>POTY helper script</option><option value='OAuth CID: 1366'>QR Code Generator [1.0]</option><option value='OAuth CID: 1744'>QR Code Generator [2.0]</option><option value='OAuth CID: 2394'>QR Code Generator [3.0]</option><option value='OAuth CID: 1277'>QuickCategories [1.0]</option><option value='OAuth CID: 1703'>QuickCategories [1.1]</option><option value='OAuth CID: 1351'>QuickStatements [1.5]</option><option value='OAuth CID: 1776'>quickstatements [2.0]</option><option value='mw-recreated'>Recreated</option><option value='mw-changed-redirect-target'>Redirect target changed</option><option value='removal of duplicates'>Removal of duplicates</option><option value='Removal of ticket permission'>Removal of ticket permission</option><option value='mw-removed-redirect'>Removed redirect</option><option value='removing information template'>Removing information template</option><option value='RenameLink'>RenameLink</option><option value='mw-replace'>Replaced</option><option value='discussiontools-reply'>Reply</option><option value='mw-reverted'>Reverted</option><option value='mw-rollback'>Rollback</option><option value='RotateLink'>RotateLink</option><option value='OAuth CID: 737'>samwilson-ia-upload [1.0]</option><option value='OAuth CID: 5710'>Savica 2, now with file uploads [1.0]</option><option value='OAuth CID: 5731'>Savica 2023-10-09a [1.0]</option><option value='mw-server-side-upload'>Server-side upload</option><option value='Short caption'>Short file caption</option><option value='OAuth CID: 864'>sibutest [1.2]</option><option value='OAuth CID: 913'>sibutest [2.4]</option><option value='OAuth CID: 933'>sibutest [3.0]</option><option value='small image note'>Small image note</option><option value='OAuth CID: 6972'>soda-test-croptool-toolforge-test [0.0]</option><option value='OAuth CID: 1932'>Staging.ajapaik.ee [1.0]</option><option value='apps-suggested-edits'>Suggested Edits edit</option><option value='sunflower'>Sunflower</option><option value='OAuth CID: 1268'>SVG Translate [1.2]</option><option value='OAuth CID: 1404'>SVG Translate [1.3]</option><option value='OAuth CID: 1732'>SVG Translate [1.4]</option><option value='OAuth CID: 116'>SVGTranslate [1.0]</option><option value='OAuth CID: 1188'>SWViewer [1.0]</option><option value='OAuth CID: 1261'>SWViewer [1.2]</option><option value='OAuth CID: 1352'>SWViewer [1.3]</option><option value='OAuth CID: 1805'>SWViewer [1.4]</option><option value='OAuth CID: 6365'>SWViewer [1.6]</option><option value='OAuth CID: 951'>Takedown Tools [1.0]</option><option value='template removed'>template removed</option><option value='OAuth CID: 7699'>test-v2-agpb [1.0]</option><option value='OAuth CID: 1281'>TestApp [1.0]</option><option value='OAuth CID: 2951'>TestOauthWikiMI [7.0]</option><option value='OAuth CID: 3154'>TestOauthWikiMI [8.0]</option><option value='OAuth CID: 1901'>Thenoun Uploader [1.0]</option><option value='Ticket permission added by non-VRT member'>Ticket permission added by non-VRT member</option><option value='OAuth CID: 1229'>Tracker (localhost) [1.3]</option><option value='OAuth CID: 1317'>Tracker (localhost) [1.4]</option><option value='OAuth CID: 1322'>Tracker (test) [1.3]</option><option value='OAuth CID: 1231'>Tracker [1.1]</option><option value='OAuth CID: 1321'>Tracker [1.3]</option><option value='OAuth CID: 1872'>Tracker [1.4]</option><option value='translate-translation-pages'>translate-translation-pages (hidden tag)</option><option value='translation with only definition'>translation with only definition</option><option value='twinkle'>Twinkle</option><option value='OAuth CID: 1372'>Twitter to Commons [1.0]</option><option value='OAuth CID: 1378'>Twitter to Commons [1.1]</option><option value='mw-undo'>Undo</option><option value='uploadwizard'>uploadwizard (hidden tag)</option><option value='OAuth CID: 1839'>url2commons [1.0]</option><option value='OAuth CID: 157'>Video Editing Server - Cloud9 [1.0]</option><option value='OAuth CID: 163'>Video Editing Server - Production [1.0]</option><option value='OAuth CID: 1424'>video-cut-tool-back-end [3.1]</option><option value='OAuth CID: 1425'>video-cut-tool-back-end [3.2]</option><option value='OAuth CID: 394'>video2commons</option><option value='OAuth CID: 392'>video2commons [0.2]</option><option value='OAuth CID: 8887'>video2commons-test [1.0]</option><option value='OAuth CID: 3599'>video_cutting [1.0]</option><option value='OAuth CID: 72'>Videoconvert [0.2]</option><option value='OAuth CID: 81'>Videoconvert [0.3]</option><option value='OAuth CID: 3795'>VideocutTool [1.0]</option><option value='OAuth CID: 13623'>VideoCutTool [1.0]</option><option value='OAuth CID: 1626'>VideoCutTool [2.6]</option><option value='OAuth CID: 2937'>videocuttool [5.0]</option><option value='OAuth CID: 2938'>videocuttool [7]</option><option value='OAuth CID: 4651'>VideoCutTool development [0.1]</option><option value='OAuth CID: 4185'>videocuttool-test-2 [1.0]</option><option value='OAuth CID: 2551'>VideoCutTools - Beta [1.0]</option><option value='OAuth CID: 2573'>VideoCutTools - oauth2 [1.0]</option><option value='OAuth CID: 2195'>VideoCutTools [1.0]</option><option value='OAuth CID: 1164'>Videowiki [1.0]</option><option value='OAuth CID: 1165'>Videowiki [1.1]</option><option value='OAuth CID: 1192'>Videowiki [1.2]</option><option value='OAuth CID: 1241'>Videowiki [1.6]</option><option value='OAuth CID: 2718'>Videowiki [2.2]</option><option value='OAuth CID: 1303'>VideowikiLocal [1.1]</option><option value='OAuth CID: 1329'>VideowikiLocal [1.2]</option><option value='OAuth CID: 1692'>VideowikiLocal [1.3]</option><option value='visualeditor'>Visual edit</option><option value='visualeditor-switched'>Visual edit: Switched</option><option value='VisualFileChange'>VisualFileChange</option><option value='OAuth CID: 378'>Widar [1.4]</option><option value='OAuth CID: 2576'>Wiki Commons uploader [1.0]</option><option value='OAuth CID: 2928'>Wiki Loves Brasil [1.0]</option><option value='OAuth CID: 2386'>Wiki Loves Monuments Italia photo upload app [2.0.1]</option><option value='OAuth CID: 8214'>Wiki Loves Monuments tool for improving data [1.0]</option><option value='OAuth CID: 2322'>Wiki Museu do Ipiranga - Onde o objeto é usado? [1.0]</option><option value='OAuth CID: 2404'>Wiki Museu do Ipiranga - Para que serve? [3.0]</option><option value='OAuth CID: 2642'>wikicrowd [1.0]</option><option value='OAuth CID: 2641'>wikicrowd-localhost [1.0]</option><option value='wikidata-for-firefox'>Wikidata for Firefox</option><option value='OAuth CID: 1453'>Wikidata Image Positions [1.1]</option><option value='OAuth CID: 1702'>Wikidata Image Positions [1.2]</option><option value='OAuth CID: 5385'>Wikidocumentaries demo [1.0]</option><option value='OAuth CID: 5125'>wikidocumentaries_upload_test5 [2.0]</option><option value='wikieditor'>wikieditor (hidden tag)</option><option value='OAuth CID: 1745'>Wikifile Transfer [2.0]</option><option value='OAuth CID: 2395'>Wikifile Transfer [4.0]</option><option value='OAuth CID: 10649'>Wikifile Transfer [5.0]</option><option value='wikilove'>wikilove</option><option value='OAuth CID: 5309'>wikilovesmonuments-italy-app [3.0]</option><option value='OAuth CID: 346'>Wikimedia Commons Project Wikimaps Warper [2.0]</option><option value='OAuth CID: 350'>Wikimedia Commons Project Wikimaps Warper [2.1]</option><option value='OAuth CID: 544'>Wikimedia Commons Project Wikimaps Warper [2.2]</option><option value='OAuth CID: 619'>wikishootme [1.1]</option><option value='OAuth CID: 1828'>wikishootme [1.9]</option><option value='OAuth CID: 2348'>Wikisource Image Uploader [1.0]</option><option value='OAuth CID: 579'>WLM-IT wikigite [1.0]</option><option value='OAuth CID: 5228'>work2 [1.0]</option></datalist></div></div></div></div><div id='ooui-php-18' class='mw-htmlform-field-HTMLCheckField mw-htmlform-hide-if mw-htmlform-hide-if-hidden oo-ui-layout oo-ui-labelElement oo-ui-fieldLayout oo-ui-fieldLayout-align-inline' data-ooui='{"_":"mw.htmlform.FieldLayout","fieldWidget":{"tag":"tagInvert"},"align":"inline","helpInline":true,"$overlay":true,"label":{"html":"Invert selection"},"condState":{"hide":["===","tagfilter",""]},"classes":["mw-htmlform-field-HTMLCheckField","mw-htmlform-hide-if","mw-htmlform-hide-if-hidden"]}'><div class='oo-ui-fieldLayout-body'><span class='oo-ui-fieldLayout-field'><span id='tagInvert' class='oo-ui-widget oo-ui-widget-enabled oo-ui-inputWidget oo-ui-checkboxInputWidget' data-ooui='{"_":"OO.ui.CheckboxInputWidget","name":"tagInvert","value":"1","inputId":"ooui-php-9","required":false}'><input type='checkbox' tabindex='0' name='tagInvert' value='1' id='ooui-php-9' class='oo-ui-inputWidget-input' /><span class='oo-ui-checkboxInputWidget-checkIcon oo-ui-widget oo-ui-widget-enabled oo-ui-iconElement-icon oo-ui-icon-check oo-ui-iconElement oo-ui-labelElement-invisible oo-ui-iconWidget oo-ui-image-invert'></span></span></span><span class='oo-ui-fieldLayout-header'><label for='ooui-php-9' class='oo-ui-labelElement-label'>Invert selection</label></span></div></div><div id='ooui-php-19' class='mw-htmlform-field-HTMLCheckField oo-ui-layout oo-ui-labelElement oo-ui-fieldLayout oo-ui-fieldLayout-align-inline' data-ooui='{"_":"mw.htmlform.FieldLayout","fieldWidget":{"tag":"mw-show-top-only"},"align":"inline","helpInline":true,"$overlay":true,"label":{"html":"Only show edits that are latest revisions"},"classes":["mw-htmlform-field-HTMLCheckField"]}'><div class='oo-ui-fieldLayout-body'><span class='oo-ui-fieldLayout-field'><span id='mw-show-top-only' class='oo-ui-widget oo-ui-widget-enabled oo-ui-inputWidget oo-ui-checkboxInputWidget' data-ooui='{"_":"OO.ui.CheckboxInputWidget","name":"topOnly","value":"1","inputId":"ooui-php-10","required":false}'><input type='checkbox' tabindex='0' name='topOnly' value='1' id='ooui-php-10' class='oo-ui-inputWidget-input' /><span class='oo-ui-checkboxInputWidget-checkIcon oo-ui-widget oo-ui-widget-enabled oo-ui-iconElement-icon oo-ui-icon-check oo-ui-iconElement oo-ui-labelElement-invisible oo-ui-iconWidget oo-ui-image-invert'></span></span></span><span class='oo-ui-fieldLayout-header'><label for='ooui-php-10' class='oo-ui-labelElement-label'>Only show edits that are latest revisions</label></span></div></div><div id='ooui-php-20' class='mw-htmlform-field-HTMLCheckField oo-ui-layout oo-ui-labelElement oo-ui-fieldLayout oo-ui-fieldLayout-align-inline' data-ooui='{"_":"mw.htmlform.FieldLayout","fieldWidget":{"tag":"mw-show-new-only"},"align":"inline","helpInline":true,"$overlay":true,"label":{"html":"Only show edits that are page creations"},"classes":["mw-htmlform-field-HTMLCheckField"]}'><div class='oo-ui-fieldLayout-body'><span class='oo-ui-fieldLayout-field'><span id='mw-show-new-only' class='oo-ui-widget oo-ui-widget-enabled oo-ui-inputWidget oo-ui-checkboxInputWidget' data-ooui='{"_":"OO.ui.CheckboxInputWidget","name":"newOnly","value":"1","inputId":"ooui-php-11","required":false}'><input type='checkbox' tabindex='0' name='newOnly' value='1' id='ooui-php-11' class='oo-ui-inputWidget-input' /><span class='oo-ui-checkboxInputWidget-checkIcon oo-ui-widget oo-ui-widget-enabled oo-ui-iconElement-icon oo-ui-icon-check oo-ui-iconElement oo-ui-labelElement-invisible oo-ui-iconWidget oo-ui-image-invert'></span></span></span><span class='oo-ui-fieldLayout-header'><label for='ooui-php-11' class='oo-ui-labelElement-label'>Only show edits that are page creations</label></span></div></div><div id='ooui-php-21' class='mw-htmlform-field-HTMLCheckField mw-hide-minor-edits oo-ui-layout oo-ui-labelElement oo-ui-fieldLayout oo-ui-fieldLayout-align-inline' data-ooui='{"_":"mw.htmlform.FieldLayout","fieldWidget":{"tag":"mw-show-new-only"},"align":"inline","helpInline":true,"$overlay":true,"label":{"html":"Hide minor edits"},"classes":["mw-htmlform-field-HTMLCheckField","mw-hide-minor-edits"]}'><div class='oo-ui-fieldLayout-body'><span class='oo-ui-fieldLayout-field'><span id='mw-show-new-only' class='mw-hide-minor-edits oo-ui-widget oo-ui-widget-enabled oo-ui-inputWidget oo-ui-checkboxInputWidget' data-ooui='{"_":"OO.ui.CheckboxInputWidget","name":"hideMinor","value":"1","inputId":"ooui-php-12","required":false,"classes":["mw-hide-minor-edits"]}'><input type='checkbox' tabindex='0' name='hideMinor' value='1' id='ooui-php-12' class='oo-ui-inputWidget-input' /><span class='oo-ui-checkboxInputWidget-checkIcon oo-ui-widget oo-ui-widget-enabled oo-ui-iconElement-icon oo-ui-icon-check oo-ui-iconElement oo-ui-labelElement-invisible oo-ui-iconWidget oo-ui-image-invert'></span></span></span><span class='oo-ui-fieldLayout-header'><label for='ooui-php-12' class='oo-ui-labelElement-label'>Hide minor edits</label></span></div></div></div></div></div></fieldset></div><div class='oo-ui-layout oo-ui-panelLayout oo-ui-panelLayout-padded oo-ui-panelLayout-framed'><fieldset class='oo-ui-layout oo-ui-labelElement oo-ui-fieldsetLayout'><legend class='oo-ui-fieldsetLayout-header'><span class='oo-ui-iconElement-icon oo-ui-iconElement-noIcon'></span><span class='oo-ui-labelElement-label'>⧼contribs-date⧽</span></legend><div class='oo-ui-fieldsetLayout-group'><div class='oo-ui-widget oo-ui-widget-enabled'><div id="mw-htmlform-contribs-date"><div data-mw-modules='mediawiki.widgets.DateInputWidget' id='ooui-php-24' class='mw-htmlform-field-HTMLDateTimeField  mw-htmlform-datetime-field mw-htmlform-autoinfuse oo-ui-layout oo-ui-labelElement oo-ui-fieldLayout oo-ui-fieldLayout-align-top' data-ooui='{"_":"mw.htmlform.FieldLayout","fieldWidget":{"tag":"mw-date-start"},"align":"top","helpInline":true,"$overlay":true,"label":{"html":"From date:"},"classes":["mw-htmlform-field-HTMLDateTimeField"," mw-htmlform-datetime-field","mw-htmlform-autoinfuse"]}'><div class='oo-ui-fieldLayout-body'><span class='oo-ui-fieldLayout-header'><label for='ooui-php-22' class='oo-ui-labelElement-label'>From date:</label></span><div class='oo-ui-fieldLayout-field'><div id='mw-date-start' class='oo-ui-widget oo-ui-widget-enabled oo-ui-inputWidget oo-ui-textInputWidget oo-ui-textInputWidget-type-text oo-ui-textInputWidget-php mw-widget-dateInputWidget' data-ooui='{"_":"mw.widgets.DateInputWidget","longDisplayFormat":false,"precision":"day","$overlay":true,"placeholder":"YYYY-MM-DD","name":"start","inputId":"ooui-php-22","required":false}'><input type='date' tabindex='0' name='start' value='' placeholder='YYYY-MM-DD' id='ooui-php-22' class='oo-ui-inputWidget-input' /><span class='oo-ui-iconElement-icon oo-ui-iconElement-noIcon'></span><span class='oo-ui-indicatorElement-indicator oo-ui-indicatorElement-noIndicator'></span><div aria-disabled='true' class='oo-ui-widget oo-ui-widget-disabled oo-ui-inputWidget oo-ui-textInputWidget oo-ui-textInputWidget-type-text oo-ui-textInputWidget-php'><input type='text' tabindex='-1' aria-disabled='true' disabled='disabled' value='' class='oo-ui-inputWidget-input mw-widgets-pendingTextInputWidget oo-ui-pendingElement-pending' /><span class='oo-ui-iconElement-icon oo-ui-iconElement-noIcon'></span><span class='oo-ui-indicatorElement-indicator oo-ui-indicatorElement-noIndicator'></span></div></div></div></div></div><div data-mw-modules='mediawiki.widgets.DateInputWidget' id='ooui-php-25' class='mw-htmlform-field-HTMLDateTimeField  mw-htmlform-datetime-field mw-htmlform-autoinfuse oo-ui-layout oo-ui-labelElement oo-ui-fieldLayout oo-ui-fieldLayout-align-top' data-ooui='{"_":"mw.htmlform.FieldLayout","fieldWidget":{"tag":"mw-date-end"},"align":"top","helpInline":true,"$overlay":true,"label":{"html":"To date:"},"classes":["mw-htmlform-field-HTMLDateTimeField"," mw-htmlform-datetime-field","mw-htmlform-autoinfuse"]}'><div class='oo-ui-fieldLayout-body'><span class='oo-ui-fieldLayout-header'><label for='ooui-php-23' class='oo-ui-labelElement-label'>To date:</label></span><div class='oo-ui-fieldLayout-field'><div id='mw-date-end' class='oo-ui-widget oo-ui-widget-enabled oo-ui-inputWidget oo-ui-textInputWidget oo-ui-textInputWidget-type-text oo-ui-textInputWidget-php mw-widget-dateInputWidget' data-ooui='{"_":"mw.widgets.DateInputWidget","longDisplayFormat":false,"precision":"day","$overlay":true,"placeholder":"YYYY-MM-DD","name":"end","inputId":"ooui-php-23","required":false}'><input type='date' tabindex='0' name='end' value='' placeholder='YYYY-MM-DD' id='ooui-php-23' class='oo-ui-inputWidget-input' /><span class='oo-ui-iconElement-icon oo-ui-iconElement-noIcon'></span><span class='oo-ui-indicatorElement-indicator oo-ui-indicatorElement-noIndicator'></span><div aria-disabled='true' class='oo-ui-widget oo-ui-widget-disabled oo-ui-inputWidget oo-ui-textInputWidget oo-ui-textInputWidget-type-text oo-ui-textInputWidget-php'><input type='text' tabindex='-1' aria-disabled='true' disabled='disabled' value='' class='oo-ui-inputWidget-input mw-widgets-pendingTextInputWidget oo-ui-pendingElement-pending' /><span class='oo-ui-iconElement-icon oo-ui-iconElement-noIcon'></span><span class='oo-ui-indicatorElement-indicator oo-ui-indicatorElement-noIndicator'></span></div></div></div></div></div></div></div></div></fieldset></div>
<input id="mw-input-limit" name="limit" type="hidden" value="50">
<div class="mw-htmlform-submit-buttons">
<span id='ooui-php-26' class='mw-htmlform-submit oo-ui-widget oo-ui-widget-enabled oo-ui-inputWidget oo-ui-buttonElement oo-ui-buttonElement-framed oo-ui-labelElement oo-ui-flaggedElement-primary oo-ui-flaggedElement-progressive oo-ui-buttonInputWidget' data-ooui='{"_":"OO.ui.ButtonInputWidget","type":"submit","value":"Search","label":"Search","flags":["primary","progressive"],"classes":["mw-htmlform-submit"]}'><button type='submit' tabindex='0' value='Search' class='oo-ui-inputWidget-input oo-ui-buttonElement-button'><span class='oo-ui-iconElement-icon oo-ui-iconElement-noIcon oo-ui-image-invert'></span><span class='oo-ui-labelElement-label'>Search</span><span class='oo-ui-indicatorElement-indicator oo-ui-indicatorElement-noIndicator oo-ui-image-invert'></span></button></span></div>
</div></div></fieldset></form></div><section class='mw-pager-body'>
<h4 class="mw-index-pager-list-header-first mw-index-pager-list-header">4 November 2014</h4><ul class="mw-contributions-list">
<li data-mw-revid="138595180"><span class="mw-changeslist-time">11:09</span><bdi dir="ltr"><a href="https://commons.wikimedia.org/w/index.php?title=File:Biriani.jpg&amp;oldid=138595180" class="mw-changeslist-date" title="File:Biriani.jpg">11:09, 4 November 2014</a></bdi>
<span class="mw-changeslist-links"><span>diff</span> <span><a href="https://commons.wikimedia.org/w/index.php?title=File:Biriani.jpg&amp;action=history" class="mw-changeslist-history" title="File:Biriani.jpg">hist</a></span></span> <span class="mw-changeslist-separator"></span> <span dir="ltr" class="mw-plusminus-pos mw-diff-bytes" title="476 bytes after change">+476</span> <span class="mw-changeslist-separator"></span> <abbr class="newpage" title="This edit created a new page">N</abbr>
<bdi dir="ltr"><a href="https://commons.wikimedia.org/wiki/File:Biriani.jpg" class="mw-contributions-title" title="File:Biriani.jpg">File:Biriani.jpg</a></bdi>
 <span class="comment comment--without-parentheses">User created page with UploadWizard</span>


</li>
<li data-mw-revid="138595176"><span class="mw-changeslist-time">11:09</span><bdi dir="ltr"><a href="https://commons.wikimedia.org/w/index.php?title=File:Madafu-chopping.jpg&amp;oldid=138595176" class="mw-changeslist-date" title="File:Madafu-chopping.jpg">11:09, 4 November 2014</a></bdi>
<span class="mw-changeslist-links"><span>diff</span> <span><a href="https://commons.wikimedia.org/w/index.php?title=File:Madafu-chopping.jpg&amp;action=history" class="mw-changeslist-history" title="File:Madafu-chopping.jpg">hist</a></span></span> <span class="mw-changeslist-separator"></span> <span dir="ltr" class="mw-plusminus-pos mw-diff-bytes" title="456 bytes after change">+456</span> <span class="mw-changeslist-separator"></span> <abbr class="newpage" title="This edit created a new page">N</abbr>
<bdi dir="ltr"><a href="https://commons.wikimedia.org/wiki/File:Madafu-chopping.jpg" class="mw-contributions-title" title="File:Madafu-chopping.jpg">File:Madafu-chopping.jpg</a></bdi>
 <span class="comment comment--without-parentheses">User created page with UploadWizard</span>


</li>
<li data-mw-revid="138595175"><span class="mw-changeslist-time">11:09</span><bdi dir="ltr"><a href="https://commons.wikimedia.org/w/index.php?title=File:Madafu.jpg&amp;oldid=138595175" class="mw-changeslist-date" title="File:Madafu.jpg">11:09, 4 November 2014</a></bdi>
<span class="mw-changeslist-links"><span>diff</span> <span><a href="https://commons.wikimedia.org/w/index.php?title=File:Madafu.jpg&amp;action=history" class="mw-changeslist-history" title="File:Madafu.jpg">hist</a></span></span> <span class="mw-changeslist-separator"></span> <span dir="ltr" class="mw-plusminus-pos mw-diff-bytes" title="394 bytes after change">+394</span> <span class="mw-changeslist-separator"></span> <abbr class="newpage" title="This edit created a new page">N</abbr>
<bdi dir="ltr"><a href="https://commons.wikimedia.org/wiki/File:Madafu.jpg" class="mw-contributions-title" title="File:Madafu.jpg">File:Madafu.jpg</a></bdi>
 <span class="comment comment--without-parentheses">User created page with UploadWizard</span>


</li>
</ul></section>
<div class="mw-contributions-footer">
<table class="plainlinks sp-contributions-footer" style="margin:.2em auto; border:1px solid #aaa; background-color: var(--background-color-interactive-subtle, #f9f9f9); font-size:90%; text-align:center;">
<tbody><tr>
<td style="padding:2px 0 2px 1.5em;"><span typeof="mw:File"><a href="https://commons.wikimedia.org/wiki/File:User-info.svg" class="mw-file-description" title="User info"><img alt="User info" src="https://upload.wikimedia.org/wikipedia/commons/thumb/c/ce/User-info.svg/40px-User-info.svg.png" decoding="async" width="40" height="40" class="mw-file-element" srcset="https://upload.wikimedia.org/wikipedia/commons/thumb/c/ce/User-info.svg/60px-User-info.svg.png 1.5x, https://upload.wikimedia.org/wikipedia/commons/thumb/c/ce/User-info.svg/80px-User-info.svg.png 2x" data-file-width="48" data-file-height="48"></a></span></td>
<td style="padding: .25em 1.5em; width: 100%;" class="hlist">
<ul><li><a href="https://commons.wikimedia.org/w/index.php?title=User:Kungu01&amp;action=edit&amp;redlink=1" class="new" title="User:Kungu01 (page does not exist)">Kungu01</a>:&nbsp;<a href="https://commons.wikimedia.org/wiki/Special:PrefixIndex/User:Kungu01/" title="Special:PrefixIndex/User:Kungu01/">Subpages</a></li>
<li><a class="external text" href="https://commons.wikimedia.org/w/index.php?title=Special:ListUsers&amp;limit=1&amp;username=Kungu01">User&nbsp;rights</a></li>
<li><a rel="nofollow" class="external text" href="https://xtools.wmflabs.org/ec/commons.wikimedia/Kungu01">Edit&nbsp;count</a></li>
<li><a href="https://commons.wikimedia.org/wiki/Special:ListFiles/Kungu01" title="Special:ListFiles/Kungu01">User&nbsp;uploads</a></li>
<li><a href="https://commons.wikimedia.org/wiki/Special:CentralAuth/Kungu01" title="Special:CentralAuth/Kungu01">CentralAuth</a> <sup>(<a href="https://meta.wikimedia.org/wiki/Special:CentralAuth/Kungu01" class="extiw" title="m:Special:CentralAuth/Kungu01">meta</a>)</sup></li>
<li><a class="external text" href="https://iw.toolforge.org/guc/index.php?user=Kungu01&amp;blocks=true">Global&nbsp;contribs</a></li>
<li><a class="external text" href="https://commons.wikimedia.org/w/index.php?title=Special:ActiveUsers&amp;limit=1&amp;username=Kungu01">Recent&nbsp;activity</a></li></ul>
</td>
</tr>
</tbody></table>
</div><!--esi <esi:include src="/esitest-fa8a495983347898/content" /> --><noscript><img src="https://commons.wikimedia.org/wiki/Special:CentralAutoLogin/start?type=1x1&amp;usesul3=1" alt="" width="1" height="1" style="border: none; position: absolute;"></noscript>
<div class="printfooter" data-nosnippet="">Retrieved from "<a dir="ltr" href="https://commons.wikimedia.org/wiki/Special:Contributions/Kungu01">https://commons.wikimedia.org/wiki/Special:Contributions/Kungu01</a>"</div></div>
					<div id="catlinks" class="catlinks catlinks-allhidden" data-mw="interface"></div>
				</div>
			</main>
			
		</div>
		<div class="mw-footer-container">
			
<footer id="footer" class="mw-footer" >
	<ul id="footer-info">
</ul>

	<ul id="footer-places">
	<li id="footer-places-privacy"><a href="https://foundation.wikimedia.org/wiki/Special:MyLanguage/Policy:Privacy_policy">Privacy policy</a></li>
	<li id="footer-places-about"><a href="https://commons.wikimedia.org/wiki/Commons:Welcome">About Wikimedia Commons</a></li>
	<li id="footer-places-disclaimers"><a href="https://commons.wikimedia.org/wiki/Commons:General_disclaimer">Disclaimers</a></li>
	<li id="footer-places-wm-codeofconduct"><a href="https://foundation.wikimedia.org/wiki/Special:MyLanguage/Policy:Universal_Code_of_Conduct">Code of Conduct</a></li>
	<li id="footer-places-developers"><a href="https://developer.wikimedia.org/">Developers</a></li>
	<li id="footer-places-statslink"><a href="https://stats.wikimedia.org/#/commons.wikimedia.org">Statistics</a></li>
	<li id="footer-places-cookiestatement"><a href="https://foundation.wikimedia.org/wiki/Special:MyLanguage/Policy:Cookie_statement">Cookie statement</a></li>
	<li id="footer-places-mobileview"><a href="http://commons.m.wikimedia.org/w/index.php?title=Special:Contributions/Kungu01&amp;mobileaction=toggle_view_mobile" class="noprint stopMobileRedirectToggle">Mobile view</a></li>
</ul>

	<ul id="footer-icons" class="noprint">
	<li id="footer-copyrightico"><a href="https://www.wikimedia.org/" class="cdx-button cdx-button--fake-button cdx-button--size-large cdx-button--fake-button--enabled"><picture><source media="(min-width: 500px)" srcset="/static/images/footer/wikimedia-button.svg" width="84" height="29"><img src="https://commons.wikimedia.org/static/images/footer/wikimedia.svg" width="25" height="25" alt="Wikimedia Foundation" lang="en" loading="lazy"></picture></a></li>
	<li id="footer-poweredbyico"><a href="https://www.mediawiki.org/" class="cdx-button cdx-button--fake-button cdx-button--size-large cdx-button--fake-button--enabled"><picture><source media="(min-width: 500px)" srcset="/w/resources/assets/poweredby_mediawiki.svg" width="88" height="31"><img src="https://commons.wikimedia.org/w/resources/assets/mediawiki_compact.svg" alt="Powered by MediaWiki" lang="en" width="25" height="25" loading="lazy"></picture></a></li>
</ul>

</footer>

		</div>
	</div> 
</div> 
<div class="vector-header-container vector-sticky-header-container no-font-mode-scale">
	<div id="vector-sticky-header" class="vector-sticky-header">
		<div class="vector-sticky-header-start">
			<div class="vector-sticky-header-icon-start vector-button-flush-left vector-button-flush-right" aria-hidden="true">
				<button class="cdx-button cdx-button--weight-quiet cdx-button--icon-only vector-sticky-header-search-toggle" tabindex="-1" data-event-name="ui.vector-sticky-search-form.icon"><span class="vector-icon mw-ui-icon-search mw-ui-icon-wikimedia-search"></span>

<span>Search</span>
			</button>
		</div>
			
		<div role="search" class="vector-search-box-vue vector-search-box">
			<div class="vector-typeahead-search-container">
				<div class="cdx-typeahead-search">
					<form action="https://commons.wikimedia.org/w/index.php" id="vector-sticky-search-form" class="cdx-search-input cdx-search-input--has-end-button">
						<div  class="cdx-search-input__input-wrapper"  data-search-loc="header-moved">
							<div class="cdx-text-input cdx-text-input--has-start-icon">
								<input
									class="cdx-text-input__input mw-searchInput" autocomplete="off"
									
									type="search" name="search" placeholder="Search Wikimedia Commons">
								<span class="cdx-text-input__icon cdx-text-input__start-icon"></span>
							</div>
							<input type="hidden" name="title" value="Special:MediaSearch">
						</div>
						<button class="cdx-button cdx-search-input__end-button">Search</button>
					</form>
				</div>
			</div>
		</div>
		<div class="vector-sticky-header-context-bar">
				<div class="vector-sticky-header-context-bar-primary" aria-hidden="true" >User contributions for <bdi>Kungu01</bdi></div>
			</div>
		</div>
		<div class="vector-sticky-header-end" aria-hidden="true">
			<div class="vector-sticky-header-icons">
				<a href="#" class="cdx-button cdx-button--fake-button cdx-button--fake-button--enabled cdx-button--weight-quiet cdx-button--icon-only" id="ca-talk-sticky-header" tabindex="-1" data-event-name="talk-sticky-header"><span class="vector-icon mw-ui-icon-speechBubbles mw-ui-icon-wikimedia-speechBubbles"></span>

<span></span>
			</a>
			<a href="#" class="cdx-button cdx-button--fake-button cdx-button--fake-button--enabled cdx-button--weight-quiet cdx-button--icon-only" id="ca-subject-sticky-header" tabindex="-1" data-event-name="subject-sticky-header"><span class="vector-icon mw-ui-icon-article mw-ui-icon-wikimedia-article"></span>

<span></span>
			</a>
			<a href="#" class="cdx-button cdx-button--fake-button cdx-button--fake-button--enabled cdx-button--weight-quiet cdx-button--icon-only" id="ca-history-sticky-header" tabindex="-1" data-event-name="history-sticky-header"><span class="vector-icon mw-ui-icon-wikimedia-history mw-ui-icon-wikimedia-wikimedia-history"></span>

<span></span>
			</a>
			<a href="#" class="cdx-button cdx-button--fake-button cdx-button--fake-button--enabled cdx-button--weight-quiet cdx-button--icon-only mw-watchlink" id="ca-watchstar-sticky-header" tabindex="-1" data-event-name="watch-sticky-header"><span class="vector-icon mw-ui-icon-wikimedia-star mw-ui-icon-wikimedia-wikimedia-star"></span>

<span></span>
			</a>
			<a href="#" class="cdx-button cdx-button--fake-button cdx-button--fake-button--enabled cdx-button--weight-quiet cdx-button--icon-only" id="ca-edit-sticky-header" tabindex="-1" data-event-name="wikitext-edit-sticky-header"><span class="vector-icon mw-ui-icon-wikimedia-wikiText mw-ui-icon-wikimedia-wikimedia-wikiText"></span>

<span></span>
			</a>
			<a href="#" class="cdx-button cdx-button--fake-button cdx-button--fake-button--enabled cdx-button--weight-quiet cdx-button--icon-only" id="ca-ve-edit-sticky-header" tabindex="-1" data-event-name="ve-edit-sticky-header"><span class="vector-icon mw-ui-icon-wikimedia-edit mw-ui-icon-wikimedia-wikimedia-edit"></span>

<span></span>
			</a>
			<a href="#" class="cdx-button cdx-button--fake-button cdx-button--fake-button--enabled cdx-button--weight-quiet cdx-button--icon-only" id="ca-viewsource-sticky-header" tabindex="-1" data-event-name="ve-edit-protected-sticky-header"><span class="vector-icon mw-ui-icon-wikimedia-editLock mw-ui-icon-wikimedia-wikimedia-editLock"></span>

<span></span>
			</a>
		</div>
			<div class="vector-sticky-header-buttons">
				<a href="#" class="cdx-button cdx-button--fake-button cdx-button--fake-button--enabled cdx-button--weight-quiet cdx-button--action-progressive" id="ca-addsection-sticky-header" tabindex="-1" data-event-name="addsection-sticky-header"><span class="vector-icon mw-ui-icon-speechBubbleAdd-progressive mw-ui-icon-wikimedia-speechBubbleAdd-progressive"></span>

<span>Add topic</span>
			</a>
		</div>
			<div class="vector-sticky-header-icon-end">
				<div class="vector-user-links">
				</div>
			</div>
		</div>
	</div>
</div>
<div class="mw-portlet mw-portlet-dock-bottom emptyPortlet" id="p-dock-bottom">
	<ul>
		
	</ul>
</div>
<script>(RLQ=window.RLQ||[]).push(function(){mw.config.set({"wgHostname":"mw-web.eqiad.main-7b48b5fb74-ftlg7","wgBackendResponseTime":289});});</script>
</body>

<!-- Mirrored from commons.wikimedia.org/wiki/Special:Contributions/Kungu01 by HTTrack Website Copier/3.x [XR&CO'2014], Sun, 03 Aug 2025 09:00:41 GMT -->
</html>