@prefix rdf: <http://www.w3.org/1999/02/22-rdf-syntax-ns#> .
@prefix xsd: <http://www.w3.org/2001/XMLSchema#> .
@prefix rdfs: <http://www.w3.org/2000/01/rdf-schema#> .
@prefix owl: <http://www.w3.org/2002/07/owl#> .
@prefix wikibase: <http://wikiba.se/ontology#> .
@prefix skos: <http://www.w3.org/2004/02/skos/core#> .
@prefix schema: <http://schema.org/> .
@prefix cc: <http://creativecommons.org/ns#> .
@prefix geo: <http://www.opengis.net/ont/geosparql#> .
@prefix prov: <http://www.w3.org/ns/prov#> .
@prefix wd: <http://www.wikidata.org/entity/> .
@prefix data: <https://www.wikidata.org/wiki/Special:EntityData/> .
@prefix wds: <http://www.wikidata.org/entity/statement/> .
@prefix wdref: <http://www.wikidata.org/reference/> .
@prefix wdv: <http://www.wikidata.org/value/> .
@prefix wdt: <http://www.wikidata.org/prop/direct/> .
@prefix wdtn: <http://www.wikidata.org/prop/direct-normalized/> .
@prefix p: <http://www.wikidata.org/prop/> .
@prefix ps: <http://www.wikidata.org/prop/statement/> .
@prefix psv: <http://www.wikidata.org/prop/statement/value/> .
@prefix psn: <http://www.wikidata.org/prop/statement/value-normalized/> .
@prefix pq: <http://www.wikidata.org/prop/qualifier/> .
@prefix pqv: <http://www.wikidata.org/prop/qualifier/value/> .
@prefix pqn: <http://www.wikidata.org/prop/qualifier/value-normalized/> .
@prefix pr: <http://www.wikidata.org/prop/reference/> .
@prefix prv: <http://www.wikidata.org/prop/reference/value/> .
@prefix prn: <http://www.wikidata.org/prop/reference/value-normalized/> .
@prefix wdno: <http://www.wikidata.org/prop/novalue/> .
@prefix sdc: <https://commons.wikimedia.org/entity/> .
@prefix sdcdata: <https://commons.wikimedia.org/wiki/Special:EntityData/> .
@prefix sdcs: <https://commons.wikimedia.org/entity/statement/> .
@prefix sdcref: <https://commons.wikimedia.org/reference/> .
@prefix sdcv: <https://commons.wikimedia.org/value/> .
@prefix sdct: <https://commons.wikimedia.org/prop/direct/> .
@prefix sdctn: <https://commons.wikimedia.org/prop/direct-normalized/> .
@prefix sdcp: <https://commons.wikimedia.org/prop/> .
@prefix sdcps: <https://commons.wikimedia.org/prop/statement/> .
@prefix sdcpsv: <https://commons.wikimedia.org/prop/statement/value/> .
@prefix sdcpsn: <https://commons.wikimedia.org/prop/statement/value-normalized/> .
@prefix sdcpq: <https://commons.wikimedia.org/prop/qualifier/> .
@prefix sdcpqv: <https://commons.wikimedia.org/prop/qualifier/value/> .
@prefix sdcpqn: <https://commons.wikimedia.org/prop/qualifier/value-normalized/> .
@prefix sdcpr: <https://commons.wikimedia.org/prop/reference/> .
@prefix sdcprv: <https://commons.wikimedia.org/prop/reference/value/> .
@prefix sdcprn: <https://commons.wikimedia.org/prop/reference/value-normalized/> .
@prefix sdcno: <https://commons.wikimedia.org/prop/novalue/> .

sdcdata:********* a schema:Dataset ;
	schema:about sdc:********* ;
	cc:license <http://creativecommons.org/publicdomain/zero/1.0/> ;
	schema:softwareVersion "1.0.0" ;
	schema:version "**********"^^xsd:integer ;
	schema:dateModified "2025-03-04T09:31:16Z"^^xsd:dateTime .

sdc:********* a wikibase:Mediainfo ;
	wdt:P571 "2017-05-23T00:00:00Z"^^xsd:dateTime ;
	wdt:P3575 "+14349"^^xsd:decimal ;
	wdt:P1163 "image/svg+xml" ;
	wdt:P4092 "ceadc1634b02a17d67524b7c59e56acdc7278f27" ;
	p:P571 sdcs:*********-87455C05-45BE-439D-B8D7-E93A41BDEABE .

sdcs:*********-87455C05-45BE-439D-B8D7-E93A41BDEABE a wikibase:Statement,
		wikibase:BestRank ;
	wikibase:rank wikibase:NormalRank ;
	ps:P571 "2017-05-23T00:00:00Z"^^xsd:dateTime ;
	psv:P571 sdcv:12ec4edd2e8281ec4d0b4dfe863a0f39 .

sdc:********* p:P3575 sdcs:*********-E3DCBF3E-FB95-4C13-BE8C-D205F09482CA .

sdcs:*********-E3DCBF3E-FB95-4C13-BE8C-D205F09482CA a wikibase:Statement,
		wikibase:BestRank ;
	wikibase:rank wikibase:NormalRank ;
	ps:P3575 "+14349"^^xsd:decimal ;
	psv:P3575 sdcv:ac8b5bd4994f45e97a6d41358c831b90 .

sdc:********* p:P1163 sdcs:*********-FCD2AFDC-D208-4429-ADD6-427693287943 .

sdcs:*********-FCD2AFDC-D208-4429-ADD6-427693287943 a wikibase:Statement,
		wikibase:BestRank ;
	wikibase:rank wikibase:NormalRank ;
	ps:P1163 "image/svg+xml" .

sdc:********* p:P4092 sdcs:*********-706B59BB-490A-455F-9061-957D23B0AC24 .

sdcs:*********-706B59BB-490A-455F-9061-957D23B0AC24 a wikibase:Statement,
		wikibase:BestRank ;
	wikibase:rank wikibase:NormalRank ;
	ps:P4092 "ceadc1634b02a17d67524b7c59e56acdc7278f27" ;
	pq:P459 wd:Q13414952 .

sdc:********* a schema:MediaObject,
		schema:ImageObject ;
	schema:encodingFormat "image/svg+xml" ;
	schema:contentUrl <https://upload.wikimedia.org/wikipedia/commons/7/77/Emoji_u1f35c.svg> ;
	schema:url <http://commons.wikimedia.org/wiki/Special:FilePath/Emoji%20u1f35c.svg> ;
	schema:contentSize "14349"^^xsd:integer ;
	schema:height "128"^^xsd:integer ;
	schema:width "128"^^xsd:integer .

wd:P571 a wikibase:Property ;
	rdfs:label "inception"@en ;
	skos:prefLabel "inception"@en ;
	schema:name "inception"@en ;
	schema:description "time when an entity begins to exist; for date of official opening use P1619"@en ;
	wikibase:propertyType <http://wikiba.se/ontology#Time> ;
	wikibase:directClaim wdt:P571 ;
	wikibase:claim p:P571 ;
	wikibase:statementProperty ps:P571 ;
	wikibase:statementValue psv:P571 ;
	wikibase:qualifier pq:P571 ;
	wikibase:qualifierValue pqv:P571 ;
	wikibase:reference pr:P571 ;
	wikibase:referenceValue prv:P571 ;
	wikibase:novalue wdno:P571 .

p:P571 a owl:ObjectProperty .

psv:P571 a owl:ObjectProperty .

pqv:P571 a owl:ObjectProperty .

prv:P571 a owl:ObjectProperty .

wdt:P571 a owl:DatatypeProperty .

ps:P571 a owl:DatatypeProperty .

pq:P571 a owl:DatatypeProperty .

pr:P571 a owl:DatatypeProperty .

wdno:P571 a owl:Class ;
	owl:complementOf _:ed4d9507645e2210d8e4ed14d3266267 .

_:ed4d9507645e2210d8e4ed14d3266267 a owl:Restriction ;
	owl:onProperty wdt:P571 ;
	owl:someValuesFrom owl:Thing .

wd:P3575 a wikibase:Property ;
	rdfs:label "data size"@en ;
	skos:prefLabel "data size"@en ;
	schema:name "data size"@en ;
	schema:description "size of a software, dataset, neural network, or individual file"@en ;
	wikibase:propertyType <http://wikiba.se/ontology#Quantity> ;
	wikibase:directClaim wdt:P3575 ;
	wikibase:claim p:P3575 ;
	wikibase:statementProperty ps:P3575 ;
	wikibase:statementValue psv:P3575 ;
	wikibase:qualifier pq:P3575 ;
	wikibase:qualifierValue pqv:P3575 ;
	wikibase:reference pr:P3575 ;
	wikibase:referenceValue prv:P3575 ;
	wikibase:novalue wdno:P3575 ;
	wikibase:directClaimNormalized wdtn:P3575 ;
	wikibase:statementValueNormalized psn:P3575 ;
	wikibase:qualifierValueNormalized pqn:P3575 ;
	wikibase:referenceValueNormalized prn:P3575 .

p:P3575 a owl:ObjectProperty .

psv:P3575 a owl:ObjectProperty .

pqv:P3575 a owl:ObjectProperty .

prv:P3575 a owl:ObjectProperty .

wdt:P3575 a owl:DatatypeProperty .

ps:P3575 a owl:DatatypeProperty .

pq:P3575 a owl:DatatypeProperty .

pr:P3575 a owl:DatatypeProperty .

psn:P3575 a owl:ObjectProperty .

pqn:P3575 a owl:ObjectProperty .

prn:P3575 a owl:ObjectProperty .

wdtn:P3575 a owl:DatatypeProperty .

wdno:P3575 a owl:Class ;
	owl:complementOf _:b624cfad58daea2904a2149304d28205 .

_:b624cfad58daea2904a2149304d28205 a owl:Restriction ;
	owl:onProperty wdt:P3575 ;
	owl:someValuesFrom owl:Thing .

wd:P1163 a wikibase:Property ;
	rdfs:label "media type"@en ;
	skos:prefLabel "media type"@en ;
	schema:name "media type"@en ;
	schema:description "IANA-registered identifier for a file type"@en ;
	wikibase:propertyType <http://wikiba.se/ontology#String> ;
	wikibase:directClaim wdt:P1163 ;
	wikibase:claim p:P1163 ;
	wikibase:statementProperty ps:P1163 ;
	wikibase:statementValue psv:P1163 ;
	wikibase:qualifier pq:P1163 ;
	wikibase:qualifierValue pqv:P1163 ;
	wikibase:reference pr:P1163 ;
	wikibase:referenceValue prv:P1163 ;
	wikibase:novalue wdno:P1163 .

p:P1163 a owl:ObjectProperty .

psv:P1163 a owl:ObjectProperty .

pqv:P1163 a owl:ObjectProperty .

prv:P1163 a owl:ObjectProperty .

wdt:P1163 a owl:DatatypeProperty .

ps:P1163 a owl:DatatypeProperty .

pq:P1163 a owl:DatatypeProperty .

pr:P1163 a owl:DatatypeProperty .

wdno:P1163 a owl:Class ;
	owl:complementOf _:704e62989d8eb9df732666f92fa84714 .

_:704e62989d8eb9df732666f92fa84714 a owl:Restriction ;
	owl:onProperty wdt:P1163 ;
	owl:someValuesFrom owl:Thing .

wd:P4092 a wikibase:Property ;
	rdfs:label "checksum"@en ;
	skos:prefLabel "checksum"@en ;
	schema:name "checksum"@en ;
	schema:description "small-sized datum derived from a block of digital data for the purpose of detecting errors. Use qualifier \"determination method\" (P459) to indicate how it's calculated, e.g. MD5."@en ;
	wikibase:propertyType <http://wikiba.se/ontology#String> ;
	wikibase:directClaim wdt:P4092 ;
	wikibase:claim p:P4092 ;
	wikibase:statementProperty ps:P4092 ;
	wikibase:statementValue psv:P4092 ;
	wikibase:qualifier pq:P4092 ;
	wikibase:qualifierValue pqv:P4092 ;
	wikibase:reference pr:P4092 ;
	wikibase:referenceValue prv:P4092 ;
	wikibase:novalue wdno:P4092 .

p:P4092 a owl:ObjectProperty .

psv:P4092 a owl:ObjectProperty .

pqv:P4092 a owl:ObjectProperty .

prv:P4092 a owl:ObjectProperty .

wdt:P4092 a owl:DatatypeProperty .

ps:P4092 a owl:DatatypeProperty .

pq:P4092 a owl:DatatypeProperty .

pr:P4092 a owl:DatatypeProperty .

wdno:P4092 a owl:Class ;
	owl:complementOf _:6d4307bb92af815977a58e72cb8245cd .

_:6d4307bb92af815977a58e72cb8245cd a owl:Restriction ;
	owl:onProperty wdt:P4092 ;
	owl:someValuesFrom owl:Thing .

wd:Q13414952 a wikibase:Item ;
	rdfs:label "SHA-1"@en ;
	skos:prefLabel "SHA-1"@en ;
	schema:name "SHA-1"@en ;
	rdfs:label "SHA-1"@mul ;
	skos:prefLabel "SHA-1"@mul ;
	schema:name "SHA-1"@mul ;
	schema:description "individual cryptographic hash function with output size of 160 bits"@en .

wd:P459 a wikibase:Property ;
	rdfs:label "determination method or standard"@en ;
	skos:prefLabel "determination method or standard"@en ;
	schema:name "determination method or standard"@en ;
	schema:description "how a value is determined, or the standard by which it is declared"@en ;
	wikibase:propertyType <http://wikiba.se/ontology#WikibaseItem> ;
	wikibase:directClaim wdt:P459 ;
	wikibase:claim p:P459 ;
	wikibase:statementProperty ps:P459 ;
	wikibase:statementValue psv:P459 ;
	wikibase:qualifier pq:P459 ;
	wikibase:qualifierValue pqv:P459 ;
	wikibase:reference pr:P459 ;
	wikibase:referenceValue prv:P459 ;
	wikibase:novalue wdno:P459 .

p:P459 a owl:ObjectProperty .

psv:P459 a owl:ObjectProperty .

pqv:P459 a owl:ObjectProperty .

prv:P459 a owl:ObjectProperty .

wdt:P459 a owl:ObjectProperty .

ps:P459 a owl:ObjectProperty .

pq:P459 a owl:ObjectProperty .

pr:P459 a owl:ObjectProperty .

wdno:P459 a owl:Class ;
	owl:complementOf _:88b70b8bfdb5c147baf5a03f4254bd4b .

_:88b70b8bfdb5c147baf5a03f4254bd4b a owl:Restriction ;
	owl:onProperty wdt:P459 ;
	owl:someValuesFrom owl:Thing .

sdcv:ac8b5bd4994f45e97a6d41358c831b90 a wikibase:QuantityValue ;
	wikibase:quantityAmount "+14349"^^xsd:decimal ;
	wikibase:quantityUnit <http://www.wikidata.org/entity/Q8799> .

sdcv:12ec4edd2e8281ec4d0b4dfe863a0f39 a wikibase:TimeValue ;
	wikibase:timeValue "2017-05-23T00:00:00Z"^^xsd:dateTime ;
	wikibase:timePrecision "11"^^xsd:integer ;
	wikibase:timeTimezone "0"^^xsd:integer ;
	wikibase:timeCalendarModel <http://www.wikidata.org/entity/Q1985727> .
