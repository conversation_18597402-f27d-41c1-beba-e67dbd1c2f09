{"@graph": [{"@id": "sdcdata:*********", "@type": "schema:Dataset", "about": "sdc:*********", "license": "http://creativecommons.org/publicdomain/zero/1.0/", "softwareVersion": "1.0.0", "version": **********, "dateModified": "2025-05-20T22:41:37Z"}, {"@id": "sdc:*********", "@type": "wikibase:Mediainfo", "P571": "2014-11-04T00:00:00Z", "P4082": "wd:Q66215", "P6216": "wd:*********", "P275": "wd:*********", "P7482": "wd:*********", "P170": "_:41c0bc9302725910346a94cdd1d8d6e7", "p:P571": "sdcs:*********-4EC8A274-DA19-42C2-87C9-30413067E002"}, {"@id": "sdcs:*********-4EC8A274-DA19-42C2-87C9-30413067E002", "@type": ["wikibase:Statement", "wikibase:BestRank"], "rank": "wikibase:NormalRank", "ps:P571": "2014-11-04T00:00:00Z", "psv:P571": "sdcv:e4113b45e251ad1424a7ea30212173a5"}, {"@id": "sdc:*********", "p:P4082": "sdcs:*********-5C69C079-394A-47FA-B061-1D597710B93B"}, {"@id": "sdcs:*********-5C69C079-394A-47FA-B061-1D597710B93B", "@type": ["wikibase:Statement", "wikibase:BestRank"], "rank": "wikibase:NormalRank", "ps:P4082": "wd:Q66215"}, {"@id": "sdc:*********", "p:P6216": "sdcs:*********-C9F5AAF1-1BB0-430E-8E76-01F3A57E8D33"}, {"@id": "sdcs:*********-C9F5AAF1-1BB0-430E-8E76-01F3A57E8D33", "@type": ["wikibase:Statement", "wikibase:BestRank"], "rank": "wikibase:NormalRank", "ps:P6216": "wd:*********"}, {"@id": "sdc:*********", "p:P275": "sdcs:*********-A63F835D-DEEC-42B7-9457-443A2DCA6589"}, {"@id": "sdcs:*********-A63F835D-DEEC-42B7-9457-443A2DCA6589", "@type": ["wikibase:Statement", "wikibase:BestRank"], "rank": "wikibase:NormalRank", "ps:P275": "wd:*********"}, {"@id": "sdc:*********", "p:P7482": "sdcs:*********-B1801C92-410F-440E-921F-C231C0AFF8CB"}, {"@id": "sdcs:*********-B1801C92-410F-440E-921F-C231C0AFF8CB", "@type": ["wikibase:Statement", "wikibase:BestRank"], "rank": "wikibase:NormalRank", "ps:P7482": "wd:*********"}, {"@id": "sdc:*********", "p:P170": "sdcs:*********-CF584A4E-8C5C-4969-B85C-93B446DAE7BB"}, {"@id": "sdcs:*********-CF584A4E-8C5C-4969-B85C-93B446DAE7BB", "@type": ["wikibase:Statement", "wikibase:BestRank"], "rank": "wikibase:NormalRank", "ps:P170": "_:d4375539ae08fa29d89bda734f5be1ee", "P3831": "wd:Q33231", "P2093": "kungu irungu", "P4174": "Kungu01"}, {"@id": "sdc:*********", "@type": ["schema:MediaObject", "schema:ImageObject"], "encodingFormat": "image/jpeg", "contentUrl": "https://upload.wikimedia.org/wikipedia/commons/5/5f/Madafu-chopping.jpg", "url": "http://commons.wikimedia.org/wiki/Special:FilePath/Madafu-chopping.jpg", "contentSize": 7923349, "height": 3424, "width": 4544}, {"@id": "wd:P571", "@type": "wikibase:Property", "label": {"@language": "en", "@value": "inception"}, "prefLabel": {"@language": "en", "@value": "inception"}, "name": {"@language": "en", "@value": "inception"}, "description": {"@language": "en", "@value": "time when an entity begins to exist; for date of official opening use P1619"}, "propertyType": "http://wikiba.se/ontology#Time", "directClaim": "wdt:P571", "claim": "p:P571", "statementProperty": "ps:P571", "statementValue": "psv:P571", "qualifier": "pq:P571", "qualifierValue": "pqv:P571", "reference": "pr:P571", "referenceValue": "prv:P571", "novalue": "wdno:P571"}, {"@id": "p:P571", "@type": "owl:ObjectProperty"}, {"@id": "psv:P571", "@type": "owl:ObjectProperty"}, {"@id": "pqv:P571", "@type": "owl:ObjectProperty"}, {"@id": "prv:P571", "@type": "owl:ObjectProperty"}, {"@id": "wdt:P571", "@type": "owl:DatatypeProperty"}, {"@id": "ps:P571", "@type": "owl:DatatypeProperty"}, {"@id": "pq:P571", "@type": "owl:DatatypeProperty"}, {"@id": "pr:P571", "@type": "owl:DatatypeProperty"}, {"@id": "wdno:P571", "@type": "owl:Class", "complementOf": "_:ed4d9507645e2210d8e4ed14d3266267"}, {"@id": "_:ed4d9507645e2210d8e4ed14d3266267", "@type": "owl:Restriction", "onProperty": "wdt:P571", "someValuesFrom": "owl:<PERSON>"}, {"@id": "wd:Q66215", "@type": "wikibase:Item", "label": {"@language": "en", "@value": "Canon EOS 6D"}, "prefLabel": {"@language": "en", "@value": "Canon EOS 6D"}, "name": {"@language": "en", "@value": "Canon EOS 6D"}, "description": {"@language": "en", "@value": "digital single-lens reflex camera"}}, {"@id": "wd:P4082", "@type": "wikibase:Property", "label": {"@language": "en", "@value": "captured with"}, "prefLabel": {"@language": "en", "@value": "captured with"}, "name": {"@language": "en", "@value": "captured with"}, "description": {"@language": "en", "@value": "equipment (e.g. model of camera, lens, microphone), used to capture this image, video, audio, or data"}, "propertyType": "http://wikiba.se/ontology#WikibaseItem", "directClaim": "wdt:P4082", "claim": "p:P4082", "statementProperty": "ps:P4082", "statementValue": "psv:P4082", "qualifier": "pq:P4082", "qualifierValue": "pqv:P4082", "reference": "pr:P4082", "referenceValue": "prv:P4082", "novalue": "wdno:P4082"}, {"@id": "p:P4082", "@type": "owl:ObjectProperty"}, {"@id": "psv:P4082", "@type": "owl:ObjectProperty"}, {"@id": "pqv:P4082", "@type": "owl:ObjectProperty"}, {"@id": "prv:P4082", "@type": "owl:ObjectProperty"}, {"@id": "wdt:P4082", "@type": "owl:ObjectProperty"}, {"@id": "ps:P4082", "@type": "owl:ObjectProperty"}, {"@id": "pq:P4082", "@type": "owl:ObjectProperty"}, {"@id": "pr:P4082", "@type": "owl:ObjectProperty"}, {"@id": "wdno:P4082", "@type": "owl:Class", "complementOf": "_:2c1e635be6a9c129b8132793cee4cc90"}, {"@id": "_:2c1e635be6a9c129b8132793cee4cc90", "@type": "owl:Restriction", "onProperty": "wdt:P4082", "someValuesFrom": "owl:<PERSON>"}, {"@id": "wd:*********", "@type": "wikibase:Item", "label": {"@language": "en", "@value": "copyrighted"}, "prefLabel": {"@language": "en", "@value": "copyrighted"}, "name": {"@language": "en", "@value": "copyrighted"}, "description": {"@language": "en", "@value": "legal state of a work as recognised as an intellectual property of an entity"}}, {"@id": "wd:P6216", "@type": "wikibase:Property", "label": {"@language": "en", "@value": "copyright status"}, "prefLabel": {"@language": "en", "@value": "copyright status"}, "name": {"@language": "en", "@value": "copyright status"}, "description": {"@language": "en", "@value": "copyright status for intellectual creations like works of art, publications, software, etc."}, "propertyType": "http://wikiba.se/ontology#WikibaseItem", "directClaim": "wdt:P6216", "claim": "p:P6216", "statementProperty": "ps:P6216", "statementValue": "psv:P6216", "qualifier": "pq:P6216", "qualifierValue": "pqv:P6216", "reference": "pr:P6216", "referenceValue": "prv:P6216", "novalue": "wdno:P6216"}, {"@id": "p:P6216", "@type": "owl:ObjectProperty"}, {"@id": "psv:P6216", "@type": "owl:ObjectProperty"}, {"@id": "pqv:P6216", "@type": "owl:ObjectProperty"}, {"@id": "prv:P6216", "@type": "owl:ObjectProperty"}, {"@id": "wdt:P6216", "@type": "owl:ObjectProperty"}, {"@id": "ps:P6216", "@type": "owl:ObjectProperty"}, {"@id": "pq:P6216", "@type": "owl:ObjectProperty"}, {"@id": "pr:P6216", "@type": "owl:ObjectProperty"}, {"@id": "wdno:P6216", "@type": "owl:Class", "complementOf": "_:b1252803b14cb4551e630f41e1deba59"}, {"@id": "_:b1252803b14cb4551e630f41e1deba59", "@type": "owl:Restriction", "onProperty": "wdt:P6216", "someValuesFrom": "owl:<PERSON>"}, {"@id": "wd:*********", "@type": "wikibase:Item", "label": {"@language": "en", "@value": "Creative Commons Attribution-ShareAlike 4.0 International"}, "prefLabel": {"@language": "en", "@value": "Creative Commons Attribution-ShareAlike 4.0 International"}, "name": {"@language": "en", "@value": "Creative Commons Attribution-ShareAlike 4.0 International"}, "description": {"@language": "en", "@value": "Creative Commons license"}}, {"@id": "wd:P275", "@type": "wikibase:Property", "label": {"@language": "en", "@value": "copyright license"}, "prefLabel": {"@language": "en", "@value": "copyright license"}, "name": {"@language": "en", "@value": "copyright license"}, "description": {"@language": "en", "@value": "license under which this copyrighted work is released"}, "propertyType": "http://wikiba.se/ontology#WikibaseItem", "directClaim": "wdt:P275", "claim": "p:P275", "statementProperty": "ps:P275", "statementValue": "psv:P275", "qualifier": "pq:P275", "qualifierValue": "pqv:P275", "reference": "pr:P275", "referenceValue": "prv:P275", "novalue": "wdno:P275"}, {"@id": "p:P275", "@type": "owl:ObjectProperty"}, {"@id": "psv:P275", "@type": "owl:ObjectProperty"}, {"@id": "pqv:P275", "@type": "owl:ObjectProperty"}, {"@id": "prv:P275", "@type": "owl:ObjectProperty"}, {"@id": "wdt:P275", "@type": "owl:ObjectProperty"}, {"@id": "ps:P275", "@type": "owl:ObjectProperty"}, {"@id": "pq:P275", "@type": "owl:ObjectProperty"}, {"@id": "pr:P275", "@type": "owl:ObjectProperty"}, {"@id": "wdno:P275", "@type": "owl:Class", "complementOf": "_:298aabdb0974253ac321b0308875c924"}, {"@id": "_:298aabdb0974253ac321b0308875c924", "@type": "owl:Restriction", "onProperty": "wdt:P275", "someValuesFrom": "owl:<PERSON>"}, {"@id": "wd:*********", "@type": "wikibase:Item", "label": {"@language": "en", "@value": "original creation by uploader"}, "prefLabel": {"@language": "en", "@value": "original creation by uploader"}, "name": {"@language": "en", "@value": "original creation by uploader"}, "description": {"@language": "en", "@value": "value to indicate source of an image on Wikimedia Commons"}}, {"@id": "wd:P7482", "@type": "wikibase:Property", "label": {"@language": "en", "@value": "source of file"}, "prefLabel": {"@language": "en", "@value": "source of file"}, "name": {"@language": "en", "@value": "source of file"}, "description": {"@language": "en", "@value": "broad nature of the origin of the file"}, "propertyType": "http://wikiba.se/ontology#WikibaseItem", "directClaim": "wdt:P7482", "claim": "p:P7482", "statementProperty": "ps:P7482", "statementValue": "psv:P7482", "qualifier": "pq:P7482", "qualifierValue": "pqv:P7482", "reference": "pr:P7482", "referenceValue": "prv:P7482", "novalue": "wdno:P7482"}, {"@id": "p:P7482", "@type": "owl:ObjectProperty"}, {"@id": "psv:P7482", "@type": "owl:ObjectProperty"}, {"@id": "pqv:P7482", "@type": "owl:ObjectProperty"}, {"@id": "prv:P7482", "@type": "owl:ObjectProperty"}, {"@id": "wdt:P7482", "@type": "owl:ObjectProperty"}, {"@id": "ps:P7482", "@type": "owl:ObjectProperty"}, {"@id": "pq:P7482", "@type": "owl:ObjectProperty"}, {"@id": "pr:P7482", "@type": "owl:ObjectProperty"}, {"@id": "wdno:P7482", "@type": "owl:Class", "complementOf": "_:acc493f3ae8de4cf9c16f9963879aa6e"}, {"@id": "_:acc493f3ae8de4cf9c16f9963879aa6e", "@type": "owl:Restriction", "onProperty": "wdt:P7482", "someValuesFrom": "owl:<PERSON>"}, {"@id": "wd:P170", "@type": "wikibase:Property", "label": {"@language": "en", "@value": "creator"}, "prefLabel": {"@language": "en", "@value": "creator"}, "name": {"@language": "en", "@value": "creator"}, "description": {"@language": "en", "@value": "maker of this creative work or other object (where no more specific property exists)"}, "propertyType": "http://wikiba.se/ontology#WikibaseItem", "directClaim": "wdt:P170", "claim": "p:P170", "statementProperty": "ps:P170", "statementValue": "psv:P170", "qualifier": "pq:P170", "qualifierValue": "pqv:P170", "reference": "pr:P170", "referenceValue": "prv:P170", "novalue": "wdno:P170"}, {"@id": "p:P170", "@type": "owl:ObjectProperty"}, {"@id": "psv:P170", "@type": "owl:ObjectProperty"}, {"@id": "pqv:P170", "@type": "owl:ObjectProperty"}, {"@id": "prv:P170", "@type": "owl:ObjectProperty"}, {"@id": "wdt:P170", "@type": "owl:ObjectProperty"}, {"@id": "ps:P170", "@type": "owl:ObjectProperty"}, {"@id": "pq:P170", "@type": "owl:ObjectProperty"}, {"@id": "pr:P170", "@type": "owl:ObjectProperty"}, {"@id": "wdno:P170", "@type": "owl:Class", "complementOf": "_:9729a2092eee6702e50f1adf021a63b5"}, {"@id": "_:9729a2092eee6702e50f1adf021a63b5", "@type": "owl:Restriction", "onProperty": "wdt:P170", "someValuesFrom": "owl:<PERSON>"}, {"@id": "wd:Q33231", "@type": "wikibase:Item", "label": {"@language": "en", "@value": "photographer"}, "prefLabel": {"@language": "en", "@value": "photographer"}, "name": {"@language": "en", "@value": "photographer"}, "description": {"@language": "en", "@value": "person who takes photographs"}}, {"@id": "wd:P3831", "@type": "wikibase:Property", "label": {"@language": "en", "@value": "object of statement has role"}, "prefLabel": {"@language": "en", "@value": "object of statement has role"}, "name": {"@language": "en", "@value": "object of statement has role"}, "description": {"@language": "en", "@value": "(qualifier) role or generic identity of the predicate value/argument of a statement (\"object\") in the context of that statement; for the role of the item the statement is on (\"subject\"), use P2868"}, "propertyType": "http://wikiba.se/ontology#WikibaseItem", "directClaim": "wdt:P3831", "claim": "p:P3831", "statementProperty": "ps:P3831", "statementValue": "psv:P3831", "qualifier": "pq:P3831", "qualifierValue": "pqv:P3831", "reference": "pr:P3831", "referenceValue": "prv:P3831", "novalue": "wdno:P3831"}, {"@id": "p:P3831", "@type": "owl:ObjectProperty"}, {"@id": "psv:P3831", "@type": "owl:ObjectProperty"}, {"@id": "pqv:P3831", "@type": "owl:ObjectProperty"}, {"@id": "prv:P3831", "@type": "owl:ObjectProperty"}, {"@id": "wdt:P3831", "@type": "owl:ObjectProperty"}, {"@id": "ps:P3831", "@type": "owl:ObjectProperty"}, {"@id": "pq:P3831", "@type": "owl:ObjectProperty"}, {"@id": "pr:P3831", "@type": "owl:ObjectProperty"}, {"@id": "wdno:P3831", "@type": "owl:Class", "complementOf": "_:4941dbdb9a38c5a500b05366e45fe45d"}, {"@id": "_:4941dbdb9a38c5a500b05366e45fe45d", "@type": "owl:Restriction", "onProperty": "wdt:P3831", "someValuesFrom": "owl:<PERSON>"}, {"@id": "wd:P2093", "@type": "wikibase:Property", "label": {"@language": "en", "@value": "author name string"}, "prefLabel": {"@language": "en", "@value": "author name string"}, "name": {"@language": "en", "@value": "author name string"}, "description": {"@language": "en", "@value": "stores unspecified author or editor name for publications; use if Wikidata item for author (P50) or editor (P98) does not exist or is not known. Do not use both."}, "propertyType": "http://wikiba.se/ontology#String", "directClaim": "wdt:P2093", "claim": "p:P2093", "statementProperty": "ps:P2093", "statementValue": "psv:P2093", "qualifier": "pq:P2093", "qualifierValue": "pqv:P2093", "reference": "pr:P2093", "referenceValue": "prv:P2093", "novalue": "wdno:P2093"}, {"@id": "p:P2093", "@type": "owl:ObjectProperty"}, {"@id": "psv:P2093", "@type": "owl:ObjectProperty"}, {"@id": "pqv:P2093", "@type": "owl:ObjectProperty"}, {"@id": "prv:P2093", "@type": "owl:ObjectProperty"}, {"@id": "wdt:P2093", "@type": "owl:DatatypeProperty"}, {"@id": "ps:P2093", "@type": "owl:DatatypeProperty"}, {"@id": "pq:P2093", "@type": "owl:DatatypeProperty"}, {"@id": "pr:P2093", "@type": "owl:DatatypeProperty"}, {"@id": "wdno:P2093", "@type": "owl:Class", "complementOf": "_:e2221084d358cd9264ccc9b14e2b1f04"}, {"@id": "_:e2221084d358cd9264ccc9b14e2b1f04", "@type": "owl:Restriction", "onProperty": "wdt:P2093", "someValuesFrom": "owl:<PERSON>"}, {"@id": "wd:P4174", "@type": "wikibase:Property", "label": {"@language": "en", "@value": "Wikimedia username"}, "prefLabel": {"@language": "en", "@value": "Wikimedia username"}, "name": {"@language": "en", "@value": "Wikimedia username"}, "description": {"@language": "en", "@value": "user name of a person across all Wikimedia projects"}, "propertyType": "http://wikiba.se/ontology#ExternalId", "directClaim": "wdt:P4174", "claim": "p:P4174", "statementProperty": "ps:P4174", "statementValue": "psv:P4174", "qualifier": "pq:P4174", "qualifierValue": "pqv:P4174", "reference": "pr:P4174", "referenceValue": "prv:P4174", "novalue": "wdno:P4174", "directClaimNormalized": "wdtn:P4174", "statementValueNormalized": "psn:P4174", "qualifierValueNormalized": "pqn:P4174", "referenceValueNormalized": "prn:P4174"}, {"@id": "p:P4174", "@type": "owl:ObjectProperty"}, {"@id": "psv:P4174", "@type": "owl:ObjectProperty"}, {"@id": "pqv:P4174", "@type": "owl:ObjectProperty"}, {"@id": "prv:P4174", "@type": "owl:ObjectProperty"}, {"@id": "wdt:P4174", "@type": "owl:DatatypeProperty"}, {"@id": "ps:P4174", "@type": "owl:DatatypeProperty"}, {"@id": "pq:P4174", "@type": "owl:DatatypeProperty"}, {"@id": "pr:P4174", "@type": "owl:DatatypeProperty"}, {"@id": "psn:P4174", "@type": "owl:ObjectProperty"}, {"@id": "pqn:P4174", "@type": "owl:ObjectProperty"}, {"@id": "prn:P4174", "@type": "owl:ObjectProperty"}, {"@id": "wdtn:P4174", "@type": "owl:ObjectProperty"}, {"@id": "wdno:P4174", "@type": "owl:Class", "complementOf": "_:05d99daa0a62f5a308ecf65c69696222"}, {"@id": "_:05d99daa0a62f5a308ecf65c69696222", "@type": "owl:Restriction", "onProperty": "wdt:P4174", "someValuesFrom": "owl:<PERSON>"}, {"@id": "sdcv:e4113b45e251ad1424a7ea30212173a5", "@type": "wikibase:TimeValue", "timeValue": "2014-11-04T00:00:00Z", "timePrecision": 11, "timeTimezone": 0, "timeCalendarModel": "http://www.wikidata.org/entity/********"}], "@context": {"sdcdata": "https://commons.wikimedia.org/wiki/Special:EntityData/", "schema": "http://schema.org/", "about": {"@id": "schema:about", "@type": "@id"}, "sdc": "https://commons.wikimedia.org/entity/", "cc": "http://creativecommons.org/ns#", "license": {"@id": "cc:license", "@type": "@id"}, "softwareVersion": {"@id": "schema:softwareVersion"}, "version": {"@id": "schema:version"}, "xsd": "http://www.w3.org/2001/XMLSchema#", "dateModified": {"@id": "schema:dateModified", "@type": "xsd:dateTime"}, "wikibase": "http://wikiba.se/ontology#", "wdt": "http://www.wikidata.org/prop/direct/", "P571": {"@id": "wdt:P571", "@type": "xsd:dateTime"}, "P4082": {"@id": "wdt:P4082", "@type": "@id"}, "wd": "http://www.wikidata.org/entity/", "P6216": {"@id": "wdt:P6216", "@type": "@id"}, "P275": {"@id": "wdt:P275", "@type": "@id"}, "P7482": {"@id": "wdt:P7482", "@type": "@id"}, "P170": {"@id": "wdt:P170", "@type": "@id"}, "p": "http://www.wikidata.org/prop/", "sdcs": "https://commons.wikimedia.org/entity/statement/", "p:P571": {"@type": "@id"}, "rank": {"@id": "wikibase:rank", "@type": "@id"}, "ps": "http://www.wikidata.org/prop/statement/", "ps:P571": {"@type": "xsd:dateTime"}, "psv": "http://www.wikidata.org/prop/statement/value/", "sdcv": "https://commons.wikimedia.org/value/", "psv:P571": {"@type": "@id"}, "timeValue": {"@id": "wikibase:timeValue", "@type": "xsd:dateTime"}, "timePrecision": {"@id": "wikibase:timePrecision"}, "timeTimezone": {"@id": "wikibase:timeTimezone"}, "timeCalendarModel": {"@id": "wikibase:timeCalendarModel", "@type": "@id"}, "p:P4082": {"@type": "@id"}, "ps:P4082": {"@type": "@id"}, "p:P6216": {"@type": "@id"}, "ps:P6216": {"@type": "@id"}, "p:P275": {"@type": "@id"}, "ps:P275": {"@type": "@id"}, "p:P7482": {"@type": "@id"}, "ps:P7482": {"@type": "@id"}, "p:P170": {"@type": "@id"}, "ps:P170": {"@type": "@id"}, "pq": "http://www.wikidata.org/prop/qualifier/", "P3831": {"@id": "pq:P3831", "@type": "@id"}, "P2093": {"@id": "pq:P2093"}, "P4174": {"@id": "pq:P4174"}, "encodingFormat": {"@id": "schema:encodingFormat"}, "contentUrl": {"@id": "schema:contentUrl", "@type": "@id"}, "url": {"@id": "schema:url", "@type": "@id"}, "contentSize": {"@id": "schema:contentSize"}, "height": {"@id": "schema:height"}, "width": {"@id": "schema:width"}, "rdfs": "http://www.w3.org/2000/01/rdf-schema#", "label": {"@id": "rdfs:label"}, "skos": "http://www.w3.org/2004/02/skos/core#", "prefLabel": {"@id": "skos:prefLabel"}, "name": {"@id": "schema:name"}, "description": {"@id": "schema:description"}, "propertyType": {"@id": "wikibase:propertyType", "@type": "@id"}, "directClaim": {"@id": "wikibase:directClaim", "@type": "@id"}, "claim": {"@id": "wikibase:claim", "@type": "@id"}, "statementProperty": {"@id": "wikibase:statementProperty", "@type": "@id"}, "statementValue": {"@id": "wikibase:statementValue", "@type": "@id"}, "qualifier": {"@id": "wikibase:qualifier", "@type": "@id"}, "qualifierValue": {"@id": "wikibase:qualifierValue", "@type": "@id"}, "pqv": "http://www.wikidata.org/prop/qualifier/value/", "reference": {"@id": "wikibase:reference", "@type": "@id"}, "pr": "http://www.wikidata.org/prop/reference/", "referenceValue": {"@id": "wikibase:referenceValue", "@type": "@id"}, "prv": "http://www.wikidata.org/prop/reference/value/", "novalue": {"@id": "wikibase:novalue", "@type": "@id"}, "wdno": "http://www.wikidata.org/prop/novalue/", "owl": "http://www.w3.org/2002/07/owl#", "complementOf": {"@id": "owl:<PERSON><PERSON>f", "@type": "@id"}, "onProperty": {"@id": "owl:onProperty", "@type": "@id"}, "someValuesFrom": {"@id": "owl:<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "@type": "@id"}, "directClaimNormalized": {"@id": "wikibase:directClaimNormalized", "@type": "@id"}, "wdtn": "http://www.wikidata.org/prop/direct-normalized/", "statementValueNormalized": {"@id": "wikibase:statementValueNormalized", "@type": "@id"}, "psn": "http://www.wikidata.org/prop/statement/value-normalized/", "qualifierValueNormalized": {"@id": "wikibase:qualifierValueNormalized", "@type": "@id"}, "pqn": "http://www.wikidata.org/prop/qualifier/value-normalized/", "referenceValueNormalized": {"@id": "wikibase:referenceValueNormalized", "@type": "@id"}, "prn": "http://www.wikidata.org/prop/reference/value-normalized/"}}