{"@graph": [{"@id": "sdcdata:********", "@type": "schema:Dataset", "about": "sdc:********", "license": "http://creativecommons.org/publicdomain/zero/1.0/", "softwareVersion": "1.0.0", "version": 868383420, "dateModified": "2024-04-15T03:02:27Z"}, {"@id": "sdc:********", "@type": "wikibase:Mediainfo", "P1163": "image/jpeg", "P4092": "37f4550fefc54971f69030ad579fe2b2a9cea9c1", "P3575": "+72796", "P2048": "+601", "P2049": "+458", "p:P1163": "sdcs:********-0F0FD40E-4B84-4D0F-B197-D7C907DB91EC"}, {"@id": "sdcs:********-0F0FD40E-4B84-4D0F-B197-D7C907DB91EC", "@type": ["wikibase:Statement", "wikibase:BestRank"], "rank": "wikibase:NormalRank", "ps:P1163": "image/jpeg"}, {"@id": "sdc:********", "p:P4092": "sdcs:********-AD614631-5E16-4EEF-A194-F0B112185E01"}, {"@id": "sdcs:********-AD614631-5E16-4EEF-A194-F0B112185E01", "@type": ["wikibase:Statement", "wikibase:BestRank"], "rank": "wikibase:NormalRank", "ps:P4092": "37f4550fefc54971f69030ad579fe2b2a9cea9c1", "P459": "wd:*********"}, {"@id": "sdc:********", "p:P3575": "sdcs:********-E19B2383-D6E5-4311-AA70-9B2FA8DAC038"}, {"@id": "sdcs:********-E19B2383-D6E5-4311-AA70-9B2FA8DAC038", "@type": ["wikibase:Statement", "wikibase:BestRank"], "rank": "wikibase:NormalRank", "ps:P3575": "+72796", "psv:P3575": "sdcv:ff0178801c61e7bd0909c65ba7d9e798"}, {"@id": "sdc:********", "p:P2048": "sdcs:********-AC7F6EDC-5B83-4A0A-96C4-5DF9E85B6D6A"}, {"@id": "sdcs:********-AC7F6EDC-5B83-4A0A-96C4-5DF9E85B6D6A", "@type": ["wikibase:Statement", "wikibase:BestRank"], "rank": "wikibase:NormalRank", "ps:P2048": "+601", "psv:P2048": "sdcv:f72de323340e9673a6859b2c5e297726"}, {"@id": "sdc:********", "p:P2049": "sdcs:********-AD9ACB6C-FA40-40BF-BFE9-43E213F692D3"}, {"@id": "sdcs:********-AD9ACB6C-FA40-40BF-BFE9-43E213F692D3", "@type": ["wikibase:Statement", "wikibase:BestRank"], "rank": "wikibase:NormalRank", "ps:P2049": "+458", "psv:P2049": "sdcv:1038abf672c14330d0e89ddac6cdd71c"}, {"@id": "sdc:********", "@type": ["schema:MediaObject", "schema:ImageObject"], "encodingFormat": "image/jpeg", "contentUrl": "https://upload.wikimedia.org/wikipedia/commons/d/d0/Corliss_valvegear%2C_Gordon%27s_improved_%28New_Catechism_of_the_Steam_Engine%2C_1904%29.jpg", "url": "http://commons.wikimedia.org/wiki/Special:FilePath/Corliss%20valvegear%2C%20Gordon%27s%20improved%20%28New%20Catechism%20of%20the%20Steam%20Engine%2C%201904%29.jpg", "contentSize": 72796, "height": 601, "width": 458}, {"@id": "wd:P1163", "@type": "wikibase:Property", "label": {"@language": "en", "@value": "media type"}, "prefLabel": {"@language": "en", "@value": "media type"}, "name": {"@language": "en", "@value": "media type"}, "description": {"@language": "en", "@value": "IANA-registered identifier for a file type"}, "propertyType": "http://wikiba.se/ontology#String", "directClaim": "wdt:P1163", "claim": "p:P1163", "statementProperty": "ps:P1163", "statementValue": "psv:P1163", "qualifier": "pq:P1163", "qualifierValue": "pqv:P1163", "reference": "pr:P1163", "referenceValue": "prv:P1163", "novalue": "wdno:P1163"}, {"@id": "p:P1163", "@type": "owl:ObjectProperty"}, {"@id": "psv:P1163", "@type": "owl:ObjectProperty"}, {"@id": "pqv:P1163", "@type": "owl:ObjectProperty"}, {"@id": "prv:P1163", "@type": "owl:ObjectProperty"}, {"@id": "wdt:P1163", "@type": "owl:DatatypeProperty"}, {"@id": "ps:P1163", "@type": "owl:DatatypeProperty"}, {"@id": "pq:P1163", "@type": "owl:DatatypeProperty"}, {"@id": "pr:P1163", "@type": "owl:DatatypeProperty"}, {"@id": "wdno:P1163", "@type": "owl:Class", "complementOf": "_:704e62989d8eb9df732666f92fa84714"}, {"@id": "_:704e62989d8eb9df732666f92fa84714", "@type": "owl:Restriction", "onProperty": "wdt:P1163", "someValuesFrom": "owl:<PERSON>"}, {"@id": "wd:P4092", "@type": "wikibase:Property", "label": {"@language": "en", "@value": "checksum"}, "prefLabel": {"@language": "en", "@value": "checksum"}, "name": {"@language": "en", "@value": "checksum"}, "description": {"@language": "en", "@value": "small-sized datum derived from a block of digital data for the purpose of detecting errors. Use qualifier \"determination method\" (P459) to indicate how it's calculated, e.g. MD5."}, "propertyType": "http://wikiba.se/ontology#String", "directClaim": "wdt:P4092", "claim": "p:P4092", "statementProperty": "ps:P4092", "statementValue": "psv:P4092", "qualifier": "pq:P4092", "qualifierValue": "pqv:P4092", "reference": "pr:P4092", "referenceValue": "prv:P4092", "novalue": "wdno:P4092"}, {"@id": "p:P4092", "@type": "owl:ObjectProperty"}, {"@id": "psv:P4092", "@type": "owl:ObjectProperty"}, {"@id": "pqv:P4092", "@type": "owl:ObjectProperty"}, {"@id": "prv:P4092", "@type": "owl:ObjectProperty"}, {"@id": "wdt:P4092", "@type": "owl:DatatypeProperty"}, {"@id": "ps:P4092", "@type": "owl:DatatypeProperty"}, {"@id": "pq:P4092", "@type": "owl:DatatypeProperty"}, {"@id": "pr:P4092", "@type": "owl:DatatypeProperty"}, {"@id": "wdno:P4092", "@type": "owl:Class", "complementOf": "_:6d4307bb92af815977a58e72cb8245cd"}, {"@id": "_:6d4307bb92af815977a58e72cb8245cd", "@type": "owl:Restriction", "onProperty": "wdt:P4092", "someValuesFrom": "owl:<PERSON>"}, {"@id": "wd:P3575", "@type": "wikibase:Property", "label": {"@language": "en", "@value": "data size"}, "prefLabel": {"@language": "en", "@value": "data size"}, "name": {"@language": "en", "@value": "data size"}, "description": {"@language": "en", "@value": "size of a software, dataset, neural network, or individual file"}, "propertyType": "http://wikiba.se/ontology#Quantity", "directClaim": "wdt:P3575", "claim": "p:P3575", "statementProperty": "ps:P3575", "statementValue": "psv:P3575", "qualifier": "pq:P3575", "qualifierValue": "pqv:P3575", "reference": "pr:P3575", "referenceValue": "prv:P3575", "novalue": "wdno:P3575", "directClaimNormalized": "wdtn:P3575", "statementValueNormalized": "psn:P3575", "qualifierValueNormalized": "pqn:P3575", "referenceValueNormalized": "prn:P3575"}, {"@id": "p:P3575", "@type": "owl:ObjectProperty"}, {"@id": "psv:P3575", "@type": "owl:ObjectProperty"}, {"@id": "pqv:P3575", "@type": "owl:ObjectProperty"}, {"@id": "prv:P3575", "@type": "owl:ObjectProperty"}, {"@id": "wdt:P3575", "@type": "owl:DatatypeProperty"}, {"@id": "ps:P3575", "@type": "owl:DatatypeProperty"}, {"@id": "pq:P3575", "@type": "owl:DatatypeProperty"}, {"@id": "pr:P3575", "@type": "owl:DatatypeProperty"}, {"@id": "psn:P3575", "@type": "owl:ObjectProperty"}, {"@id": "pqn:P3575", "@type": "owl:ObjectProperty"}, {"@id": "prn:P3575", "@type": "owl:ObjectProperty"}, {"@id": "wdtn:P3575", "@type": "owl:DatatypeProperty"}, {"@id": "wdno:P3575", "@type": "owl:Class", "complementOf": "_:b624cfad58daea2904a2149304d28205"}, {"@id": "_:b624cfad58daea2904a2149304d28205", "@type": "owl:Restriction", "onProperty": "wdt:P3575", "someValuesFrom": "owl:<PERSON>"}, {"@id": "wd:P2048", "@type": "wikibase:Property", "label": {"@language": "en", "@value": "height"}, "prefLabel": {"@language": "en", "@value": "height"}, "name": {"@language": "en", "@value": "height"}, "description": {"@language": "en", "@value": "vertical length of an entity"}, "propertyType": "http://wikiba.se/ontology#Quantity", "directClaim": "wdt:P2048", "claim": "p:P2048", "statementProperty": "ps:P2048", "statementValue": "psv:P2048", "qualifier": "pq:P2048", "qualifierValue": "pqv:P2048", "reference": "pr:P2048", "referenceValue": "prv:P2048", "novalue": "wdno:P2048", "directClaimNormalized": "wdtn:P2048", "statementValueNormalized": "psn:P2048", "qualifierValueNormalized": "pqn:P2048", "referenceValueNormalized": "prn:P2048"}, {"@id": "p:P2048", "@type": "owl:ObjectProperty"}, {"@id": "psv:P2048", "@type": "owl:ObjectProperty"}, {"@id": "pqv:P2048", "@type": "owl:ObjectProperty"}, {"@id": "prv:P2048", "@type": "owl:ObjectProperty"}, {"@id": "wdt:P2048", "@type": "owl:DatatypeProperty"}, {"@id": "ps:P2048", "@type": "owl:DatatypeProperty"}, {"@id": "pq:P2048", "@type": "owl:DatatypeProperty"}, {"@id": "pr:P2048", "@type": "owl:DatatypeProperty"}, {"@id": "psn:P2048", "@type": "owl:ObjectProperty"}, {"@id": "pqn:P2048", "@type": "owl:ObjectProperty"}, {"@id": "prn:P2048", "@type": "owl:ObjectProperty"}, {"@id": "wdtn:P2048", "@type": "owl:DatatypeProperty"}, {"@id": "wdno:P2048", "@type": "owl:Class", "complementOf": "_:8b9e3c496971ca7a35e1bb77aa490b02"}, {"@id": "_:8b9e3c496971ca7a35e1bb77aa490b02", "@type": "owl:Restriction", "onProperty": "wdt:P2048", "someValuesFrom": "owl:<PERSON>"}, {"@id": "wd:P2049", "@type": "wikibase:Property", "label": {"@language": "en", "@value": "width"}, "prefLabel": {"@language": "en", "@value": "width"}, "name": {"@language": "en", "@value": "width"}, "description": {"@language": "en", "@value": "width of an object"}, "propertyType": "http://wikiba.se/ontology#Quantity", "directClaim": "wdt:P2049", "claim": "p:P2049", "statementProperty": "ps:P2049", "statementValue": "psv:P2049", "qualifier": "pq:P2049", "qualifierValue": "pqv:P2049", "reference": "pr:P2049", "referenceValue": "prv:P2049", "novalue": "wdno:P2049", "directClaimNormalized": "wdtn:P2049", "statementValueNormalized": "psn:P2049", "qualifierValueNormalized": "pqn:P2049", "referenceValueNormalized": "prn:P2049"}, {"@id": "p:P2049", "@type": "owl:ObjectProperty"}, {"@id": "psv:P2049", "@type": "owl:ObjectProperty"}, {"@id": "pqv:P2049", "@type": "owl:ObjectProperty"}, {"@id": "prv:P2049", "@type": "owl:ObjectProperty"}, {"@id": "wdt:P2049", "@type": "owl:DatatypeProperty"}, {"@id": "ps:P2049", "@type": "owl:DatatypeProperty"}, {"@id": "pq:P2049", "@type": "owl:DatatypeProperty"}, {"@id": "pr:P2049", "@type": "owl:DatatypeProperty"}, {"@id": "psn:P2049", "@type": "owl:ObjectProperty"}, {"@id": "pqn:P2049", "@type": "owl:ObjectProperty"}, {"@id": "prn:P2049", "@type": "owl:ObjectProperty"}, {"@id": "wdtn:P2049", "@type": "owl:DatatypeProperty"}, {"@id": "wdno:P2049", "@type": "owl:Class", "complementOf": "_:a55ba6e912ddf8da0985e42b4cc289b3"}, {"@id": "_:a55ba6e912ddf8da0985e42b4cc289b3", "@type": "owl:Restriction", "onProperty": "wdt:P2049", "someValuesFrom": "owl:<PERSON>"}, {"@id": "wd:*********", "@type": "wikibase:Item", "label": [{"@language": "en", "@value": "SHA-1"}, {"@language": "mul", "@value": "SHA-1"}], "prefLabel": [{"@language": "en", "@value": "SHA-1"}, {"@language": "mul", "@value": "SHA-1"}], "name": [{"@language": "en", "@value": "SHA-1"}, {"@language": "mul", "@value": "SHA-1"}], "description": {"@language": "en", "@value": "individual cryptographic hash function with output size of 160 bits"}}, {"@id": "wd:P459", "@type": "wikibase:Property", "label": {"@language": "en", "@value": "determination method or standard"}, "prefLabel": {"@language": "en", "@value": "determination method or standard"}, "name": {"@language": "en", "@value": "determination method or standard"}, "description": {"@language": "en", "@value": "how a value is determined, or the standard by which it is declared"}, "propertyType": "http://wikiba.se/ontology#WikibaseItem", "directClaim": "wdt:P459", "claim": "p:P459", "statementProperty": "ps:P459", "statementValue": "psv:P459", "qualifier": "pq:P459", "qualifierValue": "pqv:P459", "reference": "pr:P459", "referenceValue": "prv:P459", "novalue": "wdno:P459"}, {"@id": "p:P459", "@type": "owl:ObjectProperty"}, {"@id": "psv:P459", "@type": "owl:ObjectProperty"}, {"@id": "pqv:P459", "@type": "owl:ObjectProperty"}, {"@id": "prv:P459", "@type": "owl:ObjectProperty"}, {"@id": "wdt:P459", "@type": "owl:ObjectProperty"}, {"@id": "ps:P459", "@type": "owl:ObjectProperty"}, {"@id": "pq:P459", "@type": "owl:ObjectProperty"}, {"@id": "pr:P459", "@type": "owl:ObjectProperty"}, {"@id": "wdno:P459", "@type": "owl:Class", "complementOf": "_:88b70b8bfdb5c147baf5a03f4254bd4b"}, {"@id": "_:88b70b8bfdb5c147baf5a03f4254bd4b", "@type": "owl:Restriction", "onProperty": "wdt:P459", "someValuesFrom": "owl:<PERSON>"}, {"@id": "sdcv:ff0178801c61e7bd0909c65ba7d9e798", "@type": "wikibase:QuantityValue", "quantityAmount": "+72796", "quantityUnit": "http://www.wikidata.org/entity/Q8799"}, {"@id": "sdcv:f72de323340e9673a6859b2c5e297726", "@type": "wikibase:QuantityValue", "quantityAmount": "+601", "quantityUnit": "http://www.wikidata.org/entity/Q355198"}, {"@id": "sdcv:1038abf672c14330d0e89ddac6cdd71c", "@type": "wikibase:QuantityValue", "quantityAmount": "+458", "quantityUnit": "http://www.wikidata.org/entity/Q355198"}], "@context": {"sdcdata": "https://commons.wikimedia.org/wiki/Special:EntityData/", "schema": "http://schema.org/", "about": {"@id": "schema:about", "@type": "@id"}, "sdc": "https://commons.wikimedia.org/entity/", "cc": "http://creativecommons.org/ns#", "license": {"@id": "cc:license", "@type": "@id"}, "softwareVersion": {"@id": "schema:softwareVersion"}, "version": {"@id": "schema:version"}, "xsd": "http://www.w3.org/2001/XMLSchema#", "dateModified": {"@id": "schema:dateModified", "@type": "xsd:dateTime"}, "wikibase": "http://wikiba.se/ontology#", "wdt": "http://www.wikidata.org/prop/direct/", "P1163": {"@id": "wdt:P1163"}, "P4092": {"@id": "wdt:P4092"}, "P3575": {"@id": "wdt:P3575", "@type": "xsd:decimal"}, "P2048": {"@id": "wdt:P2048", "@type": "xsd:decimal"}, "P2049": {"@id": "wdt:P2049", "@type": "xsd:decimal"}, "p": "http://www.wikidata.org/prop/", "sdcs": "https://commons.wikimedia.org/entity/statement/", "p:P1163": {"@type": "@id"}, "rank": {"@id": "wikibase:rank", "@type": "@id"}, "ps": "http://www.wikidata.org/prop/statement/", "p:P4092": {"@type": "@id"}, "pq": "http://www.wikidata.org/prop/qualifier/", "P459": {"@id": "pq:P459", "@type": "@id"}, "wd": "http://www.wikidata.org/entity/", "p:P3575": {"@type": "@id"}, "ps:P3575": {"@type": "xsd:decimal"}, "psv": "http://www.wikidata.org/prop/statement/value/", "sdcv": "https://commons.wikimedia.org/value/", "psv:P3575": {"@type": "@id"}, "quantityAmount": {"@id": "wikibase:quantityAmount", "@type": "xsd:decimal"}, "quantityUnit": {"@id": "wikibase:quantityUnit", "@type": "@id"}, "p:P2048": {"@type": "@id"}, "ps:P2048": {"@type": "xsd:decimal"}, "psv:P2048": {"@type": "@id"}, "p:P2049": {"@type": "@id"}, "ps:P2049": {"@type": "xsd:decimal"}, "psv:P2049": {"@type": "@id"}, "encodingFormat": {"@id": "schema:encodingFormat"}, "contentUrl": {"@id": "schema:contentUrl", "@type": "@id"}, "url": {"@id": "schema:url", "@type": "@id"}, "contentSize": {"@id": "schema:contentSize"}, "height": {"@id": "schema:height"}, "width": {"@id": "schema:width"}, "rdfs": "http://www.w3.org/2000/01/rdf-schema#", "label": {"@id": "rdfs:label"}, "skos": "http://www.w3.org/2004/02/skos/core#", "prefLabel": {"@id": "skos:prefLabel"}, "name": {"@id": "schema:name"}, "description": {"@id": "schema:description"}, "propertyType": {"@id": "wikibase:propertyType", "@type": "@id"}, "directClaim": {"@id": "wikibase:directClaim", "@type": "@id"}, "claim": {"@id": "wikibase:claim", "@type": "@id"}, "statementProperty": {"@id": "wikibase:statementProperty", "@type": "@id"}, "statementValue": {"@id": "wikibase:statementValue", "@type": "@id"}, "qualifier": {"@id": "wikibase:qualifier", "@type": "@id"}, "qualifierValue": {"@id": "wikibase:qualifierValue", "@type": "@id"}, "pqv": "http://www.wikidata.org/prop/qualifier/value/", "reference": {"@id": "wikibase:reference", "@type": "@id"}, "pr": "http://www.wikidata.org/prop/reference/", "referenceValue": {"@id": "wikibase:referenceValue", "@type": "@id"}, "prv": "http://www.wikidata.org/prop/reference/value/", "novalue": {"@id": "wikibase:novalue", "@type": "@id"}, "wdno": "http://www.wikidata.org/prop/novalue/", "owl": "http://www.w3.org/2002/07/owl#", "complementOf": {"@id": "owl:<PERSON><PERSON>f", "@type": "@id"}, "onProperty": {"@id": "owl:onProperty", "@type": "@id"}, "someValuesFrom": {"@id": "owl:<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "@type": "@id"}, "directClaimNormalized": {"@id": "wikibase:directClaimNormalized", "@type": "@id"}, "wdtn": "http://www.wikidata.org/prop/direct-normalized/", "statementValueNormalized": {"@id": "wikibase:statementValueNormalized", "@type": "@id"}, "psn": "http://www.wikidata.org/prop/statement/value-normalized/", "qualifierValueNormalized": {"@id": "wikibase:qualifierValueNormalized", "@type": "@id"}, "pqn": "http://www.wikidata.org/prop/qualifier/value-normalized/", "referenceValueNormalized": {"@id": "wikibase:referenceValueNormalized", "@type": "@id"}, "prn": "http://www.wikidata.org/prop/reference/value-normalized/"}}