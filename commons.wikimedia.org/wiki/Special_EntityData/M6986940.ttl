@prefix rdf: <http://www.w3.org/1999/02/22-rdf-syntax-ns#> .
@prefix xsd: <http://www.w3.org/2001/XMLSchema#> .
@prefix rdfs: <http://www.w3.org/2000/01/rdf-schema#> .
@prefix owl: <http://www.w3.org/2002/07/owl#> .
@prefix wikibase: <http://wikiba.se/ontology#> .
@prefix skos: <http://www.w3.org/2004/02/skos/core#> .
@prefix schema: <http://schema.org/> .
@prefix cc: <http://creativecommons.org/ns#> .
@prefix geo: <http://www.opengis.net/ont/geosparql#> .
@prefix prov: <http://www.w3.org/ns/prov#> .
@prefix wd: <http://www.wikidata.org/entity/> .
@prefix data: <https://www.wikidata.org/wiki/Special:EntityData/> .
@prefix wds: <http://www.wikidata.org/entity/statement/> .
@prefix wdref: <http://www.wikidata.org/reference/> .
@prefix wdv: <http://www.wikidata.org/value/> .
@prefix wdt: <http://www.wikidata.org/prop/direct/> .
@prefix wdtn: <http://www.wikidata.org/prop/direct-normalized/> .
@prefix p: <http://www.wikidata.org/prop/> .
@prefix ps: <http://www.wikidata.org/prop/statement/> .
@prefix psv: <http://www.wikidata.org/prop/statement/value/> .
@prefix psn: <http://www.wikidata.org/prop/statement/value-normalized/> .
@prefix pq: <http://www.wikidata.org/prop/qualifier/> .
@prefix pqv: <http://www.wikidata.org/prop/qualifier/value/> .
@prefix pqn: <http://www.wikidata.org/prop/qualifier/value-normalized/> .
@prefix pr: <http://www.wikidata.org/prop/reference/> .
@prefix prv: <http://www.wikidata.org/prop/reference/value/> .
@prefix prn: <http://www.wikidata.org/prop/reference/value-normalized/> .
@prefix wdno: <http://www.wikidata.org/prop/novalue/> .
@prefix sdc: <https://commons.wikimedia.org/entity/> .
@prefix sdcdata: <https://commons.wikimedia.org/wiki/Special:EntityData/> .
@prefix sdcs: <https://commons.wikimedia.org/entity/statement/> .
@prefix sdcref: <https://commons.wikimedia.org/reference/> .
@prefix sdcv: <https://commons.wikimedia.org/value/> .
@prefix sdct: <https://commons.wikimedia.org/prop/direct/> .
@prefix sdctn: <https://commons.wikimedia.org/prop/direct-normalized/> .
@prefix sdcp: <https://commons.wikimedia.org/prop/> .
@prefix sdcps: <https://commons.wikimedia.org/prop/statement/> .
@prefix sdcpsv: <https://commons.wikimedia.org/prop/statement/value/> .
@prefix sdcpsn: <https://commons.wikimedia.org/prop/statement/value-normalized/> .
@prefix sdcpq: <https://commons.wikimedia.org/prop/qualifier/> .
@prefix sdcpqv: <https://commons.wikimedia.org/prop/qualifier/value/> .
@prefix sdcpqn: <https://commons.wikimedia.org/prop/qualifier/value-normalized/> .
@prefix sdcpr: <https://commons.wikimedia.org/prop/reference/> .
@prefix sdcprv: <https://commons.wikimedia.org/prop/reference/value/> .
@prefix sdcprn: <https://commons.wikimedia.org/prop/reference/value-normalized/> .
@prefix sdcno: <https://commons.wikimedia.org/prop/novalue/> .

sdcdata:******** a schema:Dataset ;
	schema:about sdc:******** ;
	cc:license <http://creativecommons.org/publicdomain/zero/1.0/> ;
	schema:softwareVersion "1.0.0" ;
	schema:version "868383420"^^xsd:integer ;
	schema:dateModified "2024-04-15T03:02:27Z"^^xsd:dateTime .

sdc:******** a wikibase:Mediainfo ;
	wdt:P1163 "image/jpeg" ;
	wdt:P4092 "37f4550fefc54971f69030ad579fe2b2a9cea9c1" ;
	wdt:P3575 "+72796"^^xsd:decimal ;
	wdt:P2048 "+601"^^xsd:decimal ;
	wdt:P2049 "+458"^^xsd:decimal ;
	p:P1163 sdcs:********-0F0FD40E-4B84-4D0F-B197-D7C907DB91EC .

sdcs:********-0F0FD40E-4B84-4D0F-B197-D7C907DB91EC a wikibase:Statement,
		wikibase:BestRank ;
	wikibase:rank wikibase:NormalRank ;
	ps:P1163 "image/jpeg" .

sdc:******** p:P4092 sdcs:********-AD614631-5E16-4EEF-A194-F0B112185E01 .

sdcs:********-AD614631-5E16-4EEF-A194-F0B112185E01 a wikibase:Statement,
		wikibase:BestRank ;
	wikibase:rank wikibase:NormalRank ;
	ps:P4092 "37f4550fefc54971f69030ad579fe2b2a9cea9c1" ;
	pq:P459 wd:********* .

sdc:******** p:P3575 sdcs:********-E19B2383-D6E5-4311-AA70-9B2FA8DAC038 .

sdcs:********-E19B2383-D6E5-4311-AA70-9B2FA8DAC038 a wikibase:Statement,
		wikibase:BestRank ;
	wikibase:rank wikibase:NormalRank ;
	ps:P3575 "+72796"^^xsd:decimal ;
	psv:P3575 sdcv:ff0178801c61e7bd0909c65ba7d9e798 .

sdc:******** p:P2048 sdcs:********-AC7F6EDC-5B83-4A0A-96C4-5DF9E85B6D6A .

sdcs:********-AC7F6EDC-5B83-4A0A-96C4-5DF9E85B6D6A a wikibase:Statement,
		wikibase:BestRank ;
	wikibase:rank wikibase:NormalRank ;
	ps:P2048 "+601"^^xsd:decimal ;
	psv:P2048 sdcv:f72de323340e9673a6859b2c5e297726 .

sdc:******** p:P2049 sdcs:********-AD9ACB6C-FA40-40BF-BFE9-43E213F692D3 .

sdcs:********-AD9ACB6C-FA40-40BF-BFE9-43E213F692D3 a wikibase:Statement,
		wikibase:BestRank ;
	wikibase:rank wikibase:NormalRank ;
	ps:P2049 "+458"^^xsd:decimal ;
	psv:P2049 sdcv:1038abf672c14330d0e89ddac6cdd71c .

sdc:******** a schema:MediaObject,
		schema:ImageObject ;
	schema:encodingFormat "image/jpeg" ;
	schema:contentUrl <https://upload.wikimedia.org/wikipedia/commons/d/d0/Corliss_valvegear%2C_Gordon%27s_improved_%28New_Catechism_of_the_Steam_Engine%2C_1904%29.jpg> ;
	schema:url <http://commons.wikimedia.org/wiki/Special:FilePath/Corliss%20valvegear%2C%20Gordon%27s%20improved%20%28New%20Catechism%20of%20the%20Steam%20Engine%2C%201904%29.jpg> ;
	schema:contentSize "72796"^^xsd:integer ;
	schema:height "601"^^xsd:integer ;
	schema:width "458"^^xsd:integer .

wd:P1163 a wikibase:Property ;
	rdfs:label "media type"@en ;
	skos:prefLabel "media type"@en ;
	schema:name "media type"@en ;
	schema:description "IANA-registered identifier for a file type"@en ;
	wikibase:propertyType <http://wikiba.se/ontology#String> ;
	wikibase:directClaim wdt:P1163 ;
	wikibase:claim p:P1163 ;
	wikibase:statementProperty ps:P1163 ;
	wikibase:statementValue psv:P1163 ;
	wikibase:qualifier pq:P1163 ;
	wikibase:qualifierValue pqv:P1163 ;
	wikibase:reference pr:P1163 ;
	wikibase:referenceValue prv:P1163 ;
	wikibase:novalue wdno:P1163 .

p:P1163 a owl:ObjectProperty .

psv:P1163 a owl:ObjectProperty .

pqv:P1163 a owl:ObjectProperty .

prv:P1163 a owl:ObjectProperty .

wdt:P1163 a owl:DatatypeProperty .

ps:P1163 a owl:DatatypeProperty .

pq:P1163 a owl:DatatypeProperty .

pr:P1163 a owl:DatatypeProperty .

wdno:P1163 a owl:Class ;
	owl:complementOf _:704e62989d8eb9df732666f92fa84714 .

_:704e62989d8eb9df732666f92fa84714 a owl:Restriction ;
	owl:onProperty wdt:P1163 ;
	owl:someValuesFrom owl:Thing .

wd:P4092 a wikibase:Property ;
	rdfs:label "checksum"@en ;
	skos:prefLabel "checksum"@en ;
	schema:name "checksum"@en ;
	schema:description "small-sized datum derived from a block of digital data for the purpose of detecting errors. Use qualifier \"determination method\" (P459) to indicate how it's calculated, e.g. MD5."@en ;
	wikibase:propertyType <http://wikiba.se/ontology#String> ;
	wikibase:directClaim wdt:P4092 ;
	wikibase:claim p:P4092 ;
	wikibase:statementProperty ps:P4092 ;
	wikibase:statementValue psv:P4092 ;
	wikibase:qualifier pq:P4092 ;
	wikibase:qualifierValue pqv:P4092 ;
	wikibase:reference pr:P4092 ;
	wikibase:referenceValue prv:P4092 ;
	wikibase:novalue wdno:P4092 .

p:P4092 a owl:ObjectProperty .

psv:P4092 a owl:ObjectProperty .

pqv:P4092 a owl:ObjectProperty .

prv:P4092 a owl:ObjectProperty .

wdt:P4092 a owl:DatatypeProperty .

ps:P4092 a owl:DatatypeProperty .

pq:P4092 a owl:DatatypeProperty .

pr:P4092 a owl:DatatypeProperty .

wdno:P4092 a owl:Class ;
	owl:complementOf _:6d4307bb92af815977a58e72cb8245cd .

_:6d4307bb92af815977a58e72cb8245cd a owl:Restriction ;
	owl:onProperty wdt:P4092 ;
	owl:someValuesFrom owl:Thing .

wd:P3575 a wikibase:Property ;
	rdfs:label "data size"@en ;
	skos:prefLabel "data size"@en ;
	schema:name "data size"@en ;
	schema:description "size of a software, dataset, neural network, or individual file"@en ;
	wikibase:propertyType <http://wikiba.se/ontology#Quantity> ;
	wikibase:directClaim wdt:P3575 ;
	wikibase:claim p:P3575 ;
	wikibase:statementProperty ps:P3575 ;
	wikibase:statementValue psv:P3575 ;
	wikibase:qualifier pq:P3575 ;
	wikibase:qualifierValue pqv:P3575 ;
	wikibase:reference pr:P3575 ;
	wikibase:referenceValue prv:P3575 ;
	wikibase:novalue wdno:P3575 ;
	wikibase:directClaimNormalized wdtn:P3575 ;
	wikibase:statementValueNormalized psn:P3575 ;
	wikibase:qualifierValueNormalized pqn:P3575 ;
	wikibase:referenceValueNormalized prn:P3575 .

p:P3575 a owl:ObjectProperty .

psv:P3575 a owl:ObjectProperty .

pqv:P3575 a owl:ObjectProperty .

prv:P3575 a owl:ObjectProperty .

wdt:P3575 a owl:DatatypeProperty .

ps:P3575 a owl:DatatypeProperty .

pq:P3575 a owl:DatatypeProperty .

pr:P3575 a owl:DatatypeProperty .

psn:P3575 a owl:ObjectProperty .

pqn:P3575 a owl:ObjectProperty .

prn:P3575 a owl:ObjectProperty .

wdtn:P3575 a owl:DatatypeProperty .

wdno:P3575 a owl:Class ;
	owl:complementOf _:b624cfad58daea2904a2149304d28205 .

_:b624cfad58daea2904a2149304d28205 a owl:Restriction ;
	owl:onProperty wdt:P3575 ;
	owl:someValuesFrom owl:Thing .

wd:P2048 a wikibase:Property ;
	rdfs:label "height"@en ;
	skos:prefLabel "height"@en ;
	schema:name "height"@en ;
	schema:description "vertical length of an entity"@en ;
	wikibase:propertyType <http://wikiba.se/ontology#Quantity> ;
	wikibase:directClaim wdt:P2048 ;
	wikibase:claim p:P2048 ;
	wikibase:statementProperty ps:P2048 ;
	wikibase:statementValue psv:P2048 ;
	wikibase:qualifier pq:P2048 ;
	wikibase:qualifierValue pqv:P2048 ;
	wikibase:reference pr:P2048 ;
	wikibase:referenceValue prv:P2048 ;
	wikibase:novalue wdno:P2048 ;
	wikibase:directClaimNormalized wdtn:P2048 ;
	wikibase:statementValueNormalized psn:P2048 ;
	wikibase:qualifierValueNormalized pqn:P2048 ;
	wikibase:referenceValueNormalized prn:P2048 .

p:P2048 a owl:ObjectProperty .

psv:P2048 a owl:ObjectProperty .

pqv:P2048 a owl:ObjectProperty .

prv:P2048 a owl:ObjectProperty .

wdt:P2048 a owl:DatatypeProperty .

ps:P2048 a owl:DatatypeProperty .

pq:P2048 a owl:DatatypeProperty .

pr:P2048 a owl:DatatypeProperty .

psn:P2048 a owl:ObjectProperty .

pqn:P2048 a owl:ObjectProperty .

prn:P2048 a owl:ObjectProperty .

wdtn:P2048 a owl:DatatypeProperty .

wdno:P2048 a owl:Class ;
	owl:complementOf _:8b9e3c496971ca7a35e1bb77aa490b02 .

_:8b9e3c496971ca7a35e1bb77aa490b02 a owl:Restriction ;
	owl:onProperty wdt:P2048 ;
	owl:someValuesFrom owl:Thing .

wd:P2049 a wikibase:Property ;
	rdfs:label "width"@en ;
	skos:prefLabel "width"@en ;
	schema:name "width"@en ;
	schema:description "width of an object"@en ;
	wikibase:propertyType <http://wikiba.se/ontology#Quantity> ;
	wikibase:directClaim wdt:P2049 ;
	wikibase:claim p:P2049 ;
	wikibase:statementProperty ps:P2049 ;
	wikibase:statementValue psv:P2049 ;
	wikibase:qualifier pq:P2049 ;
	wikibase:qualifierValue pqv:P2049 ;
	wikibase:reference pr:P2049 ;
	wikibase:referenceValue prv:P2049 ;
	wikibase:novalue wdno:P2049 ;
	wikibase:directClaimNormalized wdtn:P2049 ;
	wikibase:statementValueNormalized psn:P2049 ;
	wikibase:qualifierValueNormalized pqn:P2049 ;
	wikibase:referenceValueNormalized prn:P2049 .

p:P2049 a owl:ObjectProperty .

psv:P2049 a owl:ObjectProperty .

pqv:P2049 a owl:ObjectProperty .

prv:P2049 a owl:ObjectProperty .

wdt:P2049 a owl:DatatypeProperty .

ps:P2049 a owl:DatatypeProperty .

pq:P2049 a owl:DatatypeProperty .

pr:P2049 a owl:DatatypeProperty .

psn:P2049 a owl:ObjectProperty .

pqn:P2049 a owl:ObjectProperty .

prn:P2049 a owl:ObjectProperty .

wdtn:P2049 a owl:DatatypeProperty .

wdno:P2049 a owl:Class ;
	owl:complementOf _:a55ba6e912ddf8da0985e42b4cc289b3 .

_:a55ba6e912ddf8da0985e42b4cc289b3 a owl:Restriction ;
	owl:onProperty wdt:P2049 ;
	owl:someValuesFrom owl:Thing .

wd:********* a wikibase:Item ;
	rdfs:label "SHA-1"@en ;
	skos:prefLabel "SHA-1"@en ;
	schema:name "SHA-1"@en ;
	rdfs:label "SHA-1"@mul ;
	skos:prefLabel "SHA-1"@mul ;
	schema:name "SHA-1"@mul ;
	schema:description "individual cryptographic hash function with output size of 160 bits"@en .

wd:P459 a wikibase:Property ;
	rdfs:label "determination method or standard"@en ;
	skos:prefLabel "determination method or standard"@en ;
	schema:name "determination method or standard"@en ;
	schema:description "how a value is determined, or the standard by which it is declared"@en ;
	wikibase:propertyType <http://wikiba.se/ontology#WikibaseItem> ;
	wikibase:directClaim wdt:P459 ;
	wikibase:claim p:P459 ;
	wikibase:statementProperty ps:P459 ;
	wikibase:statementValue psv:P459 ;
	wikibase:qualifier pq:P459 ;
	wikibase:qualifierValue pqv:P459 ;
	wikibase:reference pr:P459 ;
	wikibase:referenceValue prv:P459 ;
	wikibase:novalue wdno:P459 .

p:P459 a owl:ObjectProperty .

psv:P459 a owl:ObjectProperty .

pqv:P459 a owl:ObjectProperty .

prv:P459 a owl:ObjectProperty .

wdt:P459 a owl:ObjectProperty .

ps:P459 a owl:ObjectProperty .

pq:P459 a owl:ObjectProperty .

pr:P459 a owl:ObjectProperty .

wdno:P459 a owl:Class ;
	owl:complementOf _:88b70b8bfdb5c147baf5a03f4254bd4b .

_:88b70b8bfdb5c147baf5a03f4254bd4b a owl:Restriction ;
	owl:onProperty wdt:P459 ;
	owl:someValuesFrom owl:Thing .

sdcv:ff0178801c61e7bd0909c65ba7d9e798 a wikibase:QuantityValue ;
	wikibase:quantityAmount "+72796"^^xsd:decimal ;
	wikibase:quantityUnit <http://www.wikidata.org/entity/Q8799> .

sdcv:f72de323340e9673a6859b2c5e297726 a wikibase:QuantityValue ;
	wikibase:quantityAmount "+601"^^xsd:decimal ;
	wikibase:quantityUnit <http://www.wikidata.org/entity/Q355198> .

sdcv:1038abf672c14330d0e89ddac6cdd71c a wikibase:QuantityValue ;
	wikibase:quantityAmount "+458"^^xsd:decimal ;
	wikibase:quantityUnit <http://www.wikidata.org/entity/Q355198> .
