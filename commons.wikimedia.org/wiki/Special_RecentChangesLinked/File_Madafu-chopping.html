<!DOCTYPE html>
<html class="client-nojs vector-feature-language-in-header-disabled vector-feature-language-in-main-page-header-disabled vector-feature-page-tools-pinned-disabled vector-feature-toc-pinned-clientpref-1 vector-feature-main-menu-pinned-disabled vector-feature-limited-width-clientpref-1 vector-feature-limited-width-content-disabled vector-feature-custom-font-size-clientpref--excluded vector-feature-appearance-pinned-clientpref-1 vector-feature-night-mode-enabled skin-theme-clientpref-day vector-sticky-header-enabled vector-toc-not-available" lang="en" dir="ltr">
<head>
<meta charset="UTF-8">
<title>Related changes - Wikimedia Commons</title>
<script>(function(){var className="client-js vector-feature-language-in-header-disabled vector-feature-language-in-main-page-header-disabled vector-feature-page-tools-pinned-disabled vector-feature-toc-pinned-clientpref-1 vector-feature-main-menu-pinned-disabled vector-feature-limited-width-clientpref-1 vector-feature-limited-width-content-disabled vector-feature-custom-font-size-clientpref--excluded vector-feature-appearance-pinned-clientpref-1 vector-feature-night-mode-enabled skin-theme-clientpref-day vector-sticky-header-enabled vector-toc-not-available";var cookie=document.cookie.match(/(?:^|; )commonswikimwclientpreferences=([^;]+)/);if(cookie){cookie[1].split('%2C').forEach(function(pref){className=className.replace(new RegExp('(^| )'+pref.replace(/-clientpref-\w+$|[^\w-]+/g,'')+'-clientpref-\\w+( |$)'),'$1'+pref+'$2');});}document.documentElement.className=className;}());RLCONF={"wgBreakFrames":true,"wgSeparatorTransformTable":["",""],"wgDigitTransformTable":["",""],"wgDefaultDateFormat":"dmy","wgMonthNames":["","January","February","March","April","May","June","July","August","September","October","November","December"],"wgRequestId":"69c4421f-7be9-4477-bd81-277e677de2d1","wgCanonicalNamespace":"Special","wgCanonicalSpecialPageName":"Recentchangeslinked","wgNamespaceNumber":-1,"wgPageName":"Special:RecentChangesLinked/File:Madafu-chopping.jpg","wgTitle":"RecentChangesLinked/File:Madafu-chopping.jpg","wgCurRevisionId":0,"wgRevisionId":0,"wgArticleId":0,"wgIsArticle":false,"wgIsRedirect":false,"wgAction":"view","wgUserName":null,"wgUserGroups":["*"],"wgCategories":[],"wgPageViewLanguage":"en","wgPageContentLanguage":"en","wgPageContentModel":"wikitext","wgRelevantPageName":"File:Madafu-chopping.jpg","wgRelevantArticleId":36601495,"wgIsProbablyEditable":false,"wgRelevantPageIsProbablyEditable":true,"wgNoticeProject":"commons","wgCiteReferencePreviewsActive":true,"wgMediaViewerOnClick":true,"wgMediaViewerEnabledByDefault":false,"wgVisualEditor":{"pageLanguageCode":"en","pageLanguageDir":"ltr","pageVariantFallbacks":"en"},"wgMFDisplayWikibaseDescriptions":{"search":true,"watchlist":true,"tagline":true,"nearby":true},"wgWMESchemaEditAttemptStepOversample":false,"wgWMEPageLength":0,"wgMetricsPlatformUserExperiments":{"active_experiments":[],"overrides":[],"enrolled":[],"assigned":[],"subject_ids":[],"sampling_units":[]},"wgStructuredChangeFilters":[{"name":"userExpLevel","type":"string_options","fullCoverage":true,"filters":[{"name":"unregistered","label":"rcfilters-filter-user-experience-level-unregistered-label","description":"rcfilters-filter-user-experience-level-unregistered-description","cssClass":"mw-changeslist-user-unregistered","priority":-2,"subset":[],"conflicts":[],"defaultHighlightColor":null},{"name":"registered","label":"rcfilters-filter-user-experience-level-registered-label","description":"rcfilters-filter-user-experience-level-registered-description","cssClass":"mw-changeslist-user-registered","priority":-3,"subset":[{"group":"userExpLevel","filter":"newcomer"},{"group":"userExpLevel","filter":"learner"},{"group":"userExpLevel","filter":"experienced"}],"conflicts":[],"defaultHighlightColor":null},{"name":"newcomer","label":"rcfilters-filter-user-experience-level-newcomer-label","description":"rcfilters-filter-user-experience-level-newcomer-description","cssClass":"mw-changeslist-user-newcomer","priority":-4,"subset":[],"conflicts":[],"defaultHighlightColor":null},{"name":"learner","label":"rcfilters-filter-user-experience-level-learner-label","description":"rcfilters-filter-user-experience-level-learner-description","cssClass":"mw-changeslist-user-learner","priority":-5,"subset":[],"conflicts":[],"defaultHighlightColor":null},{"name":"experienced","label":"rcfilters-filter-user-experience-level-experienced-label","description":"rcfilters-filter-user-experience-level-experienced-description","cssClass":"mw-changeslist-user-experienced","priority":-6,"subset":[],"conflicts":[],"defaultHighlightColor":null}],"priority":-2,"conflicts":[],"title":"rcfilters-filtergroup-user-experience-level","separator":";","default":""},{"name":"authorship","type":"send_unselected_if_any","fullCoverage":true,"filters":[{"name":"hidemyself","label":"rcfilters-filter-editsbyself-label","description":"rcfilters-filter-editsbyself-description","cssClass":"mw-changeslist-self","priority":-2,"subset":[],"conflicts":[],"defaultHighlightColor":null,"default":false},{"name":"hidebyothers","label":"rcfilters-filter-editsbyother-label","description":"rcfilters-filter-editsbyother-description","cssClass":"mw-changeslist-others","priority":-3,"subset":[],"conflicts":[],"defaultHighlightColor":null,"default":false}],"priority":-3,"conflicts":[],"title":"rcfilters-filtergroup-authorship"},{"name":"automated","type":"send_unselected_if_any","fullCoverage":true,"filters":[{"name":"hidebots","label":"rcfilters-filter-bots-label","description":"rcfilters-filter-bots-description","cssClass":"mw-changeslist-bot","priority":-2,"subset":[],"conflicts":[],"defaultHighlightColor":null,"default":true},{"name":"hidehumans","label":"rcfilters-filter-humans-label","description":"rcfilters-filter-humans-description","cssClass":"mw-changeslist-human","priority":-3,"subset":[],"conflicts":[],"defaultHighlightColor":null,"default":false}],"priority":-4,"conflicts":[],"title":"rcfilters-filtergroup-automated"},{"name":"significance","type":"send_unselected_if_any","fullCoverage":true,"filters":[{"name":"hideminor","label":"rcfilters-filter-minor-label","description":"rcfilters-filter-minor-description","cssClass":"mw-changeslist-minor","priority":-2,"subset":[],"conflicts":[{"group":"changeType","filter":"hidecategorization","globalDescription":"rcfilters-hideminor-conflicts-typeofchange-global","contextDescription":"rcfilters-hideminor-conflicts-typeofchange"},{"group":"changeType","filter":"hidelog","globalDescription":"rcfilters-hideminor-conflicts-typeofchange-global","contextDescription":"rcfilters-hideminor-conflicts-typeofchange"},{"group":"changeType","filter":"hidenewuserlog","globalDescription":"rcfilters-hideminor-conflicts-typeofchange-global","contextDescription":"rcfilters-hideminor-conflicts-typeofchange"},{"group":"changeType","filter":"hidenewpages","globalDescription":"rcfilters-hideminor-conflicts-typeofchange-global","contextDescription":"rcfilters-hideminor-conflicts-typeofchange"}],"defaultHighlightColor":null,"default":false},{"name":"hidemajor","label":"rcfilters-filter-major-label","description":"rcfilters-filter-major-description","cssClass":"mw-changeslist-major","priority":-3,"subset":[],"conflicts":[{"group":"changeType","filter":"hideWikibase","globalDescription":"wikibase-rcfilters-hide-wikibase-conflicts-major-global","contextDescription":"wikibase-rcfilters-major-conflicts-hide-wikibase"}],"defaultHighlightColor":null,"default":false}],"priority":-6,"conflicts":[],"title":"rcfilters-filtergroup-significance"},{"name":"lastRevision","type":"send_unselected_if_any","fullCoverage":true,"filters":[{"name":"hidelastrevision","label":"rcfilters-filter-lastrevision-label","description":"rcfilters-filter-lastrevision-description","cssClass":"mw-changeslist-last","priority":-2,"subset":[],"conflicts":[],"defaultHighlightColor":null,"default":false},{"name":"hidepreviousrevisions","label":"rcfilters-filter-previousrevision-label","description":"rcfilters-filter-previousrevision-description","cssClass":"mw-changeslist-previous","priority":-3,"subset":[],"conflicts":[],"defaultHighlightColor":null,"default":false}],"priority":-7,"conflicts":[],"title":"rcfilters-filtergroup-lastrevision"},{"name":"translations","type":"string_options","fullCoverage":true,"filters":[{"name":"only","label":"translate-rcfilters-translations-only-label","description":"translate-rcfilters-translations-only-desc","cssClass":"mw-changeslist-only","priority":-2,"subset":[],"conflicts":[],"defaultHighlightColor":null},{"name":"site","label":"translate-rcfilters-translations-site-label","description":"translate-rcfilters-translations-site-desc","cssClass":"mw-changeslist-site","priority":-3,"subset":[],"conflicts":[],"defaultHighlightColor":null},{"name":"filter","label":"translate-rcfilters-translations-filter-label","description":"translate-rcfilters-translations-filter-desc","cssClass":"mw-changeslist-filter","priority":-4,"subset":[],"conflicts":[],"defaultHighlightColor":null},{"name":"filter-translation-pages","label":"translate-rcfilters-translations-filter-translation-pages-label","description":"translate-rcfilters-translations-filter-translation-pages-desc","cssClass":"mw-changeslist-filter-translation-pages","priority":-5,"subset":[],"conflicts":[],"defaultHighlightColor":null}],"priority":-7,"conflicts":[],"title":"translate-rcfilters-translations","separator":";","default":"filter"},{"name":"changeType","type":"send_unselected_if_any","fullCoverage":true,"filters":[{"name":"hidepageedits","label":"rcfilters-filter-pageedits-label","description":"rcfilters-filter-pageedits-description","cssClass":"mw-changeslist-src-mw-edit","priority":-2,"subset":[],"conflicts":[],"defaultHighlightColor":null,"default":false},{"name":"hidenewpages","label":"rcfilters-filter-newpages-label","description":"rcfilters-filter-newpages-description","cssClass":"mw-changeslist-src-mw-new","priority":-3,"subset":[],"conflicts":[{"group":"significance","filter":"hideminor","globalDescription":"rcfilters-hideminor-conflicts-typeofchange-global","contextDescription":"rcfilters-typeofchange-conflicts-hideminor"}],"defaultHighlightColor":null,"default":false},{"name":"hidecategorization","label":"rcfilters-filter-categorization-label","description":"rcfilters-filter-categorization-description","cssClass":"mw-changeslist-src-mw-categorize","priority":-4,"subset":[],"conflicts":[{"group":"significance","filter":"hideminor","globalDescription":"rcfilters-hideminor-conflicts-typeofchange-global","contextDescription":"rcfilters-typeofchange-conflicts-hideminor"}],"defaultHighlightColor":null,"default":true},{"name":"hideWikibase","label":"wikibase-rcfilters-hide-wikibase-label","description":"wikibase-rcfilters-hide-wikibase-description","cssClass":"mw-changeslist-src-mw-wikibase","priority":-4,"subset":[],"conflicts":[{"group":"significance","filter":"hidemajor","globalDescription":"wikibase-rcfilters-hide-wikibase-conflicts-major-global","contextDescription":"wikibase-rcfilters-hide-wikibase-conflicts-major"}],"defaultHighlightColor":null,"default":true},{"name":"hidelog","label":"rcfilters-filter-logactions-label","description":"rcfilters-filter-logactions-description","cssClass":"mw-changeslist-src-mw-log","priority":-5,"subset":[],"conflicts":[{"group":"significance","filter":"hideminor","globalDescription":"rcfilters-hideminor-conflicts-typeofchange-global","contextDescription":"rcfilters-typeofchange-conflicts-hideminor"}],"defaultHighlightColor":null,"default":false},{"name":"hidenewuserlog","label":"rcfilters-filter-accountcreations-label","description":"rcfilters-filter-accountcreations-description","cssClass":"mw-changeslist-src-mw-newuserlog","priority":-6,"subset":[],"conflicts":[{"group":"significance","filter":"hideminor","globalDescription":"rcfilters-hideminor-conflicts-typeofchange-global","contextDescription":"rcfilters-typeofchange-conflicts-hideminor"}],"defaultHighlightColor":null,"default":false}],"priority":-8,"conflicts":[],"title":"rcfilters-filtergroup-changetype"}],"wgStructuredChangeFiltersMessages":{"rcfilters-filtergroup-user-experience-level":"User registration and experience","rcfilters-filter-user-experience-level-unregistered-label":"Unregistered","rcfilters-filter-user-experience-level-unregistered-description":"Editors who aren't logged-in.","rcfilters-filter-user-experience-level-registered-label":"Registered","rcfilters-filter-user-experience-level-registered-description":"Logged-in editors.","rcfilters-filter-user-experience-level-newcomer-label":"Newcomers","rcfilters-filter-user-experience-level-newcomer-description":"Registered editors who have fewer than 10 edits or 4 days of activity.","rcfilters-filter-user-experience-level-learner-label":"Learners","rcfilters-filter-user-experience-level-learner-description":"Registered editors whose experience falls between \"Newcomers\" and \"Experienced users.\"","rcfilters-filter-user-experience-level-experienced-label":"Experienced users","rcfilters-filter-user-experience-level-experienced-description":"Registered editors with more than 500 edits and 30 days of activity.","rcfilters-filtergroup-authorship":"Contribution authorship","rcfilters-filter-editsbyself-label":"Changes by you","rcfilters-filter-editsbyself-description":"Your own contributions.","rcfilters-filter-editsbyother-label":"Changes by others","rcfilters-filter-editsbyother-description":"All changes except your own.","rcfilters-filtergroup-automated":"Automated contributions","rcfilters-filter-bots-label":"Bot","rcfilters-filter-bots-description":"Edits made by automated tools.","rcfilters-filter-humans-label":"Human (not bot)","rcfilters-filter-humans-description":"Edits made by human editors.","rcfilters-filtergroup-significance":"Significance","rcfilters-filter-minor-label":"Minor edits","rcfilters-filter-minor-description":"Edits the author labeled as minor.","rcfilters-hideminor-conflicts-typeofchange-global":"The \"Minor edits\" filter conflicts with one or more Type of change filters, because certain types of change cannot be designated as \"minor\". The conflicting filters are marked in the Active filters area, above.","rcfilters-hideminor-conflicts-typeofchange":"Certain types of change cannot be designated as \"minor\", so this filter conflicts with the following Type of Change filters: $1","rcfilters-filter-major-label":"Non-minor edits","rcfilters-filter-major-description":"Edits not labeled as minor.","wikibase-rcfilters-hide-wikibase-conflicts-major-global":"All {{WBREPONAME}} edits are designated as \"minor\", so the \"{{WBREPONAME}} edits\" filter conflicts with the \"Non-minor edits\" filter.","wikibase-rcfilters-major-conflicts-hide-wikibase":"All {{WBREPONAME}} edits are designated as \"minor\", so the \"{{WBREPONAME}} edits\" filter conflicts with the \"Non-minor edits\" filter.","rcfilters-filtergroup-lastrevision":"Latest revisions","rcfilters-filter-lastrevision-label":"Latest revision","rcfilters-filter-lastrevision-description":"Only the most recent change to a page.","rcfilters-filter-previousrevision-label":"Not the latest revision","rcfilters-filter-previousrevision-description":"All changes that are not the \"latest revision\".","translate-rcfilters-translations":"Translations","translate-rcfilters-translations-only-label":"Translation namespaces","translate-rcfilters-translations-only-desc":"Translations submitted to message groups.","translate-rcfilters-translations-site-label":"Site messages","translate-rcfilters-translations-site-desc":"Changes to messages in the site language.","translate-rcfilters-translations-filter-label":"Not in translation namespaces","translate-rcfilters-translations-filter-desc":"Everything except translations submitted to message groups.","translate-rcfilters-translations-filter-translation-pages-label":"Not translation pages","translate-rcfilters-translations-filter-translation-pages-desc":"Everything except changes to translation pages.","rcfilters-filtergroup-changetype":"Type of change","rcfilters-filter-pageedits-label":"Page edits","rcfilters-filter-pageedits-description":"Edits to wiki content, discussions, category descriptions…","rcfilters-filter-newpages-label":"Page creations","rcfilters-filter-newpages-description":"Edits that make new pages.","rcfilters-typeofchange-conflicts-hideminor":"This Type of change filter conflicts with the \"Minor edits\" filter. Certain types of change cannot be designated as \"minor\".","rcfilters-filter-categorization-label":"Category changes","rcfilters-filter-categorization-description":"Records of pages being added or removed from categories.","wikibase-rcfilters-hide-wikibase-label":"{{WBREPONAME}} edits","wikibase-rcfilters-hide-wikibase-description":"Edits that originate on {{WBREPONAME}}.","wikibase-rcfilters-hide-wikibase-conflicts-major":"All {{WBREPONAME}} edits are designated as \"minor\", so the \"{{WBREPONAME}} edits\" filter conflicts with the \"Non-minor edits\" filter.","rcfilters-filter-logactions-label":"Logged actions","rcfilters-filter-logactions-description":"Administrative actions, page deletions, uploads…","rcfilters-filter-accountcreations-label":"Account creations","rcfilters-filter-accountcreations-description":"Log entries for account creation. Logged actions must be selected."},"wgStructuredChangeFiltersCollapsedState":false,"StructuredChangeFiltersDisplayConfig":{"maxDays":30,"limitArray":[50,100,250,500],"limitDefault":50,"daysArray":[1,3,7,14,30],"daysDefault":7},"wgStructuredChangeFiltersSavedQueriesPreferenceName":"rcfilters-saved-queries","wgStructuredChangeFiltersLimitPreferenceName":"rcfilters-limit","wgStructuredChangeFiltersDaysPreferenceName":"rcdays","wgStructuredChangeFiltersCollapsedPreferenceName":"rcfilters-rc-collapsed","upwizPropertyTitles":{"P180":"Main subjects visible in this work"},"upwizPropertyPlaceholders":{"P180":"e.g., Angolan giraffe"},"upwizPropertyCopyLabels":{"P180":"Main subjects"},"wgEditSubmitButtonLabelPublish":true,"thanks-confirmation-required":true,"wgULSPosition":"personal","wgULSisCompactLinksEnabled":true,"wgVector2022LanguageInHeader":false,"wgULSisLanguageSelectorEmpty":true,"wbmiDefaultProperties":["P180"],"wbmiPropertyTitles":{"P180":"Items portrayed in this file"},"wbmiPropertyTypes":{"P180":"wikibase-item"},"wbmiRepoApiUrl":"/w/api.php","wbmiHelpUrls":{"P180":"https://commons.wikimedia.org/wiki/Special:MyLanguage/Commons:Depicts"},"wbmiExternalEntitySearchBaseUri":"https://www.wikidata.org/w/api.php","wbmiSupportedDataTypes":["wikibase-item","string","quantity","time","monolingualtext","external-id","globe-coordinate","url"],"wgCheckUserClientHintsHeadersJsApi":["brands","architecture","bitness","fullVersionList","mobile","model","platform","platformVersion"]};
RLSTATE={"ext.gadget.Long-Image-Names-in-Categories":"ready","ext.globalCssJs.user.styles":"ready","site.styles":"ready","user.styles":"ready","ext.globalCssJs.user":"ready","user":"ready","user.options":"loading","mediawiki.helplink":"ready","wikibase.client.miscStyles":"ready","oojs-ui-core.styles":"ready","oojs-ui.styles.indicators":"ready","mediawiki.widgets.styles":"ready","oojs-ui-core.icons":"ready","mediawiki.interface.helpers.styles":"ready","mediawiki.special.changeslist.legend":"ready","mediawiki.special.changeslist":"ready","mediawiki.rcfilters.filters.base.styles":"ready","skins.vector.search.codex.styles":"ready","skins.vector.styles":"ready","skins.vector.icons":"ready","mediawiki.feedlink":"ready","ext.wikimediamessages.styles":"ready","ext.visualEditor.desktopArticleTarget.noscript":"ready","ext.uls.pt":"ready"};RLPAGEMODULES=["ext.xLab","mediawiki.special.changeslist.watchlistexpiry","mediawiki.special.changeslist.legend.js","mediawiki.rcfilters.filters.ui","site","mediawiki.page.ready","skins.vector.js","ext.centralNotice.geoIP","ext.gadget.Slideshow","ext.gadget.ZoomViewer","ext.gadget.CollapsibleTemplates","ext.gadget.Stockphoto","ext.gadget.WatchlistNotice","ext.gadget.AjaxQuickDelete","ext.gadget.WikiMiniAtlas","ext.gadget.LanguageSelect","ext.gadget.PictureOfTheYearEnhancements","ext.urlShortener.toolbar","ext.centralauth.centralautologin","ext.visualEditor.desktopArticleTarget.init","ext.visualEditor.targetLoader","ext.echo.centralauth","ext.thanks.corethank","ext.eventLogging","ext.wikimediaEvents","ext.wikimediaEvents.xLabGroupbyExperiment","ext.wikimediaEvents.wikibase","ext.navigationTiming","ext.uls.compactlinks","ext.uls.interface","ext.checkUser.clientHints"];</script>
<script>(RLQ=window.RLQ||[]).push(function(){mw.loader.impl(function(){return["user.options@12s5i",function($,jQuery,require,module){mw.user.tokens.set({"patrolToken":"+\\","watchToken":"+\\","csrfToken":"+\\"});
}];});});</script>
<link rel="stylesheet" href="/w/load.php?lang=en&amp;modules=ext.uls.pt%7Cext.visualEditor.desktopArticleTarget.noscript%7Cext.wikimediamessages.styles%7Cmediawiki.feedlink%2Chelplink%7Cmediawiki.interface.helpers.styles%7Cmediawiki.rcfilters.filters.base.styles%7Cmediawiki.special.changeslist%7Cmediawiki.special.changeslist.legend%7Cmediawiki.widgets.styles%7Coojs-ui-core.icons%2Cstyles%7Coojs-ui.styles.indicators%7Cskins.vector.icons%2Cstyles%7Cskins.vector.search.codex.styles%7Cwikibase.client.miscStyles&amp;only=styles&amp;skin=vector-2022">
<script async="" src="/w/load.php?lang=en&amp;modules=startup&amp;only=scripts&amp;raw=1&amp;skin=vector-2022"></script>
<meta name="ResourceLoaderDynamicStyles" content="">
<link rel="stylesheet" href="/w/load.php?lang=en&amp;modules=ext.gadget.Long-Image-Names-in-Categories&amp;only=styles&amp;skin=vector-2022">
<link rel="stylesheet" href="/w/load.php?lang=en&amp;modules=site.styles&amp;only=styles&amp;skin=vector-2022">
<meta name="generator" content="MediaWiki 1.45.0-wmf.12">
<meta name="referrer" content="origin">
<meta name="referrer" content="origin-when-cross-origin">
<meta name="robots" content="noindex,nofollow,max-image-preview:standard">
<meta name="format-detection" content="telephone=no">
<meta name="viewport" content="width=1120">
<meta property="og:title" content="Related changes - Wikimedia Commons">
<meta property="og:type" content="website">
<link rel="alternate" media="only screen and (max-width: 640px)" href="//commons.m.wikimedia.org/wiki/Special:RecentChangesLinked/File:Madafu-chopping.jpg">
<link rel="apple-touch-icon" href="/static/apple-touch/commons.png">
<link rel="icon" href="/static/favicon/commons.ico">
<link rel="search" type="application/opensearchdescription+xml" href="/w/rest.php/v1/search" title="Wikimedia Commons">
<link rel="EditURI" type="application/rsd+xml" href="//commons.wikimedia.org/w/api.php?action=rsd">
<link rel="canonical" href="https://commons.wikimedia.org/wiki/Special:RecentChangesLinked/File:Madafu-chopping.jpg">
<link rel="license" href="https://creativecommons.org/licenses/by-sa/4.0/">
<link rel="alternate" type="application/atom+xml" title="&quot;Special:RecentChangesLinked/File:Madafu-chopping.jpg&quot; Atom feed" href="/w/api.php?hidebots=1&amp;hidecategorization=1&amp;hideWikibase=1&amp;translations=filter&amp;urlversion=1&amp;days=7&amp;limit=50&amp;target=File%3AMadafu-chopping.jpg&amp;action=feedrecentchanges&amp;feedformat=atom">
<link rel="alternate" type="application/atom+xml" title="Wikimedia Commons Atom feed" href="/w/index.php?title=Special:RecentChanges&amp;feed=atom">
<link rel="dns-prefetch" href="auth.wikimedia.org">
</head>
<body class="mw-rcfilters-enabled skin--responsive skin-vector skin-vector-search-vue mediawiki ltr sitedir-ltr mw-hide-empty-elt ns--1 ns-special mw-special-Recentchangeslinked page-Special_RecentChangesLinked_File_Madafu-chopping_jpg rootpage-Special_RecentChangesLinked_File_Madafu-chopping_jpg skin-vector-2022 action-view"><a class="mw-jump-link" href="#bodyContent">Jump to content</a>
<div class="vector-header-container">
	<header class="vector-header mw-header no-font-mode-scale">
		<div class="vector-header-start">
			<nav class="vector-main-menu-landmark" aria-label="Site">
				
<div id="vector-main-menu-dropdown" class="vector-dropdown vector-main-menu-dropdown vector-button-flush-left vector-button-flush-right"  title="Main menu" >
	<input type="checkbox" id="vector-main-menu-dropdown-checkbox" role="button" aria-haspopup="true" data-event-name="ui.dropdown-vector-main-menu-dropdown" class="vector-dropdown-checkbox "  aria-label="Main menu"  >
	<label id="vector-main-menu-dropdown-label" for="vector-main-menu-dropdown-checkbox" class="vector-dropdown-label cdx-button cdx-button--fake-button cdx-button--fake-button--enabled cdx-button--weight-quiet cdx-button--icon-only " aria-hidden="true"  ><span class="vector-icon mw-ui-icon-menu mw-ui-icon-wikimedia-menu"></span>

<span class="vector-dropdown-label-text">Main menu</span>
	</label>
	<div class="vector-dropdown-content">


				<div id="vector-main-menu-unpinned-container" class="vector-unpinned-container">
		
<div id="vector-main-menu" class="vector-main-menu vector-pinnable-element">
	<div
	class="vector-pinnable-header vector-main-menu-pinnable-header vector-pinnable-header-unpinned"
	data-feature-name="main-menu-pinned"
	data-pinnable-element-id="vector-main-menu"
	data-pinned-container-id="vector-main-menu-pinned-container"
	data-unpinned-container-id="vector-main-menu-unpinned-container"
>
	<div class="vector-pinnable-header-label">Main menu</div>
	<button class="vector-pinnable-header-toggle-button vector-pinnable-header-pin-button" data-event-name="pinnable-header.vector-main-menu.pin">move to sidebar</button>
	<button class="vector-pinnable-header-toggle-button vector-pinnable-header-unpin-button" data-event-name="pinnable-header.vector-main-menu.unpin">hide</button>
</div>

	
<div id="p-navigation" class="vector-menu mw-portlet mw-portlet-navigation"  >
	<div class="vector-menu-heading">
		Navigate
	</div>
	<div class="vector-menu-content">
		
		<ul class="vector-menu-content-list">
			
			<li id="n-mainpage-description" class="mw-list-item"><a href="/wiki/Main_Page" title="Visit the main page [z]" accesskey="z"><span>Main page</span></a></li><li id="n-welcome" class="mw-list-item"><a href="/wiki/Commons:Welcome"><span>Welcome</span></a></li><li id="n-portal" class="mw-list-item"><a href="/wiki/Commons:Community_portal" title="About the project, what you can do, where to find things"><span>Community portal</span></a></li><li id="n-village-pump" class="mw-list-item"><a href="/wiki/Commons:Village_pump"><span>Village pump</span></a></li><li id="n-help" class="mw-list-item"><a href="/wiki/Special:MyLanguage/Help:Contents" title="The place to find out"><span>Help center</span></a></li>
		</ul>
		
	</div>
</div>

	
	
<div id="p-participate" class="vector-menu mw-portlet mw-portlet-participate"  >
	<div class="vector-menu-heading">
		Participate
	</div>
	<div class="vector-menu-content">
		
		<ul class="vector-menu-content-list">
			
			<li id="n-uploadbtn" class="mw-list-item"><a href="/wiki/Special:UploadWizard"><span>Upload file</span></a></li><li id="n-recentchanges" class="mw-list-item"><a href="/wiki/Special:RecentChanges" title="A list of recent changes in the wiki [r]" accesskey="r"><span>Recent changes</span></a></li><li id="n-latestfiles" class="mw-list-item"><a href="/wiki/Special:NewFiles"><span>Latest files</span></a></li><li id="n-randomimage" class="mw-list-item"><a href="/wiki/Special:Random/File" title="Load a random file [x]" accesskey="x"><span>Random file</span></a></li><li id="n-contact" class="mw-list-item"><a href="/wiki/Commons:Contact_us"><span>Contact us</span></a></li><li id="n-specialpages" class="mw-list-item"><a href="/wiki/Special:SpecialPages"><span>Special pages</span></a></li>
		</ul>
		
	</div>
</div>

	
<div id="vector-main-menu" class="vector-menu "  >
	<div class="vector-menu-heading">
		
	</div>
	<div class="vector-menu-content">
		
		<ul class="vector-menu-content-list">
			
			
		</ul>
		
	</div>
</div>

</div>

				</div>

	</div>
</div>

		</nav>
			
<a href="/wiki/Main_Page" class="mw-logo">
	<img class="mw-logo-icon" src="/static/images/icons/commonswiki.svg" alt="" aria-hidden="true" height="50" width="50">
	<span class="mw-logo-container skin-invert">
		<img class="mw-logo-wordmark" alt="Wikimedia Commons" src="/static/images/mobile/copyright/commonswiki-wordmark.svg" style="width: 7.1875em; height: 2em;">
	</span>
</a>

		</div>
		<div class="vector-header-end">
			
<div id="p-search" role="search" class="vector-search-box-vue  vector-search-box-collapses vector-search-box">
	<a href="/wiki/Special:MediaSearch" class="cdx-button cdx-button--fake-button cdx-button--fake-button--enabled cdx-button--weight-quiet cdx-button--icon-only search-toggle" title="Search Wikimedia Commons [f]" accesskey="f"><span class="vector-icon mw-ui-icon-search mw-ui-icon-wikimedia-search"></span>

<span>Search</span>
	</a>
	<div class="vector-typeahead-search-container">
		<div class="cdx-typeahead-search">
			<form action="/w/index.php" id="searchform" class="cdx-search-input cdx-search-input--has-end-button">
				<div id="simpleSearch" class="cdx-search-input__input-wrapper"  data-search-loc="header-moved">
					<div class="cdx-text-input cdx-text-input--has-start-icon">
						<input
							class="cdx-text-input__input mw-searchInput" autocomplete="off"
							 type="search" name="search" placeholder="Search Wikimedia Commons" aria-label="Search Wikimedia Commons" autocapitalize="sentences" spellcheck="false" title="Search Wikimedia Commons [f]" accesskey="f" id="searchInput"
							>
						<span class="cdx-text-input__icon cdx-text-input__start-icon"></span>
					</div>
					<input type="hidden" name="title" value="Special:MediaSearch">
				</div>
				<button class="cdx-button cdx-search-input__end-button">Search</button>
			</form>
		</div>
	</div>
</div>

			<nav class="vector-user-links vector-user-links-wide" aria-label="Personal tools">
	<div class="vector-user-links-main">
	
<div id="p-vector-user-menu-preferences" class="vector-menu mw-portlet"  >
	<div class="vector-menu-content">
		
		<ul class="vector-menu-content-list">
			<li id="ca-uls" class="mw-list-item active user-links-collapsible-item"><a data-mw="interface" href="#" class="uls-trigger cdx-button cdx-button--fake-button cdx-button--fake-button--enabled cdx-button--weight-quiet"><span class="vector-icon mw-ui-icon-wikimedia-language mw-ui-icon-wikimedia-wikimedia-language"></span>

<span>English</span></a>
</li>

			
		</ul>
		
	</div>
</div>

	
<div id="p-vector-user-menu-userpage" class="vector-menu mw-portlet emptyPortlet"  >
	<div class="vector-menu-content">
		
		<ul class="vector-menu-content-list">
			
			
		</ul>
		
	</div>
</div>

	<nav class="vector-appearance-landmark" aria-label="Appearance">
		
<div id="vector-appearance-dropdown" class="vector-dropdown "  title="Change the appearance of the page&#039;s font size, width, and color" >
	<input type="checkbox" id="vector-appearance-dropdown-checkbox" role="button" aria-haspopup="true" data-event-name="ui.dropdown-vector-appearance-dropdown" class="vector-dropdown-checkbox "  aria-label="Appearance"  >
	<label id="vector-appearance-dropdown-label" for="vector-appearance-dropdown-checkbox" class="vector-dropdown-label cdx-button cdx-button--fake-button cdx-button--fake-button--enabled cdx-button--weight-quiet cdx-button--icon-only " aria-hidden="true"  ><span class="vector-icon mw-ui-icon-appearance mw-ui-icon-wikimedia-appearance"></span>

<span class="vector-dropdown-label-text">Appearance</span>
	</label>
	<div class="vector-dropdown-content">


			<div id="vector-appearance-unpinned-container" class="vector-unpinned-container">
				
			</div>
		
	</div>
</div>

	</nav>
	
<div id="p-vector-user-menu-notifications" class="vector-menu mw-portlet emptyPortlet"  >
	<div class="vector-menu-content">
		
		<ul class="vector-menu-content-list">
			
			
		</ul>
		
	</div>
</div>

	
<div id="p-vector-user-menu-overflow" class="vector-menu mw-portlet"  >
	<div class="vector-menu-content">
		
		<ul class="vector-menu-content-list">
			<li id="pt-sitesupport-2" class="user-links-collapsible-item mw-list-item user-links-collapsible-item"><a data-mw="interface" href="https://donate.wikimedia.org/?wmf_source=donate&amp;wmf_medium=sidebar&amp;wmf_campaign=commons.wikimedia.org&amp;uselang=en" class=""><span>Donate</span></a>
</li>
<li id="pt-createaccount-2" class="user-links-collapsible-item mw-list-item user-links-collapsible-item"><a data-mw="interface" href="/w/index.php?title=Special:CreateAccount&amp;returnto=Special%3ARecentChangesLinked%2FFile%3AMadafu-chopping.jpg" title="You are encouraged to create an account and log in; however, it is not mandatory" class=""><span>Create account</span></a>
</li>
<li id="pt-login-2" class="user-links-collapsible-item mw-list-item user-links-collapsible-item"><a data-mw="interface" href="/w/index.php?title=Special:UserLogin&amp;returnto=Special%3ARecentChangesLinked%2FFile%3AMadafu-chopping.jpg" title="You are encouraged to log in; however, it is not mandatory [o]" accesskey="o" class=""><span>Log in</span></a>
</li>

			
		</ul>
		
	</div>
</div>

	</div>
	
<div id="vector-user-links-dropdown" class="vector-dropdown vector-user-menu vector-button-flush-right vector-user-menu-logged-out"  title="More options" >
	<input type="checkbox" id="vector-user-links-dropdown-checkbox" role="button" aria-haspopup="true" data-event-name="ui.dropdown-vector-user-links-dropdown" class="vector-dropdown-checkbox "  aria-label="Personal tools"  >
	<label id="vector-user-links-dropdown-label" for="vector-user-links-dropdown-checkbox" class="vector-dropdown-label cdx-button cdx-button--fake-button cdx-button--fake-button--enabled cdx-button--weight-quiet cdx-button--icon-only " aria-hidden="true"  ><span class="vector-icon mw-ui-icon-ellipsis mw-ui-icon-wikimedia-ellipsis"></span>

<span class="vector-dropdown-label-text">Personal tools</span>
	</label>
	<div class="vector-dropdown-content">


		
<div id="p-personal" class="vector-menu mw-portlet mw-portlet-personal user-links-collapsible-item"  title="User menu" >
	<div class="vector-menu-content">
		
		<ul class="vector-menu-content-list">
			
			<li id="pt-sitesupport" class="user-links-collapsible-item mw-list-item"><a href="https://donate.wikimedia.org/?wmf_source=donate&amp;wmf_medium=sidebar&amp;wmf_campaign=commons.wikimedia.org&amp;uselang=en"><span>Donate</span></a></li><li id="pt-createaccount" class="user-links-collapsible-item mw-list-item"><a href="/w/index.php?title=Special:CreateAccount&amp;returnto=Special%3ARecentChangesLinked%2FFile%3AMadafu-chopping.jpg" title="You are encouraged to create an account and log in; however, it is not mandatory"><span class="vector-icon mw-ui-icon-userAdd mw-ui-icon-wikimedia-userAdd"></span> <span>Create account</span></a></li><li id="pt-login" class="user-links-collapsible-item mw-list-item"><a href="/w/index.php?title=Special:UserLogin&amp;returnto=Special%3ARecentChangesLinked%2FFile%3AMadafu-chopping.jpg" title="You are encouraged to log in; however, it is not mandatory [o]" accesskey="o"><span class="vector-icon mw-ui-icon-logIn mw-ui-icon-wikimedia-logIn"></span> <span>Log in</span></a></li>
		</ul>
		
	</div>
</div>

<div id="p-user-menu-anon-editor" class="vector-menu mw-portlet mw-portlet-user-menu-anon-editor"  >
	<div class="vector-menu-heading">
		Pages for logged out editors <a href="/wiki/Help:Introduction" aria-label="Learn more about editing"><span>learn more</span></a>
	</div>
	<div class="vector-menu-content">
		
		<ul class="vector-menu-content-list">
			
			<li id="pt-anoncontribs" class="mw-list-item"><a href="/wiki/Special:MyContributions" title="A list of edits made from this IP address [y]" accesskey="y"><span>Contributions</span></a></li><li id="pt-anontalk" class="mw-list-item"><a href="/wiki/Special:MyTalk" title="Discussion about edits from this IP address [n]" accesskey="n"><span>Talk</span></a></li>
		</ul>
		
	</div>
</div>

	
	</div>
</div>

</nav>

		</div>
	</header>
</div>
<div class="mw-page-container">
	<div class="mw-page-container-inner">
		<div class="vector-sitenotice-container">
			<div id="siteNotice"><!-- CentralNotice --></div>
		</div>
		<div class="vector-column-start">
			<div class="vector-main-menu-container">
		<div id="mw-navigation">
			<nav id="mw-panel" class="vector-main-menu-landmark" aria-label="Site">
				<div id="vector-main-menu-pinned-container" class="vector-pinned-container">
				
				</div>
		</nav>
		</div>
	</div>
</div>
		<div class="mw-content-container">
			<main id="content" class="mw-body">
				<header class="mw-body-header vector-page-titlebar no-font-mode-scale">
					<h1 id="firstHeading" class="firstHeading mw-first-heading">Related changes</h1>
						<div class="mw-indicators">
		<div id="mw-indicator-mw-helplink" class="mw-indicator"><a href="https://www.mediawiki.org/wiki/Special:MyLanguage/Help:Related_changes" target="_blank" class="mw-helplink"><span class="mw-helplink-icon"></span>Help</a></div>
		</div>
</header>
				<div class="vector-page-toolbar vector-feature-custom-font-size-clientpref--excluded">
					<div class="vector-page-toolbar-container">
						<div id="left-navigation">
							<nav aria-label="Namespaces">
								
<div id="p-associated-pages" class="vector-menu vector-menu-tabs mw-portlet mw-portlet-associated-pages"  >
	<div class="vector-menu-content">
		
		<ul class="vector-menu-content-list">
			
			<li id="ca-nstab-image" class="selected vector-tab-noicon mw-list-item"><a href="/wiki/File:Madafu-chopping.jpg" title="View the file page [c]" accesskey="c"><span>File</span></a></li><li id="ca-talk" class="new vector-tab-noicon mw-list-item"><a href="/w/index.php?title=File_talk:Madafu-chopping.jpg&amp;action=edit&amp;redlink=1" rel="discussion" class="new" title="Discussion about the content page (page does not exist) [t]" accesskey="t"><span>Discussion</span></a></li>
		</ul>
		
	</div>
</div>

								
<div id="vector-variants-dropdown" class="vector-dropdown emptyPortlet"  >
	<input type="checkbox" id="vector-variants-dropdown-checkbox" role="button" aria-haspopup="true" data-event-name="ui.dropdown-vector-variants-dropdown" class="vector-dropdown-checkbox " aria-label="Change language variant"   >
	<label id="vector-variants-dropdown-label" for="vector-variants-dropdown-checkbox" class="vector-dropdown-label cdx-button cdx-button--fake-button cdx-button--fake-button--enabled cdx-button--weight-quiet" aria-hidden="true"  ><span class="vector-dropdown-label-text">English</span>
	</label>
	<div class="vector-dropdown-content">


					
<div id="p-variants" class="vector-menu mw-portlet mw-portlet-variants emptyPortlet"  >
	<div class="vector-menu-content">
		
		<ul class="vector-menu-content-list">
			
			
		</ul>
		
	</div>
</div>

				
	</div>
</div>

							</nav>
						</div>
						<div id="right-navigation" class="vector-collapsible">
							<nav aria-label="Views">
								
<div id="p-views" class="vector-menu vector-menu-tabs mw-portlet mw-portlet-views"  >
	<div class="vector-menu-content">
		
		<ul class="vector-menu-content-list">
			
			<li id="ca-view" class="vector-tab-noicon mw-list-item"><a href="/wiki/File:Madafu-chopping.jpg"><span>Read</span></a></li><li id="ca-edit" class="vector-tab-noicon mw-list-item"><a href="/w/index.php?title=File:Madafu-chopping.jpg&amp;action=edit" title="Edit this page [e]" accesskey="e"><span>Edit</span></a></li><li id="ca-history" class="vector-tab-noicon mw-list-item"><a href="/w/index.php?title=File:Madafu-chopping.jpg&amp;action=history" title="Past revisions of this page [h]" accesskey="h"><span>View history</span></a></li>
		</ul>
		
	</div>
</div>

							</nav>
				
							<nav class="vector-page-tools-landmark" aria-label="Page tools">
								
<div id="vector-page-tools-dropdown" class="vector-dropdown vector-page-tools-dropdown"  >
	<input type="checkbox" id="vector-page-tools-dropdown-checkbox" role="button" aria-haspopup="true" data-event-name="ui.dropdown-vector-page-tools-dropdown" class="vector-dropdown-checkbox "  aria-label="Tools"  >
	<label id="vector-page-tools-dropdown-label" for="vector-page-tools-dropdown-checkbox" class="vector-dropdown-label cdx-button cdx-button--fake-button cdx-button--fake-button--enabled cdx-button--weight-quiet" aria-hidden="true"  ><span class="vector-dropdown-label-text">Tools</span>
	</label>
	<div class="vector-dropdown-content">


									<div id="vector-page-tools-unpinned-container" class="vector-unpinned-container">
						
<div id="vector-page-tools" class="vector-page-tools vector-pinnable-element">
	<div
	class="vector-pinnable-header vector-page-tools-pinnable-header vector-pinnable-header-unpinned"
	data-feature-name="page-tools-pinned"
	data-pinnable-element-id="vector-page-tools"
	data-pinned-container-id="vector-page-tools-pinned-container"
	data-unpinned-container-id="vector-page-tools-unpinned-container"
>
	<div class="vector-pinnable-header-label">Tools</div>
	<button class="vector-pinnable-header-toggle-button vector-pinnable-header-pin-button" data-event-name="pinnable-header.vector-page-tools.pin">move to sidebar</button>
	<button class="vector-pinnable-header-toggle-button vector-pinnable-header-unpin-button" data-event-name="pinnable-header.vector-page-tools.unpin">hide</button>
</div>

	
<div id="p-cactions" class="vector-menu mw-portlet mw-portlet-cactions emptyPortlet vector-has-collapsible-items"  title="More options" >
	<div class="vector-menu-heading">
		Actions
	</div>
	<div class="vector-menu-content">
		
		<ul class="vector-menu-content-list">
			
			<li id="ca-more-view" class="vector-more-collapsible-item mw-list-item"><a href="/wiki/File:Madafu-chopping.jpg"><span>Read</span></a></li><li id="ca-more-edit" class="vector-more-collapsible-item mw-list-item"><a href="/w/index.php?title=File:Madafu-chopping.jpg&amp;action=edit" title="Edit this page [e]" accesskey="e"><span>Edit</span></a></li><li id="ca-more-history" class="vector-more-collapsible-item mw-list-item"><a href="/w/index.php?title=File:Madafu-chopping.jpg&amp;action=history"><span>View history</span></a></li>
		</ul>
		
	</div>
</div>

<div id="p-tb" class="vector-menu mw-portlet mw-portlet-tb"  >
	<div class="vector-menu-heading">
		General
	</div>
	<div class="vector-menu-content">
		
		<ul class="vector-menu-content-list">
			
			<li id="feedlinks" class="mw-list-item"><a href="/w/api.php?hidebots=1&amp;hidecategorization=1&amp;hideWikibase=1&amp;translations=filter&amp;urlversion=1&amp;days=7&amp;limit=50&amp;target=File%3AMadafu-chopping.jpg&amp;action=feedrecentchanges&amp;feedformat=atom" id="feed-atom" rel="alternate" type="application/atom+xml" class="feedlink" title="Atom feed for this page"><span>Atom</span></a></li><li id="t-print" class="mw-list-item"><a href="javascript:print();" rel="alternate" title="Printable version of this page [p]" accesskey="p"><span>Printable version</span></a></li><li id="t-urlshortener" class="mw-list-item"><a href="/w/index.php?title=Special:UrlShortener&amp;url=https%3A%2F%2Fcommons.wikimedia.org%2Fwiki%2FSpecial%3ARecentChangesLinked%2FFile%3AMadafu-chopping.jpg"><span>Get shortened URL</span></a></li><li id="t-urlshortener-qrcode" class="mw-list-item"><a href="/w/index.php?title=Special:QrCode&amp;url=https%3A%2F%2Fcommons.wikimedia.org%2Fwiki%2FSpecial%3ARecentChangesLinked%2FFile%3AMadafu-chopping.jpg"><span>Download QR code</span></a></li>
		</ul>
		
	</div>
</div>

<div id="p-wikibase-otherprojects" class="vector-menu mw-portlet mw-portlet-wikibase-otherprojects emptyPortlet"  >
	<div class="vector-menu-heading">
		In other projects
	</div>
	<div class="vector-menu-content">
		
		<ul class="vector-menu-content-list">
			
			
		</ul>
		
	</div>
</div>

</div>

									</div>
				
	</div>
</div>

							</nav>
						</div>
					</div>
				</div>
				<div class="vector-column-end no-font-mode-scale">
					<div class="vector-sticky-pinned-container">
						<nav class="vector-page-tools-landmark" aria-label="Page tools">
							<div id="vector-page-tools-pinned-container" class="vector-pinned-container">
				
							</div>
		</nav>
						<nav class="vector-appearance-landmark" aria-label="Appearance">
							<div id="vector-appearance-pinned-container" class="vector-pinned-container">
				<div id="vector-appearance" class="vector-appearance vector-pinnable-element">
	<div
	class="vector-pinnable-header vector-appearance-pinnable-header vector-pinnable-header-pinned"
	data-feature-name="appearance-pinned"
	data-pinnable-element-id="vector-appearance"
	data-pinned-container-id="vector-appearance-pinned-container"
	data-unpinned-container-id="vector-appearance-unpinned-container"
>
	<div class="vector-pinnable-header-label">Appearance</div>
	<button class="vector-pinnable-header-toggle-button vector-pinnable-header-pin-button" data-event-name="pinnable-header.vector-appearance.pin">move to sidebar</button>
	<button class="vector-pinnable-header-toggle-button vector-pinnable-header-unpin-button" data-event-name="pinnable-header.vector-appearance.unpin">hide</button>
</div>


</div>

							</div>
		</nav>
					</div>
				</div>
				<div id="bodyContent" class="vector-body" aria-labelledby="firstHeading" data-mw-ve-target-container>
					<div class="vector-body-before-content">
					
						
					</div>
					<div id="contentSub"><div id="mw-content-subtitle">← <a href="/wiki/File:Madafu-chopping.jpg" title="File:Madafu-chopping.jpg">File:Madafu-chopping.jpg</a></div></div>
					
					
					<div id="mw-content-text" class="mw-body-content"><div class="mw-specialpage-summary">
<p>Enter a page name to see changes on pages linked to or from that page. (To see members of a category, enter Category:Name of category). Changes to pages on <a href="/wiki/Special:Watchlist" title="Special:Watchlist">your Watchlist</a> are in <strong>bold</strong>.
</p>
</div><div class="mw-rcfilters-head"><div class="mw-rcfilters-container"></div><fieldset class="rcoptions cloptions"><legend>Recent changes options</legend><span class="rclinks">Show last <a href="/w/index.php?title=Special:RecentChangesLinked&amp;limit=50&amp;target=File%3AMadafu-chopping.jpg" title="Special:RecentChangesLinked" data-params="{&quot;limit&quot;:50}" data-keys="limit"><strong>50</strong></a> | <a href="/w/index.php?title=Special:RecentChangesLinked&amp;limit=100&amp;target=File%3AMadafu-chopping.jpg" title="Special:RecentChangesLinked" data-params="{&quot;limit&quot;:100}" data-keys="limit">100</a> | <a href="/w/index.php?title=Special:RecentChangesLinked&amp;limit=250&amp;target=File%3AMadafu-chopping.jpg" title="Special:RecentChangesLinked" data-params="{&quot;limit&quot;:250}" data-keys="limit">250</a> | <a href="/w/index.php?title=Special:RecentChangesLinked&amp;limit=500&amp;target=File%3AMadafu-chopping.jpg" title="Special:RecentChangesLinked" data-params="{&quot;limit&quot;:500}" data-keys="limit">500</a> changes in last <a href="/w/index.php?title=Special:RecentChangesLinked&amp;days=1&amp;from=&amp;target=File%3AMadafu-chopping.jpg" title="Special:RecentChangesLinked" data-params="{&quot;days&quot;:1,&quot;from&quot;:&quot;&quot;}" data-keys="days,from">1</a> | <a href="/w/index.php?title=Special:RecentChangesLinked&amp;days=3&amp;from=&amp;target=File%3AMadafu-chopping.jpg" title="Special:RecentChangesLinked" data-params="{&quot;days&quot;:3,&quot;from&quot;:&quot;&quot;}" data-keys="days,from">3</a> | <a href="/w/index.php?title=Special:RecentChangesLinked&amp;days=7&amp;from=&amp;target=File%3AMadafu-chopping.jpg" title="Special:RecentChangesLinked" data-params="{&quot;days&quot;:7,&quot;from&quot;:&quot;&quot;}" data-keys="days,from"><strong>7</strong></a> | <a href="/w/index.php?title=Special:RecentChangesLinked&amp;days=14&amp;from=&amp;target=File%3AMadafu-chopping.jpg" title="Special:RecentChangesLinked" data-params="{&quot;days&quot;:14,&quot;from&quot;:&quot;&quot;}" data-keys="days,from">14</a> | <a href="/w/index.php?title=Special:RecentChangesLinked&amp;days=30&amp;from=&amp;target=File%3AMadafu-chopping.jpg" title="Special:RecentChangesLinked" data-params="{&quot;days&quot;:30,&quot;from&quot;:&quot;&quot;}" data-keys="days,from">30</a> days</span><br /><span class="rcshowhide"><span class="rcshowhideliu rcshowhideoption clshowhideoption" data-filter-name="hideliu" data-feature-in-structured-ui="1"><a href="/w/index.php?title=Special:RecentChangesLinked&amp;hideliu=1&amp;target=File%3AMadafu-chopping.jpg" title="Special:RecentChangesLinked" data-params="{&quot;hideliu&quot;:1}" data-keys="hideliu">Hide</a> registered users</span> | <span class="rcshowhideanons rcshowhideoption clshowhideoption" data-filter-name="hideanons" data-feature-in-structured-ui="1"><a href="/w/index.php?title=Special:RecentChangesLinked&amp;hideanons=1&amp;target=File%3AMadafu-chopping.jpg" title="Special:RecentChangesLinked" data-params="{&quot;hideanons&quot;:1}" data-keys="hideanons">Hide</a> anonymous users</span> | <span class="rcshowhidemine rcshowhideoption clshowhideoption" data-filter-name="hidemyself" data-feature-in-structured-ui="1"><a href="/w/index.php?title=Special:RecentChangesLinked&amp;hidemyself=1&amp;target=File%3AMadafu-chopping.jpg" title="Special:RecentChangesLinked" data-params="{&quot;hidemyself&quot;:1}" data-keys="hidemyself">Hide</a> my edits</span> | <span class="rcshowhidebots rcshowhideoption clshowhideoption" data-filter-name="hidebots" data-feature-in-structured-ui="1"><a href="/w/index.php?title=Special:RecentChangesLinked&amp;hidebots=0&amp;target=File%3AMadafu-chopping.jpg" title="Special:RecentChangesLinked" data-params="{&quot;hidebots&quot;:0}" data-keys="hidebots">Show</a> bots</span> | <span class="rcshowhideminor rcshowhideoption clshowhideoption" data-filter-name="hideminor" data-feature-in-structured-ui="1"><a href="/w/index.php?title=Special:RecentChangesLinked&amp;hideminor=1&amp;target=File%3AMadafu-chopping.jpg" title="Special:RecentChangesLinked" data-params="{&quot;hideminor&quot;:1}" data-keys="hideminor">Hide</a> minor edits</span> | <span class="rcshowhidecategorization rcshowhideoption clshowhideoption" data-filter-name="hidecategorization" data-feature-in-structured-ui="1"><a href="/w/index.php?title=Special:RecentChangesLinked&amp;hidecategorization=0&amp;target=File%3AMadafu-chopping.jpg" title="Special:RecentChangesLinked" data-params="{&quot;hidecategorization&quot;:0}" data-keys="hidecategorization">Show</a> page categorization</span> | <span class="wikibase-rc-hide-wikidata rcshowhideoption clshowhideoption" data-filter-name="hideWikibase" data-feature-in-structured-ui="1"><a href="/w/index.php?title=Special:RecentChangesLinked&amp;hideWikibase=0&amp;target=File%3AMadafu-chopping.jpg" title="Special:RecentChangesLinked" data-params="{&quot;hideWikibase&quot;:0}" data-keys="hideWikibase">Show</a> Wikidata</span></span><br /><span class="rclistfrom"><a href="/w/index.php?title=Special:RecentChangesLinked&amp;from=20250803074448&amp;fromFormatted=07%3A44%2C+3+August+2025&amp;target=File%3AMadafu-chopping.jpg" title="Special:RecentChangesLinked" data-params="{&quot;from&quot;:&quot;20250803074448&quot;,&quot;fromFormatted&quot;:&quot;07:44, 3 August 2025&quot;}" data-keys="from,fromFormatted">Show new changes starting from 07:44, 3 August 2025</a></span>
<hr />
<form action="/w/index.php"><table class="mw-recentchanges-table"><tr class="namespaceForm"><td class="mw-label mw-namespace-label"><label for="namespace">Namespace:</label></td><td class="mw-input"><select id="namespace" name="namespace">
<option value="" selected="">all</option>
<option value="0">(Gallery)</option>
<option value="1">Talk</option>
<option value="2">User</option>
<option value="3">User talk</option>
<option value="4">Commons</option>
<option value="5">Commons talk</option>
<option value="6">File</option>
<option value="7">File talk</option>
<option value="8">MediaWiki</option>
<option value="9">MediaWiki talk</option>
<option value="10">Template</option>
<option value="11">Template talk</option>
<option value="12">Help</option>
<option value="13">Help talk</option>
<option value="14">Category</option>
<option value="15">Category talk</option>
<option value="100">Creator</option>
<option value="101">Creator talk</option>
<option value="102">TimedText</option>
<option value="103">TimedText talk</option>
<option value="104">Sequence</option>
<option value="105">Sequence talk</option>
<option value="106">Institution</option>
<option value="107">Institution talk</option>
<option value="460">Campaign</option>
<option value="461">Campaign talk</option>
<option value="486">Data</option>
<option value="487">Data talk</option>
<option value="828">Module</option>
<option value="829">Module talk</option>
<option value="1198">Translations</option>
<option value="1199">Translations talk</option>
<option value="2600">Topic</option>
</select> <label class="mw-input-with-label" title="Check this box to hide changes to pages within the selected namespace (and the associated namespace if checked)"><input type="checkbox" name="invert" value="1">&nbsp;Invert selection</label> <label class="mw-input-with-label" title="Check this box to also include the talk or subject namespace associated with the selected namespace"><input type="checkbox" name="associated" value="1">&nbsp;Associated namespace</label></td></tr><tr class="tagfilterForm"><td class="mw-label mw-tagfilter-label"><label for="tagfilter"><a href="/wiki/Special:Tags" title="Special:Tags">Tag</a> filter:</label></td><td class="mw-input"><input class="mw-tagfilter-input mw-ui-input mw-ui-input-inline" size="20" id="tagfilter" list="tagfilter-datalist" name="tagfilter"><datalist id="tagfilter-datalist"><option value="visualeditor-wikitext">2017 wikitext editor</option><option value="Abusefilter: Smiley">Abusefilter: Smiley</option><option value="ACDC">AC/DC</option><option value="OAuth CID: 6093">addshore-v1-nonowner-test [1.0]</option><option value="Adiutor">Adiutor</option><option value="advanced mobile edit">Advanced mobile edit</option><option value="OAuth CID: 7626">African German Phrasebook server v2 [1.0]</option><option value="OAuth CID: 13699">agpbv3 [1.0]</option><option value="android app edit">Android app edit</option><option value="removed source information">Anonymous or new user removed source information</option><option value="app-full-source">App full source</option><option value="app-image-caption-add">App image caption add</option><option value="app-image-caption-translate">App image caption translate</option><option value="app-image-tag-add">App image tag add</option><option value="app-suggestededit">App suggested edit</option><option value="app-talk-reply">App talk reply</option><option value="app-talk-source">App talk source</option><option value="app-talk-topic">App talk topic</option><option value="app-undo">App undo</option><option value="OAuth CID: 3695">app5 [1.0]</option><option value="ASCII text added">ASCII text added</option><option value="AWB">AWB</option><option value="OAuth CID: 4986">beta-vct [1.0]</option><option value="mw-blank">Blanking</option><option value="Blocked user editing own talk page">Blocked user editing own talk page</option><option value="BotSDC">Bot adding SDC</option><option value="OAuth CID: 8209">BUB2 - wmcloud [1.0]</option><option value="OAuth CID: 5210">BUB2-new [2.0]</option><option value="OAuth CID: 6442">bub2-uploader [1.0]</option><option value="OAuth CID: 13141">CampWiz-Categorizer-Dev [1.0]</option><option value="OAuth CID: 13118">CampWiz-Categorizer-Localhost [1.0]</option><option value="Cat-a-lot">Cat-a-lot</option><option value="convenient-discussions">CD</option><option value="OAuth CID: 1313">commons localhost testing [1.0]</option><option value="OAuth CID: 1181">Commons Mass Description (test local) [1.0]</option><option value="OAuth CID: 821">Commons mass description filler [1.2]</option><option value="OAuth CID: 1835">commonshelper [1.0]</option><option value="community configuration">Community Configuration</option><option value="OAuth CID: 7093">Como [2.1]</option><option value="OAuth CID: 6609">Como allauth test [1.0]</option><option value="abusefilter-condition-limit">condition limit reached</option><option value="mw-contentmodelchange">content model change</option><option value="OAuth CID: 18">CropCrop [1.0]</option><option value="OAuth CID: 64">CropTool</option><option value="OAuth CID: 19">CropTool [1.0]</option><option value="OAuth CID: 27">CropTool [1.1]</option><option value="OAuth CID: 593">CropTool [1.4]</option><option value="OAuth CID: 1784">CropTool [1.5]</option><option value="OAuth CID: 6969">CropTool testing [1.0]</option><option value="cross-wiki-upload">Cross-wiki upload</option><option value="OAuth CID: 128">Debug Flickipedia [1.0]</option><option value="DepictAssist">DepictAssist</option><option value="OAuth CID: 2459">Depictor [0.1]</option><option value="OAuth CID: 1904">DiBabel [1.2]</option><option value="disambiguator-link-added">Disambiguation links</option><option value="discussiontools">discussiontools (hidden tag)</option><option value="discussiontools-added-comment">discussiontools-added-comment (hidden tag)</option><option value="discussiontools-source">discussiontools-source (hidden tag)</option><option value="discussiontools-source-enhanced">discussiontools-source-enhanced (hidden tag)</option><option value="discussiontools-visual">discussiontools-visual (hidden tag)</option><option value="OAuth CID: 99">Dispenser [1.0]</option><option value="OAuth CID: 410">Dispenser [2.4]</option><option value="OAuth CID: 1842">drawshield [1.0]</option><option value="OAuth CID: 1510">dtz [1.1]</option><option value="OAuth CID: 1855">dtz [1.2]</option><option value="editcheck-references-activated">Edit Check (references) activated</option><option value="editcheck-reference-decline-irrelevant">Edit Check (references) declined (irrelevant)</option><option value="editcheck-newreference">editcheck-newreference (hidden tag)</option><option value="OAuth CID: 2499">EditGroups for Commons [1.0]</option><option value="editing policy page">Editing policy page</option><option value="editing POTD or MOTD description">editing POTD or MOTD description</option><option value="emoji">Emoji</option><option value="EnhancedStash">EnhancedStash</option><option value="FileImporter">FileImporter</option><option value="OAuth CID: 1770">FIST [1.1]</option><option value="OAuth CID: 5738">Flcikypedia 2023-10-10 [1.0]</option><option value="OAuth CID: 1285">Flickommons [1.0]</option><option value="uploadwizard-flickr">Flickr</option><option value="OAuth CID: 1327">Flickr Dashboard [1.0]</option><option value="flickr2commons">flickr2commons</option><option value="OAuth CID: 1771">flickr2commons [1.0]</option><option value="OAuth CID: 9374">flickr2commons-ng [1.0]</option><option value="OAuth CID: 6125">Flickypedia (demo for Montevideo) [1.0]</option><option value="OAuth CID: 6361">Flickypedia [1.0]</option><option value="OAuth CID: 1910">GDrive to Commons Uploader [1.6]</option><option value="OAuth CID: 1838">geograph2commons [1.0]</option><option value="OAuth CID: 927">glam2commons [1.0]</option><option value="OAuth CID: 1337">Google drive to Wikimedia Commons [1.4]</option><option value="OAuth CID: 1738">Google drive to Wikimedia Commons [1.5]</option><option value="OAuth CID: 3060">Google Photos Commons Upload [2.0]</option><option value="HotCat">HotCat</option><option value="OAuth CID: 2343">IA Upload [2.0]</option><option value="OAuth CID: 40">ia-upload [0.2]</option><option value="OAuth CID: 772">ia-upload [1.1]</option><option value="OAuth CID: 1762">ia-upload [2.0]</option><option value="OAuth CID: 1804">IABotManagementConsole [1.2]</option><option value="OAuth CID: 8989">Image Annotation Tool [1.0]</option><option value="OAuth CID: 2039">Image Annotator [1.0]</option><option value="image notes by new users">Image notes by new users</option><option value="OAuth CID: 1140">import-500px [0.1]</option><option value="fileimporter-imported">Imported with FileImporter</option><option value="ios app edit">iOS app edit</option><option value="OAuth CID: 2450">Isa [2.2]</option><option value="OAuth CID: 1561">ISA Local Dev 2 (TEST) [1.0]</option><option value="OAuth CID: 3006">Isa-dev [1.0]</option><option value="OAuth CID: 1558">Isa-tool (dev-personal) [1.0]</option><option value="OAuth CID: 1393">Isa-tool [1.0]</option><option value="OAuth CID: 8356">KenBurnsEffect tool [1.0]</option><option value="OAuth CID: 8393">KenBurnsEffect tool [1.1]</option><option value="Large upload by a new user">Large upload by a new user</option><option value="OAuth CID: 42">LCA Tools [0.1]</option><option value="OAuth CID: 60">LCA Tools [1.0]</option><option value="OAuth CID: 76">LCA Tools [1.5]</option><option value="License review by non-Image-reviewers">License review by non-image-reviewers</option><option value="OAuth CID: 1077">Lingua Libre [2.0]</option><option value="OAuth CID: 1735">Lingua Libre [2.2]</option><option value="OAuth CID: 2231">Lingua Libre [2.30]</option><option value="OAuth CID: 13275">Lingua Libre [3.2]</option><option value="OAuth CID: 5971">Lingua Libre Development - localhost 2 [1.0]</option><option value="OAuth CID: 13063">Lingualibre [4.0]</option><option value="OAuth CID: 496">locator-tool [2.1]</option><option value="OAuth CID: 1857">locator-tool [3.0]</option><option value="Logo with questionable license">Logo with questionable license</option><option value="manual deletion request by new user">Manual deletion request</option><option value="mw-manual-revert">Manual revert</option><option value="massmessage-delivery">MassMessage delivery</option><option value="OAuth CID: 1614">Media Data Verification Tool (Dev 2) [1.0]</option><option value="OAuth CID: 1993">Media Data Verification Tool [1.0]</option><option value="OAuth CID: 968">merge2pdf [0.3]</option><option value="meta spam id">meta spam id</option><option value="mobile app edit">Mobile app edit</option><option value="mobile edit">Mobile edit</option><option value="mobile web edit">Mobile web edit</option><option value="fileimporter">Modified by FileImporter</option><option value="OAuth CID: 926">monumental-upload-https [1.5]</option><option value="OAuth CID: 931">monumental-wlm [1.5]</option><option value="OAuth CID: 359">mpaatools [1.3]</option><option value="OAuth CID: 1314">Musikverket Description Translations [1.0]</option><option value="rillke-mw-js-bot">MwJSBot.js (Software by Rillke)</option><option value="OAuth CID: 8283">NCC2Commons1 [1.0]</option><option value="mw-new-redirect">New redirect</option><option value="discussiontools-newtopic">New topic</option><option value="New user rapidly uploading files">New user rapidly uploading files</option><option value="OAuth CID: 1190">NOA Upload Tool [1.0]</option><option value="OAuth CID: 1349">Nordic Museum Depicts [1.0]</option><option value="OAuth CID: 1700">Noun Project Uploader (localhost) [1.0]</option><option value="OAuth CID: 1975">Noun Project Uploader (localhost) [3.0]</option><option value="nuke">Nuke</option><option value="OAuth CID: 67">OAuth Uploader</option><option value="openrefine">openrefine</option><option value="openrefine-3.6">openrefine-3.6</option><option value="openrefine-3.7">openrefine-3.7</option><option value="openrefine-3.8">openrefine-3.8</option><option value="OAuth CID: 8754">OSM-Zoom Tool [1.0]</option><option value="OUploadForm">OUploadForm (hidden tag)</option><option value="Overwriting artwork">Overwriting artwork</option><option value="OAuth CID: 9443">OWIDImporter [1.0]</option><option value="OAuth CID: 8698">ParliamentDiagram [1.0]</option><option value="OAuth CID: 538">parliamentdiagram [1.1]</option><option value="OAuth CID: 8472">ParliamentDiagram-Beta [1.2]</option><option value="OAuth CID: 1696">PatrAll [1.0]</option><option value="OAuth CID: 429">PAWS [1.2]</option><option value="OAuth CID: 3711">PAWS [2.1]</option><option value="OAuth CID: 4664">paws [2.2]</option><option value="PermissionOTRS">PermissionVRTS</option><option value="OAuth CID: 10974">PicStalker Local [2.0]</option><option value="possible vandalism">Possible vandalism</option><option value="possibly out of scope">possibly out of scope</option><option value="OAuth CID: 4182">POTY admin [1.0]</option><option value="poty-script">POTY helper script</option><option value="OAuth CID: 1366">QR Code Generator [1.0]</option><option value="OAuth CID: 1744">QR Code Generator [2.0]</option><option value="OAuth CID: 2394">QR Code Generator [3.0]</option><option value="OAuth CID: 1277">QuickCategories [1.0]</option><option value="OAuth CID: 1703">QuickCategories [1.1]</option><option value="OAuth CID: 1351">QuickStatements [1.5]</option><option value="OAuth CID: 1776">quickstatements [2.0]</option><option value="mw-recreated">Recreated</option><option value="mw-changed-redirect-target">Redirect target changed</option><option value="removal of duplicates">Removal of duplicates</option><option value="Removal of ticket permission">Removal of ticket permission</option><option value="mw-removed-redirect">Removed redirect</option><option value="removing information template">Removing information template</option><option value="RenameLink">RenameLink</option><option value="mw-replace">Replaced</option><option value="discussiontools-reply">Reply</option><option value="mw-reverted">Reverted</option><option value="mw-rollback">Rollback</option><option value="RotateLink">RotateLink</option><option value="OAuth CID: 737">samwilson-ia-upload [1.0]</option><option value="OAuth CID: 5710">Savica 2, now with file uploads [1.0]</option><option value="OAuth CID: 5731">Savica 2023-10-09a [1.0]</option><option value="mw-server-side-upload">Server-side upload</option><option value="Short caption">Short file caption</option><option value="OAuth CID: 864">sibutest [1.2]</option><option value="OAuth CID: 913">sibutest [2.4]</option><option value="OAuth CID: 933">sibutest [3.0]</option><option value="small image note">Small image note</option><option value="OAuth CID: 6972">soda-test-croptool-toolforge-test [0.0]</option><option value="OAuth CID: 1932">Staging.ajapaik.ee [1.0]</option><option value="apps-suggested-edits">Suggested Edits edit</option><option value="sunflower">Sunflower</option><option value="OAuth CID: 1268">SVG Translate [1.2]</option><option value="OAuth CID: 1404">SVG Translate [1.3]</option><option value="OAuth CID: 1732">SVG Translate [1.4]</option><option value="OAuth CID: 116">SVGTranslate [1.0]</option><option value="OAuth CID: 1188">SWViewer [1.0]</option><option value="OAuth CID: 1261">SWViewer [1.2]</option><option value="OAuth CID: 1352">SWViewer [1.3]</option><option value="OAuth CID: 1805">SWViewer [1.4]</option><option value="OAuth CID: 6365">SWViewer [1.6]</option><option value="OAuth CID: 951">Takedown Tools [1.0]</option><option value="template removed">template removed</option><option value="OAuth CID: 7699">test-v2-agpb [1.0]</option><option value="OAuth CID: 1281">TestApp [1.0]</option><option value="OAuth CID: 2951">TestOauthWikiMI [7.0]</option><option value="OAuth CID: 3154">TestOauthWikiMI [8.0]</option><option value="OAuth CID: 1901">Thenoun Uploader [1.0]</option><option value="Ticket permission added by non-VRT member">Ticket permission added by non-VRT member</option><option value="OAuth CID: 1229">Tracker (localhost) [1.3]</option><option value="OAuth CID: 1317">Tracker (localhost) [1.4]</option><option value="OAuth CID: 1322">Tracker (test) [1.3]</option><option value="OAuth CID: 1231">Tracker [1.1]</option><option value="OAuth CID: 1321">Tracker [1.3]</option><option value="OAuth CID: 1872">Tracker [1.4]</option><option value="translate-translation-pages">translate-translation-pages (hidden tag)</option><option value="translation with only definition">translation with only definition</option><option value="twinkle">Twinkle</option><option value="OAuth CID: 1372">Twitter to Commons [1.0]</option><option value="OAuth CID: 1378">Twitter to Commons [1.1]</option><option value="mw-undo">Undo</option><option value="uploadwizard">uploadwizard (hidden tag)</option><option value="OAuth CID: 1839">url2commons [1.0]</option><option value="OAuth CID: 157">Video Editing Server - Cloud9 [1.0]</option><option value="OAuth CID: 163">Video Editing Server - Production [1.0]</option><option value="OAuth CID: 1424">video-cut-tool-back-end [3.1]</option><option value="OAuth CID: 1425">video-cut-tool-back-end [3.2]</option><option value="OAuth CID: 394">video2commons</option><option value="OAuth CID: 392">video2commons [0.2]</option><option value="OAuth CID: 8887">video2commons-test [1.0]</option><option value="OAuth CID: 3599">video_cutting [1.0]</option><option value="OAuth CID: 72">Videoconvert [0.2]</option><option value="OAuth CID: 81">Videoconvert [0.3]</option><option value="OAuth CID: 3795">VideocutTool [1.0]</option><option value="OAuth CID: 13623">VideoCutTool [1.0]</option><option value="OAuth CID: 1626">VideoCutTool [2.6]</option><option value="OAuth CID: 2937">videocuttool [5.0]</option><option value="OAuth CID: 2938">videocuttool [7]</option><option value="OAuth CID: 4651">VideoCutTool development [0.1]</option><option value="OAuth CID: 4185">videocuttool-test-2 [1.0]</option><option value="OAuth CID: 2551">VideoCutTools - Beta [1.0]</option><option value="OAuth CID: 2573">VideoCutTools - oauth2 [1.0]</option><option value="OAuth CID: 2195">VideoCutTools [1.0]</option><option value="OAuth CID: 1164">Videowiki [1.0]</option><option value="OAuth CID: 1165">Videowiki [1.1]</option><option value="OAuth CID: 1192">Videowiki [1.2]</option><option value="OAuth CID: 1241">Videowiki [1.6]</option><option value="OAuth CID: 2718">Videowiki [2.2]</option><option value="OAuth CID: 1303">VideowikiLocal [1.1]</option><option value="OAuth CID: 1329">VideowikiLocal [1.2]</option><option value="OAuth CID: 1692">VideowikiLocal [1.3]</option><option value="visualeditor">Visual edit</option><option value="visualeditor-switched">Visual edit: Switched</option><option value="VisualFileChange">VisualFileChange</option><option value="OAuth CID: 378">Widar [1.4]</option><option value="OAuth CID: 2576">Wiki Commons uploader [1.0]</option><option value="OAuth CID: 2928">Wiki Loves Brasil [1.0]</option><option value="OAuth CID: 2386">Wiki Loves Monuments Italia photo upload app [2.0.1]</option><option value="OAuth CID: 8214">Wiki Loves Monuments tool for improving data [1.0]</option><option value="OAuth CID: 2322">Wiki Museu do Ipiranga - Onde o objeto é usado? [1.0]</option><option value="OAuth CID: 2404">Wiki Museu do Ipiranga - Para que serve? [3.0]</option><option value="OAuth CID: 2642">wikicrowd [1.0]</option><option value="OAuth CID: 2641">wikicrowd-localhost [1.0]</option><option value="wikidata-for-firefox">Wikidata for Firefox</option><option value="OAuth CID: 1453">Wikidata Image Positions [1.1]</option><option value="OAuth CID: 1702">Wikidata Image Positions [1.2]</option><option value="OAuth CID: 5385">Wikidocumentaries demo [1.0]</option><option value="OAuth CID: 5125">wikidocumentaries_upload_test5 [2.0]</option><option value="wikieditor">wikieditor (hidden tag)</option><option value="OAuth CID: 1745">Wikifile Transfer [2.0]</option><option value="OAuth CID: 2395">Wikifile Transfer [4.0]</option><option value="OAuth CID: 10649">Wikifile Transfer [5.0]</option><option value="wikilove">wikilove</option><option value="OAuth CID: 5309">wikilovesmonuments-italy-app [3.0]</option><option value="OAuth CID: 346">Wikimedia Commons Project Wikimaps Warper [2.0]</option><option value="OAuth CID: 350">Wikimedia Commons Project Wikimaps Warper [2.1]</option><option value="OAuth CID: 544">Wikimedia Commons Project Wikimaps Warper [2.2]</option><option value="OAuth CID: 619">wikishootme [1.1]</option><option value="OAuth CID: 1828">wikishootme [1.9]</option><option value="OAuth CID: 2348">Wikisource Image Uploader [1.0]</option><option value="OAuth CID: 579">WLM-IT wikigite [1.0]</option><option value="OAuth CID: 5228">work2 [1.0]</option></datalist> <span class="mw-input-with-label"><input type="checkbox" name="inverttags" value="1" id="inverttags">&nbsp;<label for="inverttags">Invert selection</label></span></td></tr><tr class="targetForm"><td class="mw-label mw-target-label">Page name:</td><td class="mw-input"><input size="40" value="File:Madafu-chopping.jpg" name="target"> <input id="showlinkedto" type="checkbox" value="1" name="showlinkedto"> <label for="showlinkedto">Show changes to pages linked to the given page instead</label> <input type="submit" value="Show"></td></tr></table><input type="hidden" value="Special:RecentChangesLinked" name="title"></form></fieldset></div><div class="mw-rcfilters-spinner"><div class="mw-rcfilters-spinner-bounce"></div></div><div class="mw-changeslist-empty">No changes during the given period match these criteria.</div><!--esi <esi:include src="/esitest-fa8a495983347898/content" /> --><noscript><img src="https://commons.wikimedia.org/wiki/Special:CentralAutoLogin/start?type=1x1&amp;usesul3=1" alt="" width="1" height="1" style="border: none; position: absolute;"></noscript>
<div class="printfooter" data-nosnippet="">Retrieved from "<a dir="ltr" href="https://commons.wikimedia.org/wiki/Special:RecentChangesLinked/File:Madafu-chopping.jpg">https://commons.wikimedia.org/wiki/Special:RecentChangesLinked/File:Madafu-chopping.jpg</a>"</div></div>
					<div id="catlinks" class="catlinks catlinks-allhidden" data-mw="interface"></div>
				</div>
			</main>
			
		</div>
		<div class="mw-footer-container">
			
<footer id="footer" class="mw-footer" >
	<ul id="footer-info">
</ul>

	<ul id="footer-places">
	<li id="footer-places-privacy"><a href="https://foundation.wikimedia.org/wiki/Special:MyLanguage/Policy:Privacy_policy">Privacy policy</a></li>
	<li id="footer-places-about"><a href="/wiki/Commons:Welcome">About Wikimedia Commons</a></li>
	<li id="footer-places-disclaimers"><a href="/wiki/Commons:General_disclaimer">Disclaimers</a></li>
	<li id="footer-places-wm-codeofconduct"><a href="https://foundation.wikimedia.org/wiki/Special:MyLanguage/Policy:Universal_Code_of_Conduct">Code of Conduct</a></li>
	<li id="footer-places-developers"><a href="https://developer.wikimedia.org">Developers</a></li>
	<li id="footer-places-statslink"><a href="https://stats.wikimedia.org/#/commons.wikimedia.org">Statistics</a></li>
	<li id="footer-places-cookiestatement"><a href="https://foundation.wikimedia.org/wiki/Special:MyLanguage/Policy:Cookie_statement">Cookie statement</a></li>
	<li id="footer-places-mobileview"><a href="//commons.m.wikimedia.org/w/index.php?title=Special:RecentChangesLinked/File:Madafu-chopping.jpg&amp;mobileaction=toggle_view_mobile" class="noprint stopMobileRedirectToggle">Mobile view</a></li>
</ul>

	<ul id="footer-icons" class="noprint">
	<li id="footer-copyrightico"><a href="https://www.wikimedia.org/" class="cdx-button cdx-button--fake-button cdx-button--size-large cdx-button--fake-button--enabled"><picture><source media="(min-width: 500px)" srcset="/static/images/footer/wikimedia-button.svg" width="84" height="29"><img src="/static/images/footer/wikimedia.svg" width="25" height="25" alt="Wikimedia Foundation" lang="en" loading="lazy"></picture></a></li>
	<li id="footer-poweredbyico"><a href="https://www.mediawiki.org/" class="cdx-button cdx-button--fake-button cdx-button--size-large cdx-button--fake-button--enabled"><picture><source media="(min-width: 500px)" srcset="/w/resources/assets/poweredby_mediawiki.svg" width="88" height="31"><img src="/w/resources/assets/mediawiki_compact.svg" alt="Powered by MediaWiki" lang="en" width="25" height="25" loading="lazy"></picture></a></li>
</ul>

</footer>

		</div>
	</div> 
</div> 
<div class="vector-header-container vector-sticky-header-container no-font-mode-scale">
	<div id="vector-sticky-header" class="vector-sticky-header">
		<div class="vector-sticky-header-start">
			<div class="vector-sticky-header-icon-start vector-button-flush-left vector-button-flush-right" aria-hidden="true">
				<button class="cdx-button cdx-button--weight-quiet cdx-button--icon-only vector-sticky-header-search-toggle" tabindex="-1" data-event-name="ui.vector-sticky-search-form.icon"><span class="vector-icon mw-ui-icon-search mw-ui-icon-wikimedia-search"></span>

<span>Search</span>
			</button>
		</div>
			
		<div role="search" class="vector-search-box-vue vector-search-box">
			<div class="vector-typeahead-search-container">
				<div class="cdx-typeahead-search">
					<form action="/w/index.php" id="vector-sticky-search-form" class="cdx-search-input cdx-search-input--has-end-button">
						<div  class="cdx-search-input__input-wrapper"  data-search-loc="header-moved">
							<div class="cdx-text-input cdx-text-input--has-start-icon">
								<input
									class="cdx-text-input__input mw-searchInput" autocomplete="off"
									
									type="search" name="search" placeholder="Search Wikimedia Commons">
								<span class="cdx-text-input__icon cdx-text-input__start-icon"></span>
							</div>
							<input type="hidden" name="title" value="Special:MediaSearch">
						</div>
						<button class="cdx-button cdx-search-input__end-button">Search</button>
					</form>
				</div>
			</div>
		</div>
		<div class="vector-sticky-header-context-bar">
				<div class="vector-sticky-header-context-bar-primary" aria-hidden="true" >Related changes</div>
			</div>
		</div>
		<div class="vector-sticky-header-end" aria-hidden="true">
			<div class="vector-sticky-header-icons">
				<a href="#" class="cdx-button cdx-button--fake-button cdx-button--fake-button--enabled cdx-button--weight-quiet cdx-button--icon-only" id="ca-talk-sticky-header" tabindex="-1" data-event-name="talk-sticky-header"><span class="vector-icon mw-ui-icon-speechBubbles mw-ui-icon-wikimedia-speechBubbles"></span>

<span></span>
			</a>
			<a href="#" class="cdx-button cdx-button--fake-button cdx-button--fake-button--enabled cdx-button--weight-quiet cdx-button--icon-only" id="ca-subject-sticky-header" tabindex="-1" data-event-name="subject-sticky-header"><span class="vector-icon mw-ui-icon-article mw-ui-icon-wikimedia-article"></span>

<span></span>
			</a>
			<a href="#" class="cdx-button cdx-button--fake-button cdx-button--fake-button--enabled cdx-button--weight-quiet cdx-button--icon-only" id="ca-history-sticky-header" tabindex="-1" data-event-name="history-sticky-header"><span class="vector-icon mw-ui-icon-wikimedia-history mw-ui-icon-wikimedia-wikimedia-history"></span>

<span></span>
			</a>
			<a href="#" class="cdx-button cdx-button--fake-button cdx-button--fake-button--enabled cdx-button--weight-quiet cdx-button--icon-only mw-watchlink" id="ca-watchstar-sticky-header" tabindex="-1" data-event-name="watch-sticky-header"><span class="vector-icon mw-ui-icon-wikimedia-star mw-ui-icon-wikimedia-wikimedia-star"></span>

<span></span>
			</a>
			<a href="#" class="cdx-button cdx-button--fake-button cdx-button--fake-button--enabled cdx-button--weight-quiet cdx-button--icon-only" id="ca-edit-sticky-header" tabindex="-1" data-event-name="wikitext-edit-sticky-header"><span class="vector-icon mw-ui-icon-wikimedia-wikiText mw-ui-icon-wikimedia-wikimedia-wikiText"></span>

<span></span>
			</a>
			<a href="#" class="cdx-button cdx-button--fake-button cdx-button--fake-button--enabled cdx-button--weight-quiet cdx-button--icon-only" id="ca-ve-edit-sticky-header" tabindex="-1" data-event-name="ve-edit-sticky-header"><span class="vector-icon mw-ui-icon-wikimedia-edit mw-ui-icon-wikimedia-wikimedia-edit"></span>

<span></span>
			</a>
			<a href="#" class="cdx-button cdx-button--fake-button cdx-button--fake-button--enabled cdx-button--weight-quiet cdx-button--icon-only" id="ca-viewsource-sticky-header" tabindex="-1" data-event-name="ve-edit-protected-sticky-header"><span class="vector-icon mw-ui-icon-wikimedia-editLock mw-ui-icon-wikimedia-wikimedia-editLock"></span>

<span></span>
			</a>
		</div>
			<div class="vector-sticky-header-buttons">
				<a href="#" class="cdx-button cdx-button--fake-button cdx-button--fake-button--enabled cdx-button--weight-quiet cdx-button--action-progressive" id="ca-addsection-sticky-header" tabindex="-1" data-event-name="addsection-sticky-header"><span class="vector-icon mw-ui-icon-speechBubbleAdd-progressive mw-ui-icon-wikimedia-speechBubbleAdd-progressive"></span>

<span>Add topic</span>
			</a>
		</div>
			<div class="vector-sticky-header-icon-end">
				<div class="vector-user-links">
				</div>
			</div>
		</div>
	</div>
</div>
<div class="mw-portlet mw-portlet-dock-bottom emptyPortlet" id="p-dock-bottom">
	<ul>
		
	</ul>
</div>
<script>(RLQ=window.RLQ||[]).push(function(){mw.config.set({"wgHostname":"mw-web.eqiad.main-7b48b5fb74-zw5tl","wgBackendResponseTime":231});});</script>
</body>
</html>