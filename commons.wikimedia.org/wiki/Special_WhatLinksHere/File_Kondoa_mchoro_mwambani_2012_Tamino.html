<!DOCTYPE html>
<html class="client-nojs vector-feature-language-in-header-disabled vector-feature-language-in-main-page-header-disabled vector-feature-page-tools-pinned-disabled vector-feature-toc-pinned-clientpref-1 vector-feature-main-menu-pinned-disabled vector-feature-limited-width-clientpref-1 vector-feature-limited-width-content-disabled vector-feature-custom-font-size-clientpref--excluded vector-feature-appearance-pinned-clientpref-1 vector-feature-night-mode-enabled skin-theme-clientpref-day vector-sticky-header-enabled vector-toc-not-available" lang="en" dir="ltr">

<!-- Mirrored from commons.wikimedia.org/wiki/Special:WhatLinksHere/File:Kondoa_mchoro_mwambani_2012_Tamino.jpg by HTTrack Website Copier/3.x [XR&CO'2014], Sun, 03 Aug 2025 15:20:49 GMT -->
<!-- Added by HTTrack --><meta http-equiv="content-type" content="text/html;charset=UTF-8" /><!-- /Added by HTTrack -->
<head>
<meta charset="UTF-8">
<title>Pages that link to "File:Kondoa mchoro mwambani 2012 Tamino.jpg" - Wikimedia Commons</title>
<script>(function(){var className="client-js vector-feature-language-in-header-disabled vector-feature-language-in-main-page-header-disabled vector-feature-page-tools-pinned-disabled vector-feature-toc-pinned-clientpref-1 vector-feature-main-menu-pinned-disabled vector-feature-limited-width-clientpref-1 vector-feature-limited-width-content-disabled vector-feature-custom-font-size-clientpref--excluded vector-feature-appearance-pinned-clientpref-1 vector-feature-night-mode-enabled skin-theme-clientpref-day vector-sticky-header-enabled vector-toc-not-available";var cookie=document.cookie.match(/(?:^|; )commonswikimwclientpreferences=([^;]+)/);if(cookie){cookie[1].split('%2C').forEach(function(pref){className=className.replace(new RegExp('(^| )'+pref.replace(/-clientpref-\w+$|[^\w-]+/g,'')+'-clientpref-\\w+( |$)'),'$1'+pref+'$2');});}document.documentElement.className=className;}());RLCONF={"wgBreakFrames":true,"wgSeparatorTransformTable":["",""],"wgDigitTransformTable":["",""],"wgDefaultDateFormat":"dmy","wgMonthNames":["","January","February","March","April","May","June","July","August","September","October","November","December"],"wgRequestId":"ff8d9cd1-f6d9-40f8-bb53-b49b39daf478","wgCanonicalNamespace":"Special","wgCanonicalSpecialPageName":"Whatlinkshere","wgNamespaceNumber":-1,"wgPageName":"Special:WhatLinksHere/File:Kondoa_mchoro_mwambani_2012_Tamino.jpg","wgTitle":"WhatLinksHere/File:Kondoa mchoro mwambani 2012 Tamino.jpg","wgCurRevisionId":0,"wgRevisionId":0,"wgArticleId":0,"wgIsArticle":false,"wgIsRedirect":false,"wgAction":"view","wgUserName":null,"wgUserGroups":["*"],"wgCategories":[],"wgPageViewLanguage":"en","wgPageContentLanguage":"en","wgPageContentModel":"wikitext","wgRelevantPageName":"File:Kondoa_mchoro_mwambani_2012_Tamino.jpg","wgRelevantArticleId":33335664,"wgIsProbablyEditable":false,"wgRelevantPageIsProbablyEditable":true,"wgNoticeProject":"commons","wgCiteReferencePreviewsActive":true,"wgMediaViewerOnClick":true,"wgMediaViewerEnabledByDefault":false,"wgVisualEditor":{"pageLanguageCode":"en","pageLanguageDir":"ltr","pageVariantFallbacks":"en"},"wgMFDisplayWikibaseDescriptions":{"search":true,"watchlist":true,"tagline":true,"nearby":true},"wgWMESchemaEditAttemptStepOversample":false,"wgWMEPageLength":0,"wgMetricsPlatformUserExperiments":{"active_experiments":[],"overrides":[],"enrolled":[],"assigned":[],"subject_ids":[],"sampling_units":[]},"upwizPropertyTitles":{"P180":"Main subjects visible in this work"},"upwizPropertyPlaceholders":{"P180":"e.g., Angolan giraffe"},"upwizPropertyCopyLabels":{"P180":"Main subjects"},"wgEditSubmitButtonLabelPublish":true,"wgULSPosition":"personal","wgULSisCompactLinksEnabled":true,"wgVector2022LanguageInHeader":false,"wgULSisLanguageSelectorEmpty":true,"wbmiDefaultProperties":["P180"],"wbmiPropertyTitles":{"P180":"Items portrayed in this file"},"wbmiPropertyTypes":{"P180":"wikibase-item"},"wbmiRepoApiUrl":"/w/api.php","wbmiHelpUrls":{"P180":"https://commons.wikimedia.org/wiki/Special:MyLanguage/Commons:Depicts"},"wbmiExternalEntitySearchBaseUri":"https://www.wikidata.org/w/api.php","wbmiSupportedDataTypes":["wikibase-item","string","quantity","time","monolingualtext","external-id","globe-coordinate","url"],"wgCheckUserClientHintsHeadersJsApi":["brands","architecture","bitness","fullVersionList","mobile","model","platform","platformVersion"]};
RLSTATE={"ext.gadget.Long-Image-Names-in-Categories":"ready","ext.globalCssJs.user.styles":"ready","site.styles":"ready","user.styles":"ready","ext.globalCssJs.user":"ready","user":"ready","user.options":"loading","mediawiki.helplink":"ready","mediawiki.special":"ready","oojs-ui-core.styles":"ready","oojs-ui.styles.indicators":"ready","mediawiki.widgets.styles":"ready","oojs-ui-core.icons":"ready","mediawiki.htmlform.ooui.styles":"ready","mediawiki.htmlform.styles":"ready","mediawiki.codex.messagebox.styles":"ready","skins.vector.search.codex.styles":"ready","skins.vector.styles":"ready","skins.vector.icons":"ready","ext.wikimediamessages.styles":"ready","ext.visualEditor.desktopArticleTarget.noscript":"ready","ext.uls.pt":"ready"};RLPAGEMODULES=["ext.xLab","mediawiki.htmlform","mediawiki.htmlform.ooui","mediawiki.widgets","site","mediawiki.page.ready","https://commons.wikimedia.org/wiki/Special:WhatLinksHere/skins.vector.js","ext.centralNotice.geoIP","ext.gadget.Slideshow","ext.gadget.ZoomViewer","ext.gadget.CollapsibleTemplates","ext.gadget.Stockphoto","ext.gadget.WatchlistNotice","ext.gadget.AjaxQuickDelete","ext.gadget.WikiMiniAtlas","ext.gadget.LanguageSelect","ext.gadget.PictureOfTheYearEnhancements","ext.urlShortener.toolbar","ext.centralauth.centralautologin","ext.visualEditor.desktopArticleTarget.init","ext.visualEditor.targetLoader","ext.echo.centralauth","ext.eventLogging","ext.wikimediaEvents","ext.wikimediaEvents.wikibase","ext.navigationTiming","ext.uls.compactlinks","ext.uls.interface","ext.checkUser.clientHints"];</script>
<script>(RLQ=window.RLQ||[]).push(function(){mw.loader.impl(function(){return["user.options@12s5i",function($,jQuery,require,module){mw.user.tokens.set({"patrolToken":"+\\","watchToken":"+\\","csrfToken":"+\\"});
}];});});</script>
<link rel="stylesheet" href="https://commons.wikimedia.org/w/load.php?lang=en&amp;modules=ext.uls.pt%7Cext.visualEditor.desktopArticleTarget.noscript%7Cext.wikimediamessages.styles%7Cmediawiki.codex.messagebox.styles%7Cmediawiki.helplink%2Cspecial%7Cmediawiki.htmlform.ooui.styles%7Cmediawiki.htmlform.styles%7Cmediawiki.widgets.styles%7Coojs-ui-core.icons%2Cstyles%7Coojs-ui.styles.indicators%7Cskins.vector.icons%2Cstyles%7Cskins.vector.search.codex.styles&amp;only=styles&amp;skin=vector-2022">
<script async="" src="https://commons.wikimedia.org/w/load.php?lang=en&amp;modules=startup&amp;only=scripts&amp;raw=1&amp;skin=vector-2022"></script>
<meta name="ResourceLoaderDynamicStyles" content="">
<link rel="stylesheet" href="https://commons.wikimedia.org/w/load.php?lang=en&amp;modules=ext.gadget.Long-Image-Names-in-Categories&amp;only=styles&amp;skin=vector-2022">
<link rel="stylesheet" href="https://commons.wikimedia.org/w/load.php?lang=en&amp;modules=site.styles&amp;only=styles&amp;skin=vector-2022">
<meta name="generator" content="MediaWiki 1.45.0-wmf.12">
<meta name="referrer" content="origin">
<meta name="referrer" content="origin-when-cross-origin">
<meta name="robots" content="noindex,nofollow,max-image-preview:standard">
<meta name="format-detection" content="telephone=no">
<meta name="viewport" content="width=1120">
<meta property="og:title" content="Pages that link to &quot;File:Kondoa mchoro mwambani 2012 Tamino.jpg&quot; - Wikimedia Commons">
<meta property="og:type" content="website">
<link rel="alternate" media="only screen and (max-width: 640px)" href="http://commons.m.wikimedia.org/wiki/Special:WhatLinksHere/File:Kondoa_mchoro_mwambani_2012_Tamino.jpg">
<link rel="apple-touch-icon" href="https://commons.wikimedia.org/static/apple-touch/commons.png">
<link rel="icon" href="https://commons.wikimedia.org/static/favicon/commons.ico">
<link rel="search" type="application/opensearchdescription+xml" href="https://commons.wikimedia.org/w/rest.php/v1/search" title="Wikimedia Commons">
<link rel="EditURI" type="application/rsd+xml" href="http://commons.wikimedia.org/w/api.php?action=rsd">
<link rel="canonical" href="https://commons.wikimedia.org/wiki/Special:WhatLinksHere/File:Kondoa_mchoro_mwambani_2012_Tamino.jpg">
<link rel="license" href="https://creativecommons.org/licenses/by-sa/4.0/">
<link rel="alternate" type="application/atom+xml" title="Wikimedia Commons Atom feed" href="https://commons.wikimedia.org/w/index.php?title=Special:RecentChanges&amp;feed=atom">
<link rel="dns-prefetch" href="https://commons.wikimedia.org/wiki/Special:WhatLinksHere/auth.wikimedia.org">
</head>
<body class="skin--responsive skin-vector skin-vector-search-vue mediawiki ltr sitedir-ltr mw-hide-empty-elt ns--1 ns-special mw-special-Whatlinkshere page-Special_WhatLinksHere_File_Kondoa_mchoro_mwambani_2012_Tamino_jpg rootpage-Special_WhatLinksHere_File_Kondoa_mchoro_mwambani_2012_Tamino_jpg skin-vector-2022 action-view"><a class="mw-jump-link" href="#bodyContent">Jump to content</a>
<div class="vector-header-container">
	<header class="vector-header mw-header no-font-mode-scale">
		<div class="vector-header-start">
			<nav class="vector-main-menu-landmark" aria-label="Site">
				
<div id="vector-main-menu-dropdown" class="vector-dropdown vector-main-menu-dropdown vector-button-flush-left vector-button-flush-right"  title="Main menu" >
	<input type="checkbox" id="vector-main-menu-dropdown-checkbox" role="button" aria-haspopup="true" data-event-name="ui.dropdown-vector-main-menu-dropdown" class="vector-dropdown-checkbox "  aria-label="Main menu"  >
	<label id="vector-main-menu-dropdown-label" for="vector-main-menu-dropdown-checkbox" class="vector-dropdown-label cdx-button cdx-button--fake-button cdx-button--fake-button--enabled cdx-button--weight-quiet cdx-button--icon-only " aria-hidden="true"  ><span class="vector-icon mw-ui-icon-menu mw-ui-icon-wikimedia-menu"></span>

<span class="vector-dropdown-label-text">Main menu</span>
	</label>
	<div class="vector-dropdown-content">


				<div id="vector-main-menu-unpinned-container" class="vector-unpinned-container">
		
<div id="vector-main-menu" class="vector-main-menu vector-pinnable-element">
	<div
	class="vector-pinnable-header vector-main-menu-pinnable-header vector-pinnable-header-unpinned"
	data-feature-name="main-menu-pinned"
	data-pinnable-element-id="vector-main-menu"
	data-pinned-container-id="vector-main-menu-pinned-container"
	data-unpinned-container-id="vector-main-menu-unpinned-container"
>
	<div class="vector-pinnable-header-label">Main menu</div>
	<button class="vector-pinnable-header-toggle-button vector-pinnable-header-pin-button" data-event-name="pinnable-header.vector-main-menu.pin">move to sidebar</button>
	<button class="vector-pinnable-header-toggle-button vector-pinnable-header-unpin-button" data-event-name="pinnable-header.vector-main-menu.unpin">hide</button>
</div>

	
<div id="p-navigation" class="vector-menu mw-portlet mw-portlet-navigation"  >
	<div class="vector-menu-heading">
		Navigate
	</div>
	<div class="vector-menu-content">
		
		<ul class="vector-menu-content-list">
			
			<li id="n-mainpage-description" class="mw-list-item"><a href="https://commons.wikimedia.org/wiki/Main_Page" title="Visit the main page [z]" accesskey="z"><span>Main page</span></a></li><li id="n-welcome" class="mw-list-item"><a href="https://commons.wikimedia.org/wiki/Commons:Welcome"><span>Welcome</span></a></li><li id="n-portal" class="mw-list-item"><a href="https://commons.wikimedia.org/wiki/Commons:Community_portal" title="About the project, what you can do, where to find things"><span>Community portal</span></a></li><li id="n-village-pump" class="mw-list-item"><a href="https://commons.wikimedia.org/wiki/Commons:Village_pump"><span>Village pump</span></a></li><li id="n-help" class="mw-list-item"><a href="https://commons.wikimedia.org/wiki/Special:MyLanguage/Help:Contents" title="The place to find out"><span>Help center</span></a></li>
		</ul>
		
	</div>
</div>

	
	
<div id="p-participate" class="vector-menu mw-portlet mw-portlet-participate"  >
	<div class="vector-menu-heading">
		Participate
	</div>
	<div class="vector-menu-content">
		
		<ul class="vector-menu-content-list">
			
			<li id="n-uploadbtn" class="mw-list-item"><a href="https://commons.wikimedia.org/wiki/Special:UploadWizard"><span>Upload file</span></a></li><li id="n-recentchanges" class="mw-list-item"><a href="https://commons.wikimedia.org/wiki/Special:RecentChanges" title="A list of recent changes in the wiki [r]" accesskey="r"><span>Recent changes</span></a></li><li id="n-latestfiles" class="mw-list-item"><a href="https://commons.wikimedia.org/wiki/Special:NewFiles"><span>Latest files</span></a></li><li id="n-randomimage" class="mw-list-item"><a href="https://commons.wikimedia.org/wiki/Special:Random/File" title="Load a random file [x]" accesskey="x"><span>Random file</span></a></li><li id="n-contact" class="mw-list-item"><a href="https://commons.wikimedia.org/wiki/Commons:Contact_us"><span>Contact us</span></a></li><li id="n-specialpages" class="mw-list-item"><a href="https://commons.wikimedia.org/wiki/Special:SpecialPages"><span>Special pages</span></a></li>
		</ul>
		
	</div>
</div>

	
<div id="vector-main-menu" class="vector-menu "  >
	<div class="vector-menu-heading">
		
	</div>
	<div class="vector-menu-content">
		
		<ul class="vector-menu-content-list">
			
			
		</ul>
		
	</div>
</div>

</div>

				</div>

	</div>
</div>

		</nav>
			
<a href="https://commons.wikimedia.org/wiki/Main_Page" class="mw-logo">
	<img class="mw-logo-icon" src="https://commons.wikimedia.org/static/images/icons/commonswiki.svg" alt="" aria-hidden="true" height="50" width="50">
	<span class="mw-logo-container skin-invert">
		<img class="mw-logo-wordmark" alt="Wikimedia Commons" src="https://commons.wikimedia.org/static/images/mobile/copyright/commonswiki-wordmark.svg" style="width: 7.1875em; height: 2em;">
	</span>
</a>

		</div>
		<div class="vector-header-end">
			
<div id="p-search" role="search" class="vector-search-box-vue  vector-search-box-collapses vector-search-box">
	<a href="https://commons.wikimedia.org/wiki/Special:MediaSearch" class="cdx-button cdx-button--fake-button cdx-button--fake-button--enabled cdx-button--weight-quiet cdx-button--icon-only search-toggle" title="Search Wikimedia Commons [f]" accesskey="f"><span class="vector-icon mw-ui-icon-search mw-ui-icon-wikimedia-search"></span>

<span>Search</span>
	</a>
	<div class="vector-typeahead-search-container">
		<div class="cdx-typeahead-search">
			<form action="https://commons.wikimedia.org/w/index.php" id="searchform" class="cdx-search-input cdx-search-input--has-end-button">
				<div id="simpleSearch" class="cdx-search-input__input-wrapper"  data-search-loc="header-moved">
					<div class="cdx-text-input cdx-text-input--has-start-icon">
						<input
							class="cdx-text-input__input mw-searchInput" autocomplete="off"
							 type="search" name="search" placeholder="Search Wikimedia Commons" aria-label="Search Wikimedia Commons" autocapitalize="sentences" spellcheck="false" title="Search Wikimedia Commons [f]" accesskey="f" id="searchInput"
							>
						<span class="cdx-text-input__icon cdx-text-input__start-icon"></span>
					</div>
					<input type="hidden" name="title" value="Special:MediaSearch">
				</div>
				<button class="cdx-button cdx-search-input__end-button">Search</button>
			</form>
		</div>
	</div>
</div>

			<nav class="vector-user-links vector-user-links-wide" aria-label="Personal tools">
	<div class="vector-user-links-main">
	
<div id="p-vector-user-menu-preferences" class="vector-menu mw-portlet"  >
	<div class="vector-menu-content">
		
		<ul class="vector-menu-content-list">
			<li id="ca-uls" class="mw-list-item active user-links-collapsible-item"><a data-mw="interface" href="#" class="uls-trigger cdx-button cdx-button--fake-button cdx-button--fake-button--enabled cdx-button--weight-quiet"><span class="vector-icon mw-ui-icon-wikimedia-language mw-ui-icon-wikimedia-wikimedia-language"></span>

<span>English</span></a>
</li>

			
		</ul>
		
	</div>
</div>

	
<div id="p-vector-user-menu-userpage" class="vector-menu mw-portlet emptyPortlet"  >
	<div class="vector-menu-content">
		
		<ul class="vector-menu-content-list">
			
			
		</ul>
		
	</div>
</div>

	<nav class="vector-appearance-landmark" aria-label="Appearance">
		
<div id="vector-appearance-dropdown" class="vector-dropdown "  title="Change the appearance of the page&#039;s font size, width, and color" >
	<input type="checkbox" id="vector-appearance-dropdown-checkbox" role="button" aria-haspopup="true" data-event-name="ui.dropdown-vector-appearance-dropdown" class="vector-dropdown-checkbox "  aria-label="Appearance"  >
	<label id="vector-appearance-dropdown-label" for="vector-appearance-dropdown-checkbox" class="vector-dropdown-label cdx-button cdx-button--fake-button cdx-button--fake-button--enabled cdx-button--weight-quiet cdx-button--icon-only " aria-hidden="true"  ><span class="vector-icon mw-ui-icon-appearance mw-ui-icon-wikimedia-appearance"></span>

<span class="vector-dropdown-label-text">Appearance</span>
	</label>
	<div class="vector-dropdown-content">


			<div id="vector-appearance-unpinned-container" class="vector-unpinned-container">
				
			</div>
		
	</div>
</div>

	</nav>
	
<div id="p-vector-user-menu-notifications" class="vector-menu mw-portlet emptyPortlet"  >
	<div class="vector-menu-content">
		
		<ul class="vector-menu-content-list">
			
			
		</ul>
		
	</div>
</div>

	
<div id="p-vector-user-menu-overflow" class="vector-menu mw-portlet"  >
	<div class="vector-menu-content">
		
		<ul class="vector-menu-content-list">
			<li id="pt-sitesupport-2" class="user-links-collapsible-item mw-list-item user-links-collapsible-item"><a data-mw="interface" href="https://donate.wikimedia.org/?wmf_source=donate&amp;wmf_medium=sidebar&amp;wmf_campaign=commons.wikimedia.org&amp;uselang=en" class=""><span>Donate</span></a>
</li>
<li id="pt-createaccount-2" class="user-links-collapsible-item mw-list-item user-links-collapsible-item"><a data-mw="interface" href="https://commons.wikimedia.org/w/index.php?title=Special:CreateAccount&amp;returnto=Special%3AWhatLinksHere%2FFile%3AKondoa+mchoro+mwambani+2012+Tamino.jpg" title="You are encouraged to create an account and log in; however, it is not mandatory" class=""><span>Create account</span></a>
</li>
<li id="pt-login-2" class="user-links-collapsible-item mw-list-item user-links-collapsible-item"><a data-mw="interface" href="https://commons.wikimedia.org/w/index.php?title=Special:UserLogin&amp;returnto=Special%3AWhatLinksHere%2FFile%3AKondoa+mchoro+mwambani+2012+Tamino.jpg" title="You are encouraged to log in; however, it is not mandatory [o]" accesskey="o" class=""><span>Log in</span></a>
</li>

			
		</ul>
		
	</div>
</div>

	</div>
	
<div id="vector-user-links-dropdown" class="vector-dropdown vector-user-menu vector-button-flush-right vector-user-menu-logged-out"  title="More options" >
	<input type="checkbox" id="vector-user-links-dropdown-checkbox" role="button" aria-haspopup="true" data-event-name="ui.dropdown-vector-user-links-dropdown" class="vector-dropdown-checkbox "  aria-label="Personal tools"  >
	<label id="vector-user-links-dropdown-label" for="vector-user-links-dropdown-checkbox" class="vector-dropdown-label cdx-button cdx-button--fake-button cdx-button--fake-button--enabled cdx-button--weight-quiet cdx-button--icon-only " aria-hidden="true"  ><span class="vector-icon mw-ui-icon-ellipsis mw-ui-icon-wikimedia-ellipsis"></span>

<span class="vector-dropdown-label-text">Personal tools</span>
	</label>
	<div class="vector-dropdown-content">


		
<div id="p-personal" class="vector-menu mw-portlet mw-portlet-personal user-links-collapsible-item"  title="User menu" >
	<div class="vector-menu-content">
		
		<ul class="vector-menu-content-list">
			
			<li id="pt-sitesupport" class="user-links-collapsible-item mw-list-item"><a href="https://donate.wikimedia.org/?wmf_source=donate&amp;wmf_medium=sidebar&amp;wmf_campaign=commons.wikimedia.org&amp;uselang=en"><span>Donate</span></a></li><li id="pt-createaccount" class="user-links-collapsible-item mw-list-item"><a href="https://commons.wikimedia.org/w/index.php?title=Special:CreateAccount&amp;returnto=Special%3AWhatLinksHere%2FFile%3AKondoa+mchoro+mwambani+2012+Tamino.jpg" title="You are encouraged to create an account and log in; however, it is not mandatory"><span class="vector-icon mw-ui-icon-userAdd mw-ui-icon-wikimedia-userAdd"></span> <span>Create account</span></a></li><li id="pt-login" class="user-links-collapsible-item mw-list-item"><a href="https://commons.wikimedia.org/w/index.php?title=Special:UserLogin&amp;returnto=Special%3AWhatLinksHere%2FFile%3AKondoa+mchoro+mwambani+2012+Tamino.jpg" title="You are encouraged to log in; however, it is not mandatory [o]" accesskey="o"><span class="vector-icon mw-ui-icon-logIn mw-ui-icon-wikimedia-logIn"></span> <span>Log in</span></a></li>
		</ul>
		
	</div>
</div>

<div id="p-user-menu-anon-editor" class="vector-menu mw-portlet mw-portlet-user-menu-anon-editor"  >
	<div class="vector-menu-heading">
		Pages for logged out editors <a href="https://commons.wikimedia.org/wiki/Help:Introduction" aria-label="Learn more about editing"><span>learn more</span></a>
	</div>
	<div class="vector-menu-content">
		
		<ul class="vector-menu-content-list">
			
			<li id="pt-anoncontribs" class="mw-list-item"><a href="https://commons.wikimedia.org/wiki/Special:MyContributions" title="A list of edits made from this IP address [y]" accesskey="y"><span>Contributions</span></a></li><li id="pt-anontalk" class="mw-list-item"><a href="https://commons.wikimedia.org/wiki/Special:MyTalk" title="Discussion about edits from this IP address [n]" accesskey="n"><span>Talk</span></a></li>
		</ul>
		
	</div>
</div>

	
	</div>
</div>

</nav>

		</div>
	</header>
</div>
<div class="mw-page-container">
	<div class="mw-page-container-inner">
		<div class="vector-sitenotice-container">
			<div id="siteNotice"><!-- CentralNotice --></div>
		</div>
		<div class="vector-column-start">
			<div class="vector-main-menu-container">
		<div id="mw-navigation">
			<nav id="mw-panel" class="vector-main-menu-landmark" aria-label="Site">
				<div id="vector-main-menu-pinned-container" class="vector-pinned-container">
				
				</div>
		</nav>
		</div>
	</div>
</div>
		<div class="mw-content-container">
			<main id="content" class="mw-body">
				<header class="mw-body-header vector-page-titlebar no-font-mode-scale">
					<h1 id="firstHeading" class="firstHeading mw-first-heading">Pages that link to "File:Kondoa mchoro mwambani 2012 Tamino.jpg"</h1>
						<div class="mw-indicators">
		<div id="mw-indicator-mw-helplink" class="mw-indicator"><a href="https://www.mediawiki.org/wiki/Special:MyLanguage/Help:What_links_here" target="_blank" class="mw-helplink"><span class="mw-helplink-icon"></span>Help</a></div>
		</div>
</header>
				<div class="vector-page-toolbar vector-feature-custom-font-size-clientpref--excluded">
					<div class="vector-page-toolbar-container">
						<div id="left-navigation">
							<nav aria-label="Namespaces">
								
<div id="p-associated-pages" class="vector-menu vector-menu-tabs mw-portlet mw-portlet-associated-pages"  >
	<div class="vector-menu-content">
		
		<ul class="vector-menu-content-list">
			
			<li id="ca-nstab-image" class="selected vector-tab-noicon mw-list-item"><a href="https://commons.wikimedia.org/wiki/File:Kondoa_mchoro_mwambani_2012_Tamino.jpg" title="View the file page [c]" accesskey="c"><span>File</span></a></li><li id="ca-talk" class="new vector-tab-noicon mw-list-item"><a href="https://commons.wikimedia.org/w/index.php?title=File_talk:Kondoa_mchoro_mwambani_2012_Tamino.jpg&amp;action=edit&amp;redlink=1" rel="discussion" class="new" title="Discussion about the content page (page does not exist) [t]" accesskey="t"><span>Discussion</span></a></li>
		</ul>
		
	</div>
</div>

								
<div id="vector-variants-dropdown" class="vector-dropdown emptyPortlet"  >
	<input type="checkbox" id="vector-variants-dropdown-checkbox" role="button" aria-haspopup="true" data-event-name="ui.dropdown-vector-variants-dropdown" class="vector-dropdown-checkbox " aria-label="Change language variant"   >
	<label id="vector-variants-dropdown-label" for="vector-variants-dropdown-checkbox" class="vector-dropdown-label cdx-button cdx-button--fake-button cdx-button--fake-button--enabled cdx-button--weight-quiet" aria-hidden="true"  ><span class="vector-dropdown-label-text">English</span>
	</label>
	<div class="vector-dropdown-content">


					
<div id="p-variants" class="vector-menu mw-portlet mw-portlet-variants emptyPortlet"  >
	<div class="vector-menu-content">
		
		<ul class="vector-menu-content-list">
			
			
		</ul>
		
	</div>
</div>

				
	</div>
</div>

							</nav>
						</div>
						<div id="right-navigation" class="vector-collapsible">
							<nav aria-label="Views">
								
<div id="p-views" class="vector-menu vector-menu-tabs mw-portlet mw-portlet-views"  >
	<div class="vector-menu-content">
		
		<ul class="vector-menu-content-list">
			
			<li id="ca-view" class="vector-tab-noicon mw-list-item"><a href="https://commons.wikimedia.org/wiki/File:Kondoa_mchoro_mwambani_2012_Tamino.jpg"><span>Read</span></a></li><li id="ca-edit" class="vector-tab-noicon mw-list-item"><a href="https://commons.wikimedia.org/w/index.php?title=File:Kondoa_mchoro_mwambani_2012_Tamino.jpg&amp;action=edit" title="Edit this page [e]" accesskey="e"><span>Edit</span></a></li><li id="ca-history" class="vector-tab-noicon mw-list-item"><a href="https://commons.wikimedia.org/w/index.php?title=File:Kondoa_mchoro_mwambani_2012_Tamino.jpg&amp;action=history" title="Past revisions of this page [h]" accesskey="h"><span>View history</span></a></li>
		</ul>
		
	</div>
</div>

							</nav>
				
							<nav class="vector-page-tools-landmark" aria-label="Page tools">
								
<div id="vector-page-tools-dropdown" class="vector-dropdown vector-page-tools-dropdown"  >
	<input type="checkbox" id="vector-page-tools-dropdown-checkbox" role="button" aria-haspopup="true" data-event-name="ui.dropdown-vector-page-tools-dropdown" class="vector-dropdown-checkbox "  aria-label="Tools"  >
	<label id="vector-page-tools-dropdown-label" for="vector-page-tools-dropdown-checkbox" class="vector-dropdown-label cdx-button cdx-button--fake-button cdx-button--fake-button--enabled cdx-button--weight-quiet" aria-hidden="true"  ><span class="vector-dropdown-label-text">Tools</span>
	</label>
	<div class="vector-dropdown-content">


									<div id="vector-page-tools-unpinned-container" class="vector-unpinned-container">
						
<div id="vector-page-tools" class="vector-page-tools vector-pinnable-element">
	<div
	class="vector-pinnable-header vector-page-tools-pinnable-header vector-pinnable-header-unpinned"
	data-feature-name="page-tools-pinned"
	data-pinnable-element-id="vector-page-tools"
	data-pinned-container-id="vector-page-tools-pinned-container"
	data-unpinned-container-id="vector-page-tools-unpinned-container"
>
	<div class="vector-pinnable-header-label">Tools</div>
	<button class="vector-pinnable-header-toggle-button vector-pinnable-header-pin-button" data-event-name="pinnable-header.vector-page-tools.pin">move to sidebar</button>
	<button class="vector-pinnable-header-toggle-button vector-pinnable-header-unpin-button" data-event-name="pinnable-header.vector-page-tools.unpin">hide</button>
</div>

	
<div id="p-cactions" class="vector-menu mw-portlet mw-portlet-cactions emptyPortlet vector-has-collapsible-items"  title="More options" >
	<div class="vector-menu-heading">
		Actions
	</div>
	<div class="vector-menu-content">
		
		<ul class="vector-menu-content-list">
			
			<li id="ca-more-view" class="vector-more-collapsible-item mw-list-item"><a href="https://commons.wikimedia.org/wiki/File:Kondoa_mchoro_mwambani_2012_Tamino.jpg"><span>Read</span></a></li><li id="ca-more-edit" class="vector-more-collapsible-item mw-list-item"><a href="https://commons.wikimedia.org/w/index.php?title=File:Kondoa_mchoro_mwambani_2012_Tamino.jpg&amp;action=edit" title="Edit this page [e]" accesskey="e"><span>Edit</span></a></li><li id="ca-more-history" class="vector-more-collapsible-item mw-list-item"><a href="https://commons.wikimedia.org/w/index.php?title=File:Kondoa_mchoro_mwambani_2012_Tamino.jpg&amp;action=history"><span>View history</span></a></li>
		</ul>
		
	</div>
</div>

<div id="p-tb" class="vector-menu mw-portlet mw-portlet-tb"  >
	<div class="vector-menu-heading">
		General
	</div>
	<div class="vector-menu-content">
		
		<ul class="vector-menu-content-list">
			
			<li id="t-print" class="mw-list-item"><a href="javascript:print();" rel="alternate" title="Printable version of this page [p]" accesskey="p"><span>Printable version</span></a></li><li id="t-urlshortener" class="mw-list-item"><a href="https://commons.wikimedia.org/w/index.php?title=Special:UrlShortener&amp;url=https%3A%2F%2Fcommons.wikimedia.org%2Fwiki%2FSpecial%3AWhatLinksHere%2FFile%3AKondoa_mchoro_mwambani_2012_Tamino.jpg"><span>Get shortened URL</span></a></li><li id="t-urlshortener-qrcode" class="mw-list-item"><a href="https://commons.wikimedia.org/w/index.php?title=Special:QrCode&amp;url=https%3A%2F%2Fcommons.wikimedia.org%2Fwiki%2FSpecial%3AWhatLinksHere%2FFile%3AKondoa_mchoro_mwambani_2012_Tamino.jpg"><span>Download QR code</span></a></li>
		</ul>
		
	</div>
</div>

<div id="p-wikibase-otherprojects" class="vector-menu mw-portlet mw-portlet-wikibase-otherprojects emptyPortlet"  >
	<div class="vector-menu-heading">
		In other projects
	</div>
	<div class="vector-menu-content">
		
		<ul class="vector-menu-content-list">
			
			
		</ul>
		
	</div>
</div>

</div>

									</div>
				
	</div>
</div>

							</nav>
						</div>
					</div>
				</div>
				<div class="vector-column-end no-font-mode-scale">
					<div class="vector-sticky-pinned-container">
						<nav class="vector-page-tools-landmark" aria-label="Page tools">
							<div id="vector-page-tools-pinned-container" class="vector-pinned-container">
				
							</div>
		</nav>
						<nav class="vector-appearance-landmark" aria-label="Appearance">
							<div id="vector-appearance-pinned-container" class="vector-pinned-container">
				<div id="vector-appearance" class="vector-appearance vector-pinnable-element">
	<div
	class="vector-pinnable-header vector-appearance-pinnable-header vector-pinnable-header-pinned"
	data-feature-name="appearance-pinned"
	data-pinnable-element-id="vector-appearance"
	data-pinned-container-id="vector-appearance-pinned-container"
	data-unpinned-container-id="vector-appearance-unpinned-container"
>
	<div class="vector-pinnable-header-label">Appearance</div>
	<button class="vector-pinnable-header-toggle-button vector-pinnable-header-pin-button" data-event-name="pinnable-header.vector-appearance.pin">move to sidebar</button>
	<button class="vector-pinnable-header-toggle-button vector-pinnable-header-unpin-button" data-event-name="pinnable-header.vector-appearance.unpin">hide</button>
</div>


</div>

							</div>
		</nav>
					</div>
				</div>
				<div id="bodyContent" class="vector-body" aria-labelledby="firstHeading" data-mw-ve-target-container>
					<div class="vector-body-before-content">
					
						
					</div>
					<div id="contentSub"><div id="mw-content-subtitle">← <a href="https://commons.wikimedia.org/wiki/File:Kondoa_mchoro_mwambani_2012_Tamino.jpg" title="File:Kondoa mchoro mwambani 2012 Tamino.jpg">File:Kondoa mchoro mwambani 2012 Tamino.jpg</a></div></div>
					
					
					<div id="mw-content-text" class="mw-body-content"><div class='mw-htmlform-ooui-wrapper oo-ui-layout oo-ui-panelLayout oo-ui-panelLayout-padded oo-ui-panelLayout-framed'><form action='https://commons.wikimedia.org/wiki/Special:WhatLinksHere' method='get' enctype='application/x-www-form-urlencoded' class='mw-htmlform mw-htmlform-ooui oo-ui-layout oo-ui-formLayout'><fieldset class='oo-ui-layout oo-ui-labelElement oo-ui-fieldsetLayout'><legend class='oo-ui-fieldsetLayout-header'><span class='oo-ui-iconElement-icon oo-ui-iconElement-noIcon'></span><span class='oo-ui-labelElement-label'>What links here</span></legend><div class='oo-ui-fieldsetLayout-group'><div class='oo-ui-widget oo-ui-widget-enabled'><div class='oo-ui-layout oo-ui-panelLayout oo-ui-panelLayout-padded oo-ui-panelLayout-framed'><fieldset class='oo-ui-layout oo-ui-labelElement oo-ui-fieldsetLayout'><legend class='oo-ui-fieldsetLayout-header'><span class='oo-ui-iconElement-icon oo-ui-iconElement-noIcon'></span><span class='oo-ui-labelElement-label'>⧼whatlinkshere-whatlinkshere-target⧽</span></legend><div class='oo-ui-fieldsetLayout-group'><div class='oo-ui-widget oo-ui-widget-enabled'><div id="mw-htmlform-whatlinkshere-target"><div data-mw-modules='mediawiki.widgets' id='ooui-php-2' class='mw-htmlform-field-HTMLTitleTextField mw-htmlform-autoinfuse oo-ui-layout oo-ui-labelElement oo-ui-fieldLayout oo-ui-fieldLayout-align-top' data-ooui='{"_":"mw.htmlform.FieldLayout","fieldWidget":{"tag":"mw-whatlinkshere-target"},"align":"top","helpInline":true,"$overlay":true,"label":{"html":"Page:"},"classes":["mw-htmlform-field-HTMLTitleTextField","mw-htmlform-autoinfuse"]}'><div class='oo-ui-fieldLayout-body'><span class='oo-ui-fieldLayout-header'><label for='ooui-php-1' class='oo-ui-labelElement-label'>Page:</label></span><div class='oo-ui-fieldLayout-field'><div id='mw-whatlinkshere-target' class='oo-ui-widget oo-ui-widget-enabled oo-ui-inputWidget oo-ui-indicatorElement oo-ui-textInputWidget oo-ui-textInputWidget-type-text oo-ui-textInputWidget-php mw-widget-titleInputWidget' data-ooui='{"_":"mw.widgets.TitleInputWidget","relative":false,"$overlay":true,"maxLength":255,"name":"target","value":"File:Kondoa mchoro mwambani 2012 Tamino.jpg","inputId":"ooui-php-1","indicator":"required","required":true}'><input type='text' tabindex='0' name='target' value='File:Kondoa mchoro mwambani 2012 Tamino.jpg' required='' maxlength='255' id='ooui-php-1' class='oo-ui-inputWidget-input' /><span class='oo-ui-iconElement-icon oo-ui-iconElement-noIcon'></span><span class='oo-ui-indicatorElement-indicator oo-ui-indicator-required'></span></div></div></div></div></div></div></div></fieldset></div><div class='oo-ui-layout oo-ui-panelLayout oo-ui-panelLayout-padded oo-ui-panelLayout-framed'><fieldset class='oo-ui-layout oo-ui-labelElement oo-ui-fieldsetLayout'><legend class='oo-ui-fieldsetLayout-header'><span class='oo-ui-iconElement-icon oo-ui-iconElement-noIcon'></span><span class='oo-ui-labelElement-label'>⧼whatlinkshere-whatlinkshere-ns⧽</span></legend><div class='oo-ui-fieldsetLayout-group'><div class='oo-ui-widget oo-ui-widget-enabled'><div id="mw-htmlform-whatlinkshere-ns"><div data-mw-modules='mediawiki.widgets' id='ooui-php-5' class='mw-htmlform-field-HTMLSelectNamespace mw-htmlform-autoinfuse oo-ui-layout oo-ui-labelElement oo-ui-fieldLayout oo-ui-fieldLayout-align-top' data-ooui='{"_":"mw.htmlform.FieldLayout","fieldWidget":{"tag":"namespace"},"align":"top","helpInline":true,"$overlay":true,"label":{"html":"Namespace:"},"classes":["mw-htmlform-field-HTMLSelectNamespace","mw-htmlform-autoinfuse"]}'><div class='oo-ui-fieldLayout-body'><span class='oo-ui-fieldLayout-header'><label for='ooui-php-3' class='oo-ui-labelElement-label'>Namespace:</label></span><div class='oo-ui-fieldLayout-field'><div id='namespace' class='oo-ui-widget oo-ui-widget-enabled oo-ui-inputWidget oo-ui-dropdownInputWidget oo-ui-dropdownInputWidget-php mw-widget-namespaceInputWidget' data-ooui='{"_":"mw.widgets.NamespaceInputWidget","includeAllValue":"","userLang":true,"exclude":[],"include":null,"dropdown":{"$overlay":true},"name":"namespace","inputId":"ooui-php-3","required":false}'><select tabindex='0' name='namespace' id='ooui-php-3' class='oo-ui-inputWidget-input'><option value='' selected='selected'>all</option><option value='0'>(Gallery)</option><option value='1'>Talk</option><option value='2'>User</option><option value='3'>User talk</option><option value='4'>Commons</option><option value='5'>Commons talk</option><option value='6'>File</option><option value='7'>File talk</option><option value='8'>MediaWiki</option><option value='9'>MediaWiki talk</option><option value='10'>Template</option><option value='11'>Template talk</option><option value='12'>Help</option><option value='13'>Help talk</option><option value='14'>Category</option><option value='15'>Category talk</option><option value='100'>Creator</option><option value='101'>Creator talk</option><option value='102'>TimedText</option><option value='103'>TimedText talk</option><option value='104'>Sequence</option><option value='105'>Sequence talk</option><option value='106'>Institution</option><option value='107'>Institution talk</option><option value='460'>Campaign</option><option value='461'>Campaign talk</option><option value='486'>Data</option><option value='487'>Data talk</option><option value='828'>Module</option><option value='829'>Module talk</option><option value='1198'>Translations</option><option value='1199'>Translations talk</option><option value='2600'>Topic</option></select><span class='oo-ui-widget oo-ui-widget-enabled oo-ui-indicatorElement-indicator oo-ui-indicator-down oo-ui-indicatorElement oo-ui-labelElement-invisible oo-ui-indicatorWidget'></span></div></div></div></div><div id='ooui-php-6' class='mw-htmlform-field-HTMLCheckField mw-htmlform-autoinfuse mw-htmlform-hide-if mw-htmlform-hide-if-hidden oo-ui-layout oo-ui-labelElement oo-ui-fieldLayout oo-ui-fieldLayout-align-inline' data-ooui='{"_":"mw.htmlform.FieldLayout","fieldWidget":{"tag":"nsinvert"},"align":"inline","help":{"html":"Check this box to hide links from pages within the selected namespace."},"$overlay":true,"label":{"html":"Invert selection"},"condState":{"hide":["===","namespace",""]},"classes":["mw-htmlform-field-HTMLCheckField","mw-htmlform-autoinfuse","mw-htmlform-hide-if","mw-htmlform-hide-if-hidden"]}'><div class='oo-ui-fieldLayout-body'><span class='oo-ui-fieldLayout-field'><span id='nsinvert' class='oo-ui-widget oo-ui-widget-enabled oo-ui-inputWidget oo-ui-checkboxInputWidget' data-ooui='{"_":"OO.ui.CheckboxInputWidget","name":"invert","value":"1","inputId":"ooui-php-4","required":false}'><input type='checkbox' tabindex='0' name='invert' value='1' id='ooui-php-4' class='oo-ui-inputWidget-input' /><span class='oo-ui-checkboxInputWidget-checkIcon oo-ui-widget oo-ui-widget-enabled oo-ui-iconElement-icon oo-ui-icon-check oo-ui-iconElement oo-ui-labelElement-invisible oo-ui-iconWidget oo-ui-image-invert'></span></span></span><span class='oo-ui-fieldLayout-header'><span class='oo-ui-fieldLayout-help oo-ui-widget oo-ui-widget-enabled oo-ui-buttonElement oo-ui-buttonElement-frameless oo-ui-iconElement oo-ui-buttonWidget'><a role='button' title='Check this box to hide links from pages within the selected namespace.' tabindex='0' rel='nofollow' class='oo-ui-buttonElement-button'><span class='oo-ui-iconElement-icon oo-ui-icon-info'></span><span class='oo-ui-labelElement-label'></span><span class='oo-ui-indicatorElement-indicator oo-ui-indicatorElement-noIndicator'></span></a></span><label for='ooui-php-4' class='oo-ui-labelElement-label'>Invert selection</label></span></div></div></div></div></div></fieldset></div><div class='oo-ui-layout oo-ui-panelLayout oo-ui-panelLayout-padded oo-ui-panelLayout-framed'><fieldset class='oo-ui-layout oo-ui-labelElement oo-ui-fieldsetLayout'><legend class='oo-ui-fieldsetLayout-header'><span class='oo-ui-iconElement-icon oo-ui-iconElement-noIcon'></span><span class='oo-ui-labelElement-label'>⧼whatlinkshere-whatlinkshere-filter⧽</span></legend><div class='oo-ui-fieldsetLayout-group'><div class='oo-ui-widget oo-ui-widget-enabled'><div id="mw-htmlform-whatlinkshere-filter"><div id='ooui-php-15' class='mw-htmlform-field-HTMLCheckField oo-ui-layout oo-ui-labelElement oo-ui-fieldLayout oo-ui-fieldLayout-align-inline' data-ooui='{"_":"mw.htmlform.FieldLayout","fieldWidget":{"tag":"mw-input-hidetrans"},"align":"inline","helpInline":true,"$overlay":true,"label":{"html":"Hide transclusions"},"classes":["mw-htmlform-field-HTMLCheckField"]}'><div class='oo-ui-fieldLayout-body'><span class='oo-ui-fieldLayout-field'><span id='mw-input-hidetrans' class='oo-ui-widget oo-ui-widget-enabled oo-ui-inputWidget oo-ui-checkboxInputWidget' data-ooui='{"_":"OO.ui.CheckboxInputWidget","name":"hidetrans","value":"1","inputId":"ooui-php-11","required":false}'><input type='checkbox' tabindex='0' name='hidetrans' value='1' id='ooui-php-11' class='oo-ui-inputWidget-input' /><span class='oo-ui-checkboxInputWidget-checkIcon oo-ui-widget oo-ui-widget-enabled oo-ui-iconElement-icon oo-ui-icon-check oo-ui-iconElement oo-ui-labelElement-invisible oo-ui-iconWidget oo-ui-image-invert'></span></span></span><span class='oo-ui-fieldLayout-header'><label for='ooui-php-11' class='oo-ui-labelElement-label'>Hide transclusions</label></span></div></div><div id='ooui-php-16' class='mw-htmlform-field-HTMLCheckField oo-ui-layout oo-ui-labelElement oo-ui-fieldLayout oo-ui-fieldLayout-align-inline' data-ooui='{"_":"mw.htmlform.FieldLayout","fieldWidget":{"tag":"mw-input-hidelinks"},"align":"inline","helpInline":true,"$overlay":true,"label":{"html":"Hide links"},"classes":["mw-htmlform-field-HTMLCheckField"]}'><div class='oo-ui-fieldLayout-body'><span class='oo-ui-fieldLayout-field'><span id='mw-input-hidelinks' class='oo-ui-widget oo-ui-widget-enabled oo-ui-inputWidget oo-ui-checkboxInputWidget' data-ooui='{"_":"OO.ui.CheckboxInputWidget","name":"hidelinks","value":"1","inputId":"ooui-php-12","required":false}'><input type='checkbox' tabindex='0' name='hidelinks' value='1' id='ooui-php-12' class='oo-ui-inputWidget-input' /><span class='oo-ui-checkboxInputWidget-checkIcon oo-ui-widget oo-ui-widget-enabled oo-ui-iconElement-icon oo-ui-icon-check oo-ui-iconElement oo-ui-labelElement-invisible oo-ui-iconWidget oo-ui-image-invert'></span></span></span><span class='oo-ui-fieldLayout-header'><label for='ooui-php-12' class='oo-ui-labelElement-label'>Hide links</label></span></div></div><div id='ooui-php-17' class='mw-htmlform-field-HTMLCheckField oo-ui-layout oo-ui-labelElement oo-ui-fieldLayout oo-ui-fieldLayout-align-inline' data-ooui='{"_":"mw.htmlform.FieldLayout","fieldWidget":{"tag":"mw-input-hideredirs"},"align":"inline","helpInline":true,"$overlay":true,"label":{"html":"Hide redirects"},"classes":["mw-htmlform-field-HTMLCheckField"]}'><div class='oo-ui-fieldLayout-body'><span class='oo-ui-fieldLayout-field'><span id='mw-input-hideredirs' class='oo-ui-widget oo-ui-widget-enabled oo-ui-inputWidget oo-ui-checkboxInputWidget' data-ooui='{"_":"OO.ui.CheckboxInputWidget","name":"hideredirs","value":"1","inputId":"ooui-php-13","required":false}'><input type='checkbox' tabindex='0' name='hideredirs' value='1' id='ooui-php-13' class='oo-ui-inputWidget-input' /><span class='oo-ui-checkboxInputWidget-checkIcon oo-ui-widget oo-ui-widget-enabled oo-ui-iconElement-icon oo-ui-icon-check oo-ui-iconElement oo-ui-labelElement-invisible oo-ui-iconWidget oo-ui-image-invert'></span></span></span><span class='oo-ui-fieldLayout-header'><label for='ooui-php-13' class='oo-ui-labelElement-label'>Hide redirects</label></span></div></div><div id='ooui-php-18' class='mw-htmlform-field-HTMLCheckField oo-ui-layout oo-ui-labelElement oo-ui-fieldLayout oo-ui-fieldLayout-align-inline' data-ooui='{"_":"mw.htmlform.FieldLayout","fieldWidget":{"tag":"mw-input-hideimages"},"align":"inline","helpInline":true,"$overlay":true,"label":{"html":"Hide file links"},"classes":["mw-htmlform-field-HTMLCheckField"]}'><div class='oo-ui-fieldLayout-body'><span class='oo-ui-fieldLayout-field'><span id='mw-input-hideimages' class='oo-ui-widget oo-ui-widget-enabled oo-ui-inputWidget oo-ui-checkboxInputWidget' data-ooui='{"_":"OO.ui.CheckboxInputWidget","name":"hideimages","value":"1","inputId":"ooui-php-14","required":false}'><input type='checkbox' tabindex='0' name='hideimages' value='1' id='ooui-php-14' class='oo-ui-inputWidget-input' /><span class='oo-ui-checkboxInputWidget-checkIcon oo-ui-widget oo-ui-widget-enabled oo-ui-iconElement-icon oo-ui-icon-check oo-ui-iconElement oo-ui-labelElement-invisible oo-ui-iconWidget oo-ui-image-invert'></span></span></span><span class='oo-ui-fieldLayout-header'><label for='ooui-php-14' class='oo-ui-labelElement-label'>Hide file links</label></span></div></div></div></div></div></fieldset></div>
<input id="mw-input-limit" name="limit" type="hidden" value="50">
<div class="mw-htmlform-submit-buttons">
<span id='ooui-php-19' class='mw-htmlform-submit oo-ui-widget oo-ui-widget-enabled oo-ui-inputWidget oo-ui-buttonElement oo-ui-buttonElement-framed oo-ui-labelElement oo-ui-flaggedElement-primary oo-ui-flaggedElement-progressive oo-ui-buttonInputWidget' data-ooui='{"_":"OO.ui.ButtonInputWidget","type":"submit","value":"Go","label":"Go","flags":["primary","progressive"],"classes":["mw-htmlform-submit"]}'><button type='submit' tabindex='0' value='Go' class='oo-ui-inputWidget-input oo-ui-buttonElement-button'><span class='oo-ui-iconElement-icon oo-ui-iconElement-noIcon oo-ui-image-invert'></span><span class='oo-ui-labelElement-label'>Go</span><span class='oo-ui-indicatorElement-indicator oo-ui-indicatorElement-noIndicator oo-ui-image-invert'></span></button></span></div>
</div></div></fieldset></form></div><p>The following pages link to <b><a href="https://commons.wikimedia.org/wiki/File:Kondoa_mchoro_mwambani_2012_Tamino.jpg" title="File:Kondoa mchoro mwambani 2012 Tamino.jpg">File:Kondoa mchoro mwambani 2012 Tamino.jpg</a></b>
</p>
<div class="plainlinks" style="display: none">
<p>External tools: <a class="external text" href="https://iw.toolforge.org/templatecount/?lang=commons&amp;name=Kondoa+mchoro+mwambani+2012+Tamino.jpg&amp;namespace=6">Transclusion count</a>
</p>
<hr />
</div><p>Displaying 3 items.
</p><div class="mw-pager-navigation-bar">View (<span class="mw-prevlink">previous 50</span> | <span class="mw-nextlink">next 50</span>) (<a href="https://commons.wikimedia.org/w/index.php?title=Special:WhatLinksHere/File:Kondoa_mchoro_mwambani_2012_Tamino.jpg&amp;limit=20" class="mw-numlink">20</a> | <span class="mw-numlink">50</span> | <a href="https://commons.wikimedia.org/w/index.php?title=Special:WhatLinksHere/File:Kondoa_mchoro_mwambani_2012_Tamino.jpg&amp;limit=100" class="mw-numlink">100</a> | <a href="https://commons.wikimedia.org/w/index.php?title=Special:WhatLinksHere/File:Kondoa_mchoro_mwambani_2012_Tamino.jpg&amp;limit=250" class="mw-numlink">250</a> | <a href="https://commons.wikimedia.org/w/index.php?title=Special:WhatLinksHere/File:Kondoa_mchoro_mwambani_2012_Tamino.jpg&amp;limit=500" class="mw-numlink">500</a>)</div><ul id="mw-whatlinkshere-list"><li><bdi dir="ltr"><a href="https://commons.wikimedia.org/wiki/File:Kondoa_mchoro_mwambani_2012_Tamino.jpg" title="File:Kondoa mchoro mwambani 2012 Tamino.jpg">File:Kondoa mchoro mwambani 2012 Tamino.jpg</a></bdi> (file link) <span class="mw-whatlinkshere-tools">(<a href="https://commons.wikimedia.org/w/index.php?title=Special:WhatLinksHere&amp;target=File%3AKondoa+mchoro+mwambani+2012+Tamino.jpg" title="Special:WhatLinksHere">← links</a> | <a href="https://commons.wikimedia.org/w/index.php?title=File:Kondoa_mchoro_mwambani_2012_Tamino.jpg&amp;action=edit" title="File:Kondoa mchoro mwambani 2012 Tamino.jpg">edit</a>)</span></li>
<li><bdi dir="ltr"><a href="https://commons.wikimedia.org/wiki/File:Kondoa_mchoro_mwambani_2012_Tamino_(cropped).jpg" title="File:Kondoa mchoro mwambani 2012 Tamino (cropped).jpg">File:Kondoa mchoro mwambani 2012 Tamino (cropped).jpg</a></bdi> (file link) <span class="mw-whatlinkshere-tools">(<a href="https://commons.wikimedia.org/w/index.php?title=Special:WhatLinksHere&amp;target=File%3AKondoa+mchoro+mwambani+2012+Tamino+%28cropped%29.jpg" title="Special:WhatLinksHere">← links</a> | <a href="https://commons.wikimedia.org/w/index.php?title=File:Kondoa_mchoro_mwambani_2012_Tamino_(cropped).jpg&amp;action=edit" title="File:Kondoa mchoro mwambani 2012 Tamino (cropped).jpg">edit</a>)</span></li>
<li><bdi dir="ltr"><a href="https://commons.wikimedia.org/wiki/Category:Kondoa_Rock_Art_Sites" title="Category:Kondoa Rock Art Sites">Category:Kondoa Rock Art Sites</a></bdi> (file link) <span class="mw-whatlinkshere-tools">(<a href="https://commons.wikimedia.org/w/index.php?title=Special:WhatLinksHere&amp;target=Category%3AKondoa+Rock+Art+Sites" title="Special:WhatLinksHere">← links</a> | <a href="https://commons.wikimedia.org/w/index.php?title=Category:Kondoa_Rock_Art_Sites&amp;action=edit" title="Category:Kondoa Rock Art Sites">edit</a>)</span></li>
</ul><div class="mw-pager-navigation-bar">View (<span class="mw-prevlink">previous 50</span> | <span class="mw-nextlink">next 50</span>) (<a href="https://commons.wikimedia.org/w/index.php?title=Special:WhatLinksHere/File:Kondoa_mchoro_mwambani_2012_Tamino.jpg&amp;limit=20" class="mw-numlink">20</a> | <span class="mw-numlink">50</span> | <a href="https://commons.wikimedia.org/w/index.php?title=Special:WhatLinksHere/File:Kondoa_mchoro_mwambani_2012_Tamino.jpg&amp;limit=100" class="mw-numlink">100</a> | <a href="https://commons.wikimedia.org/w/index.php?title=Special:WhatLinksHere/File:Kondoa_mchoro_mwambani_2012_Tamino.jpg&amp;limit=250" class="mw-numlink">250</a> | <a href="https://commons.wikimedia.org/w/index.php?title=Special:WhatLinksHere/File:Kondoa_mchoro_mwambani_2012_Tamino.jpg&amp;limit=500" class="mw-numlink">500</a>)</div><!--esi <esi:include src="/esitest-fa8a495983347898/content" /> --><noscript><img src="https://commons.wikimedia.org/wiki/Special:CentralAutoLogin/start?type=1x1&amp;usesul3=1" alt="" width="1" height="1" style="border: none; position: absolute;"></noscript>
<div class="printfooter" data-nosnippet="">Retrieved from "<a dir="ltr" href="https://commons.wikimedia.org/wiki/Special:WhatLinksHere/File:Kondoa_mchoro_mwambani_2012_Tamino.jpg">https://commons.wikimedia.org/wiki/Special:WhatLinksHere/File:Kondoa_mchoro_mwambani_2012_Tamino.jpg</a>"</div></div>
					<div id="catlinks" class="catlinks catlinks-allhidden" data-mw="interface"></div>
				</div>
			</main>
			
		</div>
		<div class="mw-footer-container">
			
<footer id="footer" class="mw-footer" >
	<ul id="footer-info">
</ul>

	<ul id="footer-places">
	<li id="footer-places-privacy"><a href="https://foundation.wikimedia.org/wiki/Special:MyLanguage/Policy:Privacy_policy">Privacy policy</a></li>
	<li id="footer-places-about"><a href="https://commons.wikimedia.org/wiki/Commons:Welcome">About Wikimedia Commons</a></li>
	<li id="footer-places-disclaimers"><a href="https://commons.wikimedia.org/wiki/Commons:General_disclaimer">Disclaimers</a></li>
	<li id="footer-places-wm-codeofconduct"><a href="https://foundation.wikimedia.org/wiki/Special:MyLanguage/Policy:Universal_Code_of_Conduct">Code of Conduct</a></li>
	<li id="footer-places-developers"><a href="https://developer.wikimedia.org/">Developers</a></li>
	<li id="footer-places-statslink"><a href="https://stats.wikimedia.org/#/commons.wikimedia.org">Statistics</a></li>
	<li id="footer-places-cookiestatement"><a href="https://foundation.wikimedia.org/wiki/Special:MyLanguage/Policy:Cookie_statement">Cookie statement</a></li>
	<li id="footer-places-mobileview"><a href="http://commons.m.wikimedia.org/w/index.php?title=Special:WhatLinksHere/File:Kondoa_mchoro_mwambani_2012_Tamino.jpg&amp;mobileaction=toggle_view_mobile" class="noprint stopMobileRedirectToggle">Mobile view</a></li>
</ul>

	<ul id="footer-icons" class="noprint">
	<li id="footer-copyrightico"><a href="https://www.wikimedia.org/" class="cdx-button cdx-button--fake-button cdx-button--size-large cdx-button--fake-button--enabled"><picture><source media="(min-width: 500px)" srcset="/static/images/footer/wikimedia-button.svg" width="84" height="29"><img src="https://commons.wikimedia.org/static/images/footer/wikimedia.svg" width="25" height="25" alt="Wikimedia Foundation" lang="en" loading="lazy"></picture></a></li>
	<li id="footer-poweredbyico"><a href="https://www.mediawiki.org/" class="cdx-button cdx-button--fake-button cdx-button--size-large cdx-button--fake-button--enabled"><picture><source media="(min-width: 500px)" srcset="/w/resources/assets/poweredby_mediawiki.svg" width="88" height="31"><img src="https://commons.wikimedia.org/w/resources/assets/mediawiki_compact.svg" alt="Powered by MediaWiki" lang="en" width="25" height="25" loading="lazy"></picture></a></li>
</ul>

</footer>

		</div>
	</div> 
</div> 
<div class="vector-header-container vector-sticky-header-container no-font-mode-scale">
	<div id="vector-sticky-header" class="vector-sticky-header">
		<div class="vector-sticky-header-start">
			<div class="vector-sticky-header-icon-start vector-button-flush-left vector-button-flush-right" aria-hidden="true">
				<button class="cdx-button cdx-button--weight-quiet cdx-button--icon-only vector-sticky-header-search-toggle" tabindex="-1" data-event-name="ui.vector-sticky-search-form.icon"><span class="vector-icon mw-ui-icon-search mw-ui-icon-wikimedia-search"></span>

<span>Search</span>
			</button>
		</div>
			
		<div role="search" class="vector-search-box-vue vector-search-box">
			<div class="vector-typeahead-search-container">
				<div class="cdx-typeahead-search">
					<form action="https://commons.wikimedia.org/w/index.php" id="vector-sticky-search-form" class="cdx-search-input cdx-search-input--has-end-button">
						<div  class="cdx-search-input__input-wrapper"  data-search-loc="header-moved">
							<div class="cdx-text-input cdx-text-input--has-start-icon">
								<input
									class="cdx-text-input__input mw-searchInput" autocomplete="off"
									
									type="search" name="search" placeholder="Search Wikimedia Commons">
								<span class="cdx-text-input__icon cdx-text-input__start-icon"></span>
							</div>
							<input type="hidden" name="title" value="Special:MediaSearch">
						</div>
						<button class="cdx-button cdx-search-input__end-button">Search</button>
					</form>
				</div>
			</div>
		</div>
		<div class="vector-sticky-header-context-bar">
				<div class="vector-sticky-header-context-bar-primary" aria-hidden="true" >Pages that link to "File:Kondoa mchoro mwambani 2012 Tamino.jpg"</div>
			</div>
		</div>
		<div class="vector-sticky-header-end" aria-hidden="true">
			<div class="vector-sticky-header-icons">
				<a href="#" class="cdx-button cdx-button--fake-button cdx-button--fake-button--enabled cdx-button--weight-quiet cdx-button--icon-only" id="ca-talk-sticky-header" tabindex="-1" data-event-name="talk-sticky-header"><span class="vector-icon mw-ui-icon-speechBubbles mw-ui-icon-wikimedia-speechBubbles"></span>

<span></span>
			</a>
			<a href="#" class="cdx-button cdx-button--fake-button cdx-button--fake-button--enabled cdx-button--weight-quiet cdx-button--icon-only" id="ca-subject-sticky-header" tabindex="-1" data-event-name="subject-sticky-header"><span class="vector-icon mw-ui-icon-article mw-ui-icon-wikimedia-article"></span>

<span></span>
			</a>
			<a href="#" class="cdx-button cdx-button--fake-button cdx-button--fake-button--enabled cdx-button--weight-quiet cdx-button--icon-only" id="ca-history-sticky-header" tabindex="-1" data-event-name="history-sticky-header"><span class="vector-icon mw-ui-icon-wikimedia-history mw-ui-icon-wikimedia-wikimedia-history"></span>

<span></span>
			</a>
			<a href="#" class="cdx-button cdx-button--fake-button cdx-button--fake-button--enabled cdx-button--weight-quiet cdx-button--icon-only mw-watchlink" id="ca-watchstar-sticky-header" tabindex="-1" data-event-name="watch-sticky-header"><span class="vector-icon mw-ui-icon-wikimedia-star mw-ui-icon-wikimedia-wikimedia-star"></span>

<span></span>
			</a>
			<a href="#" class="cdx-button cdx-button--fake-button cdx-button--fake-button--enabled cdx-button--weight-quiet cdx-button--icon-only" id="ca-edit-sticky-header" tabindex="-1" data-event-name="wikitext-edit-sticky-header"><span class="vector-icon mw-ui-icon-wikimedia-wikiText mw-ui-icon-wikimedia-wikimedia-wikiText"></span>

<span></span>
			</a>
			<a href="#" class="cdx-button cdx-button--fake-button cdx-button--fake-button--enabled cdx-button--weight-quiet cdx-button--icon-only" id="ca-ve-edit-sticky-header" tabindex="-1" data-event-name="ve-edit-sticky-header"><span class="vector-icon mw-ui-icon-wikimedia-edit mw-ui-icon-wikimedia-wikimedia-edit"></span>

<span></span>
			</a>
			<a href="#" class="cdx-button cdx-button--fake-button cdx-button--fake-button--enabled cdx-button--weight-quiet cdx-button--icon-only" id="ca-viewsource-sticky-header" tabindex="-1" data-event-name="ve-edit-protected-sticky-header"><span class="vector-icon mw-ui-icon-wikimedia-editLock mw-ui-icon-wikimedia-wikimedia-editLock"></span>

<span></span>
			</a>
		</div>
			<div class="vector-sticky-header-buttons">
				<a href="#" class="cdx-button cdx-button--fake-button cdx-button--fake-button--enabled cdx-button--weight-quiet cdx-button--action-progressive" id="ca-addsection-sticky-header" tabindex="-1" data-event-name="addsection-sticky-header"><span class="vector-icon mw-ui-icon-speechBubbleAdd-progressive mw-ui-icon-wikimedia-speechBubbleAdd-progressive"></span>

<span>Add topic</span>
			</a>
		</div>
			<div class="vector-sticky-header-icon-end">
				<div class="vector-user-links">
				</div>
			</div>
		</div>
	</div>
</div>
<div class="mw-portlet mw-portlet-dock-bottom emptyPortlet" id="p-dock-bottom">
	<ul>
		
	</ul>
</div>
<script>(RLQ=window.RLQ||[]).push(function(){mw.config.set({"wgHostname":"mw-web.eqiad.main-7b48b5fb74-ggngv","wgBackendResponseTime":139});});</script>
</body>

<!-- Mirrored from commons.wikimedia.org/wiki/Special:WhatLinksHere/File:Kondoa_mchoro_mwambani_2012_Tamino.jpg by HTTrack Website Copier/3.x [XR&CO'2014], Sun, 03 Aug 2025 15:20:50 GMT -->
</html>