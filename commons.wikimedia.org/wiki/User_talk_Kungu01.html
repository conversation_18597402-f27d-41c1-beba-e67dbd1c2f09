<!DOCTYPE html>
<html class="client-nojs vector-feature-language-in-header-disabled vector-feature-language-in-main-page-header-disabled vector-feature-page-tools-pinned-disabled vector-feature-toc-pinned-clientpref-1 vector-feature-main-menu-pinned-disabled vector-feature-limited-width-clientpref-1 vector-feature-limited-width-content-enabled vector-feature-custom-font-size-clientpref-1 vector-feature-appearance-pinned-clientpref-1 vector-feature-night-mode-enabled skin-theme-clientpref-day vector-sticky-header-enabled vector-toc-available" lang="en" dir="ltr">

<!-- Mirrored from commons.wikimedia.org/wiki/User_talk:Kungu01 by HTTrack Website Copier/3.x [XR&CO'2014], Sun, 03 Aug 2025 08:57:37 GMT -->
<!-- Added by HTTrack --><meta http-equiv="content-type" content="text/html;charset=UTF-8" /><!-- /Added by HTTrack -->
<head>
<meta charset="UTF-8">
<title>User talk:Kungu01 - Wikimedia Commons</title>
<script>(function(){var className="client-js vector-feature-language-in-header-disabled vector-feature-language-in-main-page-header-disabled vector-feature-page-tools-pinned-disabled vector-feature-toc-pinned-clientpref-1 vector-feature-main-menu-pinned-disabled vector-feature-limited-width-clientpref-1 vector-feature-limited-width-content-enabled vector-feature-custom-font-size-clientpref-1 vector-feature-appearance-pinned-clientpref-1 vector-feature-night-mode-enabled skin-theme-clientpref-day vector-sticky-header-enabled vector-toc-available";var cookie=document.cookie.match(/(?:^|; )commonswikimwclientpreferences=([^;]+)/);if(cookie){cookie[1].split('%2C').forEach(function(pref){className=className.replace(new RegExp('(^| )'+pref.replace(/-clientpref-\w+$|[^\w-]+/g,'')+'-clientpref-\\w+( |$)'),'$1'+pref+'$2');});}document.documentElement.className=className;}());RLCONF={"wgBreakFrames":false,"wgSeparatorTransformTable":["",""],"wgDigitTransformTable":["",""],"wgDefaultDateFormat":"dmy","wgMonthNames":["","January","February","March","April","May","June","July","August","September","October","November","December"],"wgRequestId":"05fb2736-25ec-4873-8a0d-9b9df2a965ed","wgCanonicalNamespace":"User_talk","wgCanonicalSpecialPageName":false,"wgNamespaceNumber":3,"wgPageName":"User_talk:Kungu01","wgTitle":"Kungu01","wgCurRevisionId":538859226,"wgRevisionId":538859226,"wgArticleId":36598950,"wgIsArticle":true,"wgIsRedirect":false,"wgAction":"view","wgUserName":null,"wgUserGroups":["*"],"wgCategories":[],"wgPageViewLanguage":"en","wgPageContentLanguage":"en","wgPageContentModel":"wikitext","wgRelevantPageName":"User_talk:Kungu01","wgRelevantArticleId":36598950,"wgIsProbablyEditable":true,"wgRelevantPageIsProbablyEditable":true,"wgRestrictionEdit":[],"wgRestrictionMove":[],"wgRelevantUserName":"Kungu01","wgNoticeProject":"commons","wgCiteReferencePreviewsActive":true,"wgMediaViewerOnClick":true,"wgMediaViewerEnabledByDefault":false,"wgVisualEditor":{"pageLanguageCode":"en","pageLanguageDir":"ltr","pageVariantFallbacks":"en"},"wgMFDisplayWikibaseDescriptions":{"search":true,"watchlist":true,"tagline":true,"nearby":true},"wgWMESchemaEditAttemptStepOversample":false,"wgWMEPageLength":20000,"wgMetricsPlatformUserExperiments":{"active_experiments":[],"overrides":[],"enrolled":[],"assigned":[],"subject_ids":[],"sampling_units":[]},"upwizPropertyTitles":{"P180":"Main subjects visible in this work"},"upwizPropertyPlaceholders":{"P180":"e.g., Angolan giraffe"},"upwizPropertyCopyLabels":{"P180":"Main subjects"},"wgEditSubmitButtonLabelPublish":true,"wgDiscussionToolsFeaturesEnabled":{"replytool":true,"newtopictool":true,"sourcemodetoolbar":true,"topicsubscription":false,"autotopicsub":false,"visualenhancements":true,"visualenhancements_reply":true,"visualenhancements_pageframe":true},"wgDiscussionToolsFallbackEditMode":"source","wgULSPosition":"personal","wgULSisCompactLinksEnabled":true,"wgVector2022LanguageInHeader":false,"wgULSisLanguageSelectorEmpty":true,"wbmiDefaultProperties":["P180"],"wbmiPropertyTitles":{"P180":"Items portrayed in this file"},"wbmiPropertyTypes":{"P180":"wikibase-item"},"wbmiRepoApiUrl":"/w/api.php","wbmiHelpUrls":{"P180":"https://commons.wikimedia.org/wiki/Special:MyLanguage/Commons:Depicts"},"wbmiExternalEntitySearchBaseUri":"https://www.wikidata.org/w/api.php","wbmiSupportedDataTypes":["wikibase-item","string","quantity","time","monolingualtext","external-id","globe-coordinate","url"],"wgCheckUserClientHintsHeadersJsApi":["brands","architecture","bitness","fullVersionList","mobile","model","platform","platformVersion"]};
RLSTATE={"ext.gadget.Long-Image-Names-in-Categories":"ready","ext.globalCssJs.user.styles":"ready","site.styles":"ready","user.styles":"ready","ext.globalCssJs.user":"ready","user":"ready","user.options":"loading","ext.discussionTools.init.styles":"ready","oojs-ui-core.styles":"ready","oojs-ui.styles.indicators":"ready","mediawiki.widgets.styles":"ready","oojs-ui-core.icons":"ready","oojs-ui.styles.icons-interactions":"ready","skins.vector.search.codex.styles":"ready","skins.vector.styles":"ready","skins.vector.icons":"ready","ext.wikimediamessages.styles":"ready","ext.visualEditor.desktopArticleTarget.noscript":"ready","ext.uls.pt":"ready"};RLPAGEMODULES=["ext.xLab","site","mediawiki.page.ready","mediawiki.toc","Skins.vector.html","ext.centralNotice.geoIP","ext.centralNotice.startUp","ext.gadget.Slideshow","ext.gadget.ZoomViewer","ext.gadget.CollapsibleTemplates","ext.gadget.Stockphoto","ext.gadget.WatchlistNotice","ext.gadget.AjaxQuickDelete","ext.gadget.WikiMiniAtlas","ext.gadget.LanguageSelect","ext.gadget.PictureOfTheYearEnhancements","ext.urlShortener.toolbar","ext.centralauth.centralautologin","mmv.bootstrap","ext.visualEditor.desktopArticleTarget.init","ext.visualEditor.targetLoader","ext.echo.centralauth","ext.discussionTools.init","ext.eventLogging","ext.wikimediaEvents","ext.wikimediaEvents.wikibase","ext.navigationTiming","ext.uls.compactlinks","ext.uls.interface","ext.checkUser.clientHints"];</script>
<script>(RLQ=window.RLQ||[]).push(function(){mw.loader.impl(function(){return["user.options@12s5i",function($,jQuery,require,module){mw.user.tokens.set({"patrolToken":"+\\","watchToken":"+\\","csrfToken":"+\\"});
}];});});</script>
<link rel="stylesheet" href="../w/load761b.css?lang=en&amp;modules=ext.discussionTools.init.styles%7Cext.uls.pt%7Cext.visualEditor.desktopArticleTarget.noscript%7Cext.wikimediamessages.styles%7Cmediawiki.widgets.styles%7Coojs-ui-core.icons%2Cstyles%7Coojs-ui.styles.icons-interactions%2Cindicators%7Cskins.vector.icons%2Cstyles%7Cskins.vector.search.codex.styles&amp;only=styles&amp;skin=vector-2022">
<script async="" src="../w/load9565.php?lang=en&amp;modules=startup&amp;only=scripts&amp;raw=1&amp;skin=vector-2022"></script>
<meta name="ResourceLoaderDynamicStyles" content="">
<link rel="stylesheet" href="../w/load3e3b.css?lang=en&amp;modules=ext.gadget.Long-Image-Names-in-Categories&amp;only=styles&amp;skin=vector-2022">
<link rel="stylesheet" href="../w/loada24d.css?lang=en&amp;modules=site.styles&amp;only=styles&amp;skin=vector-2022">
<meta name="generator" content="MediaWiki 1.45.0-wmf.12">
<meta name="referrer" content="origin">
<meta name="referrer" content="origin-when-cross-origin">
<meta name="robots" content="max-image-preview:standard">
<meta name="format-detection" content="telephone=no">
<meta name="viewport" content="width=1120">
<meta property="og:title" content="User talk:Kungu01 - Wikimedia Commons">
<meta property="og:type" content="website">
<link rel="preconnect" href="../../upload.wikimedia.org/index.html">
<link rel="alternate" media="only screen and (max-width: 640px)" href="../../commons.m.wikimedia.org/wiki/User_talk_Kungu01.html">
<link rel="alternate" type="application/x-wiki" title="Edit" href="../w/indexf60d.html?title=User_talk:Kungu01&amp;action=edit">
<link rel="apple-touch-icon" href="../static/apple-touch/commons.png">
<link rel="icon" href="../static/favicon/commons.ico">
<link rel="search" type="application/opensearchdescription+xml" href="../w/rest.php/v1/search" title="Wikimedia Commons">
<link rel="EditURI" type="application/rsd+xml" href="https://commons.wikimedia.org/w/api.php?action=rsd">
<link rel="canonical" href="User_talk_Kungu01.html">
<link rel="license" href="../../creativecommons.org/licenses/by-sa/4.0/index.html">
<link rel="alternate" type="application/atom+xml" title="Wikimedia Commons Atom feed" href="https://commons.wikimedia.org/w/api.php?hidebots=1&amp;hidecategorization=1&amp;hideWikibase=1&amp;translations=filter&amp;urlversion=1&amp;days=7&amp;limit=50&amp;action=feedrecentchanges&amp;feedformat=atom">
<link rel="dns-prefetch" href="../../meta.wikimedia.org/index.html" />
<link rel="dns-prefetch" href="Auth.wikimedia.html">
</head>
<body class="ext-discussiontools-replytool-enabled ext-discussiontools-newtopictool-enabled ext-discussiontools-sourcemodetoolbar-enabled ext-discussiontools-visualenhancements-enabled ext-discussiontools-visualenhancements_reply-enabled ext-discussiontools-visualenhancements_pageframe-enabled skin--responsive skin-vector skin-vector-search-vue mediawiki ltr sitedir-ltr mw-hide-empty-elt ns-3 ns-talk mw-editable page-User_talk_Kungu01 rootpage-User_talk_Kungu01 skin-vector-2022 action-view"><a class="mw-jump-link" href="#bodyContent">Jump to content</a>
<div class="vector-header-container">
	<header class="vector-header mw-header no-font-mode-scale">
		<div class="vector-header-start">
			<nav class="vector-main-menu-landmark" aria-label="Site">
				
<div id="vector-main-menu-dropdown" class="vector-dropdown vector-main-menu-dropdown vector-button-flush-left vector-button-flush-right"  title="Main menu" >
	<input type="checkbox" id="vector-main-menu-dropdown-checkbox" role="button" aria-haspopup="true" data-event-name="ui.dropdown-vector-main-menu-dropdown" class="vector-dropdown-checkbox "  aria-label="Main menu"  >
	<label id="vector-main-menu-dropdown-label" for="vector-main-menu-dropdown-checkbox" class="vector-dropdown-label cdx-button cdx-button--fake-button cdx-button--fake-button--enabled cdx-button--weight-quiet cdx-button--icon-only " aria-hidden="true"  ><span class="vector-icon mw-ui-icon-menu mw-ui-icon-wikimedia-menu"></span>

<span class="vector-dropdown-label-text">Main menu</span>
	</label>
	<div class="vector-dropdown-content">


				<div id="vector-main-menu-unpinned-container" class="vector-unpinned-container">
		
<div id="vector-main-menu" class="vector-main-menu vector-pinnable-element">
	<div
	class="vector-pinnable-header vector-main-menu-pinnable-header vector-pinnable-header-unpinned"
	data-feature-name="main-menu-pinned"
	data-pinnable-element-id="vector-main-menu"
	data-pinned-container-id="vector-main-menu-pinned-container"
	data-unpinned-container-id="vector-main-menu-unpinned-container"
>
	<div class="vector-pinnable-header-label">Main menu</div>
	<button class="vector-pinnable-header-toggle-button vector-pinnable-header-pin-button" data-event-name="pinnable-header.vector-main-menu.pin">move to sidebar</button>
	<button class="vector-pinnable-header-toggle-button vector-pinnable-header-unpin-button" data-event-name="pinnable-header.vector-main-menu.unpin">hide</button>
</div>

	
<div id="p-navigation" class="vector-menu mw-portlet mw-portlet-navigation"  >
	<div class="vector-menu-heading">
		Navigate
	</div>
	<div class="vector-menu-content">
		
		<ul class="vector-menu-content-list">
			
			<li id="n-mainpage-description" class="mw-list-item"><a href="Main_Page.html" title="Visit the main page [z]" accesskey="z"><span>Main page</span></a></li><li id="n-welcome" class="mw-list-item"><a href="Commons_Welcome.html"><span>Welcome</span></a></li><li id="n-portal" class="mw-list-item"><a href="Commons_Community_portal.html" title="About the project, what you can do, where to find things"><span>Community portal</span></a></li><li id="n-village-pump" class="mw-list-item"><a href="Commons_Village_pump.html"><span>Village pump</span></a></li><li id="n-help" class="mw-list-item"><a href="Special_MyLanguage/Help_Contents.html" title="The place to find out"><span>Help center</span></a></li>
		</ul>
		
	</div>
</div>

	
	
<div id="p-participate" class="vector-menu mw-portlet mw-portlet-participate"  >
	<div class="vector-menu-heading">
		Participate
	</div>
	<div class="vector-menu-content">
		
		<ul class="vector-menu-content-list">
			
			<li id="n-uploadbtn" class="mw-list-item"><a href="Special_UploadWizard.html"><span>Upload file</span></a></li><li id="n-recentchanges" class="mw-list-item"><a href="Special_RecentChanges.html" title="A list of recent changes in the wiki [r]" accesskey="r"><span>Recent changes</span></a></li><li id="n-latestfiles" class="mw-list-item"><a href="Special_NewFiles.html"><span>Latest files</span></a></li><li id="n-randomimage" class="mw-list-item"><a href="File_Corliss_valvegear%2c_Gordon%27s_improved_(New_Catechism_of_the_Steam_Engine%2c_1904).html" title="Load a random file [x]" accesskey="x"><span>Random file</span></a></li><li id="n-contact" class="mw-list-item"><a href="Commons_Contact_us.html"><span>Contact us</span></a></li><li id="n-specialpages" class="mw-list-item"><a href="Special_SpecialPages.html"><span>Special pages</span></a></li>
		</ul>
		
	</div>
</div>

	
<div id="vector-main-menu" class="vector-menu "  >
	<div class="vector-menu-heading">
		
	</div>
	<div class="vector-menu-content">
		
		<ul class="vector-menu-content-list">
			
			
		</ul>
		
	</div>
</div>

</div>

				</div>

	</div>
</div>

		</nav>
			
<a href="Main_Page.html" class="mw-logo">
	<img class="mw-logo-icon" src="../static/images/icons/commonswiki.svg" alt="" aria-hidden="true" height="50" width="50">
	<span class="mw-logo-container skin-invert">
		<img class="mw-logo-wordmark" alt="Wikimedia Commons" src="../static/images/mobile/copyright/commonswiki-wordmark.svg" style="width: 7.1875em; height: 2em;">
	</span>
</a>

		</div>
		<div class="vector-header-end">
			
<div id="p-search" role="search" class="vector-search-box-vue  vector-search-box-collapses vector-search-box">
	<a href="Special_MediaSearch.html" class="cdx-button cdx-button--fake-button cdx-button--fake-button--enabled cdx-button--weight-quiet cdx-button--icon-only search-toggle" title="Search Wikimedia Commons [f]" accesskey="f"><span class="vector-icon mw-ui-icon-search mw-ui-icon-wikimedia-search"></span>

<span>Search</span>
	</a>
	<div class="vector-typeahead-search-container">
		<div class="cdx-typeahead-search">
			<form action="https://commons.wikimedia.org/w/index.php" id="searchform" class="cdx-search-input cdx-search-input--has-end-button">
				<div id="simpleSearch" class="cdx-search-input__input-wrapper"  data-search-loc="header-moved">
					<div class="cdx-text-input cdx-text-input--has-start-icon">
						<input
							class="cdx-text-input__input mw-searchInput" autocomplete="off"
							 type="search" name="search" placeholder="Search Wikimedia Commons" aria-label="Search Wikimedia Commons" autocapitalize="sentences" spellcheck="false" title="Search Wikimedia Commons [f]" accesskey="f" id="searchInput"
							>
						<span class="cdx-text-input__icon cdx-text-input__start-icon"></span>
					</div>
					<input type="hidden" name="title" value="Special:MediaSearch">
				</div>
				<button class="cdx-button cdx-search-input__end-button">Search</button>
			</form>
		</div>
	</div>
</div>

			<nav class="vector-user-links vector-user-links-wide" aria-label="Personal tools">
	<div class="vector-user-links-main">
	
<div id="p-vector-user-menu-preferences" class="vector-menu mw-portlet"  >
	<div class="vector-menu-content">
		
		<ul class="vector-menu-content-list">
			<li id="ca-uls" class="mw-list-item active user-links-collapsible-item"><a data-mw="interface" href="#" class="uls-trigger cdx-button cdx-button--fake-button cdx-button--fake-button--enabled cdx-button--weight-quiet"><span class="vector-icon mw-ui-icon-wikimedia-language mw-ui-icon-wikimedia-wikimedia-language"></span>

<span>English</span></a>
</li>

			
		</ul>
		
	</div>
</div>

	
<div id="p-vector-user-menu-userpage" class="vector-menu mw-portlet emptyPortlet"  >
	<div class="vector-menu-content">
		
		<ul class="vector-menu-content-list">
			
			
		</ul>
		
	</div>
</div>

	<nav class="vector-appearance-landmark" aria-label="Appearance">
		
<div id="vector-appearance-dropdown" class="vector-dropdown "  title="Change the appearance of the page&#039;s font size, width, and color" >
	<input type="checkbox" id="vector-appearance-dropdown-checkbox" role="button" aria-haspopup="true" data-event-name="ui.dropdown-vector-appearance-dropdown" class="vector-dropdown-checkbox "  aria-label="Appearance"  >
	<label id="vector-appearance-dropdown-label" for="vector-appearance-dropdown-checkbox" class="vector-dropdown-label cdx-button cdx-button--fake-button cdx-button--fake-button--enabled cdx-button--weight-quiet cdx-button--icon-only " aria-hidden="true"  ><span class="vector-icon mw-ui-icon-appearance mw-ui-icon-wikimedia-appearance"></span>

<span class="vector-dropdown-label-text">Appearance</span>
	</label>
	<div class="vector-dropdown-content">


			<div id="vector-appearance-unpinned-container" class="vector-unpinned-container">
				
			</div>
		
	</div>
</div>

	</nav>
	
<div id="p-vector-user-menu-notifications" class="vector-menu mw-portlet emptyPortlet"  >
	<div class="vector-menu-content">
		
		<ul class="vector-menu-content-list">
			
			
		</ul>
		
	</div>
</div>

	
<div id="p-vector-user-menu-overflow" class="vector-menu mw-portlet"  >
	<div class="vector-menu-content">
		
		<ul class="vector-menu-content-list">
			<li id="pt-sitesupport-2" class="user-links-collapsible-item mw-list-item user-links-collapsible-item"><a data-mw="interface" href="https://donate.wikimedia.org/?wmf_source=donate&amp;wmf_medium=sidebar&amp;wmf_campaign=commons.wikimedia.org&amp;uselang=en" class=""><span>Donate</span></a>
</li>
<li id="pt-createaccount-2" class="user-links-collapsible-item mw-list-item user-links-collapsible-item"><a data-mw="interface" href="https://commons.wikimedia.org/w/index.php?title=Special:CreateAccount&amp;returnto=User+talk%3AKungu01" title="You are encouraged to create an account and log in; however, it is not mandatory" class=""><span>Create account</span></a>
</li>
<li id="pt-login-2" class="user-links-collapsible-item mw-list-item user-links-collapsible-item"><a data-mw="interface" href="https://commons.wikimedia.org/w/index.php?title=Special:UserLogin&amp;returnto=User+talk%3AKungu01" title="You are encouraged to log in; however, it is not mandatory [o]" accesskey="o" class=""><span>Log in</span></a>
</li>

			
		</ul>
		
	</div>
</div>

	</div>
	
<div id="vector-user-links-dropdown" class="vector-dropdown vector-user-menu vector-button-flush-right vector-user-menu-logged-out"  title="More options" >
	<input type="checkbox" id="vector-user-links-dropdown-checkbox" role="button" aria-haspopup="true" data-event-name="ui.dropdown-vector-user-links-dropdown" class="vector-dropdown-checkbox "  aria-label="Personal tools"  >
	<label id="vector-user-links-dropdown-label" for="vector-user-links-dropdown-checkbox" class="vector-dropdown-label cdx-button cdx-button--fake-button cdx-button--fake-button--enabled cdx-button--weight-quiet cdx-button--icon-only " aria-hidden="true"  ><span class="vector-icon mw-ui-icon-ellipsis mw-ui-icon-wikimedia-ellipsis"></span>

<span class="vector-dropdown-label-text">Personal tools</span>
	</label>
	<div class="vector-dropdown-content">


		
<div id="p-personal" class="vector-menu mw-portlet mw-portlet-personal user-links-collapsible-item"  title="User menu" >
	<div class="vector-menu-content">
		
		<ul class="vector-menu-content-list">
			
			<li id="pt-sitesupport" class="user-links-collapsible-item mw-list-item"><a href="https://donate.wikimedia.org/?wmf_source=donate&amp;wmf_medium=sidebar&amp;wmf_campaign=commons.wikimedia.org&amp;uselang=en"><span>Donate</span></a></li><li id="pt-createaccount" class="user-links-collapsible-item mw-list-item"><a href="https://commons.wikimedia.org/w/index.php?title=Special:CreateAccount&amp;returnto=User+talk%3AKungu01" title="You are encouraged to create an account and log in; however, it is not mandatory"><span class="vector-icon mw-ui-icon-userAdd mw-ui-icon-wikimedia-userAdd"></span> <span>Create account</span></a></li><li id="pt-login" class="user-links-collapsible-item mw-list-item"><a href="https://commons.wikimedia.org/w/index.php?title=Special:UserLogin&amp;returnto=User+talk%3AKungu01" title="You are encouraged to log in; however, it is not mandatory [o]" accesskey="o"><span class="vector-icon mw-ui-icon-logIn mw-ui-icon-wikimedia-logIn"></span> <span>Log in</span></a></li>
		</ul>
		
	</div>
</div>

<div id="p-user-menu-anon-editor" class="vector-menu mw-portlet mw-portlet-user-menu-anon-editor"  >
	<div class="vector-menu-heading">
		Pages for logged out editors <a href="Help_Introduction.html" aria-label="Learn more about editing"><span>learn more</span></a>
	</div>
	<div class="vector-menu-content">
		
		<ul class="vector-menu-content-list">
			
			<li id="pt-anoncontribs" class="mw-list-item"><a href="Special_MyContributions.html" title="A list of edits made from this IP address [y]" accesskey="y"><span>Contributions</span></a></li><li id="pt-anontalk" class="mw-list-item"><a href="Special_MyTalk.html" title="Discussion about edits from this IP address [n]" accesskey="n"><span>Talk</span></a></li>
		</ul>
		
	</div>
</div>

	
	</div>
</div>

</nav>

		</div>
	</header>
</div>
<div class="mw-page-container">
	<div class="mw-page-container-inner">
		<div class="vector-sitenotice-container">
			<div id="siteNotice"><!-- CentralNotice --></div>
		</div>
		<div class="vector-column-start">
			<div class="vector-main-menu-container">
		<div id="mw-navigation">
			<nav id="mw-panel" class="vector-main-menu-landmark" aria-label="Site">
				<div id="vector-main-menu-pinned-container" class="vector-pinned-container">
				
				</div>
		</nav>
		</div>
	</div>
	<div class="vector-sticky-pinned-container">
				<nav id="mw-panel-toc" aria-label="Contents" data-event-name="ui.sidebar-toc" class="mw-table-of-contents-container vector-toc-landmark">
					<div id="vector-toc-pinned-container" class="vector-pinned-container">
					<div id="vector-toc" class="vector-toc vector-pinnable-element">
	<div
	class="vector-pinnable-header vector-toc-pinnable-header vector-pinnable-header-pinned"
	data-feature-name="toc-pinned"
	data-pinnable-element-id="vector-toc"
	
	
>
	<h2 class="vector-pinnable-header-label">Contents</h2>
	<button class="vector-pinnable-header-toggle-button vector-pinnable-header-pin-button" data-event-name="pinnable-header.vector-toc.pin">move to sidebar</button>
	<button class="vector-pinnable-header-toggle-button vector-pinnable-header-unpin-button" data-event-name="pinnable-header.vector-toc.unpin">hide</button>
</div>


	<ul class="vector-toc-contents" id="mw-panel-toc-list">
		<li id="toc-mw-content-text"
			class="vector-toc-list-item vector-toc-level-1">
			<a href="#" class="vector-toc-link">
				<div class="vector-toc-text">Beginning</div>
			</a>
		</li>
		<li id="toc-Wiki_Loves_Africa_2019"
		class="vector-toc-list-item vector-toc-level-1 vector-toc-list-item-expanded">
		<a class="vector-toc-link" href="#Wiki_Loves_Africa_2019">
			<div class="vector-toc-text">
				<span class="vector-toc-numb">1</span>
				<span>Wiki Loves Africa 2019</span>
			</div>
		</a>
		
		<ul id="toc-Wiki_Loves_Africa_2019-sublist" class="vector-toc-list">
		</ul>
	</li>
	<li id="toc-Wiki_Loves_Africa_2020"
		class="vector-toc-list-item vector-toc-level-1 vector-toc-list-item-expanded">
		<a class="vector-toc-link" href="#Wiki_Loves_Africa_2020">
			<div class="vector-toc-text">
				<span class="vector-toc-numb">2</span>
				<span>Wiki Loves Africa 2020</span>
			</div>
		</a>
		
		<ul id="toc-Wiki_Loves_Africa_2020-sublist" class="vector-toc-list">
		</ul>
	</li>
	<li id="toc-Wiki_Loves_Earth_2020_in_Kenya_is_open!"
		class="vector-toc-list-item vector-toc-level-1 vector-toc-list-item-expanded">
		<a class="vector-toc-link" href="#Wiki_Loves_Earth_2020_in_Kenya_is_open!">
			<div class="vector-toc-text">
				<span class="vector-toc-numb">3</span>
				<span>Wiki Loves Earth 2020 in Kenya is open!</span>
			</div>
		</a>
		
		<ul id="toc-Wiki_Loves_Earth_2020_in_Kenya_is_open!-sublist" class="vector-toc-list">
		</ul>
	</li>
	<li id="toc-Study_shows_Wiki_Love_Africa_recruitment_makes_a_difference"
		class="vector-toc-list-item vector-toc-level-1 vector-toc-list-item-expanded">
		<a class="vector-toc-link" href="#Study_shows_Wiki_Love_Africa_recruitment_makes_a_difference">
			<div class="vector-toc-text">
				<span class="vector-toc-numb">4</span>
				<span>Study shows Wiki Love Africa recruitment makes a difference</span>
			</div>
		</a>
		
		<ul id="toc-Study_shows_Wiki_Love_Africa_recruitment_makes_a_difference-sublist" class="vector-toc-list">
		</ul>
	</li>
	<li id="toc-Wiki_Loves_Africa"
		class="vector-toc-list-item vector-toc-level-1 vector-toc-list-item-expanded">
		<a class="vector-toc-link" href="#Wiki_Loves_Africa">
			<div class="vector-toc-text">
				<span class="vector-toc-numb">5</span>
				<span>Wiki Loves Africa</span>
			</div>
		</a>
		
		<ul id="toc-Wiki_Loves_Africa-sublist" class="vector-toc-list">
		</ul>
	</li>
</ul>
</div>

					</div>
		</nav>
			</div>
		</div>
		<div class="mw-content-container">
			<main id="content" class="mw-body">
				<header class="mw-body-header vector-page-titlebar no-font-mode-scale">
					<nav aria-label="Contents" class="vector-toc-landmark">
						
<div id="vector-page-titlebar-toc" class="vector-dropdown vector-page-titlebar-toc vector-button-flush-left"  title="Table of Contents" >
	<input type="checkbox" id="vector-page-titlebar-toc-checkbox" role="button" aria-haspopup="true" data-event-name="ui.dropdown-vector-page-titlebar-toc" class="vector-dropdown-checkbox "  aria-label="Toggle the table of contents"  >
	<label id="vector-page-titlebar-toc-label" for="vector-page-titlebar-toc-checkbox" class="vector-dropdown-label cdx-button cdx-button--fake-button cdx-button--fake-button--enabled cdx-button--weight-quiet cdx-button--icon-only " aria-hidden="true"  ><span class="vector-icon mw-ui-icon-listBullet mw-ui-icon-wikimedia-listBullet"></span>

<span class="vector-dropdown-label-text">Toggle the table of contents</span>
	</label>
	<div class="vector-dropdown-content">


							<div id="vector-page-titlebar-toc-unpinned-container" class="vector-unpinned-container">
			</div>
		
	</div>
</div>

					</nav>
					<h1 id="firstHeading" class="firstHeading mw-first-heading"><span class="mw-page-title-namespace">User talk</span><span class="mw-page-title-separator">:</span><span class="mw-page-title-main">Kungu01</span></h1>
							<a href="https://commons.wikimedia.org/w/index.php?title=User_talk:Kungu01&amp;action=edit&amp;section=new" class="cdx-button cdx-button--fake-button cdx-button--fake-button--enabled cdx-button--weight-quiet cdx-button--action-progressive" id="ca-addsection" data-event-name="addsection-header"><span class="vector-icon mw-ui-icon-speechBubbleAdd-progressive mw-ui-icon-wikimedia-speechBubbleAdd-progressive"></span>

<span>Add topic</span>
			</a>
</header>
				<div class="vector-page-toolbar vector-feature-custom-font-size-clientpref--excluded">
					<div class="vector-page-toolbar-container">
						<div id="left-navigation">
							<nav aria-label="Namespaces">
								
<div id="p-associated-pages" class="vector-menu vector-menu-tabs mw-portlet mw-portlet-associated-pages"  >
	<div class="vector-menu-content">
		
		<ul class="vector-menu-content-list">
			
			<li id="ca-nstab-user" class="new vector-tab-noicon mw-list-item"><a href="https://commons.wikimedia.org/w/index.php?title=User:Kungu01&amp;action=edit&amp;redlink=1" class="new" title="View the user page (page does not exist) [c]" accesskey="c"><span>User page</span></a></li><li id="ca-talk" class="selected vector-tab-noicon mw-list-item"><a href="User_talk_Kungu01.html" rel="discussion" title="Discussion about the content page [t]" accesskey="t"><span>Discussion</span></a></li>
		</ul>
		
	</div>
</div>

								
<div id="vector-variants-dropdown" class="vector-dropdown emptyPortlet"  >
	<input type="checkbox" id="vector-variants-dropdown-checkbox" role="button" aria-haspopup="true" data-event-name="ui.dropdown-vector-variants-dropdown" class="vector-dropdown-checkbox " aria-label="Change language variant"   >
	<label id="vector-variants-dropdown-label" for="vector-variants-dropdown-checkbox" class="vector-dropdown-label cdx-button cdx-button--fake-button cdx-button--fake-button--enabled cdx-button--weight-quiet" aria-hidden="true"  ><span class="vector-dropdown-label-text">English</span>
	</label>
	<div class="vector-dropdown-content">


					
<div id="p-variants" class="vector-menu mw-portlet mw-portlet-variants emptyPortlet"  >
	<div class="vector-menu-content">
		
		<ul class="vector-menu-content-list">
			
			
		</ul>
		
	</div>
</div>

				
	</div>
</div>

							</nav>
						</div>
						<div id="right-navigation" class="vector-collapsible">
							<nav aria-label="Views">
								
<div id="p-views" class="vector-menu vector-menu-tabs mw-portlet mw-portlet-views"  >
	<div class="vector-menu-content">
		
		<ul class="vector-menu-content-list">
			
			<li id="ca-view" class="selected vector-tab-noicon mw-list-item"><a href="User_talk_Kungu01.html"><span>Read</span></a></li><li id="ca-edit" class="istalk vector-tab-noicon mw-list-item"><a href="../w/indexf60d.html?title=User_talk:Kungu01&amp;action=edit" title="Edit this page [e]" accesskey="e"><span>Edit</span></a></li><li id="ca-history" class="vector-tab-noicon mw-list-item"><a href="https://commons.wikimedia.org/w/index.php?title=User_talk:Kungu01&amp;action=history" title="Past revisions of this page [h]" accesskey="h"><span>View history</span></a></li>
		</ul>
		
	</div>
</div>

							</nav>
				
							<nav class="vector-page-tools-landmark" aria-label="Page tools">
								
<div id="vector-page-tools-dropdown" class="vector-dropdown vector-page-tools-dropdown"  >
	<input type="checkbox" id="vector-page-tools-dropdown-checkbox" role="button" aria-haspopup="true" data-event-name="ui.dropdown-vector-page-tools-dropdown" class="vector-dropdown-checkbox "  aria-label="Tools"  >
	<label id="vector-page-tools-dropdown-label" for="vector-page-tools-dropdown-checkbox" class="vector-dropdown-label cdx-button cdx-button--fake-button cdx-button--fake-button--enabled cdx-button--weight-quiet" aria-hidden="true"  ><span class="vector-dropdown-label-text">Tools</span>
	</label>
	<div class="vector-dropdown-content">


									<div id="vector-page-tools-unpinned-container" class="vector-unpinned-container">
						
<div id="vector-page-tools" class="vector-page-tools vector-pinnable-element">
	<div
	class="vector-pinnable-header vector-page-tools-pinnable-header vector-pinnable-header-unpinned"
	data-feature-name="page-tools-pinned"
	data-pinnable-element-id="vector-page-tools"
	data-pinned-container-id="vector-page-tools-pinned-container"
	data-unpinned-container-id="vector-page-tools-unpinned-container"
>
	<div class="vector-pinnable-header-label">Tools</div>
	<button class="vector-pinnable-header-toggle-button vector-pinnable-header-pin-button" data-event-name="pinnable-header.vector-page-tools.pin">move to sidebar</button>
	<button class="vector-pinnable-header-toggle-button vector-pinnable-header-unpin-button" data-event-name="pinnable-header.vector-page-tools.unpin">hide</button>
</div>

	
<div id="p-cactions" class="vector-menu mw-portlet mw-portlet-cactions emptyPortlet vector-has-collapsible-items"  title="More options" >
	<div class="vector-menu-heading">
		Actions
	</div>
	<div class="vector-menu-content">
		
		<ul class="vector-menu-content-list">
			
			<li id="ca-more-view" class="selected vector-more-collapsible-item mw-list-item"><a href="User_talk_Kungu01.html"><span>Read</span></a></li><li id="ca-more-edit" class="istalk vector-more-collapsible-item mw-list-item"><a href="../w/indexf60d.html?title=User_talk:Kungu01&amp;action=edit" title="Edit this page [e]" accesskey="e"><span>Edit</span></a></li><li id="ca-more-addsection" class="vector-more-collapsible-item mw-list-item"><a href="https://commons.wikimedia.org/w/index.php?title=User_talk:Kungu01&amp;action=edit&amp;section=new"><span>Add topic</span></a></li><li id="ca-more-history" class="vector-more-collapsible-item mw-list-item"><a href="https://commons.wikimedia.org/w/index.php?title=User_talk:Kungu01&amp;action=history"><span>View history</span></a></li>
		</ul>
		
	</div>
</div>

<div id="p-tb" class="vector-menu mw-portlet mw-portlet-tb"  >
	<div class="vector-menu-heading">
		General
	</div>
	<div class="vector-menu-content">
		
		<ul class="vector-menu-content-list">
			
			<li id="t-whatlinkshere" class="mw-list-item"><a href="Special_WhatLinksHere/User_talk_Kungu01.html" title="A list of all wiki pages that link here [j]" accesskey="j"><span>What links here</span></a></li><li id="t-recentchangeslinked" class="mw-list-item"><a href="Special_RecentChangesLinked/User_talk_Kungu01.html" rel="nofollow" title="Recent changes in pages linked from this page [k]" accesskey="k"><span>Related changes</span></a></li><li id="tb-uploads" class="mw-list-item"><a href="https://commons.wikimedia.org/w/index.php?title=Special:ListFiles/Kungu01&amp;ilshowall=1" title="A list of uploads by this user"><span>User uploads</span></a></li><li id="t-contributions" class="mw-list-item"><a href="Special_Contributions/Kungu01.html" title="A list of contributions by this user"><span>User contributions</span></a></li><li id="t-log" class="mw-list-item"><a href="Special_Log/Kungu01.html"><span>Logs</span></a></li><li id="t-userrights" class="mw-list-item"><a href="Special_UserRights/Kungu01.html"><span>View user groups</span></a></li><li id="t-permalink" class="mw-list-item"><a href="https://commons.wikimedia.org/w/index.php?title=User_talk:Kungu01&amp;oldid=538859226" title="Permanent link to this revision of this page"><span>Permanent link</span></a></li><li id="t-info" class="mw-list-item"><a href="https://commons.wikimedia.org/w/index.php?title=User_talk:Kungu01&amp;action=info" title="More information about this page"><span>Page information</span></a></li><li id="t-urlshortener" class="mw-list-item"><a href="https://commons.wikimedia.org/w/index.php?title=Special:UrlShortener&amp;url=https%3A%2F%2Fcommons.wikimedia.org%2Fwiki%2FUser_talk%3AKungu01"><span>Get shortened URL</span></a></li><li id="t-urlshortener-qrcode" class="mw-list-item"><a href="https://commons.wikimedia.org/w/index.php?title=Special:QrCode&amp;url=https%3A%2F%2Fcommons.wikimedia.org%2Fwiki%2FUser_talk%3AKungu01"><span>Download QR code</span></a></li>
		</ul>
		
	</div>
</div>

<div id="p-coll-print_export" class="vector-menu mw-portlet mw-portlet-coll-print_export"  >
	<div class="vector-menu-heading">
		Print/export
	</div>
	<div class="vector-menu-content">
		
		<ul class="vector-menu-content-list">
			
			<li id="coll-create_a_book" class="mw-list-item"><a href="https://commons.wikimedia.org/w/index.php?title=Special:Book&amp;bookcmd=book_creator&amp;referer=User+talk%3AKungu01"><span>Create a book</span></a></li><li id="coll-download-as-rl" class="mw-list-item"><a href="https://commons.wikimedia.org/w/index.php?title=Special:DownloadAsPdf&amp;page=User_talk%3AKungu01&amp;action=show-download-screen"><span>Download as PDF</span></a></li><li id="t-print" class="mw-list-item"><a href="https://commons.wikimedia.org/w/index.php?title=User_talk:Kungu01&amp;printable=yes" title="Printable version of this page [p]" accesskey="p"><span>Printable version</span></a></li>
		</ul>
		
	</div>
</div>

<div id="p-wikibase-otherprojects" class="vector-menu mw-portlet mw-portlet-wikibase-otherprojects emptyPortlet"  >
	<div class="vector-menu-heading">
		In other projects
	</div>
	<div class="vector-menu-content">
		
		<ul class="vector-menu-content-list">
			
			
		</ul>
		
	</div>
</div>

</div>

									</div>
				
	</div>
</div>

							</nav>
						</div>
					</div>
				</div>
				<div class="vector-column-end no-font-mode-scale">
					<div class="vector-sticky-pinned-container">
						<nav class="vector-page-tools-landmark" aria-label="Page tools">
							<div id="vector-page-tools-pinned-container" class="vector-pinned-container">
				
							</div>
		</nav>
						<nav class="vector-appearance-landmark" aria-label="Appearance">
							<div id="vector-appearance-pinned-container" class="vector-pinned-container">
				<div id="vector-appearance" class="vector-appearance vector-pinnable-element">
	<div
	class="vector-pinnable-header vector-appearance-pinnable-header vector-pinnable-header-pinned"
	data-feature-name="appearance-pinned"
	data-pinnable-element-id="vector-appearance"
	data-pinned-container-id="vector-appearance-pinned-container"
	data-unpinned-container-id="vector-appearance-unpinned-container"
>
	<div class="vector-pinnable-header-label">Appearance</div>
	<button class="vector-pinnable-header-toggle-button vector-pinnable-header-pin-button" data-event-name="pinnable-header.vector-appearance.pin">move to sidebar</button>
	<button class="vector-pinnable-header-toggle-button vector-pinnable-header-unpin-button" data-event-name="pinnable-header.vector-appearance.unpin">hide</button>
</div>


</div>

							</div>
		</nav>
					</div>
				</div>
				<div id="bodyContent" class="vector-body" aria-labelledby="firstHeading" data-mw-ve-target-container>
					<div class="vector-body-before-content">
							<div class="mw-indicators">
		</div>

						<div id="siteSub" class="noprint">From Wikimedia Commons, the free media repository</div>
					</div>
					<div id="contentSub"><div id="mw-content-subtitle"><div class="ext-discussiontools-init-pageframe-latestcomment">Latest comment: <a href="#c-Wikimedia_Commons_Welcome-2014-11-04T07:45:00.000Z">10 years ago</a> by Wikimedia Commons Welcome</div></div></div>
					
					
					<div id="mw-content-text" class="mw-body-content"><div class="mw-content-ltr mw-parser-output" lang="en" dir="ltr"><span data-mw-thread-id="h--2014-11-04T07:45:00.000Z"><span data-mw-comment-start="" id="h--2014-11-04T07:45:00.000Z"></span><span data-mw-comment-end="h--2014-11-04T07:45:00.000Z"></span></span><span data-mw-comment-start="" id="c-Wikimedia_Commons_Welcome-2014-11-04T07:45:00.000Z"></span><style data-mw-deduplicate="TemplateStyles:r918678224">.mw-parser-output .welcomeBox-ltr{direction:ltr}.mw-parser-output .welcomeBox-rtl{direction:rtl}.mw-parser-output .welcomeheader{font-size:110%;font-weight:bold}.mw-parser-output .welcomebody{margin:0 0 1em;width:100%;border-spacing:0;border-collapse:collapse}.mw-parser-output .welcomebody td{padding:0;vertical-align:top;border-style:solid;border-width:1px}.mw-parser-output .welcomebody-column-1{width:45%;background-color:#faf6ed}.mw-parser-output .welcomebody-column-2{width:55%;background:#f1f5fc}.mw-parser-output .welcomebody td.welcomebody-column-separator{padding:0 0.5em;border:none}.mw-parser-output .welcomebody-column-1,.mw-parser-output .welcomebody-column-1 div{border-color:#fad67d}.mw-parser-output .welcomebody-column-2,.mw-parser-output .welcomebody-column-2 div{border-color:#abd5f5}.mw-parser-output .welcome-subheader{border-bottom-style:solid;border-bottom-width:1px;padding:0.2em 0.5em;font-size:110%;font-weight:bold}.mw-parser-output .welcomebody-column-1 .welcome-subheader{background-color:#faecc8}.mw-parser-output .welcomebody-column-2 .welcome-subheader{background-color:#d0e5f5}.mw-parser-output .welcome-bodytext{padding:0.4em 1em 0.3em}.mw-parser-output .welcome-bodytext:not(:last-child){border-bottom-style:solid;border-bottom-width:1px}html.skin-theme-clientpref-night .mw-parser-output .welcomebody-column-1{background-color:#261e0b}html.skin-theme-clientpref-night .mw-parser-output .welcomebody-column-2{background:#091E2A}html.skin-theme-clientpref-night .mw-parser-output .welcomebody-column-1,.mw-parser-output .welcomebody-column-1 div{border-color:#5F4504}html.skin-theme-clientpref-night .mw-parser-output .welcomebody-column-2,.mw-parser-output .welcomebody-column-2 div{border-color:#0B3B60}html.skin-theme-clientpref-night .mw-parser-output .welcomebody-column-1 .welcome-subheader{background-color:#3C2D05}html.skin-theme-clientpref-night .mw-parser-output .welcomebody-column-2 .welcome-subheader{background-color:#123B59}@media screen and (prefers-color-scheme:dark){html.skin-theme-clientpref-os .mw-parser-output .welcomebody-column-1{background-color:#261e0b}html.skin-theme-clientpref-os .mw-parser-output .welcomebody-column-2{background:#091E2A}html.skin-theme-clientpref-os .mw-parser-output .welcomebody-column-1,.mw-parser-output .welcomebody-column-1 div{border-color:#5F4504}html.skin-theme-clientpref-os .mw-parser-output .welcomebody-column-2,.mw-parser-output .welcomebody-column-2 div{border-color:#0B3B60}html.skin-theme-clientpref-os .mw-parser-output .welcomebody-column-1 .welcome-subheader{background-color:#3C2D05}html.skin-theme-clientpref-os .mw-parser-output .welcomebody-column-2 .welcome-subheader{background-color:#123B59}}@media screen and (max-width:719px){.mw-parser-output .welcomebody,.mw-parser-output .welcomebody>tbody,.mw-parser-output .welcomebody>tbody>tr,.mw-parser-output .welcomebody>tbody>tr>td{display:block}.mw-parser-output .welcomebody-column-1,.mw-parser-output .welcomebody-column-2{width:100%}.mw-parser-output .welcomebody td.welcomebody-column-separator{padding:0.5em 0}}</style>
<div class="welcomeBox welcomeBox-ltr layouttemplate">
<div class="welcomeheader" lang="en">Welcome to Wikimedia Commons, Kungu01!</div>
<div class="toccolours noprint" style="clear:both; margin:0 0 1em; line-height:1;"><b style="color:#888; vertical-align:top; font-size:x-small;"></b>
<div style="line-height:1.3"><span style="font-size:x-small;line-height:140%" class="plainlinks noprint"><a class="external text" href="Template_Welcome/i18n/af.html">Afrikaans</a>&nbsp;| <a class="external text" href="Template_Welcome/i18n/als.html">Alemannisch</a>&nbsp;| <a class="external text" href="Template_Welcome/i18n/ast.html">asturianu</a>&nbsp;| <a class="external text" href="Template_Welcome/i18n/az.html">azərbaycanca</a>&nbsp;| <a class="external text" href="Template_Welcome/i18n/bjn.html">Bahasa Banjar</a>&nbsp;| <a class="external text" href="Template_Welcome/i18n/ca.html">català</a>&nbsp;| <a class="external text" href="Template_Welcome/i18n/cs.html">čeština</a>&nbsp;| <a class="external text" href="Template_Welcome/i18n/cy.html">Cymraeg</a>&nbsp;| <a class="external text" href="Template_Welcome/i18n/da.html">dansk</a>&nbsp;| <a class="external text" href="Template_Welcome/i18n/de.html">Deutsch</a>&nbsp;| <a class="external text" href="Template_Welcome/i18n/de-formal.html">Deutsch (Sie-Form)‎</a>&nbsp;| <a class="external text" href="Template_Welcome/i18n/en.html">English</a>&nbsp;| <a class="external text" href="Template_Welcome/i18n/es.html">español</a>&nbsp;| <a class="external text" href="Template_Welcome/i18n/eo.html">Esperanto</a>&nbsp;| <a class="external text" href="Template_Welcome/i18n/eu.html">euskara</a>&nbsp;| <a class="external text" href="Template_Welcome/i18n/ext.html">estremeñu</a>&nbsp;| <a class="external text" href="Template_Welcome/i18n/fr.html">français</a>&nbsp;| <a class="external text" href="Template_Welcome/i18n/fy.html">Frysk</a>&nbsp;| <a class="external text" href="Template_Welcome/i18n/gl.html">galego</a>&nbsp;| <a class="external text" href="Template_Welcome/i18n/hr.html">hrvatski</a>&nbsp;| <a class="external text" href="Template_Welcome/i18n/id.html">Bahasa Indonesia</a>&nbsp;| <a class="external text" href="Template_Welcome/i18n/ia.html">interlingua</a>&nbsp;| <a class="external text" href="Template_Welcome/i18n/ie.html">Interlingue</a>&nbsp;| <a class="external text" href="Template_Welcome/i18n/is.html">íslenska</a>&nbsp;| <a class="external text" href="Template_Welcome/i18n/it.html">italiano</a>&nbsp;| <a class="external text" href="Template_Welcome/i18n/sw.html">Kiswahili</a>&nbsp;| <a class="external text" href="Template_Welcome/i18n/ku.html">Kurdî</a>&nbsp;| <a class="external text" href="Template_Welcome/i18n/la.html">Latina</a>&nbsp;| <a class="external text" href="Template_Welcome/i18n/lt.html">lietuvių</a>&nbsp;| <a class="external text" href="Template_Welcome/i18n/hu.html">magyar</a>&nbsp;| <a class="external text" href="Template_Welcome/i18n/ms.html">Bahasa Melayu</a>&nbsp;| <a class="external text" href="Template_Welcome/i18n/mwl.html">Mirandés</a>&nbsp;| <a class="external text" href="Template_Welcome/i18n/nl.html">Nederlands</a>&nbsp;| <a class="external text" href="Template_Welcome/i18n/nb.html">norsk bokmål</a>&nbsp;| <a class="external text" href="Template_Welcome/i18n/oc.html">occitan</a>&nbsp;| <a class="external text" href="Template_Welcome/i18n/nds.html">Plattdüütsch</a>&nbsp;| <a class="external text" href="Template_Welcome/i18n/pl.html">polski</a>&nbsp;| <a class="external text" href="Template_Welcome/i18n/pt.html">português</a>&nbsp;| <a class="external text" href="Template_Welcome/i18n/pt-br.html">português do Brasil</a>&nbsp;| <a class="external text" href="Template_Welcome/i18n/ro.html">română</a>&nbsp;| <a class="external text" href="Template_Welcome/i18n/rm.html">rumantsch</a>&nbsp;| <a class="external text" href="Template_Welcome/i18n/sco.html">Scots</a>&nbsp;| <a class="external text" href="Template_Welcome/i18n/sq.html">shqip</a>&nbsp;| <a class="external text" href="Template_Welcome/i18n/scn.html">sicilianu</a>&nbsp;| <a class="external text" href="Template_Welcome/i18n/sk.html">slovenčina</a>&nbsp;| <a class="external text" href="Template_Welcome/i18n/sl.html">slovenščina</a>&nbsp;| <a class="external text" href="Template_Welcome/i18n/su.html">Basa Sunda</a>&nbsp;| <a class="external text" href="Template_Welcome/i18n/fi.html">suomi</a>&nbsp;| <a class="external text" href="Template_Welcome/i18n/sv.html">svenska</a>&nbsp;| <a class="external text" href="Template_Welcome/i18n/tl.html">Tagalog</a>&nbsp;| <a class="external text" href="Template_Welcome/i18n/tr.html">Türkçe</a>&nbsp;| <a class="external text" href="Template_Welcome/i18n/vec.html">vèneto</a>&nbsp;| <a class="external text" href="Template_Welcome/i18n/vi.html">Tiếng Việt</a>&nbsp;| <a class="external text" href="Template_Welcome/i18n/diq.html">Zazaki</a>&nbsp;| <a class="external text" href="Template_Welcome/i18n/el.html">Ελληνικά</a>&nbsp;| <a class="external text" href="Template_Welcome/i18n/be.html">беларуская</a>&nbsp;| <a class="external text" href="Template_Welcome/i18n/be-tarask.html">беларуская (тарашкевіца)‎</a>&nbsp;| <a class="external text" href="Template_Welcome/i18n/bg.html">български</a>&nbsp;| <a class="external text" href="Template_Welcome/i18n/os.html">Ирон</a>&nbsp;| <a class="external text" href="Template_Welcome/i18n/mk.html">македонски</a>&nbsp;| <a class="external text" href="Template_Welcome/i18n/ce.html">нохчийн</a>&nbsp;| <a class="external text" href="Template_Welcome/i18n/ru.html">русский</a>&nbsp;| <a class="external text" href="Template_Welcome/i18n/sr.html">српски / srpski</a>&nbsp;| <a class="external text" href="Template_Welcome/i18n/tg.html">тоҷикӣ</a>&nbsp;| <a class="external text" href="Template_Welcome/i18n/uk.html">українська</a>&nbsp;| <a class="external text" href="Template_Welcome/i18n/ka.html">ქართული</a>&nbsp;| <a class="external text" href="Template_Welcome/i18n/hy.html">Հայերեն</a>&nbsp;| <a class="external text" href="Template_Welcome/i18n/ne.html">नेपाली</a>&nbsp;| <a class="external text" href="Template_Welcome/i18n/bho.html">भोजपुरी</a>&nbsp;| <a class="external text" href="Template_Welcome/i18n/mr.html">मराठी</a>&nbsp;| <a class="external text" href="Template_Welcome/i18n/hi.html">हिन्दी</a>&nbsp;| <a class="external text" href="Template_Welcome/i18n/as.html">অসমীয়া</a>&nbsp;| <a class="external text" href="Template_Welcome/i18n/bn.html">বাংলা</a>&nbsp;| <a class="external text" href="Template_Welcome/i18n/ta.html">தமிழ்</a>&nbsp;| <a class="external text" href="Template_Welcome/i18n/ml.html">മലയാളം</a>&nbsp;| <a class="external text" href="Template_Welcome/i18n/si.html">සිංහල</a>&nbsp;| <a class="external text" href="Template_Welcome/i18n/th.html">ไทย</a>&nbsp;| <a class="external text" href="Template_Welcome/i18n/shn.html">ၽႃႇသႃႇတႆး </a>&nbsp;| <a class="external text" href="Template_Welcome/i18n/my.html">မြန်မာဘာသာ</a>&nbsp;| <a class="external text" href="Template_Welcome/i18n/ko.html">한국어</a>&nbsp;| <a class="external text" href="Template_Welcome/i18n/ja.html">日本語</a>&nbsp;| <a class="external text" href="Template_Welcome/i18n/zh.html">中文</a>&nbsp;| <a class="external text" href="Template_Welcome/i18n/zh-tw.html">中文（台灣）‎</a>&nbsp;| <a class="external text" href="Template_Welcome/i18n/zh-hans.html">中文（简体）‎</a>&nbsp;| <a class="external text" href="Template_Welcome/i18n/zh-hant.html">中文（繁體）‎</a>&nbsp;| <a class="external text" href="Template_Welcome/i18n/yue.html">粵語</a>&nbsp;| <a class="external text" href="Template_Welcome/i18n/he.html">עברית</a>&nbsp;| <bdi><a class="external text" href="Template_Welcome/i18n/ur.html">اردو</a></bdi>&nbsp;| <bdi><a class="external text" href="Template_Welcome/i18n/ar.html">العربية</a></bdi>&nbsp;| <bdi><a class="external text" href="Template_Welcome/i18n/azb.html">تۆرکجه</a></bdi>&nbsp;| <bdi><a class="external text" href="Template_Welcome/i18n/sd.html">سنڌي</a></bdi>&nbsp;| <bdi><a class="external text" href="Template_Welcome/i18n/fa.html">فارسی</a></bdi>&nbsp;| <small class="plainlinks"><a class="external text" href="https://commons.wikimedia.org/w/index.php?title=Template:Welcome/i18n/lang&amp;action=edit">+/−</a></small></span></div>
</div>
<table class="welcomebody plainlinks" lang="en">
<tbody><tr>
<td class="welcomebody-column-1">
<div class="welcome-subheader"><span typeof="mw:File"><a href="File_Gnome-colors-alacarte.html" class="mw-file-description"><img src="../../upload.wikimedia.org/wikipedia/commons/thumb/4/44/Gnome-colors-alacarte.svg/40px-Gnome-colors-alacarte.svg.png" decoding="async" width="21" height="21" class="mw-file-element" srcset="https://upload.wikimedia.org/wikipedia/commons/thumb/4/44/Gnome-colors-alacarte.svg/42px-Gnome-colors-alacarte.svg.png 2x" data-file-width="48" data-file-height="48"></a></span> First steps tutorial</div>
<div class="welcome-bodytext">
<p>Our <b><a href="Special_MyLanguage/Commons_First_steps.html" title="Special:MyLanguage/Commons:First steps">first steps tour</a></b> and our <b><a href="Special_MyLanguage/Commons_FAQ.html" title="Special:MyLanguage/Commons:FAQ">frequently asked questions</a></b> will help you a lot after registration. They explain how to customize the interface (for example the language), how to <a href="Special_UploadWizard.html" title="Special:UploadWizard">upload files</a> and our basic <b><a href="Special_MyLanguage/Commons_Licensing.html" title="Special:MyLanguage/Commons:Licensing">licensing policy</a></b> (Wikimedia Commons only accepts free content).
</p><p>You don't need technical skills in order to contribute here. <i>Be bold</i> when contributing and <i>assume good faith</i> when interacting with others. This is a <i><a href="https://en.wikipedia.org/wiki/wiki" class="extiw" title="w:wiki">wiki</a></i>.
</p>
</div>
<div class="welcome-subheader"><span typeof="mw:File"><a href="File_Help-browser.html" class="mw-file-description"><img src="../../upload.wikimedia.org/wikipedia/commons/thumb/4/44/Help-browser.svg/40px-Help-browser.svg.png" decoding="async" width="21" height="21" class="mw-file-element" srcset="https://upload.wikimedia.org/wikipedia/commons/thumb/4/44/Help-browser.svg/42px-Help-browser.svg.png 2x" data-file-width="48" data-file-height="48"></a></span> Getting help</div>
<div class="welcome-bodytext">
<p>More information is available at the <a href="Commons_Community_portal.html" title="Commons:Community portal">community portal</a>. You may ask questions at the <b><a href="Commons_Help_desk.html" title="Commons:Help desk">help desk</a></b>, <a href="Commons_Village_pump.html" title="Commons:Village pump">village pump</a> or on <a href="https://en.wikipedia.org/wiki/Wikipedia:IRC/Tutorial" class="extiw" title="w:Wikipedia:IRC/Tutorial">IRC</a> channel <a href="irc://irc.libera.chat/wikimedia-commons" class="extiw" title="irc:wikimedia-commons">#wikimedia-commons</a> (<a rel="nofollow" class="external text" href="https://web.libera.chat/?#wikimedia-commons">webchat</a>). You can also contact an <a href="Special_MyLanguage/Commons_Administrators.html" title="Special:MyLanguage/Commons:Administrators">administrator</a> on their talk page. If you have a specific copyright question, ask at <a href="Commons_Village_pump/Copyright.html" title="Commons:Village pump/Copyright">the copyright village pump</a>.
</p>
</div>
</td>
<td class="welcomebody-column-separator">
</td>
<td class="welcomebody-column-2">
<div class="welcome-subheader"><span typeof="mw:File"><a href="File_Gnome-applications-utilities.html" class="mw-file-description"><img src="../../upload.wikimedia.org/wikipedia/commons/thumb/c/c7/Gnome-applications-utilities.svg/40px-Gnome-applications-utilities.svg.png" decoding="async" width="21" height="21" class="mw-file-element" srcset="https://upload.wikimedia.org/wikipedia/commons/thumb/c/c7/Gnome-applications-utilities.svg/42px-Gnome-applications-utilities.svg.png 2x" data-file-width="48" data-file-height="48"></a></span> Goodies, tips, and tricks</div>
<div class="welcome-bodytext">
<ul><li>Put <a href="Commons_Babel.html" title="Commons:Babel">Babel</a> boxes on <a href="Special_MyPage.html" title="Special:MyPage">your user page</a> so others know what languages you can speak and indicate your <a href="Category_Graphics_abilities.html" title="Category:Graphics abilities">graphic abilities</a>.</li>
<li>You can find the files you have uploaded in your <a href="Special_MyUploads.html" title="Special:MyUploads">Uploads</a>.</li>
<li>Please <a href="Special_MyLanguage/COM_SIGN.html" title="Special:MyLanguage/COM:SIGN">sign your name</a> on Talk pages by typing ~~~~.</li>
<li>To link to an image page without embedding the image, type: <kbd>[[:File:Foo.jpg]]</kbd>, which produces: <kbd><a href="File_Foo.html" title="File:Foo.jpg">File:Foo.jpg</a></kbd>.</li>
<li>You may enable <a href="https://commons.wikimedia.org/w/index.php?title=Special:UserLogin&amp;returnto=Special%3APreferences&amp;returntoquery=&amp;warning=prefsnologintext2#mw-prefsection-gadgets" title="Special:Preferences">gadgets</a> (custom features) for your account.</li>
<li>There are <a href="Special_MyLanguage/Commons_Upload_tools.html" title="Special:MyLanguage/Commons:Upload tools">several tools</a> to upload files.</li>
<li>Have you edited Wikipedia before? <a href="Special_MyLanguage/Commons_For_Wikipedians.html" title="Special:MyLanguage/Commons:For Wikipedians">This guide</a> might be useful.</li></ul>
</div>
<div class="welcome-subheader"><span typeof="mw:File"><a href="File_Gnome-edit-delete.html" class="mw-file-description"><img src="../../upload.wikimedia.org/wikipedia/commons/thumb/9/96/Gnome-edit-delete.svg/40px-Gnome-edit-delete.svg.png" decoding="async" width="21" height="21" class="mw-file-element" srcset="https://upload.wikimedia.org/wikipedia/commons/thumb/9/96/Gnome-edit-delete.svg/42px-Gnome-edit-delete.svg.png 2x" data-file-width="48" data-file-height="48"></a></span> Made a mistake?</div>
<div class="welcome-bodytext">
<ul><li>Do you want to have a file renamed or moved? Use the <a href="Special_MyLanguage/Help_RenameLink.html" title="Special:MyLanguage/Help:RenameLink"><i>move link</i></a> and wait for an authorized user to rename it.</li>
<li>Do you want to have your recently uploaded picture removed? Tag it as <kbd><span style="white-space:nowrap">{{<a href="Template_Speedy.html" class="mw-redirect" title="Template:Speedy">speedy</a>|<a href="Commons_CSD.html" class="mw-redirect" title="Commons:CSD"><i>reason here</i></a>}}</span></kbd>. For more information read the <a href="Special_MyLanguage/Commons_Deletion_guidelines.html" title="Special:MyLanguage/Commons:Deletion guidelines">deletion guidelines</a>.</li></ul>
</div>
</td></tr></tbody></table>
</div>
<p>-- <span data-mw-comment-sig="c-Wikimedia_Commons_Welcome-2014-11-04T07:45:00.000Z"></span><a href="User_Wikimedia_Commons_Welcome.html" title="User:Wikimedia Commons Welcome">Wikimedia Commons Welcome</a> (<a href="User_talk_Wikimedia_Commons_Welcome.html" class="mw-redirect" title="User talk:Wikimedia Commons Welcome"><span class="signature-talk">talk</span></a>) <a href="User_talk_Kungu01.html#c-Wikimedia_Commons_Welcome-2014-11-04T07:45:00.000Z" class="ext-discussiontools-init-timestamplink" title="10 years ago">07:45, 4 November 2014 (UTC)</a><span class="ext-discussiontools-init-replylink-buttons" data-mw-thread-id="c-Wikimedia_Commons_Welcome-2014-11-04T07:45:00.000Z"><span id="ooui-php-1" class="ext-discussiontools-init-replybutton oo-ui-widget oo-ui-widget-enabled oo-ui-buttonElement oo-ui-buttonElement-frameless oo-ui-labelElement oo-ui-flaggedElement-progressive oo-ui-buttonWidget" data-ooui='{"_":"OO.ui.ButtonWidget","rel":["nofollow"],"framed":false,"label":"Reply","flags":["progressive"],"classes":["ext-discussiontools-init-replybutton"]}'><a role="button" tabindex="0" rel="nofollow" class="oo-ui-buttonElement-button"><span class="oo-ui-iconElement-icon oo-ui-iconElement-noIcon oo-ui-image-progressive"></span><span class="oo-ui-labelElement-label">Reply</span><span class="oo-ui-indicatorElement-indicator oo-ui-indicatorElement-noIndicator oo-ui-image-progressive"></span></a></span></span><span data-mw-comment-end="c-Wikimedia_Commons_Welcome-2014-11-04T07:45:00.000Z"></span>
</p>
<meta property="mw:PageProp/toc">
<div class="mw-heading mw-heading2 ext-discussiontools-init-section"><h2 id="Wiki_Loves_Africa_2019" data-mw-thread-id="h-Wiki_Loves_Africa_2019"><span data-mw-comment-start="" id="h-Wiki_Loves_Africa_2019"></span>Wiki Loves Africa 2019<span data-mw-comment-end="h-Wiki_Loves_Africa_2019"></span></h2><span class="mw-editsection"><span class="mw-editsection-bracket">[</span><a href="https://commons.wikimedia.org/w/index.php?title=User_talk:Kungu01&amp;action=edit&amp;section=1" title="Edit section: Wiki Loves Africa 2019"><span>edit</span></a><span class="mw-editsection-bracket">]</span></span></div>
<p>Dear Kungu01,
</p>
<figure class="mw-halign-right" typeof="mw:File"><a href="File_Wiki-Loves-Africa-logo.html" class="mw-file-description"><img src="../../upload.wikimedia.org/wikipedia/commons/thumb/3/35/Wiki-Loves-Africa-logo.png/250px-Wiki-Loves-Africa-logo.png" decoding="async" width="150" height="86" class="mw-file-element" srcset="https://upload.wikimedia.org/wikipedia/commons/thumb/3/35/Wiki-Loves-Africa-logo.png/330px-Wiki-Loves-Africa-logo.png 2x" data-file-width="545" data-file-height="312"></a><figcaption></figcaption></figure><p>You have contributed in the past to the <a href="Commons_Wiki_Loves_Africa.html" title="Commons:Wiki Loves Africa">Wiki Loves Africa</a> competition. Your images have helped people learn more about Africa and see Africa more clearly from an African perspective. Overall, nearly 5000 people like you have contributed to this photo contest, and contributed nearly 40000 images about Africa. Pictures from Wiki Loves Africa illustrate more than 7000 Wikipedia articles and they received... 278.859.973 views between 2015 through the end of 2018. WOW&nbsp;! How does it feel to be part of this and really make a difference when it come to Africa representation on Wikipedia&nbsp;? <br>
</p><p>This February, <b>you have another opportunity to create beautiful photographs on the theme of "play" that could change how the world understands people and culture in your part of Africa.</b>
</p><p>Wiki Loves Africa is a annual public contest where people across Africa can contribute media (photographs, video and audio) about their environment to Wikimedia Commons for use on Wikipedia and other project websites of the Wikimedia Foundation.
</p><p><b>When does it take place?</b><br>
The 2019 competition will start on 1st February 2019 and close on 1st March 2019.
</p><p><b>What should we contribute?</b><br>
Pictures&nbsp;! audios&nbsp;! videos&nbsp;! The <a href="Commons_Wiki_Loves_Africa_2019/Theme.html" title="Commons:Wiki Loves Africa 2019/Theme">theme</a> for the 2019 contest is... Play.<br>
This theme encompasses a host of approaches and is intentionally open to interpretation. The theme Play! encourages the submission of visual representations of joyful and serious games, sport, and recreation in the form of board or mental games, physical fun or contests, playful interactions, theatrical and musical performances, etc.
</p><p>In addition to the value your photos contribute to human understanding and african visibility on the Internet, there are several prizes on offer – including two separate prize categories for photos and media that encompass 
</p>
<ul><li>(a) <i>capture Women in sport</i> or</li>
<li>(b) <i>culturally specific and traditional formal forms of play, recreation or events</i>.</li></ul>
<p>For rules and information about how to participate, or to join the contest, click on:<b> <a href="Commons_Wiki_Loves_Africa_2019.html" title="Commons:Wiki Loves Africa 2019">Contribute to the Wiki Loves Africa photo contest</a></b>.
</p><p>Warmest, 
</p><p><a href="User_Anthere.html" title="User:Anthere">Anthere</a>, for the Wiki Loves Africa Team
</p><p>++++++++++
</p><p>Cher ou chère Kungu01,
</p>
<figure class="mw-halign-right" typeof="mw:File"><a href="File_Wiki-Loves-Africa-logo.html" class="mw-file-description"><img src="../../upload.wikimedia.org/wikipedia/commons/thumb/3/35/Wiki-Loves-Africa-logo.png/250px-Wiki-Loves-Africa-logo.png" decoding="async" width="150" height="86" class="mw-file-element" srcset="https://upload.wikimedia.org/wikipedia/commons/thumb/3/35/Wiki-Loves-Africa-logo.png/330px-Wiki-Loves-Africa-logo.png 2x" data-file-width="545" data-file-height="312"></a><figcaption></figcaption></figure><p>Par le passé, vous avez participé au concours photographique <a href="Commons_Wiki_Loves_Africa.html" title="Commons:Wiki Loves Africa">Wiki Loves Africa</a>. Vos photos ont aidé de nombreuses personnes à mieux comprendre l'Afrique et à la voir sous un autre angle, l'angle des africains eux-même. Depuis sa création en 2014, près de 5000 personnes ont contribué au concours photo, en ajoutant près de 40000 photos relatives à l'Afrique. 5000 personnes, dont vous, qui ont fait une vraie différence sur la représentation de l'Afrique sur Wikipédia et en ligne. Mieux encore... ces photos illustrent aujourd'hui près de 7000 articles Wikipédia et totalisent 278.859.973 vues entre 2015 et fin 2018&nbsp;! 
</p><p>Ce mois de février <b>vous avez à nouveau l'opportunité de proposer de fabuleuses illustrations sur le thème du "Jeu", qui participeront à changer la façon dont les êtres humains à travers le monde comprennent les habitants et la culture de votre région.</b>.
</p><p>Wiki Loves Africa, c’est un concours public annuel où chacun est invité à partager des illustrations via Wikimedia Commons, en rapport avec le thème de l’année, illustrations qui pourront être utilisées sur Wikipédia ou les autres sites de la Wikimedia Foundation.
</p><p><b>Quand le concours se déroule t-il?</b><br>
Le concours 2019 démarre le 1er février 2019 et cloture le 1er mars 2019.
</p><p><b>Quelles sont les contenus attendus?</b><br>
Des photos&nbsp;! Des enregistrements audio&nbsp;! Des vidéos&nbsp;! Le <a href="Commons_Wiki_Loves_Africa_2019/Theme/fr.html" title="Commons:Wiki Loves Africa 2019/Theme/fr">thème</a> du concours 2019 est ... Jouer.<br>
Ce thème englobe de nombreuses approches, il est aussi intentionnellement ouvert à l’interprétation. Le thème Jouer&nbsp;! encourage à proposer des représentations de jeux de détente et sérieux, du sport et des divertissements sous la forme de jeux de société ou de jeux intellectuels, d'activités physiques de divertissement ou de compétitions, d'interactions ludiques, de performances théâtrales ou musicales, etc.. 
</p><p>En plus de la valeur intrinsèque que vos photos apporteront à la compréhension et à la visibilité du monde africain sur Internet, il y a de nombreux prix à gagner. Dont deux prix spéciaux pour les photos qui représentent 
</p>
<ul><li><i>a) femmes et sport</i> ou</li>
<li><i>b) formes de jeux, de divertissements ou d’évènements culturellement spécifiques ou traditionnels. </i></li></ul>
<p>Pour plus d'informations, règles de participation et pour participer au concours, cliquez sur <b> <a href="Commons_Wiki_Loves_Africa_2019/fr.html" title="Commons:Wiki Loves Africa 2019/fr">Contribuer au concours photo Wiki Loves Africa</a></b>.
</p><p>Bien à vous, 
</p><p><a href="User_Anthere.html" title="User:Anthere">Anthere</a>, pour l'équipe Wiki Loves Africa
</p>
<div class="mw-heading mw-heading2 ext-discussiontools-init-section"><h2 id="Wiki_Loves_Africa_2020" data-mw-thread-id="h-Wiki_Loves_Africa_2020"><span data-mw-comment-start="" id="h-Wiki_Loves_Africa_2020"></span>Wiki Loves Africa 2020<span data-mw-comment-end="h-Wiki_Loves_Africa_2020"></span></h2><span class="mw-editsection"><span class="mw-editsection-bracket">[</span><a href="https://commons.wikimedia.org/w/index.php?title=User_talk:Kungu01&amp;action=edit&amp;section=2" title="Edit section: Wiki Loves Africa 2020"><span>edit</span></a><span class="mw-editsection-bracket">]</span></span></div>
<figure class="mw-halign-center" typeof="mw:File"><a href="File_Wiki-Loves-Africa-logo.html" class="mw-file-description"><img src="../../upload.wikimedia.org/wikipedia/commons/thumb/3/35/Wiki-Loves-Africa-logo.png/250px-Wiki-Loves-Africa-logo.png" decoding="async" width="200" height="114" class="mw-file-element" srcset="https://upload.wikimedia.org/wikipedia/commons/thumb/3/35/Wiki-Loves-Africa-logo.png/330px-Wiki-Loves-Africa-logo.png 1.5x, https://upload.wikimedia.org/wikipedia/commons/thumb/3/35/Wiki-Loves-Africa-logo.png/500px-Wiki-Loves-Africa-logo.png 2x" data-file-width="545" data-file-height="312"></a><figcaption></figcaption></figure>
<table class="wikitable">
<caption>
</caption>
<tbody><tr>
<th>Contribute to Wiki Loves Africa and to how the world sees Africa!
</th>
<th>Contribuez à Wiki Loves Africa et impactez la façon dont le monde voit l’Afrique!
</th></tr>
<tr>
<td>Dear Kungu01<br>
<p>In the past, you contributed to the <a href="Commons_Wiki_Loves_Africa.html" title="Commons:Wiki Loves Africa">Wiki Loves Africa</a> competition. This February, you have another opportunity to create beautiful photographs on the theme of "transportation" that could change how the world understands people and culture in your part of Africa. As with previous years, there are also many exciting cash prizes.
</p><p>Wiki Loves Africa is an annual public contest where people across Africa can contribute media (photographs, video and audio) about their environment to Wikimedia Commons for use on Wikipedia and other project websites of the Wikimedia Foundation.
</p><p>Images from the competition have helped millions learn more about Africa and see Africa more clearly from an African perspective. Overall, nearly 5000 people like you have contributed to this photo contest, and contributed 18,000 images about Africa.
</p><p><b>When does it take place?</b>
</p><p>The 2020 competition will start on the 15th of February 2020 and close on 31st March 2020.
</p><p><b>What should we contribute?</b>
</p><p>Pictures&nbsp;! audios&nbsp;! videos&nbsp;! The theme for the 2020 contest is... Africa on the Move&nbsp;! This theme encompasses a host of approaches and is intentionally open to interpretation. It encourages the submission of visual representations of movement or transportation, whether by path, road, sea air, by self-propelled, animal or mechanical means, or the historical or contemporary structures that have been created to facilitate movement or the transportation of people, goods, or animals.
</p><p>In addition to the value your photos contribute to understanding and African visibility, there are several prizes on offer. The international prizes are:
</p>
<ul><li>1st prize: US$1000</li>
<li>2nd prize: US$800</li>
<li>3rd prize: US$500</li></ul>
<p>Additional categories are:
</p>
<ul><li>Culturally specific or traditional representations of transport or structures that facilitate transportation&nbsp;: US$500</li>
<li>Prize for best quality video: $500</li></ul>
<p><br>
Each winner will also receive a pack of goodies (proposed: a hat "I edit Wikipedia from Africa", a branded battery, stickers). Additional prizes will be available in some countries.
</p><p>For rules and information about how to participate, or to join the contest, <big><b>click on <a href="Commons_Wiki_Loves_Africa_2020.html" title="Commons:Wiki Loves Africa 2020">Contribute to the Wiki Loves Africa photo contest</a>.</b></big>
</p><p>Warmest,
</p><p><a href="User_Anthere.html" title="User:Anthere">Anthere</a>, for the Wiki Loves Africa Team
</p>
</td>
<td><br>
<p>Cher ou chère Kungu01,<br>
</p><p>Au cours des années passées, vous avez participé au concours photographique <a href="Commons_Wiki_Loves_Africa.html" title="Commons:Wiki Loves Africa">Wiki Loves Africa</a>. Ce mois de février vous avez à nouveau l'opportunité de proposer de fabuleuses illustrations sur le thème du "transport", qui pourraient profondément modifier la façon dont le reste du monde voit l’Afrique et comprend ses habitants et ses coutumes. Bonus, comme lors des années précédentes, il y a plusieurs prix à la clé.
</p><p>Wiki Loves Africa un concours public annuel où chacun est invité à partager des illustrations via Wikimedia Commons, en rapport avec le thème de l’année, illustrations qui pourront être utilisées sur Wikipédia ou les autres sites de la Wikimedia Foundation.
</p><p>Les photos collectées par le passé dans le cadre du concours ont aidé des millions de personne à mieux connaitre et comprendre l’Afrique et à la voir selon une perspective africaine. Au cours des dernières années, près de 5000 personnes comme vous ont contribué au concours, un total de 18,000 photos sur l’Afrique.
</p><p><b>Quand le concours se déroule t-il?</b>
</p><p>Le concours 2020 démarre le 15 février 2020 et cloture le 31 mars 2020.
</p><p><b>Quelles sont les contenus attendus?</b>
</p><p>Des photos&nbsp;! Des enregistrements audio&nbsp;! Des vidéos&nbsp;! Le thème du concours 2020 est ... Le transport.
</p><p>Ce thème englobe de nombreuses approches, il est aussi intentionnellement ouvert à l’interprétation. Le thème Transport&nbsp;! encourage à proposer des représentations liées au mouvement ou au transport, que ce soit par la route, ou l’air, ou l’eau, de façon auto-propulsée ou avec l’aide de la force animale ou mécanique, ou bien également les structures architecturales historiques ou contemporaines qui sont à l’origine ou ont facilité le mouvement et le transport des êtres humains, des animaux ou des biens.
</p><p>En plus de la valeur intrinsèque que vos photos apportent pour une meilleure visibilité et compréhension de l’Afrique à travers le monde, il y a de nombreux prix à gagner. Les prix internationaux sont les suivants:
</p>
<ul><li>1er prix: US$1000</li>
<li>2ème prix: US$800</li>
<li>3ème prix: US$500</li></ul>
<p>Catégories additionnelles:
</p>
<ul><li>Transport ou structures de transport traditionnelles&nbsp;: US$500</li>
<li>Prix de la meilleure vidéo: US$500</li></ul>
<p>Chaque gagnant recevra également des cadeaux supplémentaires tels qu'une casquette "I edit Wikipedia from Africa", une batterie portable, des stickers... Des prix supplémentaires seront offerts dans certains pays.
</p><p>Pour plus d'informations, règles de participation et pour participer au concours, <big><b>cliquez sur <a href="Commons_Wiki_Loves_Africa_2020/fr.html" title="Commons:Wiki Loves Africa 2020/fr">Participer au concours Wiki Loves Africa</a>.</b></big>
</p><p>Bien à vous,
</p><p><a href="User_Anthere.html" title="User:Anthere">Anthere</a>, pour l'équipe Wiki Loves Africa
</p>
</td></tr>
<tr>
<td>
</td>
<td>
</td></tr></tbody></table>
<div class="mw-heading mw-heading2 ext-discussiontools-init-section"><h2 id="Wiki_Loves_Earth_2020_in_Kenya_is_open!" data-mw-thread-id="h-Wiki_Loves_Earth_2020_in_Kenya_is_open!"><span id="Wiki_Loves_Earth_2020_in_Kenya_is_open.21"></span><span data-mw-comment-start="" id="h-Wiki_Loves_Earth_2020_in_Kenya_is_open!"></span>Wiki Loves Earth 2020 in Kenya is open!<span data-mw-comment-end="h-Wiki_Loves_Earth_2020_in_Kenya_is_open!"></span></h2><span class="mw-editsection"><span class="mw-editsection-bracket">[</span><a href="https://commons.wikimedia.org/w/index.php?title=User_talk:Kungu01&amp;action=edit&amp;section=3" title="Edit section: Wiki Loves Earth 2020 in Kenya is open!"><span>edit</span></a><span class="mw-editsection-bracket">]</span></span></div>
<figure class="mw-halign-right" typeof="mw:File"><a href="Commons_Wiki_Loves_Earth_2020_in_Kenya.html" title="Commons:Wiki Loves Earth 2020 in Kenya"><img src="../../upload.wikimedia.org/wikipedia/commons/thumb/3/36/WLE_Austria_Logo_(transparent).svg/250px-WLE_Austria_Logo_(transparent).svg.png" decoding="async" width="150" height="200" class="mw-file-element" srcset="https://upload.wikimedia.org/wikipedia/commons/thumb/3/36/WLE_Austria_Logo_%28transparent%29.svg/300px-WLE_Austria_Logo_%28transparent%29.svg.png 2x" data-file-width="300" data-file-height="400"></a><figcaption></figcaption></figure>
<p>Hello! We are happy to announce that Wiki Loves Earth 2020 in Kenya is now open. Your participation will help us to document the nature of Kenya and show it to the world through Wikipedia and other projects.
Please go to the page of the contest to read the rules: <a href="Commons_Wiki_Loves_Earth_2020_in_Kenya.html" title="Commons:Wiki Loves Earth 2020 in Kenya">Commons:Wiki Loves Earth 2020 in Kenya</a>. There you can also find out how to take part in the contest. If you have any questions, please feel free to contact us: wikimediake<span class="nowrap"><span class="skin-invert" typeof="mw:File"><span title="@"><img alt="@" src="../../upload.wikimedia.org/wikipedia/commons/thumb/8/88/At_sign.svg/20px-At_sign.svg.png" decoding="async" width="15" height="15" class="mw-file-element" srcset="https://upload.wikimedia.org/wikipedia/commons/thumb/8/88/At_sign.svg/40px-At_sign.svg.png 1.5x" data-file-width="145" data-file-height="145"></span></span></span>gmail.com<br>
Thanks,<br>
the organising committee of Wiki Loves Earth 2020 in Kenya - 09:19, 20 July 2020 (UTC)<br>
<i>You are receiving this message because you took part in the contest before</i>
</p>
<div class="mw-heading mw-heading2 ext-discussiontools-init-section"><h2 id="Study_shows_Wiki_Love_Africa_recruitment_makes_a_difference" data-mw-thread-id="h-Study_shows_Wiki_Love_Africa_recruitment_makes_a_difference"><span data-mw-comment-start="" id="h-Study_shows_Wiki_Love_Africa_recruitment_makes_a_difference"></span>Study shows Wiki Love Africa recruitment makes a difference<span data-mw-comment-end="h-Study_shows_Wiki_Love_Africa_recruitment_makes_a_difference"></span></h2><span class="mw-editsection"><span class="mw-editsection-bracket">[</span><a href="https://commons.wikimedia.org/w/index.php?title=User_talk:Kungu01&amp;action=edit&amp;section=4" title="Edit section: Study shows Wiki Love Africa recruitment makes a difference"><span>edit</span></a><span class="mw-editsection-bracket">]</span></span></div>
<p>Hello Kungu01
</p>
<figure class="mw-halign-right" typeof="mw:File"><a href="File_Wiki-Loves-Africa-logo.html" class="mw-file-description"><img src="../../upload.wikimedia.org/wikipedia/commons/thumb/3/35/Wiki-Loves-Africa-logo.png/120px-Wiki-Loves-Africa-logo.png" decoding="async" width="100" height="57" class="mw-file-element" srcset="https://upload.wikimedia.org/wikipedia/commons/thumb/3/35/Wiki-Loves-Africa-logo.png/250px-Wiki-Loves-Africa-logo.png 1.5x" data-file-width="545" data-file-height="312"></a><figcaption></figcaption></figure><p>We are writing because you have participated in the Wiki Loves Africa (WLA) photo competition in the past. Results have been published and <a href="Commons_Wiki_Loves_Africa_2020/Winners.html" title="Commons:Wiki Loves Africa 2020/Winners">winning pictures may be seen here</a>&nbsp;!
</p><p>This year WLA partnered with the research organization <a href="https://meta.wikimedia.org/wiki/CivilServant%27s_Wikimedia_studies" class="extiw" title="m:CivilServant's Wikimedia studies">CAT Lab</a> (previously CivilServant) to help us evaluate the effectiveness of one of our recruitment efforts. You may or may not have received a message on your user talk page encouraging you to participate again in this year's competition.
</p><p>We are excited to share with you their study found our recruitment message was a success and may have resulted in as many as 2,000 additional contributed photos. We hope you agree this is great news for Africa and its representation across Wikimedia. You can <a rel="nofollow" class="external text" href="https://citizensandtech.org/2020/07/study-results-wikilovesafrica-2020/">read more about the study and the results in this blog post</a>.
</p><p>If you have any questions about the study, we encourage you to ask CAT Lab's research manager <a href="https://meta.wikimedia.org/wiki/User:Juliakamin(cs)" class="extiw" title="m:User:Juliakamin(cs)">Juliakamin(cs)</a>.
</p><p><a href="User_Anthere.html" title="User:Anthere">Anthere</a>, for the Wiki Loves Africa Team
</p>
<div class="mw-heading mw-heading2 ext-discussiontools-init-section"><h2 id="Wiki_Loves_Africa" data-mw-thread-id="h-Wiki_Loves_Africa"><span data-mw-comment-start="" id="h-Wiki_Loves_Africa"></span>Wiki Loves Africa<span data-mw-comment-end="h-Wiki_Loves_Africa"></span></h2><span class="mw-editsection"><span class="mw-editsection-bracket">[</span><a href="https://commons.wikimedia.org/w/index.php?title=User_talk:Kungu01&amp;action=edit&amp;section=5" title="Edit section: Wiki Loves Africa"><span>edit</span></a><span class="mw-editsection-bracket">]</span></span></div>
<figure class="mw-halign-right" typeof="mw:File"><a href="File_Wiki-Loves-Africa-logo.html" class="mw-file-description"><img src="../../upload.wikimedia.org/wikipedia/commons/thumb/3/35/Wiki-Loves-Africa-logo.png/250px-Wiki-Loves-Africa-logo.png" decoding="async" width="200" height="114" class="mw-file-element" srcset="https://upload.wikimedia.org/wikipedia/commons/thumb/3/35/Wiki-Loves-Africa-logo.png/330px-Wiki-Loves-Africa-logo.png 1.5x, https://upload.wikimedia.org/wikipedia/commons/thumb/3/35/Wiki-Loves-Africa-logo.png/500px-Wiki-Loves-Africa-logo.png 2x" data-file-width="545" data-file-height="312"></a><figcaption></figcaption></figure>
<p>Dear Kungu01<br>
</p><p>In the past, you contributed to the Wiki Loves Africa competition. You have another opportunity to create and submit beautiful photographs on the theme of "Health and Wellness" that could change how the world understands people and culture in your part of Africa. As with previous years, there are also many exciting national and international prizes.
</p><p>For rules and information about how to participate, theme, or prizes click on <a href="Commons_Wiki_Loves_Africa_2021.html" title="Commons:Wiki Loves Africa 2021">Contribute to the Wiki Loves Africa photo contest</a> or directly add picture from the button&nbsp;!
</p>
<dl><dd>Warmest, <a href="User_Anthere.html" title="User:Anthere">Anthere</a>, for the Wiki Loves Africa Team</dd></dl>
<p>Cher ou chère Kungu01,<br>
Au cours des années passées, vous avez participé au concours photographique Wiki Loves Africa. Vous avez à nouveau l'opportunité de proposer de fabuleuses illustrations sur le thème de "la santé et le bien-être", qui pourraient profondément modifier la façon dont le reste du monde voit l’Afrique et comprend ses habitants et ses coutumes. Bonus, comme lors des années précédentes, il y a plusieurs prix à la clé&nbsp;!
</p><p>Pour plus d'informations, règles de participation, le thème ou les prix, cliquez sur <a href="Commons_Wiki_Loves_Africa_2021.html" title="Commons:Wiki Loves Africa 2021">Participer au concours Wiki Loves Africa</a> ou téléversez des photos en cliquant sur le bouton&nbsp;!
</p>
<dl><dd>Bien à vous, <a href="User_Anthere.html" title="User:Anthere">Anthere</a>, pour l'équipe Wiki Loves Africa</dd></dl>
<figure class="mw-halign-center" typeof="mw:File"><a href="Commons_Wiki_Loves_Africa_2021.html"><img src="../../upload.wikimedia.org/wikipedia/commons/thumb/f/f9/WLA-2021-button.png/500px-WLA-2021-button.png" decoding="async" width="350" height="194" class="mw-file-element" srcset="https://upload.wikimedia.org/wikipedia/commons/thumb/f/f9/WLA-2021-button.png/525px-WLA-2021-button.png 1.5x, https://upload.wikimedia.org/wikipedia/commons/f/f9/WLA-2021-button.png 2x" data-file-width="640" data-file-height="354"></a><figcaption></figcaption></figure>

<!-- 
NewPP limit report
Parsed by mw‐web.eqiad.main‐7b48b5fb74‐r54s4
Cached time: 20250803074827
Cache expiry: 864000
Reduced expiry: true
Complications: [show‐toc]
DiscussionTools time usage: 0.027 seconds
CPU time usage: 0.118 seconds
Real time usage: 0.147 seconds
Preprocessor visited node count: 312/1000000
Revision size: 15332/2097152 bytes
Post‐expand include size: 67582/2097152 bytes
Template argument size: 9997/2097152 bytes
Highest expansion depth: 13/100
Expensive parser function count: 0/500
Unstrip recursion depth: 0/20
Unstrip post‐expand size: 3197/5000000 bytes
Lua time usage: 0.038/10.000 seconds
Lua memory usage: 652316/52428800 bytes
Number of Wikibase entities loaded: 0/400
-->
<!--
Transclusion expansion time report (%,ms,calls,template)
100.00%   58.592      1 -total
 95.47%   55.939      1 Template:Welcome
 66.29%   38.842      1 Template:Welcome/layout
 18.07%   10.590      1 Template:Header
 15.15%    8.875      1 Template:Header/layout
 12.26%    7.186      1 Template:Welcome/i18n/lang
  4.03%    2.360      1 Template:@
  3.18%    1.866      1 Template:Tlp
  2.26%    1.323      1 Template:Edit
-->

<!-- Saved in parser cache with key commonswiki:pcache:36598950:|#|:idhash:canonical and timestamp 20250803074827 and revision id 538859226. Rendering was triggered because: page-view
 -->
</div><!--esi <esi:include src="/esitest-fa8a495983347898/content" /> --><noscript><img src="https://auth.wikimedia.org/loginwiki/wiki/Special:CentralAutoLogin/checkLoggedIn?useformat=desktop&amp;wikiid=commonswiki&amp;usesul3=1&amp;type=1x1" alt="" width="1" height="1" style="border: none; position: absolute;"></noscript>
<div class="printfooter" data-nosnippet="">Retrieved from "<a dir="ltr" href="https://commons.wikimedia.org/w/index.php?title=User_talk:Kungu01&amp;oldid=538859226">https://commons.wikimedia.org/w/index.php?title=User_talk:Kungu01&amp;oldid=538859226</a>"</div></div>
					<div id="catlinks" class="catlinks catlinks-allhidden" data-mw="interface"></div>
				</div>
			</main>
			
		</div>
		<div class="mw-footer-container">
			
<footer id="footer" class="mw-footer" >
	<ul id="footer-info">
	<li id="footer-info-lastmod"> This page was last edited on 3 March 2021, at 20:23.</li>
	<li id="footer-info-copyright">Files are available under licenses specified on their description page. All structured data from the file namespace is available under the <a rel="nofollow" class="external text" href="https://creativecommons.org/publicdomain/zero/1.0/">Creative Commons CC0 License</a>; all unstructured text is available under the <a rel="nofollow" class="external text" href="../../creativecommons.org/licenses/by-sa/4.0/index.html">Creative Commons Attribution-ShareAlike License</a>;
additional terms may apply.
By using this site, you agree to the <a class="external text" href="https://foundation.wikimedia.org/wiki/Special:MyLanguage/Policy:Terms_of_Use">Terms of Use</a> and the <a class="external text" href="https://foundation.wikimedia.org/wiki/Special:MyLanguage/Policy:Privacy_policy">Privacy Policy</a>.</li>
</ul>

	<ul id="footer-places">
	<li id="footer-places-privacy"><a href="https://foundation.wikimedia.org/wiki/Special:MyLanguage/Policy:Privacy_policy">Privacy policy</a></li>
	<li id="footer-places-about"><a href="Commons_Welcome.html">About Wikimedia Commons</a></li>
	<li id="footer-places-disclaimers"><a href="Commons_General_disclaimer.html">Disclaimers</a></li>
	<li id="footer-places-wm-codeofconduct"><a href="https://foundation.wikimedia.org/wiki/Special:MyLanguage/Policy:Universal_Code_of_Conduct">Code of Conduct</a></li>
	<li id="footer-places-developers"><a href="https://developer.wikimedia.org/">Developers</a></li>
	<li id="footer-places-statslink"><a href="https://stats.wikimedia.org/#/commons.wikimedia.org">Statistics</a></li>
	<li id="footer-places-cookiestatement"><a href="https://foundation.wikimedia.org/wiki/Special:MyLanguage/Policy:Cookie_statement">Cookie statement</a></li>
	<li id="footer-places-mobileview"><a href="http://commons.m.wikimedia.org/w/index.php?title=User_talk:Kungu01&amp;mobileaction=toggle_view_mobile" class="noprint stopMobileRedirectToggle">Mobile view</a></li>
</ul>

	<ul id="footer-icons" class="noprint">
	<li id="footer-copyrightico"><a href="https://www.wikimedia.org/" class="cdx-button cdx-button--fake-button cdx-button--size-large cdx-button--fake-button--enabled"><picture><source media="(min-width: 500px)" srcset="/static/images/footer/wikimedia-button.svg" width="84" height="29"><img src="../static/images/footer/wikimedia.svg" width="25" height="25" alt="Wikimedia Foundation" lang="en" loading="lazy"></picture></a></li>
	<li id="footer-poweredbyico"><a href="https://www.mediawiki.org/" class="cdx-button cdx-button--fake-button cdx-button--size-large cdx-button--fake-button--enabled"><picture><source media="(min-width: 500px)" srcset="/w/resources/assets/poweredby_mediawiki.svg" width="88" height="31"><img src="../w/resources/assets/mediawiki_compact.svg" alt="Powered by MediaWiki" lang="en" width="25" height="25" loading="lazy"></picture></a></li>
</ul>

</footer>

		</div>
	</div> 
</div> 
<div class="vector-header-container vector-sticky-header-container no-font-mode-scale">
	<div id="vector-sticky-header" class="vector-sticky-header">
		<div class="vector-sticky-header-start">
			<div class="vector-sticky-header-icon-start vector-button-flush-left vector-button-flush-right" aria-hidden="true">
				<button class="cdx-button cdx-button--weight-quiet cdx-button--icon-only vector-sticky-header-search-toggle" tabindex="-1" data-event-name="ui.vector-sticky-search-form.icon"><span class="vector-icon mw-ui-icon-search mw-ui-icon-wikimedia-search"></span>

<span>Search</span>
			</button>
		</div>
			
		<div role="search" class="vector-search-box-vue vector-search-box">
			<div class="vector-typeahead-search-container">
				<div class="cdx-typeahead-search">
					<form action="https://commons.wikimedia.org/w/index.php" id="vector-sticky-search-form" class="cdx-search-input cdx-search-input--has-end-button">
						<div  class="cdx-search-input__input-wrapper"  data-search-loc="header-moved">
							<div class="cdx-text-input cdx-text-input--has-start-icon">
								<input
									class="cdx-text-input__input mw-searchInput" autocomplete="off"
									
									type="search" name="search" placeholder="Search Wikimedia Commons">
								<span class="cdx-text-input__icon cdx-text-input__start-icon"></span>
							</div>
							<input type="hidden" name="title" value="Special:MediaSearch">
						</div>
						<button class="cdx-button cdx-search-input__end-button">Search</button>
					</form>
				</div>
			</div>
		</div>
		<div class="vector-sticky-header-context-bar">
				<nav aria-label="Contents" class="vector-toc-landmark">
						
					<div id="vector-sticky-header-toc" class="vector-dropdown mw-portlet mw-portlet-sticky-header-toc vector-sticky-header-toc vector-button-flush-left"  >
						<input type="checkbox" id="vector-sticky-header-toc-checkbox" role="button" aria-haspopup="true" data-event-name="ui.dropdown-vector-sticky-header-toc" class="vector-dropdown-checkbox "  aria-label="Toggle the table of contents"  >
						<label id="vector-sticky-header-toc-label" for="vector-sticky-header-toc-checkbox" class="vector-dropdown-label cdx-button cdx-button--fake-button cdx-button--fake-button--enabled cdx-button--weight-quiet cdx-button--icon-only " aria-hidden="true"  ><span class="vector-icon mw-ui-icon-listBullet mw-ui-icon-wikimedia-listBullet"></span>

<span class="vector-dropdown-label-text">Toggle the table of contents</span>
						</label>
						<div class="vector-dropdown-content">
					
						<div id="vector-sticky-header-toc-unpinned-container" class="vector-unpinned-container">
						</div>
					
						</div>
					</div>
			</nav>
				<div class="vector-sticky-header-context-bar-primary" aria-hidden="true" ><span class="mw-page-title-namespace">User talk</span><span class="mw-page-title-separator">:</span><span class="mw-page-title-main">Kungu01</span></div>
			</div>
		</div>
		<div class="vector-sticky-header-end" aria-hidden="true">
			<div class="vector-sticky-header-icons">
				<a href="#" class="cdx-button cdx-button--fake-button cdx-button--fake-button--enabled cdx-button--weight-quiet cdx-button--icon-only" id="ca-talk-sticky-header" tabindex="-1" data-event-name="talk-sticky-header"><span class="vector-icon mw-ui-icon-speechBubbles mw-ui-icon-wikimedia-speechBubbles"></span>

<span></span>
			</a>
			<a href="#" class="cdx-button cdx-button--fake-button cdx-button--fake-button--enabled cdx-button--weight-quiet cdx-button--icon-only" id="ca-subject-sticky-header" tabindex="-1" data-event-name="subject-sticky-header"><span class="vector-icon mw-ui-icon-article mw-ui-icon-wikimedia-article"></span>

<span></span>
			</a>
			<a href="#" class="cdx-button cdx-button--fake-button cdx-button--fake-button--enabled cdx-button--weight-quiet cdx-button--icon-only" id="ca-history-sticky-header" tabindex="-1" data-event-name="history-sticky-header"><span class="vector-icon mw-ui-icon-wikimedia-history mw-ui-icon-wikimedia-wikimedia-history"></span>

<span></span>
			</a>
			<a href="#" class="cdx-button cdx-button--fake-button cdx-button--fake-button--enabled cdx-button--weight-quiet cdx-button--icon-only mw-watchlink" id="ca-watchstar-sticky-header" tabindex="-1" data-event-name="watch-sticky-header"><span class="vector-icon mw-ui-icon-wikimedia-star mw-ui-icon-wikimedia-wikimedia-star"></span>

<span></span>
			</a>
			<a href="#" class="cdx-button cdx-button--fake-button cdx-button--fake-button--enabled cdx-button--weight-quiet cdx-button--icon-only" id="ca-edit-sticky-header" tabindex="-1" data-event-name="wikitext-edit-sticky-header"><span class="vector-icon mw-ui-icon-wikimedia-wikiText mw-ui-icon-wikimedia-wikimedia-wikiText"></span>

<span></span>
			</a>
			<a href="#" class="cdx-button cdx-button--fake-button cdx-button--fake-button--enabled cdx-button--weight-quiet cdx-button--icon-only" id="ca-ve-edit-sticky-header" tabindex="-1" data-event-name="ve-edit-sticky-header"><span class="vector-icon mw-ui-icon-wikimedia-edit mw-ui-icon-wikimedia-wikimedia-edit"></span>

<span></span>
			</a>
			<a href="#" class="cdx-button cdx-button--fake-button cdx-button--fake-button--enabled cdx-button--weight-quiet cdx-button--icon-only" id="ca-viewsource-sticky-header" tabindex="-1" data-event-name="ve-edit-protected-sticky-header"><span class="vector-icon mw-ui-icon-wikimedia-editLock mw-ui-icon-wikimedia-wikimedia-editLock"></span>

<span></span>
			</a>
		</div>
			<div class="vector-sticky-header-buttons">
				<a href="#" class="cdx-button cdx-button--fake-button cdx-button--fake-button--enabled cdx-button--weight-quiet cdx-button--action-progressive" id="ca-addsection-sticky-header" tabindex="-1" data-event-name="addsection-sticky-header"><span class="vector-icon mw-ui-icon-speechBubbleAdd-progressive mw-ui-icon-wikimedia-speechBubbleAdd-progressive"></span>

<span>Add topic</span>
			</a>
		</div>
			<div class="vector-sticky-header-icon-end">
				<div class="vector-user-links">
				</div>
			</div>
		</div>
	</div>
</div>
<div class="mw-portlet mw-portlet-dock-bottom emptyPortlet" id="p-dock-bottom">
	<ul>
		
	</ul>
</div>
<script>(RLQ=window.RLQ||[]).push(function(){mw.config.set({"wgHostname":"mw-web.eqiad.main-7b48b5fb74-r54s4","wgBackendResponseTime":304,"wgDiscussionToolsPageThreads":[{"headingLevel":null,"name":"h-Wikimedia_Commons_Welcome-2014-11-04T07:45:00.000Z","type":"heading","level":0,"id":"h--2014-11-04T07:45:00.000Z","replies":[{"timestamp":"2014-11-04T07:45:00.000Z","author":"Wikimedia Commons Welcome","type":"comment","level":1,"id":"c-Wikimedia_Commons_Welcome-2014-11-04T07:45:00.000Z","replies":[]}]},{"headingLevel":2,"name":"h-","type":"heading","level":0,"id":"h-Wiki_Loves_Africa_2019","replies":[]},{"headingLevel":2,"name":"h-","type":"heading","level":0,"id":"h-Wiki_Loves_Africa_2020","replies":[]},{"headingLevel":2,"name":"h-","type":"heading","level":0,"id":"h-Wiki_Loves_Earth_2020_in_Kenya_is_open!","replies":[]},{"headingLevel":2,"name":"h-","type":"heading","level":0,"id":"h-Study_shows_Wiki_Love_Africa_recruitment_makes_a_difference","replies":[]},{"headingLevel":2,"name":"h-","type":"heading","level":0,"id":"h-Wiki_Loves_Africa","replies":[]}],"wgPageParseReport":{"discussiontools":{"limitreport-timeusage":"0.027"},"limitreport":{"cputime":"0.118","walltime":"0.147","ppvisitednodes":{"value":312,"limit":1000000},"revisionsize":{"value":15332,"limit":2097152},"postexpandincludesize":{"value":67582,"limit":2097152},"templateargumentsize":{"value":9997,"limit":2097152},"expansiondepth":{"value":13,"limit":100},"expensivefunctioncount":{"value":0,"limit":500},"unstrip-depth":{"value":0,"limit":20},"unstrip-size":{"value":3197,"limit":5000000},"entityaccesscount":{"value":0,"limit":400},"timingprofile":["100.00%   58.592      1 -total"," 95.47%   55.939      1 Template:Welcome"," 66.29%   38.842      1 Template:Welcome/layout"," 18.07%   10.590      1 Template:Header"," 15.15%    8.875      1 Template:Header/layout"," 12.26%    7.186      1 Template:Welcome/i18n/lang","  4.03%    2.360      1 Template:@","  3.18%    1.866      1 Template:Tlp","  2.26%    1.323      1 Template:Edit"]},"scribunto":{"limitreport-timeusage":{"value":"0.038","limit":"10.000"},"limitreport-memusage":{"value":652316,"limit":52428800}},"cachereport":{"origin":"mw-web.eqiad.main-7b48b5fb74-r54s4","timestamp":"20250803074827","ttl":864000,"transientcontent":true}}});});</script>
</body>

<!-- Mirrored from commons.wikimedia.org/wiki/User_talk:Kungu01 by HTTrack Website Copier/3.x [XR&CO'2014], Sun, 03 Aug 2025 09:00:40 GMT -->
</html>