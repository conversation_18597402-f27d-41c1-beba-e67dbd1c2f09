EMBEDSOCIALREVIEWSCHEMA={encode:window.encodeURIComponent,random:Math.random,createElement:function(tag){return window.document.createElement(tag)},JSONP:function(options){var callback,callbackFunc,callbackName,done,head,params,script;if(options==null){options={}}params={data:options.data||{},error:options.error||EMBEDSOCIALREVIEWSCHEMA.noop,success:options.success||EMBEDSOCIALREVIEWSCHEMA.noop,beforeSend:options.beforeSend||EMBEDSOCIALREVIEWSCHEMA.noop,complete:options.complete||EMBEDSOCIALREVIEWSCHEMA.noop,url:options.url||""};params.computedUrl=EMBEDSOCIALREVIEWSCHEMA.computedUrl(params);if(params.url.length===0){return false}done=false;if(params.beforeSend({},params)!==false){callbackName=options.callbackName||"callback";callbackFunc=options.callbackFunc||"jsonp_"+EMBEDSOCIALREVIEWSCHEMA.randomString(15);callback=params.data[callbackName]=callbackFunc;window[callback]=function(data){window[callback]=null;params.success(data,params);return params.complete(data,params)};script=EMBEDSOCIALREVIEWSCHEMA.createElement("script");script.src=EMBEDSOCIALREVIEWSCHEMA.computedUrl(params);script.async=true;script.onerror=function(evt){params.error({url:script.src,event:evt});return params.complete({url:script.src,event:evt},params)};script.onload=script.onreadystatechange=function(){var ref,ref1;if(done||(ref=this.readyState)!=="loaded"&&ref!=="complete"){return}done=true;if(script){script.onload=script.onreadystatechange=null;if((ref1=script.parentNode)!=null){ref1.removeChild(script)}return script=null}};head=window.document.getElementsByTagName("head")[0]||window.document.documentElement;head.insertBefore(script,head.firstChild)}return{abort:function(){window[callback]=function(){return window[callback]=null};done=true;if(script!=null?script.parentNode:void 0){script.onload=script.onreadystatechange=null;script.parentNode.removeChild(script);return script=null}}}},noop:function(){return void 0},computedUrl:function(params){var url;url=params.url;url+=params.url.indexOf("?")<0?"?":"&";url+=EMBEDSOCIALREVIEWSCHEMA.objectToURI(params.data);return url},randomString:function(length){var str;str="";while(str.length<length){str+=EMBEDSOCIALREVIEWSCHEMA.random().toString(36).slice(2,3)}return str},objectToURI:function(obj){var data,key,value;data=function(){var results;results=[];for(key in obj){value=obj[key];results.push(EMBEDSOCIALREVIEWSCHEMA.encode(key)+"="+EMBEDSOCIALREVIEWSCHEMA.encode(value))}return results}();return data.join("&")},getSchemaData:function(reviewsRef,schemaDiv,index){var schemaObject=JSON.parse(schemaDiv.innerHTML);EMBEDSOCIALREVIEWSCHEMA.JSONP({callbackFunc:"jsonp_"+reviewsRef+"_"+index,url:"https://embedsocial.com/api/reviews_schema",data:{reviewsRef:reviewsRef},success:function(data){if(data.success){schemaObject.aggregateRating.ratingValue=data.rating;schemaObject.aggregateRating.reviewCount=data.reviews;schemaDiv.innerHTML=JSON.stringify(schemaObject)}}})}};var embedsocialReviewsSchema=document.getElementsByClassName("reviews-schema");var embedsocialReviewsRefs=[];for(i=0;i<embedsocialReviewsSchema.length;i++){var embedsocialReviewsRef=embedsocialReviewsSchema[i].getAttribute("data-ref");EMBEDSOCIALREVIEWSCHEMA.getSchemaData(embedsocialReviewsRef,embedsocialReviewsSchema[i],i)}
