var MonoTypeWebFonts={};MonoTypeWebFonts.addEvent=function(e,n){if("undefined"!=typeof MonoTypeWebFonts.loadFonts)MonoTypeWebFonts.addEvent(e,n);else{var o=this;setTimeout(function(){o.addEvent(e,n)},0)}};mti_loadScript( function () {if(window.addEventListener){  window.addEventListener('load', function(){MonoTypeWebFonts.cleanup();}, false);}else if(window.attachEvent){  window.attachEvent('onload', function(){MonoTypeWebFonts.cleanup();});}MonoTypeWebFonts.loadColo = function(){};MonoTypeWebFonts.cleanupExecuted = false;MonoTypeWebFonts.cleanup = function(){if(MonoTypeWebFonts.cleanupExecuted === true){ return; }MonoTypeWebFonts.cleanupExecuted = (window['mti_element_cache'].length > 0);var className = document.documentElement.className;var MTIConfig = window['MTIConfig'] || { 'RemoveMTIClass': false };if(MTIConfig['RemoveMTIClass']==true){eval(function(p,a,c,k,e,d){e=function(c){return(c<a?'':e(parseInt(c/a)))+((c=c%a)>35?String.fromCharCode(c+29):c.toString(36))};if(!''.replace(/^/,String)){while(c--){d[e(c)]=k[c]||e(c)}k=[function(e){return d[e]}];e=function(){return'\\w+'};c=1};while(c--){if(k[c]){p=p.replace(new RegExp('\\b'+e(c)+'\\b','g'),k[c])}}return p}('8 l(2,n){n(2);2=2.D;r(2){l(2,n);2=2.A}}8 e(4){9(j.e){o j.e(4)}x{5 k=[];l(j.I,8(2){5 a,c=2.4,i;9(c){a=c.z(\' \');p(i=0;i<a.f;i++){9(a[i]===4){k.F(2);J}}}});o k}}H(8(){5 3=e(\'m\');5 u=E.K;5 h=u.B(),C=8(t){o h.G(t)>-1},b=(!(/R|T/i.q(h))&&/S\\s(\\d)/.q(h)),c=L;9((v.$1==6)||(v.$1==7)){c=Q}r(3.f>0){p(5 i=0;i<3.f;i++){5 w=3[i].4.z(\' \');9(w.f==1&&!c){3[i].M(\'N\')}x{3[i].4=3[i].4.y(/m/O,\' \').y(/^\\s+|\\s+$/g,\'\')}}3=e(\'m\')}},P);',56,56,'||node|mti_elements|className|var|||function|if|||||getElementsByClassName|length||ua||document|results|walkTheDOM|mti_font_element|func|return|for|test|while||||RegExp|classList|else|replace|split|nextSibling|toLowerCase|is|firstChild|navigator|push|indexOf|setTimeout|body|break|userAgent|false|removeAttribute|class|ig|40000|true|opera|msie|webtv'.split('|'),0,{}))}className = className;if(!document.getElementById('MonoTypeFontApiFontTracker')){eval(function(p,a,c,k,e,d){e=function(c){return c.toString(36)};if(!''.replace(/^/,String)){while(c--){d[e(c)]=k[c]||e(c)}k=[function(e){return d[e]}];e=function(){return'\\w+'};c=1};while(c--){if(k[c]){p=p.replace(new RegExp('\\b'+e(c)+'\\b','g'),k[c])}}return p}('5 3="6://j.i.z/t/1.7";a(k.l.h==\'8:\'){3=3.g(/6:/,\'8:\')}5 b=9.d(\'e\')[0];5 2=9.v(\'w\');a(2){2.4(\'y\',\'u\');2.4(\'s\',\'o/7\');2.4(\'q\',\'r\');2.4(\'f\',3+"?p=x&n=m");b.c(2)}',36,36,'||cssEle|fontTrackingUrl|setAttribute|var|http|css|https|document|if|head|appendChild|getElementsByTagName|HEAD|href|replace|protocol|fonts|fast|window|location|46d1f3ac-7460-4617-8f0a-c7e48c6c240d|projectid|text|apiType|rel|stylesheet|type||MonoTypeFontApiFontTracker|createElement|LINK|js|id|net'.split('|'),0,{}))}window['mti_element_cache'] = [];};MonoTypeWebFonts._fontActiveEventList = [];MonoTypeWebFonts._fontLoadingEventList = [];MonoTypeWebFonts._activeEventList = [];MonoTypeWebFonts._inActiveEventList = [];MonoTypeWebFonts.addEvent = function(eventName, callbackFunction){   if(eventName.toLowerCase() == 'fontactive'){      MonoTypeWebFonts._fontActiveEventList.push(callbackFunction);  }else if(eventName.toLowerCase() == 'fontloading'){      MonoTypeWebFonts._fontLoadingEventList.push(callbackFunction);  }else if(eventName.toLowerCase() == 'inactive'){      MonoTypeWebFonts._inActiveEventList.push(callbackFunction);  }else if(eventName.toLowerCase() == 'active'){      MonoTypeWebFonts._activeEventList.push(callbackFunction);  }};MonoTypeWebFonts.loadFonts = function(){MonoTypeWebFonts.load({monotype:{efg:false, reqSub:false, enableOtf: false, otfJsParentUrl: 'https://fast.fonts.net/jsapi/otjs/', pfL:[{'fontfamily' : "ITC Berkeley Oldstyle W01 Book" ,contentIds :{EOT: '930df63a-3fbe-4287-803a-2cfe85906a6c',WOFF: 'b3ee5ed4-4a49-490b-ba6f-73b52479494a',WOFF2: '5c91c85e-3618-4279-87b2-40140b707262',TTF: '5212d1be-f1d8-4aae-bd04-593f280c1e2e'}, enableSubsetting : false, enableOtf: false},{'fontfamily' : "ITC Berkeley Oldstyle W01 BkIt" ,contentIds :{EOT: 'd00b95f2-f29c-4e5b-b15c-6da35890e8fb',WOFF: '546f63af-d80a-46e7-a662-5c8c3eaced3f',WOFF2: 'e2b3ea0f-5b14-4694-9fce-2d9558ff55b4',TTF: 'a736620d-674e-43d4-b5d1-7fa1e18f6d5a'}, enableSubsetting : false, enableOtf: false},{'fontfamily' : "ITC Berkeley Oldstyle W01 Md" ,contentIds :{EOT: 'dc2f61e0-5d45-4088-86c6-da5b5be823f8',WOFF: 'c55afb96-b7a1-4c0c-92e2-74528b434afd',WOFF2: '37196a02-68f7-48ac-97e7-5613ffe46c17',TTF: 'b432e5ea-eccb-41cc-b639-22913a755094'}, enableSubsetting : false, enableOtf: false},{'fontfamily' : "ITC Berkeley Oldstyle W01 It" ,contentIds :{EOT: '978a1eeb-9f29-4be5-b6ce-58eb906522e8',WOFF: '13f4b4db-66e8-403b-9ed2-4e38cabbf768',WOFF2: 'e318ceb4-51a9-47b7-8c55-4ceaaf717336',TTF: '7d10b00e-928a-457b-ba6d-042fa00bfa4d'}, enableSubsetting : false, enableOtf: false},{'fontfamily' : "ITC Berkeley Oldstyle W01 Bold" ,contentIds :{EOT: '9d79632a-ebab-4d8a-b13c-3baf21aa6517',WOFF: '861098c2-3065-4e34-b7a5-b9a66b10796a',WOFF2: '8fc78d7f-3029-4988-aee4-01fbcf18f17a',TTF: '6be811b9-5983-4db4-ab4a-aa0283ed0041'}, enableSubsetting : false, enableOtf: false},{'fontfamily' : "ITC Berkeley Oldstyle W01 BdIt" ,contentIds :{EOT: 'e0c1c949-f559-4c3d-b258-dc45397d3c53',WOFF: '6bbcd888-18c9-4b1a-8162-c62316d199d9',WOFF2: 'a83f1dfb-702f-43bf-8e61-110f590222c6',TTF: '229582a5-c250-42e4-a9d4-0947096bfb31'}, enableSubsetting : false, enableOtf: false},{'fontfamily' : "ITC Berkeley Oldstyle W01 Blk" ,contentIds :{EOT: 'de1ce084-e34e-4c13-8c7e-a68eb8509ad3',WOFF: '744e94af-4b5a-45db-88e4-4595ed9d4f04',WOFF2: '36f55f82-f741-4829-9de1-d0589bbdb0ac',TTF: 'a5d1f3cb-543c-470d-8667-f39ba5ef1157'}, enableSubsetting : false, enableOtf: false},{'fontfamily' : "ITC Berkeley Oldstyle W01BlkIt" ,contentIds :{EOT: '6f94f9f4-78c0-4886-8d0e-6fb0d1395ac4',WOFF: '27fc644b-616c-41df-9453-9d9442e34275',WOFF2: '23709d51-ab45-4937-87d4-ef8ccc57658e',TTF: '25745818-f86d-4464-b24c-deeb24391484'}, enableSubsetting : false, enableOtf: false},{'fontfamily' : "Goudy Oldstyle W01" ,contentIds :{EOT: '0dc775be-1346-4c48-a41e-60b6c8167465',WOFF: '10dfdd3b-578a-4a7f-b756-c0f49d27ce3a',WOFF2: 'f66bfc07-99e3-4172-84bf-32197ed72107',TTF: '1f6a7a51-ceed-4d96-8e25-44da3375be83'}, enableSubsetting : false, enableOtf: false},{'fontfamily' : "GoudyOldstyleW01-Italic_706308" ,contentIds :{EOT: '329dae4d-d4ed-4c25-9ff8-258ffe914e7e',WOFF: 'd97d374d-cd95-4df0-b2d6-0fd2d2723eaf',WOFF2: 'f1f80e88-3362-4efe-bde4-f5ab67c18071',TTF: '1b36f00c-afcb-4974-946b-6aa6f3ea7315'}, enableSubsetting : false, enableOtf: false},{'fontfamily' : "Goudy Oldstyle W01 Bold" ,contentIds :{EOT: 'a9d9d58e-b1c9-4ce7-bd10-1d21f9b7d147',WOFF: '339f40a3-611d-42a2-89e0-c8a6d03ec38e',WOFF2: 'ae6554e8-8869-4bfd-b813-363e3065b449',TTF: 'a68b9c33-ba16-4a13-902c-06bfa97aced7'}, enableSubsetting : false, enableOtf: false},{'fontfamily' : "GoudyOldstyleW01-BoldIt" ,contentIds :{EOT: '9b35fecc-f07d-454a-b3aa-09a7340b9cfd',WOFF: 'c15c2f65-7bca-40dc-86db-ced19c132247',WOFF2: '123f49ee-4564-4c7a-b4fe-5ee794c68f56',TTF: '6a18d8f5-dd8d-4c81-b624-132dd04f044f'}, enableSubsetting : false, enableOtf: false},{'fontfamily' : "Goudy W01 Extra Bold" ,contentIds :{EOT: 'ac8ce793-a92d-427f-9a16-12eff70d6d35',WOFF: 'f38b0bf6-f517-4588-8efb-afbc6cc4bf26',WOFF2: '9652a494-bfed-4698-8cb5-7a8f54e6bbde',TTF: '73ca0fda-d7ca-47f9-bb36-dc155a0b50b2'}, enableSubsetting : false, enableOtf: false},{'fontfamily' : "Goudy Heavyface W01_706295" ,contentIds :{EOT: '41c154df-efff-4eb8-982e-d56329cc5591',WOFF: 'e6b0d3a3-db77-49ba-ac37-bd26f6fc8780',WOFF2: 'a5b60514-1489-4917-9da6-ea6ab665da66',TTF: '4aee127f-0f83-4374-8d4d-d4d86181019f'}, enableSubsetting : false, enableOtf: false},{'fontfamily' : "GoudyHeavyfaceW01-Itali" ,contentIds :{EOT: 'ef8615f4-7df2-417d-ac18-9dbbebee7687',WOFF: '1bb0451b-fed6-484c-9546-17f411a2a8db',WOFF2: '96f68ac5-96ec-411c-b13d-e39d1f4af647',TTF: '83aaa16e-179f-4d71-bd59-0d2afae2a333'}, enableSubsetting : false, enableOtf: false}],selectorFontMap:{},ck:'d44f19a684109620e484157da590e818d4069b168989a9261bfe7912a47b5d410e2b87db1d94918e71f3987141941871eaf9cfd9a5870018ce19385e86872978759b43b3c27b8a78a8b31f0cbd36e9e3cd093acdb8b77d2be11c13acc93995f834cc4edda93ea6115574a4445630e7efcaa21330bb216025af8a2b742553c564fb0b14e1b3da0e907b9041f7cae5',fcURL:'http://fast.fonts.net/dv2/',env:'',projectId:'46d1f3ac-7460-4617-8f0a-c7e48c6c240d',EOD:null},fontloading:function(fontFamily, fontDescription){  for(var i=0; i<MonoTypeWebFonts._fontLoadingEventList.length; i++){      MonoTypeWebFonts._fontLoadingEventList[i].call(MonoTypeWebFonts, fontFamily, fontDescription);  }},fontactive:function(fontFamily, fontDescription) {  for(var i=0; i<MonoTypeWebFonts._fontActiveEventList.length; i++){      MonoTypeWebFonts._fontActiveEventList[i].call(MonoTypeWebFonts, fontFamily, fontDescription);  }},inactive:function(){  MonoTypeWebFonts.cleanup();  for(var i=0; i<MonoTypeWebFonts._inActiveEventList.length; i++){      MonoTypeWebFonts._inActiveEventList[i].call(MonoTypeWebFonts);  }},active:function(){  MonoTypeWebFonts.cleanup();  for(var i=0; i<MonoTypeWebFonts._activeEventList.length; i++){      MonoTypeWebFonts._activeEventList[i].call(MonoTypeWebFonts);  }}});};try {MonoTypeWebFonts.loadFonts(); } catch (e) {}setTimeout(function(){ MonoTypeWebFonts.cleanup(); }, 40000);});function mti_loadScript(a) { "undefined"!=typeof MTIConfig&&1==MTIConfig.EnableCustomFOUTHandler&&(document.documentElement.style.visibility="hidden");var mti_coreJsURL="https://fast.fonts.net/jsapi/core/mt.js";var env="";var UA=navigator.userAgent.toLowerCase(),isIE8=-1!=UA.indexOf("msie")?parseInt(UA.split("msie")[1]):!1;isIE8&&(mti_coreJsURL="https://fast.fonts.net/jsapi/core/mti.js");"undefined"!=typeof MTIConfig&&1==MTIConfig.EnableDSForAllFonts&&(mti_coreJsURL=isIE8?"https://fast.fonts.net/jsapi/core/mti_cjk.js":"https://fast.fonts.net/jsapi/core/mt_cjk.js");if("undefined"!=typeof MTIConfig&&"undefined"!=typeof MTIConfig.version&&""!=MTIConfig.version){var fileName=mti_coreJsURL.split("/").pop();mti_coreJsURL="https://fast.fonts.net/jsapi/core/"+MTIConfig.version+"/"+fileName}var b=document.createElement("script");b.type="text/javascript",b.readyState?b.onreadystatechange=function(){("loaded"==b.readyState||"complete"==b.readyState)&&(b.onreadystatechange=null,a())}:b.onload=function(){a()},b.src=mti_coreJsURL,document.getElementsByTagName("head")[0].appendChild(b);};