!function(){var e={87851:function(e,t,o){"use strict";var n=o(9571),r=o(73355);t.Z=function(){for(var e=arguments.length,t=new Array(e),o=0;o<e;o++)t[o]=arguments[o];const s=(0,n.Z)().toString();return(0,r.U)()?new Promise(((e,o)=>{const n=t=>{try{if(t.data){const{data:r}=t,{payload:a}=r;if("PROXY_FETCH_RESPONSE"===r.EVENT_NAME&&a.token===s){if(204===t.data.payload.fetchResponseProps.status)e(new Response(null,{status:204}));else if(a.fetchResponseBlob){const t=new Response(a.fetchResponseBlob.stream(),a.fetchResponseProps);e(t)}else a.fetchError?o(a.fetchError):o("Need to handle this");window.removeEventListener("message",n)}}}catch(e){console.log(e)}};window.addEventListener("message",n),(0,r.Z)({EVENT_NAME:"PROXY_FETCH",payload:{fetch:t,token:s}},"*")})):window.fetch(...t)}},73355:function(e,t,o){"use strict";o.d(t,{U:function(){return n}});const n=()=>!!window.BUGHERD_REACT_APP_SIDEBAR_MOUNT&&(window.document.querySelector("#bugherd_embed_communication_frame")||window.BUGHERD_EXTENSION_CONFIG&&window.BUGHERD_EXTENSION_CONFIG.extensionId);t.Z=function(){if(window.BUGHERD_REACT_APP_SIDEBAR_MOUNT){const e=window.document.querySelector("#bugherd_embed_communication_frame");if(e)return void e.contentWindow.postMessage(...arguments)}window.BUGHERD_EXTENSION_CONFIG&&window.BUGHERD_EXTENSION_CONFIG.extensionId?window.postMessage(...arguments):console.error("Couldn't find bugherd_embed_communication_frame iframe or window.BUGHERD_EXTENSION_CONFIG.extensionId to use for postMessage")}},40629:function(e){var t={cmd:"Cmd",ctrl:"Ctrl",alt:"Alt",shift:"Shift",joinWith:" + "},o={},n={8:"Backspace",9:"Tab",13:"Enter",27:"Escape",32:"Space",36:"Home",33:"Page Up",34:"Page Down",35:"End",37:"Left",38:"Up",39:"Right",40:"Down",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",186:";",187:"=",188:",",189:"-",190:".",192:"`",222:"'"};function r(e){return{character:-1!==[16,17,18,91,93,224].indexOf(e.keyCode)?null:n[e.keyCode]||String.fromCharCode(e.keyCode),modifiers:{cmd:e.metaKey,ctrl:e.ctrlKey,alt:e.altKey,shift:e.shiftKey}}}function s(e){return function(e){var t=r(e),n=t.modifiers,s=[];return n.cmd&&s.push(o.cmd),n.ctrl&&s.push(o.ctrl),n.alt&&s.push(o.alt),n.shift&&s.push(o.shift),t.character&&s.push(t.character),s}(e).join(o.joinWith)}e.exports=function(e){return o=Object.assign(t,e),s},e.exports.details=function(e){var t=r(e),o=t.modifiers,n=o.cmd||o.ctrl||o.alt||o.shift;return{hasKey:null!=t.character,hasModifier:n,map:t}}},9571:function(e,t,o){"use strict";var n;o.d(t,{Z:function(){return u}});var r=new Uint8Array(16);function s(){if(!n&&!(n="undefined"!=typeof crypto&&crypto.getRandomValues&&crypto.getRandomValues.bind(crypto)||"undefined"!=typeof msCrypto&&"function"==typeof msCrypto.getRandomValues&&msCrypto.getRandomValues.bind(msCrypto)))throw new Error("crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported");return n(r)}for(var a=/^(?:[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}|00000000-0000-0000-0000-000000000000)$/i,i=[],c=0;c<256;++c)i.push((c+256).toString(16).substr(1));var d=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,o=(i[e[t+0]]+i[e[t+1]]+i[e[t+2]]+i[e[t+3]]+"-"+i[e[t+4]]+i[e[t+5]]+"-"+i[e[t+6]]+i[e[t+7]]+"-"+i[e[t+8]]+i[e[t+9]]+"-"+i[e[t+10]]+i[e[t+11]]+i[e[t+12]]+i[e[t+13]]+i[e[t+14]]+i[e[t+15]]).toLowerCase();if(!function(e){return"string"==typeof e&&a.test(e)}(o))throw TypeError("Stringified UUID is invalid");return o},u=function(e,t,o){var n=(e=e||{}).random||(e.rng||s)();if(n[6]=15&n[6]|64,n[8]=63&n[8]|128,t){o=o||0;for(var r=0;r<16;++r)t[o+r]=n[r];return t}return d(n)}}},t={};function o(n){var r=t[n];if(void 0!==r)return r.exports;var s=t[n]={exports:{}};return e[n](s,s.exports,o),s.exports}o.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return o.d(t,{a:t}),t},o.d=function(e,t){for(var n in t)o.o(t,n)&&!o.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},o.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},function(){"use strict";const e=(e,t,o)=>{o&&e.setAttribute(`data-${t}`,o)};const t=function(){};var n=(()=>{const e=new URLSearchParams(window.location.search).keys(),o=Array.from(e).map((e=>"bugherddebug"===e?.toLowerCase?.())).some((e=>e)),n="true"===window.BugHerdConfig?.debug;return o||n?(console.log("[BUGHERD] BugHerd debug logging is enabled"),function(){for(var e=arguments.length,t=new Array(e),o=0;o<e;o++)t[o]=arguments[o];return console.log("[BUGHERD]",...t)}):t})();const r={default:{width:300,height:80,right:0},warning:{width:330,height:144,right:0}},s=()=>document.getElementById("bugherd_embed_communication_frame");let a=null;const i=(e,t)=>{t&&(a=t);const o=s(),{width:n,height:i,right:c}=r[e];o.style.width=`${n}px`,o.style.height=`${i}px`,o.style.top=function(e){return e?`calc((100% - ${(arguments.length>1&&void 0!==arguments[1]?arguments[1]:70)+10}px) * ${e/100})`:"0px"}(t||a,i),o.style.right=`${c}px`},c=e=>{s().style.display=e?"block":"none"},d=()=>{n("[AUTH] Destroying auth iframe"),window.removeEventListener("bugherd_loaded",d),c(!1),window._bugHerd_sidebar2021=void 0};var u=o(40629),l=o.n(u);let p=window.location.search.includes("showBugherd=true"),g=!1;const m=(e,t)=>{document.addEventListener("keyup",(function(e){const o=document.getElementById("bugherd_embed_communication_frame"),n=l()({})(e);"string"==typeof n&&o&&n===(t||"Cmd + Ctrl + B")&&(o.style.display="block")}));const o=document.referrer.includes(e),n="proxy"===window.BUGHERD_SIDEBAR_CONFIG?.source;if(g||(n&&(navigator.sendBeacon(`${e}/binoculars`,JSON.stringify({event:"Sidebar displayed",properties:{source:"proxy"}})),window.parent.postMessage({message:"Sidebar is loaded"},"*")),g=!0),p){const e=setTimeout((()=>{const e=new URLSearchParams(window.location.search);e.delete("showBugherd");const t=`${window.location.protocol}//${window.location.host}${window.location.pathname}?${e.toString()}`;window.history.pushState({path:t},"",t)}),1e3);return()=>{clearTimeout(e)}}return!(!o&&!n)},w=(e,t)=>{const o=document.getElementById("bugherd_embed_communication_frame"),n=l()({})(t),r=e.shortcut_to_show_widget||"Cmd + Ctrl + B";"string"==typeof n&&o&&n===r&&(o.style.display="block")},h={hasStorageAccess:void 0,project:null,resources:null},_=e=>{let{origin:t,search:o}=e;return(async e=>{let{origin:t,search:o}=e;try{await new Promise(((e,t)=>{if(document.body)return e(document.body);const o=setTimeout((()=>{t()}),4e3),n=new MutationObserver((function(){document.body&&document.head&&(n.disconnect(),clearTimeout(o),e(document.body))}));n.observe(document.documentElement,{childList:!0})}))}catch(e){return console.error("BugHerd wanted to start but document.body or document.head is null"),null}let n="allow-scripts allow-popups allow-same-origin allow-popups-to-escape-sandbox";"requestStorageAccess"in document&&(n+=" allow-storage-access-by-user-activation");const r=document.createElement("iframe");return r.id="bugherd_embed_communication_frame",r.name="embedCommunication",r.sandbox=n,r.style.display="none",r.style.border="none",r.style.position="fixed",r.style.zIndex="2147483647",r.style.right="0",r.style.height="calc((100% - 154px) * 0.9)",r.src=`${t}/sidebar/embed_html${o}`,document.body.appendChild(r),r})({origin:t,search:o}).then((e=>{if(null===e)throw Error("iFrame wasnt created");return new Promise(((o,r)=>{const s=t.replace("sidebar.","www.");window.addEventListener("bugherd_loaded",d);const a=e=>{let{project:t,resources:n}=e;const r=t.shortcut_to_show_widget;let a=!0;a=m(s,r),c(a),i("default",t.toggle_vertical_position),o({authenticated:!0,resources:n})},u=e=>{o({resources:e})};e.onload=()=>{window.addEventListener("message",(o=>{o.data?((e,t)=>{let{onSuccess:o,onShowPublicFeedback:r,reject:s,iframe:a,origin:u}=t;const l=`${u.replace("sidebar.","www.")}/users/sign_in?source=sidebar&utm_campaign=sidebar&redirect_to=${window.location.href}&anonymous_user_id=${e.data?.payload?.anonymousUserId}`,p=u.replace("sidebar.","www.");if("COOKIE_CHECK_RESPONSE"===e.data.eventName){h.project=e.data.payload.resources.project,h.resources=e.data.payload.resources,g=h.project,document.removeEventListener("keyup",w.bind(null,g)),document.addEventListener("keyup",w.bind(null,g));const t=h.project?.shortcut_to_show_widget,l=window.location.search.includes("showBugherd=true")||document.referrer.includes(p);let _=!0;_=m(p,t),h.project.is_active?null===h.project.id?(n("[BUGHERD] There is no project associated with this API Key"),s(),d()):!h.project.is_public||h.project.authenticated||l?!1!==h.hasStorageAccess||h.resources?.project.authenticated?e.data.payload.success&&!h.project.has_access?(c(_),i("warning",h.project.toggle_vertical_position)):e.data.payload.success?(c(_),o(h)):null===h.hasStorageAccess?(i("default",h.project.toggle_vertical_position),c(_)):(a.contentWindow.postMessage({eventName:"SHOW_UNAUTHENTICATED_UI",payload:{origin:u,projectOrigin:window.location.origin}},u),i("warning",h.project.toggle_vertical_position),h.project.authenticated&&c(_)):(i("default",h.project.toggle_vertical_position),c(_),a.contentWindow.postMessage({eventName:"AUTH_REQUEST_STORAGE_ACCESS",payload:{origin:u}},u)):r(h.resources):(n(`[BUGHERD] Project (${h.project.id}) is disabled`),s(),d())}else"AUTH_HAS_STORAGE_ACCESS_RESPONSE"===e.data.eventName?(h.hasStorageAccess=e.data.payload.success,null===e.data.payload.success?(n("[AUTH] hasStorageAccess: no api"),a.contentWindow.postMessage({eventName:"COOKIE_CHECK",payload:{origin:u}},u)):e.data.payload.success?(n("[AUTH] hasStorageAccess: true"),a.contentWindow.postMessage({eventName:"COOKIE_CHECK",payload:{origin:u}},u)):(n("[AUTH] hasStorageAccess: false"),a.contentWindow.postMessage({eventName:"COOKIE_CHECK",payload:{origin:u}},u))):"AUTH_REQUEST_STORAGE_ACCESS_RESPONSE"===e.data.eventName?e.data.payload.success?(h.hasStorageAccess=!0,n("[AUTH] requestStorageAccess: true"),i("default"),a.contentWindow.postMessage({eventName:"COOKIE_CHECK",payload:{origin:u,manuallyAuthenticated:!0}},u)):(n("[AUTH] requestStorageAccess: false"),i("warning"),e.data.payload.isAutoDenied&&(window.location.href=l)):"AUTH_REDIRECT_TO_LOGIN"===e.data.eventName&&(window.location.href=l);var g})(o,{onSuccess:a,onShowPublicFeedback:u,reject:r,iframe:e,origin:t}):console.warn("Received message with null or undefined data")})),e.contentWindow.postMessage({eventName:"AUTH_HAS_STORAGE_ACCESS",payload:{origin:t}},t)}}))})).catch((e=>{throw console.error(e),new Error("Cannot initialize BugHerd")}))},E="hasStorageAccess"in document&&"requestStorageAccess"in document,y=e=>document.requestStorageAccess().then((()=>{e(!0)}),(()=>{e(!1)})),f=()=>document.getElementById("bugherd_toggle"),b=e=>{f().onclick=e},S=(e,t)=>{if(!e)return;e.custom_logo_dark_background&&(document.getElementById("logo").src=e.custom_logo_dark_background);const o=f();if(o?.style){if(e.id){const n=t({projectId:e.id,preference:"togglePosition"});n&&(o.style.transform=`translate(0px, ${n}px)`)}e.toggle_vertical_position&&(o.style.top=`calc((100% - 70px) * ${e.toggle_vertical_position/100})`)}};var A=o(87851).Z;var v=o(9571);const T=e=>{let{projectId:t,preferences:o}=e;try{const e={...JSON.parse(window.localStorage.getItem(`sidebar_project_${t}`)),...o};window.localStorage.setItem(`sidebar_project_${t}`,JSON.stringify(e))}catch(e){console.log("Couldnt save user preferences to local storage",e)}},C=e=>{let{projectId:t,preference:o}=e;try{const e=JSON.parse(window.localStorage.getItem(`sidebar_project_${t}`));if(!e)return;return e[o]}catch(e){console.log("Couldnt get user preferences from local storage",e)}},N=e=>{let{sidebarOrigin:t,apiKey:o}=e;f();const r=(0,v.Z)();let s=!1;window.addEventListener("message",(e=>{if("COOKIE_CHECK"===e.data.eventName)n("[AUTH] Check cookies"),(async(e,t)=>{try{return await(async(e,t)=>{const o=new URL(`${e}/sidebar/resources?apikey=${t}`),r=await A(o.href,{method:"GET",credentials:"include",headers:{"Content-Type":"application/json"}}),s=await r.json();return s.project.is_active?s.project.authenticated?(n("[AUTH] _bugherd_session5 cookie valid"),{state:"AUTHENTICATED",resources:s}):(document.cookie.includes("_bugherd_session5")?n("[AUTH] _bugherd_session5 cookie invalid"):n("[AUTH] _bugherd_session5 cookie does not exist"),{state:"UNAUTHENTICATED",resources:s}):(n("[AUTH] Bugherd project disabled"),{state:"PROJECT_DISABLED",resources:s})})(e,t)}catch(e){if(n("[AUTH] _bugherd_session5 ping error"),e instanceof Error)throw e;throw n("[AUTH] unknown error raised",e),new Error("Unknown Error")}})(t,o).then((t=>{let{resources:o,state:a}=t;const{project:i}=o;if("UNAUTHENTICATED"===a)return n("[AUTH] Unauthenticated"),S(i,C),void e.source.postMessage({eventName:"COOKIE_CHECK_RESPONSE",payload:{success:!1,resources:o}},e.origin);if(n("[AUTH] Authenticated"),i.active_task_id&&T({projectId:i.id,preferences:{route:`/sidebar/tasks/${i.active_task_id}`}}),!i.is_active)return e.source.postMessage({eventName:"COOKIE_CHECK_RESPONSE",payload:{success:!1,resources:o}},e.origin);i.has_access?f().className="loading":i.authenticated?S(i,C):f().className="noAccess",S(i,C);const c=C({projectId:i.id,preference:"route"});let d=e.data.manuallyAuthenticated;window.sessionStorage.getItem("bugherdSidebarManuallyAuthenticated")&&(d=!0,window.sessionStorage.removeItem("bugherdSidebarManuallyAuthenticated")),c&&"/toggle"!==c||!d||T({projectId:i.id,preferences:{route:"/sidebar"}}),(d||s)&&T({projectId:i.id,preferences:{anonymousUserId:r,route:"/sidebar"}}),e.source.postMessage({eventName:"COOKIE_CHECK_RESPONSE",payload:{success:!0,resources:o}},e.origin)}));else if("AUTH_HAS_STORAGE_ACCESS"===e.data.eventName){n("[AUTH] AUTH_HAS_STORAGE_ACCESS");const t=t=>{e.source.postMessage({eventName:"AUTH_HAS_STORAGE_ACCESS_RESPONSE",payload:{success:t}},e.origin)};if(!E)return t(null);navigator.permissions.query({name:"storage-access"}).then((e=>{if("granted"===e.state)return y(t);document.hasStorageAccess().then((e=>{t(!!e)})).catch((()=>{t(!1)}))})).catch((e=>{"Type error"===e?.message?document.hasStorageAccess().then((e=>{t(!!e)})).catch((()=>{t(!1)})):(n("[AUTH] AUTH_HAS_STORAGE_ACCESS error",e),t(!1))}))}else if("AUTH_REQUEST_STORAGE_ACCESS"===e.data.eventName){let t;n("[AUTH] AUTH_REQUEST_STORAGE_ACCESS");const o=o=>{o||(f().className="error");const n=(new Date).getTime()-t<=250;e.source.postMessage({eventName:"AUTH_REQUEST_STORAGE_ACCESS_RESPONSE",payload:{success:o,isAutoDenied:n,anonymousUserId:r}},e.origin)};b((()=>{t=(new Date).getTime(),y(o),s=!0}))}else"SHOW_UNAUTHENTICATED_UI"===e.data.eventName&&(f().className="",b((()=>{e.source.postMessage({eventName:"AUTH_REDIRECT_TO_LOGIN",payload:{anonymousUserId:r}},e.origin)})))}),!1)},U=(e,t)=>{let{eventName:o,responseEventName:n,eventData:r={}}=e;n&&t&&((e,t)=>{const o=n=>{try{const r=JSON.parse(n.detail);t(r),window.removeEventListener(e,o)}catch(e){console.warn(e)}};window.addEventListener(e,o)})(n,t);const s=new CustomEvent(o,{bubbles:!0,cancelable:!0,detail:JSON.stringify(r)});window.document.dispatchEvent(s)};function O(t){if(U({eventName:"bugherdSidebarParsed",eventData:{status:"success",apiKey:t.apiKey}}),"loading"===document.readyState)return void document.addEventListener("readystatechange",(e=>{"interactive"===e.target.readyState&&O(t)}));const{resources:o,apiKey:n,origin:r,embeddedByOldExtension:s}=t,{project:a,user:i}=o,c=["app-images","app-vendor",a.is_public&&!a.authenticated?"public":"app"];(t=>{let{apiKey:o,origin:n,project:r,bugsnagSidebarNewKey:s,embeddedByOldExtension:a,files:i,user:c}=t;const d=document.createElement("bugherd-sidebar");d.style.cssText="\n    display: block;\n    position: absolute;\n    right: 0; \n    top: 0;\n    width: 100%;\n    height: unset;\n  ",e(d,"projectid",r.id.toString()),e(d,"project",JSON.stringify(r)),e(d,"user",JSON.stringify(c)),e(d,"bugsnagsidebarnewkey",s),e(d,"embeddedByOldExtension",a.toString()),d.addEventListener("keydown",(e=>{e.stopPropagation()})),d.addEventListener("focusin",(e=>{e.stopPropagation()})),d.addEventListener("wheel",(e=>{e.stopPropagation()})),d.addEventListener("mousewheel",(e=>{e.stopPropagation()}));const u=d.attachShadow({mode:"open"});u.innerHTML='<div id="sidebar-root"></div>',window.BUGHERD_REACT_APP_SIDEBAR_MOUNT=u.getElementById("sidebar-root"),window.BUGHERD_API_KEY=o,(e=>{let{origin:t,files:o}=e;o.forEach((e=>{const o=document.createElement("script");o.src=`${t.replace("www.","sidebar.")}/clients/sidebar/${e}.1754021914433.js`,window.document.head.appendChild(o)}))})({origin:n,files:i}),document.body.appendChild(d)})({apiKey:n,origin:r,project:a,bugsnagSidebarNewKey:o.config.bugsnag_sidebar_new_key,embeddedByOldExtension:s,files:c,user:i})}(async()=>{if(window.location.search.includes("elementor-preview"))return;let e=(e=>{let{embedUrl:t}=e;try{const e=JSON.parse(window.localStorage.getItem("BugHerd_change_project")||"[]"),o=window.localStorage.getItem("BugHerd_change_project_api_key"),n=new URL(t).search.replace(/^\?/,"").split("&").map((e=>e.split("="))).reduce(((e,t)=>(e[t[0]]=t[1],e)),{apikey:""}),r=n&&n.apikey?n.apikey:null,s=e.find((e=>e.from===r));return s?.to||o||r}catch{return null}})({embedUrl:document?.currentScript?.src||window.embedUrl});(document?.currentScript?.src||window.embedUrl).includes("utm_source=wordpress")&&(window.BUGHERD_WORDPRESS_INSTALL=!0),window.BUGHERD_EXTENSION_CONFIG?.apiKey&&(e=window.BUGHERD_EXTENSION_CONFIG.apiKey);let t=window.BUGHERD_EXTENSION_CONFIG?.domain||"https://localhost:3000",o="";if(document?.currentScript?.src){const e=new URL(document?.currentScript?.src);t=e.origin,o=e.search}const n=t.replace("www.","sidebar.");if("/sidebar/embed_html"===window.document.location.pathname)N({sidebarOrigin:n,apiKey:e});else{const r=document.getElementsByTagName("bugherd-sidebar")[0],s=document.querySelector('script[src*="sidebarv2"]')&&!document.currentScript?.getAttribute("src").includes("sidebarv2"),a=document.querySelector('script[src*="sidebarv2"]')&&document.currentScript?.getAttribute("src").includes("source=extension");if(r){const t=r.getAttribute("data-projectid");console.warn(`Bugherd: Wanted to embed project with apiKey ${e} but project with id ${t} was already embedded.`)}else if(s)console.warn("Bugherd: Wanted to embed project via new embed code (embed.js) but looks like old embed code exists (sidebarv2.js).");else if(window._bugHerd_sidebar2021)console.warn("Bugherd: Wanted to embed new sidebar but _bugHerd_sidebar2021 was already set");else{if(window._bugHerd_sidebar2021=!0,!e)throw new Error("Bugherd: sidebar is missing apiKey");try{const{resources:r}=await _({origin:n,search:o}),s=document.getElementsByTagName("bugherd-sidebar")[0];if(s){const t=s.getAttribute("data-projectid");console.warn(`Bugherd: Wanted to embed project with apiKey ${e} but project with id ${t} was already embedded after checking authentication.`)}O({apiKey:e,origin:t,resources:r,embeddedByOldExtension:!!a})}catch(e){console.error("[AUTH] Unauthenticated",e)}}}})()}()}();
//# sourceMappingURL=embed.1754021914433.js.map;
